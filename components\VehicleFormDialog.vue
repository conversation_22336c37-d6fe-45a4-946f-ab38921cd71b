<template>
  <v-dialog
    v-model="dialog"
    width="540"
    persistent
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="px-10 py-9">
      <v-tabs v-model="tab" height="0">
        <v-tab href="#tabVehicle">
          Create Vehicle
        </v-tab>
        <v-tab href="#tabVehicleType">
          Create Vehicle Type
        </v-tab>
      </v-tabs>

      <v-tabs-items v-model="tab">
        <v-tab-item value="tabVehicle">
          <vehicle-form
            :vehicle="vehicle"
            :vendor-id="vendorId"
            :dialog="dialog"
            @on-click-close="$emit('on-click-close')"
            @on-success-remove-image="$emit('on-success-remove-image')"
            @on-success-create="$emit('on-success-create')"
            @on-success-update="$emit('on-success-update')"
          >
            <template #tab-activator>
              <v-btn
                block
                large
                elevation="0"
                color="primary"
                class="text-capitalize"
                @click="tab = 'tabVehicleType'"
              >
                <p class="subtitle-1 ma-0">
                  {{ $t('vehicleFormDialog.button_manage_vehicle_type') }}
                </p>
                <v-icon>mdi-chevron-right</v-icon>
              </v-btn>
            </template>
          </vehicle-form>
        </v-tab-item>

        <v-tab-item value="tabVehicleType">
          <vehicle-type-form
            @on-click-close="$emit('on-click-close')"
          >
            <template #tab-activator>
              <v-btn
                text
                class="mb-6 px-3 text-capitalize"
                @click="tab= 'tabVehicle'"
              >
                <v-icon class="mr-3">
                  mdi-chevron-left
                </v-icon>
                <p class="ma-0 subtitle-1">
                  {{ $t('vehicleFormDialog.button_back') }}
                </p>
              </v-btn>
            </template>
          </vehicle-type-form>
        </v-tab-item>
      </v-tabs-items>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import VehicleForm from '~/components/VehicleForm.vue'
import VehicleTypeForm from '~/components/VehicleTypeForm.vue'
import { Vehicle } from '~/types/vehicle'

export default Vue.extend({
  name: 'VehicleFormDialog',

  components: {
    VehicleForm,
    VehicleTypeForm
  },

  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    vehicle: {
      type: Object as () => Vehicle | null,
      default: null
    },
    vendorId: {
      type: String,
      default: ''
    }
  },

  data: () => ({
    tab: ''
  })
})
</script>

<style scoped> </style>

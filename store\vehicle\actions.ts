import { ActionTree } from 'vuex'
import { VehicleState } from './state'
import { RootState } from '~/store'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<VehicleState, RootState> = {
  getItems ({ commit }, payload) {
    payload.entries === -1
      ? commit('SET_IS_LOADING_LIST', true)
      : commit('SET_IS_LOADING', true)

    const searchColumns = payload.searchColumns || 'name'
    if (!searchColumns.includes('name') || !searchColumns.includes('vehicle_details.plate_number')) {
      const additionalColumns = []
      if (!searchColumns.includes('name')) {
        additionalColumns.push('name')
      }
      if (!searchColumns.includes('vehicle_details.plate_number')) {
        additionalColumns.push('vehicle_details.plate_number')
      }
      payload.searchColumns = `${searchColumns},${additionalColumns.join(',')}`
    }

    commit('SET_SEARCH_KEY', payload.searchKey)

    this.$axios
      .get('/v1/vehicle', {
        params: {
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          search_columns: payload.searchColumns,
          sort_column: payload.sortColumn == null ? 'name' : payload.sortColumn,
          sort_type: payload.sortType == null ? 'ASC' : payload.sortType,
          filter_columns: payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          page: payload.page == null ? '' : payload.page,
          entries: payload.entries == null ? null : payload.entries
        }
      })
      .then((response: any) => {
        if (payload.entries === -1) {
          commit('SET_LIST_VEHICLES', response.data.data)
        } else {
          commit('SET_RESULT', response.data)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        payload.entries === -1
          ? commit('SET_IS_LOADING_LIST', false)
          : commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.value.name)
    formData.append('length', payload.value.length)
    formData.append('width', payload.value.width)
    formData.append('height', payload.value.height)
    formData.append('max_volume', payload.value.maxVolume)
    formData.append('max_weight', payload.value.maxWeight)
    formData.append('vehicle_type_id', payload.value.vehicleTypeId)
    formData.append('vendor_id', payload.vendorId)

    payload.value.featureIds.forEach((item: any) => {
      formData.append('feature_ids[]', item.value)
    })

    if (payload.value.photo) {
      formData.append('photo', payload.value.photo)
    }

    return await this.$axios
      .post('/v1/vehicle', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async editItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.value.name)
    formData.append('length', payload.value.length)
    formData.append('width', payload.value.width)
    formData.append('height', payload.value.height)
    formData.append('max_volume', payload.value.maxVolume)
    formData.append('max_weight', payload.value.maxWeight)
    formData.append('vehicle_type_id', payload.value.vehicleTypeId)
    formData.append('vendor_id', payload.vendorId)

    payload.value.featureIds.forEach((item: any) => {
      formData.append('feature_ids[]', item.value)
    })

    formData.append('_method', 'PUT')

    if (payload.value.photo) {
      formData.append('photo', payload.value.photo)
    }

    return await this.$axios
      .post('/v1/vehicle/' + payload.value.id, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .delete('/v1/vehicle/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },
  async addItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    const formData = new FormData()
    for (const item of payload.vehicle_detail_ids) {
      formData.append('vehicle_detail_ids[]', item)
    }
    formData.append('_method', 'PUT')

    return await this.$axios
      .post('/v1/vehicle/' + payload.id + '/add-vehicle-details', formData)
      .then((response: any) => {
        // toastSuccess(response.data.message, this)
        // dispatch('getItems', {
        //   page: 1,
        //   filterColumns: 'vendor_id',
        //   filterKeys: payload.vendorId,
        //   entries: payload.entries
        // })
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  selectVehicle ({ commit }, payload) {
    commit('SET_SELECTED_VEHICLE', payload)
  },

  clearVehicles ({ commit }) {
    commit('SET_SELECTED_VEHICLE', null)
    commit('SET_VEHICLE_LIST_ITEM', [])
    commit('SET_PAGE', 1)
    commit('SET_TOTAL_PAGE', 1)
  },

  increaseVehicleQuantity ({ commit }, payload) {
    const qty = payload.quantity ?? 0
    commit('SET_VEHICLE_QUANTITY', { id: payload.id, quantity: qty + 1 })
  },

  decreaseVehicleQuantity ({ commit }, payload) {
    const qty = payload.quantity ?? 0
    const newQty = Math.max(0, qty - 1)
    
    commit('SET_VEHICLE_QUANTITY', { id: payload.id, quantity: newQty })
    
    const maxQty = payload.vehicle_details_count
    commit('SET_EXCEED_STATUS', { id: payload.id, isExceeded: newQty > maxQty })
  },

  weightVehicle ({ commit }, payload) {
    const weightVehicle = payload.weightValue
    commit('SET_WEIGHT_VEHICLE', { id: payload.vendor.vendor.id, weight: weightVehicle })
  },

  async removeImage ({ commit }, payload) {
    commit('SET_IS_LOADING_DELETE', true)

    return await this.$axios.delete('/v1/vehicle/' + payload.vehicleId + '/image')
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_DELETE', false)
      })
  }
}

export default actions

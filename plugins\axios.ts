export default function (context: any) {
  const pendingRequests: any[] = []

  context.$axios.interceptors.request.use((config: any) => {
    const CancelToken = context.$axios.CancelToken
    const source = CancelToken.source()
    
    // Add timezone offset header
    const timezoneOffset = new Date().getTimezoneOffset()
    config.headers['X-Timezone-Offset'] = timezoneOffset.toString()
    
    config.cancelToken = source.token
    pendingRequests.push(source)
    
    return config
  })

  context.$axios.interceptors.response.use(
    (response: any) => {
      context.store.dispatch('network-error/setIsNetworkError', false)
      
      const index = pendingRequests.indexOf(response.config.cancelToken.source)
      if (index > -1) {
        pendingRequests.splice(index, 1)
      }
      
      return response
    },
    async (error: any) => {
      if (error.message === 'Network Error') {
        context.store.dispatch('network-error/setIsNetworkError', true)
        await context.store.dispatch('network-error/retryAction')
      }
      return Promise.reject(error)
    }
  )

  context.app.router.beforeEach((to: any, from: any, next: any) => {
    pendingRequests.forEach((request) => {
      request.cancel('Navigation cancelled')
    })
    pendingRequests.length = 0
    next()
  })
}

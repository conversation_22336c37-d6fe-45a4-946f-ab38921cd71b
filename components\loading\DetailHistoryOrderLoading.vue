<template>
  <div>
    <v-sheet v-if="$vuetify.breakpoint.xs" height="300">
      <v-skeleton-loader type="image" height="150" style="border-radius: 0 !important;" />
      <v-skeleton-loader type="image" height="150" style="border-radius: 0 !important;" />
    </v-sheet>
    <v-sheet v-if="!$vuetify.breakpoint.xs" height="600">
      <div v-for="i in 3" :key="i">
        <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
      </div>
    </v-sheet>
    <v-row >
      <v-col class="col-12 col-sm-5">
        <v-sheet height="730" width="100%" class="mt-5 mb-5 pa-10">
          <div v-if="$vuetify.breakpoint.xs">
            <v-skeleton-loader type="image" height="25" width="100%" class="mb-10" style="border-radius: 12px !important;" />
            <v-skeleton-loader type="image" height="25" width="75%" class="mb-10" style="border-radius: 12px !important;" />
          </div>
          <div v-if="!$vuetify.breakpoint.xs">
            <v-skeleton-loader type="heading" width="100%" class="mb-10" />
            <v-skeleton-loader type="heading" width="75%" class="mb-5" />
          </div>
          <v-container class="d-flex justify-space-between pa-0">
            <div v-for="i in 2" :key="i">
              <div class="d-flex flex-column">
                <v-skeleton-loader type="text" width="100" />
                <v-skeleton-loader type="text" width="100" />
              </div>
            </div>
          </v-container>
          <v-skeleton-loader type="heading" width="100%" class="mt-5 mb-5" />
          <v-container class="d-flex justify-space-between pa-0 mb-5">
            <div v-for="i in 3" :key="i">
              <div class="d-flex flex-column">
                <div v-if="$vuetify.breakpoint.xs">
                  <v-skeleton-loader type="text" width="60" />
                  <v-skeleton-loader type="text" width="60" />
                </div>
                <div v-if="!$vuetify.breakpoint.xs">
                  <v-skeleton-loader type="text" width="100" />
                  <v-skeleton-loader type="text" width="100" />
                </div>
              </div>
            </div>
          </v-container>
          <v-divider class="mb-10" />
          <v-skeleton-loader type="heading" class="mb-5" />
          <v-skeleton-loader type="heading" class="mb-5" width="75%" />
          <div class="d-flex flex-row" style="width: 100%">
            <v-skeleton-loader type="image" width="70" height="70" />
            <div class="d-flex flex-column" style="width: 100%">
              <div v-if="$vuetify.breakpoint.xs">
                <v-skeleton-loader type="image" height="25" width="100%" class="mt-3 ml-6" style="border-radius: 12px !important;" />
                <v-skeleton-loader type="text" width="75%" class="mt-3 ml-6" />
              </div>
              <div v-if="!$vuetify.breakpoint.xs">
                <v-skeleton-loader type="heading" width="50%" class="mt-3 ml-6" />
                <v-skeleton-loader type="text" width="15%" class="mt-3 ml-6" />
              </div>
            </div>
          </div>
          <v-divider class="mt-10 mb-10" />
          <v-skeleton-loader type="text" class="mt-10" />
          <v-skeleton-loader type="text" width="75%" class="mt-10" />
        </v-sheet>
        <v-sheet height="420" width="100%" class="pa-10">
          <v-skeleton-loader type="heading" class="mb-16" />
          <v-skeleton-loader type="heading" width="75%" class="mt-16 mb-12" />
          <div class="d-flex flex-row" style="width: 100%">
            <v-skeleton-loader type="image" width="70" height="70" />
            <div class="d-flex flex-column" style="width: 100%">
              <div v-if="$vuetify.breakpoint.xs">
                <v-skeleton-loader type="image" height="25" width="100%" class="mt-3 ml-6" style="border-radius: 12px !important;" />
                <v-skeleton-loader type="text" width="75%" class="mt-3 ml-6" />
              </div>
              <div v-if="!$vuetify.breakpoint.xs">
                <v-skeleton-loader type="heading" width="50%" class="mt-3 ml-6" />
                <v-skeleton-loader type="text" width="15%" class="mt-3 ml-6" />
              </div>
            </div>
          </div>
          <v-container class="d-flex justify-space-between pa-0 mb-5 mt-16">
            <div class="d-flex flex-column">
              <v-skeleton-loader type="text" width="150" />
              <v-skeleton-loader type="text" width="100" />
            </div>
            <v-skeleton-loader type="button" />
          </v-container>
        </v-sheet>
      </v-col>
      <v-col class="col-sm-7 col-12">
        <v-sheet height="1000" width="100%" class="d-flex flex-column mt-5 mb-5 overflow-hidden pa-10">
          <v-container class="d-flex justify-space-between mb-5 pa-0">
            <v-skeleton-loader type="text" width="300" />
            <v-skeleton-loader type="text" width="150" />
          </v-container>
          <div v-for="i in 2" :key="i">
            <v-skeleton-loader type="heading" class="mb-5 mt-10" />
            <v-container class="d-flex flex-column justify-center pl-16 pr-16">
              <v-skeleton-loader type="image" width="100%" height="150" style="border-radius: 0 !important;" />
              <v-skeleton-loader type="image" width="100%" height="150" style="border-radius: 0 !important;" />
            </v-container>
          </div>
        </v-sheet>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'DetaiHistoryOrderLoading'
})
</script>

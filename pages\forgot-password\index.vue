<template>
  <v-row class="ma-0 d-flex">
    <v-col class="pa-10 d-flex">
      <change-password
        :is-forgot-password="true"
        @on-click-send="changePassword($event)"
      />
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import ChangePassword from '~/components/ChangePassword.vue'

export default Vue.extend({
  name: 'ChangePasswordPage',

  components: { ChangePassword },

  data: () => ({
    token: null as string | null
  }),

  mounted () {
    const urlParams = new URLSearchParams(window.location.search)
    this.token = urlParams.get('token')
  },

  methods: {
    async changePassword (form: any) {
      const response = await this.$store.dispatch('password/changePassword', {
        password: form.password,
        password_confirmation: form.passwordConfirmation,
        token: this.token
      })
      if (response) {
        this.$router.push(('/forgot-password/success-change-password'))
      }
    }
  }
})
</script>

<style lang="scss" scoped></style>

import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { UserState } from './state'

export const getters: GetterTree<UserState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataLogs (state) {
    return {
      itemLogs: state.itemLogs,
      totalPageLogs: state.totalPageLogs,
      pageLogs: state.pageLogs
    }
  },

  dataPermission (state) {
    return {
      itemsPermission: state.itemsPermission
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },
  isLoadingFormClearImage (state) {
    return state.isLoadingFormClearImage
  }
}

export default getters

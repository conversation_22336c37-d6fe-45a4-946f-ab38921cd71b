import { Vehicle, VehicleDetail } from "~/types/vehicle";
import { Order, PickupDropOffLocationPoint, Product } from "~/types/product";
import { LogisticsServiceProvider, Vendor } from "~/types/user";
import { ShipmentHistory } from "~/types/shipment-history";
import { Invoice } from "~/types/invoice";
import { Driver } from "~/types/driver";

export interface Route {
  id: string;
  type: string;
  estimated_time_arrival: string;
  duration: number;
  wait: number;
  odometer: number;
  order: number;
  track_id: string;
  pickup_drop_off_location_point_id: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
  pickup_drop_off_location_point: PickupDropOffLocationPoint;
  products: Product[];
  shipment_history?: ShipmentHistory;
}

export interface Direction {
  id: string;
  type: string;
  longitude: string;
  latitude: string;
  track_id: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
}

export interface Track {
  weight_bridge: any;
  shipment_vendor_id: string;
  id: string;
  identity: string;
  shipment_id: string;
  vehicle_detail_id: string;
  driver_id: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
  vehicle_detail?: VehicleDetail | any;
  driver?: Driver;
  routes: Route[];
  directions: Direction[];
  current_status: string;
  shipment_company_name: string;
  selected_vehicle_detail_id?: string[];
  selected_driver_id?: string[];
  status: string;
  shipment_vendor: any;
  weight_bridges: any;
  tag: string;
}

export interface Shipment {
  id: string;
  identity: string;
  read_at: Date | null;
  status: string;
  vendor_id: string;
  vendor?: Vendor;
  logistics_service_provider?: LogisticsServiceProvider;
  logistics_service_provider_id: string;
  driver_id: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
  orders: Order[];
  tracks_count: number;
  tracks: Track[];
  invoice_logistic_service_provider?: Invoice;
  invoice_vendor?: Invoice;
  total_volume?: number;
  total_weight?: number;
  total_dimension_length?: number;
  total_dimension_width?: number;
  total_dimension_height?: number;
  total_odometer?: number;
  route_state?: string;
  route_state_message?: string;
  shipment_vendors: any;
}

export interface Destination {
  route_id: string;
  loc_name: string;
  lat: string;
  lng: string;
  type: string;
}

export interface TrackActiveOrder {
  id: string;
  driver: string;
  destinations: Destination[];
  live_tracking_status: string;
  plate_number: string;
  vehicle_detail_id: string;
  vehicle_synced: string;
}

export interface ShipmentActiveOrder {
  order_identity: string;
  order_id: string;
  id: string;
  tracks: TrackActiveOrder[];
}

export interface ShipmentVendor {
  id: string;
  shipment_id: string;
  vendor_id: string;
  deleted_at: null;
  created_at: Date;
  updated_at: Date;
  status: string;
  note: null;
  response: null | string;
  weight: string;
  type: string;
  ritase_identity: number | null;
  expired_at: null;
  system_accepted_at: null;
  user_accepted_at: Date | null;
  tracks_count?: number;
  vendor?: null;
  vehicles: Vehicle[];
  shipment?: Shipment;
  tracks?: Track[];
  disable?: boolean;
}

export interface VehicleDevice {
  lat: number;
  lng: number;
  course: number;
  icon_url: string;
  plate_number: string;
  vehicle_detail_id: string;
  eta: string;
  eta_in_second: number;
  status: string;
  order_identity: string;
  track_id: string;
  driver_name: string;
  track: Track;
}

export interface DriverDevice {
  id: number;
  attributes: {
    batteryLevel: number;
    distance: number;
    totalDistance: number;
    motion: boolean;
  };
  deviceId: number;
  protocol: string;
  serverTime: Date;
  deviceTime: Date;
  fixTime: Date;
  outdated: boolean;
  valid: boolean;
  latitude: number;
  longitude: number;
  altitude: number;
  speed: number;
  course: number;
  address?: string;
  accuracy: number;
  network?: any;
  geofenceIds?: any;
  plate_number: string;
  vehicle_detail_id: string;
  track: Track;
}

export interface LiveTracking {
  vehicle_device: VehicleDevice[];
  driver_device: DriverDevice[];
}

export interface Playback {
  latitude: number
  longitude: number
  speed: number
  course: number
  timestamp: string
  order: number
}

export interface PointOfInterest {
  fms_poi_id: number
  name: string
  description: string
  lat: string
  long: string
  icon_url: string
}

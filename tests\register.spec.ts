import { test, expect } from '@playwright/test'

test('Register via LSP Action Success', async ({ page }) => {
  await page.goto('http://localhost:3000/')

  await page.getByRole('button', { name: 'Register' }).click()

  await expect(page).toHaveURL(/.*register-vendor/)
  await expect(page.getByText('Registration').first()).toBeVisible()

  await expect(page.getByTestId('image-side').first()).toBeVisible()

  const randomName = Math.random().toString(36).substring(2, 15)
  const randomAddress = Math.random().toString(36).substring(2, 15)
  const randomUsername = Math.random().toString(36).substring(2, 15)
  const randomPhone = Math.floor(Math.random() * 10000000000)
  const randomEmail = Math.random().toString(36).substring(2, 15) + '@example.com'
  const password = 'password'

  await page
    .getByTestId('input-name')
    .fill(randomName)

  await page
    .getByTestId('input-address')
    .fill(randomAddress)

  await page
    .getByTestId('input-username')
    .fill(randomUsername)

  await page
    .getByTestId('input-phone-country-code')
    .fill('62')

  await page
    .getByTestId('input-phone')
    .fill(`${randomPhone}`)

  await page
    .getByTestId('input-email')
    .fill(randomEmail)

  await page
    .getByTestId('input-password')
    .fill(password)

  await page
    .getByTestId('input-password-confirmation')
    .fill(password)

  await page.getByRole('button', { name: 'Registration' }).click()

  await expect(page.locator('h3').first()).toHaveText('Dashboard')
})

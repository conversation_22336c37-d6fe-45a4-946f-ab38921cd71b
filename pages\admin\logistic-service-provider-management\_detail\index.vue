<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-row class="d-flex mt-4 mb-8">
      <v-btn tile text class="ml-2" @click="$router.back()">
        <v-icon left color="black">
          mdi-chevron-left
        </v-icon>
        {{ $t('adminLsp.back_to_list_lsp') }}
      </v-btn>
    </v-row>

    <v-row class="mb-8 mx-0 pa-5 rounded white">
      <v-col class="d-flex flex-row align-center">
        <v-avatar rounded size="80" class="ma-8">
          <image-component v-if="company?.logo_url" :image="company?.logo_url" max-width="40" max-height="40" />

          <v-icon v-else color="secondary">
            mdi-domain
          </v-icon>
        </v-avatar>
        <v-col>
          <h4 class="ml-2 text-wrap ma-4">
            {{ company?.name }}
          </h4>
          <p class="ml-2 text-wrap" style="color: #6E6666">
            {{ company?.domain }}
          </p>
        </v-col>
      </v-col>

      <v-divider vertical />
      <v-col
        class="flex flex-column justify-content-center align-items-center ma-8"
      >
        <div class="body-1 text-secondary">
          {{ $t('adminLsp.address') }}
        </div>
        <div class="body-1 black--text">
          {{ company?.address }}
        </div>
      </v-col>
    </v-row>

    <tab-component>
      <template #tab>
        <v-tab class="subtitle-1 text-capitalize">
          {{ $t('adminLsp.user') }}
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Product Owner
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Transporter
        </v-tab>
      </template>

      <template #tab-item>
        <v-tab-item>
          <v-container fluid class="pa-0">
            <header-datatable
              default-sort-column="name"
              default-sort-type="asc"
              :sort-column-items="sortColumnItemsAccounts"
              :sort-type-items="sortTypeItems"
              sort-column-id="sort_column_user"
              sort-type-id="sort_type_user"
              @on-filter-change="filterUsers"
              @on-search-icon-click="searchUsers"
            >
              <template #button>
                <user-form-item
                  label="User Logistic Provider"
                  :is-loading-form="isLoadingForm"
                  :dialog="dialogCreateUser"
                  :clear-form="clearForm"
                  @on-click-save="createUser"
                  @on-close-dialog="
                    dialogCreateUser = false
                    clearForm = true
                  "
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      v-if="$vuetify.breakpoint.xs"
                      depressed
                      color="primary"
                      height="52"
                      v-bind="attrs"
                      block
                      class="mb-4 mt-5"
                      v-on="on"
                      @click="
                        dialogCreateUser = true
                        clearForm = false
                      "
                    >
                      {{ $t('adminLsp.add_user') }}
                    </v-btn>
                    <v-btn
                      v-else
                      depressed
                      color="primary"
                      height="52"
                      v-bind="attrs"
                      v-on="on"
                      @click="
                        dialogCreateUser = true
                        clearForm = false
                      "
                    >
                      {{ $t('adminLsp.add_user') }}
                    </v-btn>
                  </template>
                </user-form-item>
              </template>
            </header-datatable>
          </v-container>

          <users-loading v-if="isLoading" />

          <v-container v-else fluid class="pa-0 mb-10">
            <v-row v-if="dataAccount.items.length !== 0" class="ma-n5">
              <v-col
                v-for="(item, i) in dataAccount.items"
                :key="item.id"
                md="4"
                sm="6"
                class="pa-5"
              >
                <user-card-item
                  :user="item"
                  :is-loading-form="isLoadingForm"
                  :is-loading-form-clear-image="isLoadingFormClearImage"
                  :index="i"
                  :is-has-dialog-delete-user="true"
                  :is-has-dialog-active-user="true"
                  :is-has-dialog-permission-user="true"
                  :dialog-update-user="dialogUpdateUser[i]"
                  :dialog-delete-user="dialogDeleteUser[i]"
                  :dialog-active-user="dialogActiveUser[i]"
                  :permission-user-id="permissionUsers"
                  :dialog-permission-user="dialogPermissionUser[i]"
                  @on-click-edit="editUser"
                  @on-click-delete="deleteUser"
                  @on-click-active="activeUser"
                  @on-click-permission="permissionUser"
                  @on-click-end-session="endSession"
                  @on-clear-image="clearImage($event, i)"
                  @on-open-update-dialog="$set(dialogUpdateUser, i, true)"
                  @on-close-update-dialog="$set(dialogUpdateUser, i, false)"
                  @on-open-delete-dialog="$set(dialogDeleteUser, i, true)"
                  @on-close-delete-dialog="$set(dialogDeleteUser, i, false)"
                  @on-open-active-dialog="$set(dialogActiveUser, i, true)"
                  @on-close-active-dialog="$set(dialogActiveUser, i, false)"
                  @on-open-permission-dialog="$set(dialogPermissionUser, i, true)"
                  @on-close-permission-dialog="$set(dialogPermissionUser, i, false)"
                />
              </v-col>
            </v-row>

            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-placeholder.svg"
                  :message-title="$t('adminLsp.empty_message_title_lsp_user')"
                  :message-description="$t('adminLsp.empty_message_description_lsp_user')"
                />
              </v-col>
            </v-row>
          </v-container>

          <pagination-component
            :page="dataAccount.page"
            :total-page="dataAccount.totalPage"
            page-id="page_user"
            class="float-end"
            @on-change-page="getUsers({
              page: $event,
              filter: {
                sortColumn: $route.query?.sort_column_user,
                sortType: $route.query?.sort_type_user
              }
            })"
          />
        </v-tab-item>

        <v-tab-item>
          <v-container fluid class="pa-0">
            <header-datatable
              default-sort-column="name"
              default-sort-type="asc"
              :sort-column-items="sortColumnItemsSC"
              :sort-type-items="sortTypeItems"
              sort-column-id="sort_column_sc"
              sort-type-id="sort_type_sc"
              @on-filter-change="getDataSC({filter: $event, page: $route.query?.page_sc})"
              @on-search-icon-click="getDataSC({searchKey: $event})"
            />
          </v-container>

          <company-loading v-if="isLoadingSc" />

          <v-container v-else fluid class="pa-0 mb-10">
            <v-row v-if="dataShipmentCompany.items.length !== 0" class="ma-n5">
              <v-col
                v-for="(item, i) in dataShipmentCompany.items"
                :key="i"
                md="4"
                sm="6"
                class="pa-5"
              >
                <sc-card-item
                  v-if="!isLoadingFormSc && !isLoadingSc"
                  :shipping-company="item"
                  @on-click-block="blockShipmentCompany"
                  @on-click-collaborate="unblockShipmentCompany"
                />
              </v-col>
            </v-row>

            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-shipment.svg"
                  :message-title="$t('adminLsp.empty_message_title_lsp_sc')"
                  :message-description="$t('adminLsp.empty_message_description_lsp_sc')"
                />
              </v-col>
            </v-row>
          </v-container>

          <pagination-component
            :page="dataShipmentCompany.page"
            :total-page="dataShipmentCompany.totalPage"
            page-id="page_sc"
            class="float-end"
            @on-change-page="getDataSC({
              page: $event,
              filter: {
                sortColumn: $route.query?.sort_column_sc,
                sortType: $route.query?.sort_type_sc
              }
            })"
          />
        </v-tab-item>

        <v-tab-item>
          <v-container fluid class="pa-0">
            <header-datatable
              default-sort-column="name"
              default-sort-type="asc"
              :sort-column-items="sortColumnItemsVendors"
              :sort-type-items="sortTypeItems"
              sort-column-id="sort_column_vendor"
              sort-type-id="sort_type_vendor"
              @on-filter-change="filterVendor"
              @on-search-icon-click="searchVendor"
            />
          </v-container>

          <vendor-company-loading v-if="isLoadingVendor" />

          <v-container v-else fluid class="pa-0 mb-10">
            <v-row v-if="dataVendor.items.length !== 0" class="ma-n5">
              <v-col
                v-for="item in dataVendor.items"
                :key="item.id"
                md="4"
                sm="6"
                class="pa-5"
              >
                <user-role-card-item
                  :item="item"
                  :is-has-menu="false"
                  :is-has-status="true"
                  :is-loading-form="isLoadingForm"
                  :is-has-join-date-admin="true"
                  :is-location-visible="true"
                  detail-route="/admin/vendor-management"
                  @on-click-save-edit="editVendor"
                  @on-click-save-delete="deleteVendor"
                />
              </v-col>
            </v-row>

            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-shipment.svg"
                  :message-title="$t('adminLsp.empty_message_title_lsp_vendor')"
                  :message-description="$t('adminLsp.empty_message_description_lsp_vendor')"
                />
              </v-col>
            </v-row>
          </v-container>

          <pagination-component
            :page="dataVendor.page"
            :total-page="dataVendor.totalPage"
            page-id="page_vendor"
            class="float-end"
            @on-change-page="getDataVendors({
              page: $event,
              filter: {
                sortColumn: $route.query?.sort_column_vendor,
                sortType: $route.query?.sort_type_vendor
              }
            })"
          />
        </v-tab-item>
      </template>
    </tab-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserCardItem from '~/components/UserCardItem.vue'
import ScCardItem from '~/components/ScCardItem.vue'
import UsersLoading from '~/components/loading/UsersLoading.vue'
import CompanyLoading from '~/components/loading/CompanyLoading.vue'
import VendorCompanyLoading from '~/components/loading/VendorCompanyLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import TabComponent from '~/components/TabComponent.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'LogisticServiceProviderDetailPage',
  components: {
    UserCardItem,
    ScCardItem,
    UsersLoading,
    CompanyLoading,
    VendorCompanyLoading,
    EmptyPlaceholder,
    TabComponent,
    PaginationComponent
  },
  layout: 'admin/body',
  middleware: ['auth', 'is-admin'],
  data: () => ({
    tab: null,
    searchKeyAccount: '',
    searchKeyVendor: '',
    searchKeySc: '',
    sortColumnItemsVendors: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortColumnItemsAccounts: {
      name: {
        label: 'Name',
        value: 'name'
      },
      email: {
        label: 'Email',
        value: 'email'
      },
      phoneNumber: {
        label: 'Phone Number',
        value: 'phone_number'
      }
    },
    sortColumnItemsSC: {
      name: {
        label: 'Name',
        value: 'name'
      },
      address: {
        label: 'Address',
        value: 'address'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateUser: false,
    dialogUpdateUser: [] as Array<Boolean>,
    dialogDeleteUser: [] as Array<Boolean>,
    dialogActiveUser: [] as Array<Boolean>,
    dialogPermissionUser: [] as Array<Boolean>,
    clearForm: false
  }),
  computed: {
    dataAccount () {
      return this.$store.getters['users/data']
    },
    dataVendor () {
      return this.$store.getters['admin/logistic-service-provider/vendor/data']
    },
    dataShipmentCompany () {
      return this.$store.getters['shipping-company/data']
    },
    company () {
      const items = this.$store.getters['users/data'].items

      if (items.length > 0) {
        return items[0].logistics_service_provider
      }

      return null
    },
    isLoading () {
      return this.$store.getters['users/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    },
    isLoadingSc () {
      return this.$store.getters['shipping-company/isLoading']
    },
    isLoadingFormSc () {
      return this.$store.getters['shipping-company/isLoadingForm']
    },
    isLoadingVendor () {
      return this.$store.getters['admin/logistic-service-provider/vendor/isLoading']
    },
    permissionUsers () {
      return this.$store.getters['users/dataPermission'].itemsPermission
    }
  },
  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'Logistic Provider'
    )
    this.getUsers({
      page: this.$route.query?.page_user as string
    })

    this.getDataSC({
      page: this.$route.query?.page_sc as string
    })

    this.getDataVendors({
      page: this.$route.query?.page_vendor as string
    })

    this.getPermissionUsers()
  },
  methods: {
    clearImage (id: string, index: number) {
      this.$store.dispatch('users/removeAvatar', id)
        .then(() => {
          this.getUsers({})
        })
        .then(() => {
          this.$set(this.dialogUpdateUser, index, true)
        })
    },
    async unblockShipmentCompany (data: any) {
      if (data.status === 'BLOCK') {
        await this.$store.dispatch('shipping-company/blockUnblock', {
          idLsp: this.$route.params.detail,
          idSc: data.id
        })
      }
    },
    async blockShipmentCompany (data: any) {
      if (data.status === 'COLLABORATE') {
        await this.$store.dispatch('shipping-company/blockUnblock', {
          idLsp: this.$route.params.detail,
          idSc: data.id
        })
      }
    },
    getUsers ({ page = '' }) {
      this.$store.dispatch('users/getItems', {
        filterKeys: this.$route.params.detail,
        searchKey: this.searchKeyAccount,
        role: 'LOGISTIC_SERVICE_PROVIDER',
        page
      })
    },
    getDataSC ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('shipping-company/getItems', {
        filterColumns: 'logistics_service_provider_id',
        filterKeys: this.$route.params.detail,
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page,
        fetchDetail: false,
        entries: 9
      })
    },
    getDataVendors ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page,
        entries: 9
      })
    },
    searchUsers (searchKey: any) {
      this.searchKeyAccount = searchKey

      this.$store.dispatch('users/getItems', {
        filterKeys: this.$route.params.detail,
        searchKey,
        role: 'LOGISTIC_SERVICE_PROVIDER',
        page: 1
      })
    },
    filterUsers (filter: any) {
      this.$store.dispatch('users/getItems', {
        filterKeys: this.$route.params.detail,
        searchColumns: filter.searchColumns,
        searchKey: this.searchKeyAccount,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        role: 'LOGISTIC_SERVICE_PROVIDER',
        page: this.$route.query?.page_user
      })
    },
    async createUser (value: any) {
      const response = await this.$store.dispatch('users/createItem', {
        value,
        selectedId: this.$route.params.detail,
        role: 'LOGISTIC_SERVICE_PROVIDER'
      })
      if (response) {
        this.dialogCreateUser = false
        this.clearForm = true

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async editUser (value: any, i: any) {
      const response = await this.$store.dispatch('users/editItem', {
        value,
        id: value.id,
        role: 'LOGISTIC_SERVICE_PROVIDER'
      })
      if (response) {
        this.$set(this.dialogUpdateUser, i, false)

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async deleteUser (id: any, i: any) {
      const response = await this.$store.dispatch('users/deleteItem', {
        id,
        selectedId: this.$route.params.detail,
        role: 'LOGISTIC_SERVICE_PROVIDER'
      })

      if (response) {
        this.$set(this.dialogDeleteUser, i, false)

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async activeUser (id: any, i: any) {
      const response = await this.$store.dispatch('users/enableUser', {
        id
      })

      if (response) {
        this.$set(this.dialogActiveUser, i, false)

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async permissionUser (id: any, i: any, permissionId: any) {
      const response = await this.$store.dispatch('users/updatePermission', {
        id,
        permission_id: permissionId
      })

      if (response) {
        this.$set(this.dialogPermissionUser, i, false)

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async endSession (id: any) {
      const response = await this.$store.dispatch('users/endSession', {
        id
      })

      if (response) {
        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    paginationVendor (page: any) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchKey: this.searchKeyVendor,
        page
      })
    },
    searchVendor (searchKey: any) {
      this.searchKeyVendor = searchKey

      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchColumns: 'name',
        searchKey: this.searchKeyVendor,
        page: 1
      })
    },
    filterVendor (filter: any) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchColumns: filter.searchColumns,
        searchKey: this.searchKeyAccount,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page: this.$route.query?.page_vendor
      })
    },
    createVendor (value: any) {
      this.$store.dispatch(
        'admin/logistic-service-provider/vendor/createItem',
        {
          value,
          selectedId: this.$route.params.detail
        }
      )
    },
    editVendor (value: any) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/editItem', {
        value,
        selectedId: this.$route.params.detail
      })
    },
    deleteVendor (id: any) {
      this.$store.dispatch(
        'admin/logistic-service-provider/vendor/deleteItem',
        {
          id,
          selectedId: this.$route.params.detail
        }
      )
    },

    getPermissionUsers () {
      this.$store.dispatch('users/getPermissionUsers')
    }
  }
})
</script>

<style scoped> </style>

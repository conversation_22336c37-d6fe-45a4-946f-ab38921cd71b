<template>
  <v-container fluid class="pa-0 px-md-10 px-8 mb-10">
    <v-row class="d-flex ma-0 mt-4 mb-8">
      <v-btn tile text class="text-capitalize" @click="$router.back()">
        <v-icon left color="black">
          mdi-chevron-left
        </v-icon>
        {{ $t('adminSc.back_to_list_sc') }}
      </v-btn>
    </v-row>

    <v-row class="ma-0 mb-8 mx-0 pa-5 rounded white">
      <v-col class="d-flex flex-row align-center">
        <div class="ma-8">
          <image-component v-if="company?.logo_url" :image="company?.logo_url" max-width="40" max-height="40" />

          <v-icon v-else size="55">
            mdi-domain
          </v-icon>
        </div>

        <h4 class="ml-2 text-wrap ma-4">
          {{ company?.name }}
        </h4>
      </v-col>

      <v-divider vertical />
      <v-col
        class="flex flex-column justify-content-center align-items-center ma-8"
      >
        <div class="body-1 text-secondary">
          {{ $t('adminSc.address') }}
        </div>
        <div class="body-1 black--text">
          {{ company?.address }}
        </div>
      </v-col>
    </v-row>

    <tab-component>
      <template #tab>
        <v-tab class="subtitle-1 text-capitalize">
          Users
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Products
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Locations
        </v-tab>
      </template>

      <template #tab-item>
        <v-tab-item>
          <v-container fluid class="pa-0">
            <header-datatable
              default-sort-column="name"
              default-sort-type="asc"
              :sort-column-items="sortColumnItems"
              :sort-type-items="sortTypeItems"
              sort-column-id="sort_column_user"
              sort-type-id="sort_type_user"
              @on-filter-change="getData({filter: $event, page: $route.query?.page_user})"
              @on-search-icon-click="getData({searchKey: $event})"
            >
              <template #button>
                <user-form-item
                  label="User Product Owner"
                  :is-loading-form="isLoadingForm"
                  :dialog="dialogCreateUser"
                  :clear-form="clearForm"
                  @on-click-save="createItem"
                  @on-close-dialog="
                    dialogCreateUser = false
                    clearForm = true
                  "
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      v-if="$vuetify.breakpoint.xs"
                      depressed
                      color="primary"
                      height="52"
                      v-bind="attrs"
                      block
                      class="mb-4 mt-5 text-capitalize"
                      v-on="on"
                      @click="
                        dialogCreateUser = true
                        clearForm = false
                      "
                    >
                      {{ $t('adminSc.add_user') }}
                    </v-btn>
                    <v-btn
                      v-else
                      depressed
                      color="primary"
                      height="52"
                      v-bind="attrs"
                      class="text-capitalize"
                      v-on="on"
                      @click="
                        dialogCreateUser = true
                        clearForm = false
                      "
                    >
                      {{ $t('adminSc.add_user') }}
                    </v-btn>
                  </template>
                </user-form-item>
              </template>
            </header-datatable>
          </v-container>

          <v-container fluid class="pa-0">
            <users-loading v-if="isLoading" />

            <v-container v-else fluid class="pa-0 mb-10">
              <v-row v-if="data.items.length !== 0" class="ma-n5">
                <v-col
                  v-for="(item, i) in data.items"
                  :key="item.id"
                  md="4"
                  sm="6"
                  class="pa-5"
                >
                  <user-card-item
                    :user="item"
                    :is-loading-form="isLoadingForm"
                    :is-loading-form-clear-image="isLoadingFormClearImage"
                    :index="i"
                    :is-has-dialog-delete-user="true"
                    :dialog-update-user="dialogUpdateUser[i]"
                    :dialog-delete-user="dialogDeleteUser[i]"
                    @on-click-delete="deleteItem"
                    @on-click-edit="editItem"
                    @on-clear-image="clearImage($event, i)"
                    @on-open-update-dialog="$set(dialogUpdateUser, i, true)"
                    @on-close-update-dialog="$set(dialogUpdateUser, i, false)"
                    @on-open-delete-dialog="$set(dialogDeleteUser, i, true)"
                    @on-close-delete-dialog="$set(dialogDeleteUser, i, false)"
                  />
                </v-col>
              </v-row>

              <v-row v-else>
                <v-col class="justify-center align-center fill-height">
                  <empty-placeholder
                    hero="empty-placeholder.svg"
                    :message-title="$t('adminSc.empty_message_title_sc_user')"
                    :message-description="$t('adminSc.empty_message_description_sc_user')"
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-container>

          <pagination-component
            :page="data.page"
            :total-page="data.totalPage"
            page-id="page_user"
            class="float-end"
            @on-change-page="getData({
              page: $event,
              filter: {
                sortColumn: $route.query?.sort_column_user,
                sortType: $route.query?.sort_type_user
              }
            })"
          />
        </v-tab-item>

        <v-tab-item>
          <product-management :id="$route.params.accounts" class-grid="col-xs-12 col-sm-6 col-lg-4 col-12" />
        </v-tab-item>

        <v-tab-item>
          <v-container fluid>
            <detail-sc-location
              :id-sc="$route.params.accounts"
            />
          </v-container>
        </v-tab-item>
      </template>
    </tab-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserFormItem from '~/components/UserFormItem.vue'
import UserCardItem from '~/components/UserCardItem.vue'
import UsersLoading from '~/components/loading/UsersLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import DetailScLocation from '~/components/logistic-service-provider/DetailScLocation.vue'
import ProductManagement from '~/components/ProductManagement.vue'
import TabComponent from '~/components/TabComponent.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'ShipmentAccountsPage',
  components: {
    UserFormItem,
    UserCardItem,
    UsersLoading,
    EmptyPlaceholder,
    DetailScLocation,
    ProductManagement,
    TabComponent,
    PaginationComponent
  },
  layout: 'admin/body',
  middleware: ['auth', 'is-admin'],
  data: () => ({
    tab: null,
    searchKey: '',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      },
      email: {
        label: 'Email',
        value: 'email'
      },
      phoneNumber: {
        label: 'Phone Number',
        value: 'phone_number'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateUser: false,
    dialogUpdateUser: [] as Array<Boolean>,
    dialogDeleteUser: [] as Array<Boolean>,
    clearForm: false
  }),
  computed: {
    data () {
      return this.$store.getters['users/data']
    },
    company () {
      const items = this.$store.getters['users/data'].items

      if (items.length > 0) {
        return items[0].shipment_company
      }

      return undefined
    },
    isLoading () {
      return this.$store.getters['users/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    }
  },
  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Product Owner')
    this.getData({
      page: this.$route.query?.page_user as string
    })
  },
  methods: {
    clearImage (id: string, index: number) {
      this.$store.dispatch('users/removeAvatar', id)
        .then(() => {
          this.getData({})
        })
        .then(() => {
          this.$set(this.dialogUpdateUser, index, true)
        })
    },
    getData ({
      page = '', searchKey = '', filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('users/getItems', {
        searchKey,
        filterKeys: this.$route.params.accounts,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        role: 'SHIPMENT_COMPANY',
        page
      })
    },
    searchData (filter: any) {
      this.$store.dispatch('users/getItems', {
        filterKeys: this.$route.params.accounts,
        searchKey: this.searchKey,
        searchColumns: filter.searchColumns,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        role: 'SHIPMENT_COMPANY',
        page: 1
      })
    },
    async createItem (value: any) {
      const response = await this.$store.dispatch('users/createItem', {
        value,
        selectedId: this.$route.params.accounts,
        role: 'SHIPMENT_COMPANY'
      })
      if (response) {
        this.dialogCreateUser = false
        this.clearForm = true

        this.getData({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async editItem (value: any, i: any) {
      const response = await this.$store.dispatch('users/editItem', {
        value,
        selectedId: this.$route.params.accounts,
        role: 'SHIPMENT_COMPANY'
      })
      if (response) {
        this.$set(this.dialogUpdateUser, i, false)

        this.getData({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async deleteItem (id: any, i: any) {
      const response = await this.$store.dispatch('users/deleteItem', {
        id,
        selectedId: this.$route.params.accounts,
        role: 'SHIPMENT_COMPANY'
      })

      if (response) {
        this.$set(this.dialogDeleteUser, i, false)

        this.getData({
          page: this.$route.query?.page_user as string
        })
      }
    }
  }
})
</script>

<style scoped> </style>

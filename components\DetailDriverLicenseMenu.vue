<template>
  <v-menu
    v-model="menu"
    bottom
    offset-y
    :close-on-click="false"
    :close-on-content-click="false"
    transition="slide-y-transition"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-5">
      <v-container fluid class="pa-0 mb-4 d-flex align-center justify-space-between">
        <p class="subtitle-1 ma-0 mr-10">
          Detail License
        </p>
        <v-icon size="24" color="secondary" @click="$emit('on-click-close')">
          mdi-close
        </v-icon>
      </v-container>

      <div class="mb-4">
        <p class="caption text--secondary mb-1">
          Name
        </p>
        <p class="subtitle-1 ma-0">
          {{ driverName }}
        </p>
      </div>

      <div class="mb-4">
        <p class="caption text--secondary mb-1">
          Driver Licenses
        </p>
        <div class="mb-n1">
          <v-row v-for="item in licenses" :key="item.id" class="ma-0 my-n1">
            <v-col class="pa-0 py-1">
              <p class="subtitle-1 ma-0">
                SIM {{ item.license_category.name }}
              </p>
            </v-col>
            <v-col class="pa-0 py-1">
              <p class="subtitle-1 ma-0">
                {{ item.identity }}
              </p>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-card>
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import { License } from '~/types/driver'

export default Vue.extend({
  name: 'DetailDriverLicenseMenu',

  components: {
    ImageComponent
  },

  props: {
    menu: {
      type: Boolean,
      default: false
    },
    driverName: {
      type: String,
      default: ''
    },
    licenses: {
      type: Array as () => License[],
      default: () => []
    }
  }
})
</script>

<style scoped lang="scss"> </style>

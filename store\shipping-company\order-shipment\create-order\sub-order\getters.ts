import { GetterTree } from 'vuex'
import { RootState } from '../../../../index'
import { ShipmentCompanyCreateOrderSubOrderState } from './state'

export const getters: GetterTree<
  ShipmentCompanyCreateOrderSubOrderState,
  RootState
> = {
  pickUpData (state) {
    return {
      itemsPickUp: state.itemsPickUp
    }
  },

  dropOffData (state) {
    return {
      itemsDropOff: state.itemsDropOff
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export default getters

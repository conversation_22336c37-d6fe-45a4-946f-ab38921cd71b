import { ActionTree } from 'vuex'
import { FirebaseState } from '~/store/firebase/state'

export const actions: ActionTree<FirebaseState, FirebaseState> = {
  getDataRealtime ({ commit }, payload: any) {
    const appEnv = payload.appEnv
    const path = appEnv + payload.path

    const ref = this.$fire.database.ref(path)

    commit('SET_FIREBASE_PATH', path)

    ref.on('value', (snapshot: any) => {
      const snapVal = snapshot.val()

      commit('SET_DATA_NOTIFICATION', {
        notification: snapVal['badge-notification']
      })

      commit('SET_DATA_LSP', {
        createShipment: snapVal['badge-lsp-create-shipment'],
        invoiceShipment: snapVal['badge-lsp-invoice-shipment']
      })

      commit('SET_DATA_SC', {
        invoiceOrder: snapVal['badge-sc-invoice-order']
      })

      commit('SET_DATA_VENDOR', {
        invoiceOrder: snapVal['badge-vendor-invoice-order'],
        requestShipment: snapVal['badge-vendor-request-shipment']
      })
    })
  },

  setDataRealtime ({ state }, payload: any) {
    const ref = this.$fire.database.ref(state.firebasePath + payload)
    ref.set(false)
  },

  async detachListener ({ state }) {
    if (state.firebasePath !== '') {
      const ref = this.$fire.database.ref(state.firebasePath)
      await ref.off('value')
    }
  },

  async getMessagingToken ({ commit }, payload) {
    const messagingToken = await this.$fire.messaging.getToken({
      vapidKey: payload.vapidKey
    })

    commit('SET_MESSAGING_TOKEN', messagingToken)
  }
}

export default actions

import { GetterTree } from 'vuex'
import { ProfileLogisticShipmentCompanyState } from './state'

export const getters: GetterTree<ProfileLogisticShipmentCompanyState, ProfileLogisticShipmentCompanyState> = {
  logisticServiceProvider (state) {
    return state.logisticServiceProvider
  },
  vendor (state) {
    return state.vendor
  },
  isLoading (state) {
    return state.isLoading
  },
  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export default getters

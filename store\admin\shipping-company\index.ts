import { GetterTree, ActionTree, MutationTree } from 'vuex'
import { exception<PERSON><PERSON><PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

// eslint-disable-next-line no-use-before-define
export type VuexState = ReturnType<typeof state>

export const state = () => ({
  isLoading: false,
  isLoadingForm: false,
  items: [],
  totalPage: 1,
  page: 1
})

export const getters: GetterTree<VuexState, VuexState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export const mutations: MutationTree<VuexState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }
}

export const actions: ActionTree<VuexState, VuexState> = {

  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/shipment-companies', {
      params: {
        search_columns: 'name',
        search_key: payload == null ? '' : payload.name,
        sort_column: payload.sortColumn,
        sort_type: payload.sortType,
        page: payload.page == null ? 1 : payload.page,
        entries: 9
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async createItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('/v1/shipment-companies', {
      logistics_service_provider_domain: payload.logisticsServiceProviderDomain,
      name: payload.companyName,
      user_name: payload.name,
      user_phone_number: payload.phone,
      user_phone_country_code: payload.phoneCountryCode,
      user_email: payload.email,
      user_password: payload.password,
      user_password_confirmed: payload.passwordConfirmation
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      dispatch('getItems', { page: state.page })
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  async editItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.put('/v1/shipment-companies/' + payload.id, {
      name: payload.name,
      address: payload.address,
      logistics_service_provider_domain: payload.logisticsServiceProviderDomain
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      dispatch('getItems', { page: state.page, name: '' })
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  async deleteItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios.delete('/v1/shipment-companies/' + payload.id).then((response: any) => {
      toastSuccess(response.data.message, this)
      dispatch('getItems', { page: state.page })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }
}

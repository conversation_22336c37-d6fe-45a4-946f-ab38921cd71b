<template>
  <v-container fluid class="pa-0 px-md-10 px-8 mb-10">
    <v-row class="d-flex ma-0 mt-4 mb-8">
      <v-btn tile text class="text-capitalize" @click="$router.back()">
        <v-icon left color="black">
          mdi-chevron-left
        </v-icon>
        {{ $t('adminSc.back_to_list_sc') }}
      </v-btn>
    </v-row>

    <v-row class="ma-0 mb-8 mx-0 pa-5 rounded white">
      <v-col class="d-flex flex-row align-center">
        <div class="ma-8">
          <image-component v-if="company?.logo_url" :image="company?.logo_url" max-width="40" max-height="40" />

          <v-icon v-else size="55">
            mdi-domain
          </v-icon>
        </div>

        <h4 class="ml-2 text-wrap ma-4">
          {{ company?.name }}
        </h4>
      </v-col>

      <v-divider vertical />
      <v-col
        class="flex flex-column justify-content-center align-items-center ma-8"
      >
        <div class="body-1 text-secondary">
          {{ $t('adminSc.address') }}
        </div>
        <div class="body-1 black--text">
          {{ company?.address }}
        </div>
      </v-col>
    </v-row>

    <tab-component>
      <template #tab>
        <v-tab class="subtitle-1 text-capitalize">
          Products
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Locations
        </v-tab>
      </template>

      <template #tab-item>
        <v-tab-item>
          <product-management :id="$route.params.sc" class-grid="col-xs-12 col-sm-6 col-lg-4 col-12" />
        </v-tab-item>
        <v-tab-item>
          <v-container fluid>
            <detail-sc-location
              :id-sc="$route.params.sc"
            />
          </v-container>
        </v-tab-item>
      </template>
    </tab-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailScLocation from '~/components/logistic-service-provider/DetailScLocation.vue'
import ProductManagement from '~/components/ProductManagement.vue'
import TabComponent from '~/components/TabComponent.vue'

export default Vue.extend({
  name: 'ShipmentAccountsPage',
  components: {
    DetailScLocation,
    ProductManagement
  },
  layout: 'admin/body',
  middleware: ['auth', 'is-admin'],
  data: () => ({
    tab: null,
    searchKeyAccount: '',
    searchKeyVendor: '',
    searchKeySc: ''
  }),
  computed: {
    dataAccount () {
      return this.$store.getters['users/data']
    },
    dataVendor () {
      return this.$store.getters['admin/logistic-service-provider/vendor/data']
    },
    dataShipmentCompany () {
      return this.$store.getters['shipping-company/data']
    },
    company () {
      const items = this.$store.getters['users/data'].items

      if (items.length > 0) {
        return items[0].logistics_service_provider
      }

      return null
    },
    isLoading () {
      return this.$store.getters['users/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    }
  },
  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'Logistic Provider'
    )
    this.getUsers({
      page: this.$route.query?.page_user as string
    })

    this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
      selectedId: this.$route.params.detail,
      searchKey: this.searchKeyVendor,
      page: this.$route.query?.page_vendor as string
    })

    this.getDataSC({
      page: this.$route.query?.page_sc as string
    })
  },
  methods: {
    getUsers ({ page = '' }) {
      this.$store.dispatch('users/getItems', {
        filterKeys: this.$route.params.detail,
        searchKey: this.searchKeyAccount,
        role: 'LOGISTIC_SERVICE_PROVIDER',
        page
      })
    },
    getDataSC ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('shipping-company/getItems', {
        filterColumns: 'logistics_service_provider_id',
        filterKeys: this.$route.params.detail,
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page,
        fetchDetail: false
      })
    },
    paginationVendor (page: any) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchKey: this.searchKeyVendor,
        page
      })
    },
    filterVendor (filter: any) {
      this.$store.dispatch('admin/logistic-service-provider/vendor/getItems', {
        selectedId: this.$route.params.detail,
        searchColumns: filter.searchColumns,
        searchKey: this.searchKeyAccount,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page: 1
      })
    }
  }
})
</script>

<style scoped> </style>

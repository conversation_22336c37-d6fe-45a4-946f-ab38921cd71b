import { GetterTree } from 'vuex'
import { LogisticServiceProviderInvoiceShipmentState } from './state'

export const getters: GetterTree<LogisticServiceProviderInvoiceShipmentState, LogisticServiceProviderInvoiceShipmentState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  vendorData (state) {
    return {
      vendorItems: state.vendorItems,
      totalPage: state.totalPageVendor,
      page: state.pageVendor
    }
  },

  detailData (state) {
    return {
      item: state.item
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingVendor (state) {
    return state.isLoadingVendor
  },

  isLoadingDialog (state) {
    return state.isLoadingDialog
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  },

  isLoadingPublish (state) {
    return state.isLoadingPublish
  }
}

export default getters

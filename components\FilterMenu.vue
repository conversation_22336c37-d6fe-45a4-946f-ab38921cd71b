<template>
  <v-menu
    :close-on-content-click="false"
    min-width="400"
  >
    <slot name="filter-user" />
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>
    <v-card
      v-if="isSortBy"
      elevation="0"
      class="pa-5 d-flex flex-column align-center"
    >
      <p class="subtitle-1 mb-5">
        {{ $t('filterMenu.sort_by') }}
      </p>
      <v-container class="pa-0">
        <v-row class="ma-0 d-flex">
          <v-col class="mr-5 pa-0">
            <v-radio-group
              v-model="sortColumn"
              class="ma-0 pa-0"
              hide-details
            >
              <v-radio
                v-for="(item, i) in sortColumnItems"
                :key="i"
                :label="item.label"
                :value="item.value"
                @click="onFilterChange"
              />
            </v-radio-group>
          </v-col>
          <v-col class="pa-0">
            <v-radio-group
              v-model="sortType"
              class="ma-0 pa-0"
              hide-details
            >
              <v-radio
                v-for="(item, i) in sortTypeItems"
                :key="i"
                :label="item.label"
                :value="item.value"
                @click="onFilterChange"
              />
            </v-radio-group>
          </v-col>
        </v-row>
      </v-container>

      <p v-if="filterColumnItems" class="subtitle-1 mt-10 mb-5">
        {{ $t('filterMenu.filter_by') }}
      </p>
      <v-container v-if="filterColumnItems" class="pa-0">
        <v-row class="ma-0 d-flex">
          <v-col class="mr-5 pa-0">
            <v-radio-group
              v-model="filterColumn"
              class="ma-0 pa-0"
              hide-details
            >
              <v-radio
                v-for="(item, i) in filterColumnItems"
                :key="i"
                :label="item.label"
                :value="item.value"
                @click="onFilterChange"
              />
            </v-radio-group>
          </v-col>
          <v-col class="pa-0">
            <v-radio-group
              v-model="filterType"
              class="ma-0 pa-0"
              hide-details
            >
              <v-radio
                v-for="(item, i) in filterTypeItems"
                :key="i"
                :label="item.label"
                :value="item.value"
                @click="onFilterChange"
              />
            </v-radio-group>
          </v-col>
        </v-row>
      </v-container>
    </v-card>
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'FilterMenu',

  props: {
    defaultSortColumn: {
      type: String,
      required: true
    },

    defaultSortType: {
      type: String,
      required: true
    },

    sortColumnItems: {
      type: Object,
      required: true
    },

    sortTypeItems: {
      type: Object,
      required: true
    },

    defaultFilterColumn: {
      type: String || null,
      default: null
    },

    defaultFilterType: {
      type: String || null,
      default: null
    },

    filterColumnItems: {
      type: Object || null,
      default: null
    },

    filterTypeItems: {
      type: Object || null,
      default: null
    },

    sortColumnId: {
      type: String || null,
      default: null
    },

    sortTypeId: {
      type: String || null,
      default: null
    },

    filterColumnId: {
      type: String || null,
      default: null
    },

    filterTypeId: {
      type: String || null,
      default: null
    },

    isSortBy: {
      type: Boolean,
      default: true
    }
  },

  data: () => ({
    routeQuery: null as any,
    sortColumn: '',
    sortType: '',
    filterColumn: '',
    filterType: ''
  }),

  watch: {
    defaultSortColumn () {
      this.sortColumn = this.defaultSortColumn

      this.filterColumn = this.defaultFilterColumn
      this.filterType = this.defaultFilterType
    },

    '$route.query': {
      handler (currentQuery) {
        this.routeQuery = { ...currentQuery }
      }
    }
  },

  created () {
    this.routeQuery = { ...this.$route.query }
  },

  mounted () {
    this.sortColumn = this.defaultSortColumn
    this.sortType = this.defaultSortType

    this.filterColumn = this.defaultFilterColumn
    this.filterType = this.defaultFilterType
  },

  methods: {
    onFilterChange () {
      const columns = [] as any[]
      Object.keys(this.sortColumnItems).forEach((key) => {
        columns.push(this.sortColumnItems[key].value)
      })

      if (this.sortColumnId && this.sortTypeId) {
        this.setRouteQuerySort(this.sortColumn, this.sortType)
      }

      if (this.filterColumnId && this.filterTypeId) {
        this.setRouteQueryFilter(this.filterColumn, this.filterType)
      }

      this.$emit('on-filter-change', {
        sortColumn: this.sortColumn,
        sortType: this.sortType,
        filterColumn: this.filterColumn,
        filterType: this.filterType,
        searchColumns: columns.join(',')
      })
    },

    setRouteQuerySort (sortColumn: string | null, sortType: string | null) {
      if (this.routeQuery[this.sortColumnId] !== sortColumn || this.routeQuery[this.sortTypeId] !== sortType) {
        this.routeQuery[this.sortColumnId] = sortColumn
        this.routeQuery[this.sortTypeId] = sortType

        this.setRouteQuery()
      }
    },

    setRouteQueryFilter (filterColumn: string | null, filterType: string | null) {
      if (this.routeQuery[this.filterColumnId] !== filterColumn || this.routeQuery[this.filterTypeId] !== filterType) {
        this.routeQuery[this.filterColumnId] = filterColumn
        this.routeQuery[this.filterTypeId] = filterType

        this.setRouteQuery()
      }
    },

    async setRouteQuery () {
      await this.$router.replace({
        query: { ...this.routeQuery }
      })
    }
  }
})
</script>

<style>

</style>

<template>
  <v-dialog
    v-model="dialog"
    persistent
    max-width="600px"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>
    <v-card class="pa-md-10 pa-5 overflow-auto">
      <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
        <h4 v-if="user">
          {{ $t('userFormItem.title_edit') }} {{ label }}
        </h4>
        <h4 v-else>
          {{ $t('userFormItem.title_add') }} {{ label }}
        </h4>
        <v-icon color="black" @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </v-card-title>
      <v-container class="pa-4">
        <v-form v-model="isValid" ref="form">
          <v-row>
            <v-col class="pa-0" cols="12">
              <image-select
                :clear-image="clearForm"
                :avatar-url="user?.avatar_url"
                :is-loading-form-clear-image="isLoadingFormClearImage"
                @on-image-selected="onImageSelected"
                @on-clear-image="$emit('on-clear-image')"
              />
            </v-col>
            <slot name="national-identity-form" />
            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="form.name"
                :hint="$t('userFormItem.label_username')"
                :rules="[rulesRequired]"
              />
            </v-col>
            <v-col class="pa-0 ma-0 col-12">
              <div class="d-flex flex-row">
                <v-combobox
                  v-model="form.phoneCountryCode"
                  class="col-4 pa-0 ma-0"
                  outlined
                  hide-details
                  prefix="+"
                  :items="items"
                  :rules="[rulesRequired]"
                  @keydown="$event.target.blur()"
                  @keypress="$event.target.blur()"
                  @keyup="$event.target.blur()"
                  @change="form.phoneCountryCode = $event"
                />
                <custom-text-field
                  v-model="form.phone"
                  class="col-8 pa-0 mt-0 ml-2"
                  hint="8954 xxx xxx xx"
                  type="number"
                  :rules="[rulesRequired, rulesPhoneMaxDigit, rulesFirstZeroPhoneNumber]"
                />
              </div>
            </v-col>
            <v-col v-if="isWithEmailAndPassword" class="pa-0" cols="12">
              <div class="d-flex">
                <custom-text-field
                  v-model="form.email"
                  :hint="$t('userFormItem.label_email')"
                  prepend-inner-icon="mdi-email"
                  :rules="[rulesRequired, rulesEmail]"
                  class="flex-grow-1"
                  :disabled="isAccountSettingPage ? false : user"
                />
                <v-btn
                  v-if="user && !isChangingEmail && !isAccountSettingPage"
                  color="primary"
                  class="ml-2 mt-0"
                  style="height: 56px"
                  :loading="isLoadingOtp"
                  @click="requestOtp"
                >
                  {{ $t('userFormItem.button_change') || 'Change' }}
                </v-btn>
                <v-btn
                  v-if="user && isChangingEmail && !isAccountSettingPage"
                  color="error"
                  outlined
                  class="ml-2 mt-0"
                  style="height: 56px"
                  @click="cancelEmailChange"
                >
                  {{ $t('userFormItem.button_cancel') }}
                </v-btn>
              </div>
              <v-expand-transition>
                <div v-if="user && isChangingEmail" class="mt-3 email-change-container">
                  <otp-input
                    v-model="form.otpValue"
                    ref="otpInput"
                  />
                  <custom-text-field
                    v-model="form.newEmail"
                    :hint="$t('userFormItem.label_new_email') || 'New Email'"
                    prepend-inner-icon="mdi-email"
                    :rules="[rulesRequired, rulesEmail]"
                    class="mt-2"
                  />
                </div>
              </v-expand-transition>
            </v-col>

            <v-divider class="my-5" />

            <v-col v-if="isWithEmailAndPassword" class="pa-0" cols="12">
              <custom-text-field
                v-if="user && !isAccountSettingPage"
                v-model="form.passwordOld"
                :hint="$t('userFormItem.label_password_old') || 'Current Password'"
                :type="isShowFormPassword ? 'text' : 'password'"
                prepend-inner-icon="mdi-lock-outline"
                :rules="form.password ? [rulesRequired] : []"
                :append-icon="isShowFormPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="isShowFormPassword = !isShowFormPassword"
              />
              <custom-text-field
                v-model="form.password"
                :hint="$t('userFormItem.label_password') || 'New Password'"
                :type="isShowFormPassword ? 'text' : 'password'"
                prepend-inner-icon="mdi-lock"
                :rules="user ? [] : [rulesRequired, rulesPasswordMinLength]"
                :append-icon="isShowFormPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="isShowFormPassword = !isShowFormPassword"
              />
              <custom-text-field
                v-model="form.passwordConfirmation"
                :hint="$t('userFormItem.label_confirm_password') || 'Confirm New Password'"
                :type="isShowFormPassword ? 'text' : 'password'"
                prepend-inner-icon="mdi-lock"
                :rules="form.password ? [rulesRequired, rulesPasswordConfirmation] : []"
                :append-icon="isShowFormPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="isShowFormPassword = !isShowFormPassword"
              />
            </v-col>
            <slot name="driver-license-form" />
          </v-row>
        </v-form>
      </v-container>
      <v-card-actions class="pa-0 mt-2">
        <v-btn
          height="52"
          color="primary"
          depressed
          x-large
          :loading="isLoadingForm"
          :disabled="!isValid"
          @click="onClickSave"
        >
          {{ $t('userFormItem.button_save') }}
        </v-btn>
        <v-col cols="4">
          <v-btn
            height="52"
            color="primary"
            depressed
            outlined
            x-large
            @click="onCloseDialog"
          >
            {{ $t('userFormItem.button_cancel') }}
          </v-btn>
        </v-col>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '~/components/CustomTextField.vue'
import OtpInput from '~/components/OtpInput.vue'
import { User } from '~/types/user'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'UserFormItem',

  components: { CustomTextField, OtpInput },

  props: {
    label: {
      type: String,
      default: 'User'
    },
    user: {
      type: Object as () => User,
      default: null
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    isLoadingFormClearImage: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    },
    clearForm: {
      type: Boolean,
      default: false
    },
    isWithEmailAndPassword: {
      type: Boolean,
      default: true
    },
    isAccountSettingPage: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isValid: false,
    isShowFormDialog: false,
    isShowFormPassword: false,
    isChangingEmail: false,
    items: [
      '1', // USA/Canada
      '7', // Russia
      '20', // Egypt
      '27', // South Africa
      '30', // Greece
      '31', // Netherlands
      '32', // Belgium
      '33', // France
      '34', // Spain
      '36', // Hungary
      '39', // Italy
      '40', // Romania
      '41', // Switzerland
      '43', // Austria
      '44', // UK
      '45', // Denmark
      '46', // Sweden
      '47', // Norway
      '48', // Poland
      '49', // Germany
      '51', // Peru
      '52', // Mexico
      '54', // Argentina
      '55', // Brazil
      '56', // Chile
      '57', // Colombia
      '60', // Malaysia
      '61', // Australia
      '62', // Indonesia
      '63', // Philippines
      '64', // New Zealand
      '65', // Singapore
      '66', // Thailand
      '81', // Japan
      '82', // South Korea
      '84', // Vietnam
      '86', // China
      '90', // Turkey
      '91', // India
      '92', // Pakistan
      '93', // Afghanistan
      '94', // Sri Lanka
      '95', // Myanmar
      '98', // Iran
      '212', // Morocco
      '213', // Algeria
      '216', // Tunisia
      '218', // Libya
      '220', // Gambia
      '221', // Senegal
      '234', // Nigeria
      '254', // Kenya
      '261', // Madagascar
      '351', // Portugal
      '352', // Luxembourg
      '353', // Ireland
      '358', // Finland
      '359', // Bulgaria
      '370', // Lithuania
      '371', // Latvia
      '372', // Estonia
      '380', // Ukraine
      '420', // Czech Republic
      '421', // Slovakia
      '852', // Hong Kong
      '853', // Macau
      '855', // Cambodia
      '856', // Laos
      '880', // Bangladesh
      '886', // Taiwan
      '960', // Maldives
      '961', // Lebanon
      '962', // Jordan
      '966', // Saudi Arabia
      '971', // UAE
      '972', // Israel
      '974', // Qatar
      '977', // Nepal
      '998' // Uzbekistan
    ],
    form: {
      id: '',
      avatarUrl: '',
      avatar: null,
      name: '',
      phoneCountryCode: '',
      phone: '',
      email: '',
      passwordOld: '',
      password: '',
      passwordConfirmation: '',
      otpValue: '',
      newEmail: '',
    }
  }),

  watch: {
    clearForm () {
      if (this.clearForm) {
        const form = this.$refs.form as HTMLFormElement
        form.reset()
      }
    }
  },

  mounted () {
    const user = this.user as any
    if (this.user) {
      this.form = {
        id: user.id,
        avatarUrl: user.avatar_url,
        avatar: user.avatar,
        name: user.name,
        phoneCountryCode: user.phone_country_code,
        phone: user.phone_number,
        email: user.email,
        passwordOld: '',
        password: '',
        passwordConfirmation: '',
        otpValue: '',
        newEmail: ''
      }
    } else {
      this.form = {
        id: '',
        avatarUrl: '',
        avatar: null,
        name: '',
        phoneCountryCode: '',
        phone: '',
        email: '',
        passwordOld: '',
        password: '',
        passwordConfirmation: '',
        otpValue: '',
        newEmail: ''
      }
    }
  },

  computed: {
    isLoadingOtp () {
      return this.$store.getters['users/isLoadingForm']
    }
  },

  methods: {
    onClickSave () {
      // If email change is in progress, include OTP and new email in the form data
      if (this.isChangingEmail && this.form.otpValue && this.form.newEmail) {
        const formWithEmailChange = {
          ...this.form,
          otp: this.form.otpValue,
          newEmail: this.form.newEmail
        }
        this.$emit('on-click-save', formWithEmailChange)
      } else {
        this.$emit('on-click-save', this.form)
      }
    },
    onImageSelected (image : any) {
      this.form.avatar = image
    },
    onCloseDialog () {
      this.cancelEmailChange()
      this.$emit('on-close-dialog')
    },
    async requestOtp () {
      const success = await this.$store.dispatch('users/requestOtp')
      if (success) {
        this.isChangingEmail = true
      }
    },
    cancelEmailChange () {
      this.isChangingEmail = false
      this.form.otpValue = ''
      this.form.newEmail = ''
      if (this.$refs.otpInput) {
        (this.$refs.otpInput as any).reset()
      }
    },

    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    },
    rulesPhoneMaxDigit (value: string) {
      return rules.phoneMaxDigit(value)
    },
    rulesFirstZeroPhoneNumber (value: string) {
      return rules.firstZeroPhoneNumber(value)
    },
    rulesPasswordMinLength (value: string) {
      return rules.passwordMinLength(value)
    },
    rulesPasswordConfirmation (value: string) {
      return rules.passwordConfirm(value, this.form.password)
    }
  }
})
</script>

<style scoped>
.email-change-container {
  animation: fadeIn 0.3s ease-in;
  transform-origin: top center;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<template>
  <v-container fluid class="pa-0 px-10 mb-10">
    <button-back title="Back to Detail Shipment" class="mb-10" />

    <select-vendor-vehicle :calculation-order="calculationOrder" :filter-vendor="detailShipment?.vendor?.id" :order-number="detailShipment?.orders[0]?.identity" />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ButtonBack from '~/components/ButtonBack.vue'
import SelectVendorVehicle from '~/components/SelectVendorVehicle.vue'
import { Shipment } from '~/types/shipment'

interface CalculationOrder {
  totalVolume?: number
  totalWeight?: number
  totalLength?: number
  totalWidth?: number
  totalHeight?: number
}

export default Vue.extend({
  name: 'ChangeVendorPage',

  components: {
    ButtonBack,
    SelectVendorVehicle
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  computed: {
    detailShipment (): Shipment {
      return this.$store.getters['shipment/detailShipment']
    },

    calculationOrder (): CalculationOrder | null {
      const shipment: Shipment | null = this.detailShipment

      return shipment
        ? {
            totalVolume: shipment.total_volume,
            totalWeight: shipment.total_weight,
            totalLength: shipment.total_dimension_length,
            totalHeight: shipment.total_dimension_height,
            totalWidth: shipment.total_dimension_width
          }
        : null
    }
  },

  watch: {
    detailShipment: {
      handler (value: Shipment) {
        if (!value) { this.$router.back() }
      },
      immediate: true
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Change Vendor')
  }
})
</script>

<style scoped lang="scss"> </style>

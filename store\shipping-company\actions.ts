import { ActionTree } from 'vuex'
import { ShipmentCompanyState } from './state'
import { RootState } from '~/store'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<ShipmentCompanyState, RootState> = {
  async getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios
      .get('/v1/shipment-companies', {
        params: {
          search_columns: 'name',
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          filter_columns: payload.filterColumns == null ? '' : payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          sort_column: payload.sortColumn == null ? 'name' : payload.sortColumn,
          sort_type: payload.sortType == null ? 'asc' : payload.sortType,
          page: payload.page ? payload.page : 1,
          entries: payload.entries == null ? -1 : payload.entries
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
        commit('SET_TOTAL_PAGE', response.data.meta.last_page)
        commit('SET_PAGE', response.data.meta.current_page)
        commit('SET_ITEM', null)

        return response.data.data
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return error.response.data
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  viewItem ({ commit}, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    this.$axios
      .get('/v1/shipment-companies/' + payload.idSc)
      .then((response: any) => {
        commit('SET_ITEM', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async blockUnblock ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios
      .put('/v1/shipment-companies/' + payload.idSc + '/block')
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        dispatch('getItems', {
          idLsp: payload.idLsp,
          idSc: payload.idSc,
          page: 1
        })
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async removeLogo ({ commit, dispatch }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios.delete(`/v1/shipment-companies/${payload}/logo`)
      .then(() => {
        dispatch('profile/shipping-company/getData', null, { root: true })
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('/v1/shipment-companies', {
      logistics_service_provider_domain: payload.lspDomain,
      name: payload.companyName,
      user_name: payload.userName,
      user_phone_number: payload.phoneNumber,
      user_phone_country_code: payload.phoneCountryCode,
      user_email: payload.email,
      user_password: payload.password,
      user_password_confirmed: payload.passwordConfirmation
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }
}

export default actions

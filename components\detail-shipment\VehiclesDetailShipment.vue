<template>
  <div>
    <v-list v-if="shipment?.tracks?.length !== 0" class="pa-0 mt-5">
      <v-list-item-group color="primary" :value="selectedTrackId">
        <v-list-item
          v-for="track in shipment.tracks"
          :key="track.id"
          :value="track.id"
          class="pa-5 my-3 rounded v-sheet--outlined d-flex"
          @click="$emit('on-track-selected', track)"
        >
          <v-list-item-icon>
            <v-img
              :src="track.vehicle_detail?.vehicle?.photo_url"
              contain
              height="40"
            />
          </v-list-item-icon>
          <v-list-item-content class="pa-0">
            <v-list-item-title class="pa-0 d-flex flex-column text-wrap">
              {{ track.vehicle_detail?.vehicle?.name }}
            </v-list-item-title>
            <v-list-item-subtitle class="pa-0 d-flex flex-column">
              <v-col class="mt-2 pa-0">
                <p class="mb-2 text--secondary">
                  Features
                </p>

                <div
                  v-for="feature in track.vehicle_detail?.vehicle
                    ?.vehicle_features"
                  :key="feature.id"
                  class="pa-2 ma-0 my-1 d-inline-flex caption"
                  style="
                    border-color: #cfcccc;
                    border-style: solid;
                    border-radius: 4px;
                  "
                >
                  {{ feature.name.toUpperCase() }}
                </div>
              </v-col>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Shipment } from '~/types/shipment'
import { colorType } from '~/utils/functions'

export default Vue.extend({
  name: 'VehiclesDetailShipment',

  props: {
    shipment: {
      type: Object as () => Shipment,
      required: true
    },
    selectedTrackId: {
      type: String,
      required: true
    }
  },

  methods: {
    colorType (type: string): string {
      return colorType(type)
    }
  }
})
</script>

<style lang="scss" scoped></style>

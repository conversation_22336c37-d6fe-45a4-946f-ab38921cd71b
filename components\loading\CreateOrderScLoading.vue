<template>
  <div>
    <v-container class="d-flex justify-space-between pa-0">
      <v-skeleton-loader type="heading" width="1000" />
      <v-skeleton-loader type="image" width="280" height="50" class="mb-10" />
    </v-container>
    <v-row class="ma-0 d-flex flex-lg-row flex-column">
      <v-col class="mr-lg-10 mb-lg-0 mb-10 pa-0">
        <v-sheet height="540" class="pa-10">
          <v-container class="d-flex justify-space-between pa-0">
            <v-skeleton-loader type="heading" width="400" class="mb-8" />
            <v-skeleton-loader type="image" width="55" height="55" />
          </v-container>
          <v-skeleton-loader type="image" height="180" class="mt-10" style="border-radius: 0 !important;" />
          <v-skeleton-loader type="image" height="180" style="border-radius: 0 !important;" />
        </v-sheet>
      </v-col>
      <v-col class="pa-0">
        <v-sheet height="540" class="pa-10">
          <v-container class="d-flex justify-space-between pa-0">
            <v-skeleton-loader type="heading" width="400" class="mb-8" />
            <v-skeleton-loader type="image" width="55" height="55" />
          </v-container>
          <v-skeleton-loader type="image" height="180" class="mt-10" style="border-radius: 0 !important;" />
          <v-skeleton-loader type="image" height="180" style="border-radius: 0 !important;" />
        </v-sheet>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'CreateOrderScLoading'
})
</script>

<template>
  <v-card outlined class="fill-height">
    <v-card-text class="pa-5">
      <v-row class="ma-0 mb-5">
        <v-col class="pa-0 d-flex align-center">
          <div class="ma-0 mr-5">
            <v-avatar rounded size="40">
              <image-component v-if="item.logo_url" :image="item.logo_url" max-width="40" min-width="40" />

              <v-icon v-else color="secondary">
                mdi-domain
              </v-icon>
            </v-avatar>
          </div>

          <v-container fluid class="pa-0">
            <v-container class="pa-0 d-flex align-start justify-space-between">
              <h4 class="black--text custom-card-title mb-2">
                <div class="d-flex">
                  <div class="mx-2">
                    {{ item.name }}
                  </div>
                  <div>
                    <div v-if="isHasNew" class="d-flex align-center">
                      <span class="pl-2 red--text">{{ checkNew(item.collaborated_at) }}</span>
                    </div>
                  </div>
                </div>
              </h4>

              <div v-if="isHasMenu">
                <v-menu offset-y>
                  <template #activator="{ on, attrs }">
                    <v-btn color="secondary" icon v-bind="attrs" v-on="on">
                      <v-icon dark>
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <company-form-item
                      label=""
                      :user="{
                        id: item.id,
                        name: item.name,
                        address: item.address,
                        domain: item.domain,
                        logisticsServiceProviderDomain:
                          logisticsServiceProviderDomain
                      }"
                      :is-loading-form="isLoadingForm"
                      :is-has-domain-company="isHasDomainCompany"
                      :is-has-domains-auto-complete="
                        logisticsServiceProviderDomains.length > 0
                      "
                      :logistics-service-provider-domains="
                        logisticsServiceProviderDomains
                      "
                      :dialog="dialogUpdate"
                      @on-click-save="onClickSaveEdit"
                      @on-close-dialog="$emit('on-close-update-dialog')"
                    >
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="edit"
                          v-bind="attrs"
                          v-on="on"
                          @click="$emit('on-open-update-dialog')"
                        >
                          <v-list-item-title> Edit </v-list-item-title>
                        </v-list-item>
                      </template>
                    </company-form-item>
                    <!-- <v-dialog v-model="dialogDelete" max-width="600px">
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="delete"
                          v-bind="attrs"
                          v-on="on"
                          @click="$emit('on-open-delete-dialog')"
                        >
                          <v-list-item-title> Delete </v-list-item-title>
                        </v-list-item>
                      </template>
                      <v-card>
                        <v-card-title class="text-h6 lighten-2">
                          {{ $t('lspListVendor.confirmation_delete') }} {{ item.name }}
                        </v-card-title>

                        <v-card-text>
                          {{ $t('lspListVendor.confirm_delete_text') }}
                        </v-card-text>

                        <v-divider />

                        <v-card-actions>
                          <v-spacer />
                          <v-btn
                            color="primary"
                            text
                            :loading="isLoadingForm"
                            @click="onClickSaveDelete(item)"
                          >
                            Yes
                          </v-btn>
                          <v-btn
                            color="primary"
                            @click="$emit('on-close-delete-dialog')"
                          >
                            Cancel
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog> -->
                  </v-list>
                </v-menu>
              </div>
            </v-container>
            <v-container class="d-flex pa-0 flex-column" fluid>
              <p v-if="item.domain" class="ma-0 body-2">
                {{ item.domain }}
              </p>

              <div v-if="item.products_count != null" class="d-flex">
                <v-icon color="black" size="20" class="mr-3">
                  mdi-package-variant-closed
                </v-icon>
                <p class="ma-0 body-1 black--text">
                  {{ item.products_count }} Products
                </p>
              </div>

              <div
                v-if="item.status && isHasStatus"
                class="subtitle-2 px-3 py-2 my-1 flex"
                :style="styleContainerCollaboration"
              >
                {{ item.status }}
              </div>
              <div
                v-if="item.accounts_count != null && item.vehicles_count != null"
                class="d-flex py-2"
              >
                <div v-if="isAccountCountVisible" class="d-flex align-center mr-5">
                  <v-icon size="16" class="mr-3" color="black">
                    mdi-account
                  </v-icon>
                  <p class="caption black--text ma-0 pa-0">
                    {{ item.accounts_count }} Account
                  </p>
                </div>

                <div class="d-flex align-center">
                  <v-icon size="16" class="mr-3" color="black">
                    mdi-truck-fast
                  </v-icon>
                  <p class="caption black--text ma-0">
                    {{ item.vehicles_count }} Vehicles
                  </p>
                </div>
              </div>
            </v-container>
          </v-container>
        </v-col>
      </v-row>

      <v-row
        v-if="item.accounts_count == null || item.vehicles_count == null"
        class="ma-0 mb-5 align-center"
      >
        <div v-if="isAccountCountVisible" class="d-flex align-center">
          <v-icon size="20" class="mr-3" color="black">
            mdi-account
          </v-icon>
          <p class="body-1 black--text ma-0">
            {{ item.accounts_count }} {{ $t('lspListVendor.account') }}
          </p>
        </div>
        <v-spacer />
        <v-btn
          v-if="detailRoute"
          class="pa-0 px-2 text-capitalize"
          text
          @click="$router.push(localePath(detailRoute + '/' + item.id))"
        >
          <p class="subtitle-1 text--secondary ma-0">
            {{ $t('lspListVendor.detail_account') }}
          </p>
          <v-icon color="secondary" class="ml-3">
            mdi-chevron-right
          </v-icon>
        </v-btn>
      </v-row>

      <v-row v-if="isLocationVisible" class="d-flex justify-space-between ma-0">
        <div class="pa-0">
          <div class="caption mb-2">
            {{ $t('lspListVendor.location') }}
          </div>
          <div class="d-flex align-end ma-0 body-1 black--text">
            {{ item.address }}
          </div>
        </div>
        <div class="mt-3">
          <p v-if="isHasJoinDateAdmin" class="d-flex justify-end offset-6 subtitle-1 text--secondary">
            {{ $t('lspListVendor.join_date') }}
            <br>
            {{ $moment(item.collaborated_at).format('DD/MM/yyyy') }}
          </p>
        </div>
      </v-row>

      <v-row v-if="isHasDetailAccount" class="ma-0 my-4">
        <v-col class="pa-0">
          <div class="ma-0 col pa-0">
            <div class="caption mb-2">
              {{ $t('lspListVendor.contact') }} :
            </div>
            <div class="black--text mb-2">
              <v-icon size="16" class="mr-2" color="black">
                mdi-account
              </v-icon>{{ item.first_registered_account.name }}
            </div>
            <div class="black--text mb-2">
              <v-icon size="16" class="mr-2" color="black">
                mdi-phone
              </v-icon>{{
                "+" + item.first_registered_account.phone_country_code + " " +
                  item.first_registered_account.phone_number
              }}
            </div>
          </div>
        </v-col>
      </v-row>

      <v-row class="ma-2 d-flex justify-lg-space-between">
        <v-col class="d-flex justify-sm-center justify-md-center">
          <p v-if="isHasJoinDate" class="subtitle-1 text--secondary">
            {{ $t('lspListVendor.join_date') }}
            <v-spacer />
            {{ $moment(item.collaborated_at).format('DD/MM/yyyy') }}
          </p>
        </v-col>
        <v-col class="d-flex justify-sm-center justify-md-center pa-2">
          <v-btn
            v-if="!isLocationVisible"
            class="pa-0 text-capitalize text-detail-account"
            text
            @click="$router.push(localePath(detailRoute + '/' + item.id))"
          >
            <p class="subtitle-1 text--secondary ma-0">
              {{ $t('lspListVendor.detail_account') }}
            </p>
            <v-icon color="secondary" class="ml-2">
              mdi-chevron-right
            </v-icon>
          </v-btn>
        </v-col>
      </v-row>

      <div v-if="isHasAcceptReject" class="mt-5 d-flex justify-space-between">
        <slot name="accept" />
        <slot name="reject" />
      </div>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import CompanyFormItem from '~/components/CompanyFormItem.vue'
import { styleContainerCollaboration, checkIsNew } from '~/utils/functions'

export default Vue.extend({
  name: 'UserRoleCardItem',

  components: { ImageComponent, CompanyFormItem },

  props: {
    item: {
      type: Object as () => any,
      required: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    detailRoute: {
      type: String,
      required: true
    },
    logisticsServiceProviderDomain: {
      type: String,
      default: ''
    },
    logisticsServiceProviderDomains: {
      type: Array,
      default: () => []
    },
    isHasDomainCompany: {
      type: Boolean,
      default: false
    },
    isAccountCountVisible: {
      type: Boolean,
      default: true
    },
    isHasMenu: {
      type: Boolean,
      default: true
    },
    isLocationVisible: {
      type: Boolean,
      default: false
    },
    isHasAcceptReject: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    dialogUpdate: {
      type: Boolean,
      default: false
    },
    dialogDelete: {
      type: Boolean,
      default: false
    },
    isHasStatus: {
      type: Boolean,
      default: false
    },
    isHasDetailAccount: {
      type: Boolean,
      default: false
    },
    isHasJoinDate: {
      type: Boolean,
      default: false
    },
    isHasJoinDateAdmin: {
      type: Boolean,
      default: false
    },
    isHasNew: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({}),

  computed: {
    status (): string | null {
      return this.item.status
    },

    styleContainerCollaboration (): string {
      return styleContainerCollaboration(this.item.status)
    }
  },

  methods: {
    onClickSaveEdit (value: any) {
      this.$emit('on-click-save-edit', value, this.index)
    },
    onClickSaveDelete (item: any) {
      this.$emit('on-click-save-delete', item.id, this.index)
    },
    checkNew (date: string) {
      return checkIsNew(date)
    }
  }
})
</script>

<style scoped lang="scss">
.custom-card-title {
  display: -webkit-box;
  overflow: hidden;
  line-height: 30px;
  text-overflow: ellipsis;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}
</style>

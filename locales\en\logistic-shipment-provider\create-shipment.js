module.exports = {
  request_order_shipment: 'Request Order Shipment',
  request: 'Request',
  rejected: 'Rejected',
  reject: 'Reject',
  search: 'Search',
  number_order: 'Number Order',
  product: 'Product',
  quantity: 'Quantity',
  available_vehicle: 'Available Vehicle',
  estimation: 'Estimation',
  location: 'Location',
  calculation: 'Calculation',
  select_vehicle: 'Select Vehicle',
  select_vendor: ' Select Vendor',
  type: 'Type',
  name: 'Name',
  features: 'Features',
  note: 'Note',
  sub_order_your_choice: 'Sub Order Your Choice',
  total_weight: 'Total Weight',
  total_length: 'Total Length',
  total_width: 'Total Width',
  total_height: 'Total Height',
  dialog_reject_title: 'Reject Order Shipment',
  dialog_reject_text_first: 'Are you sure want to reject request Order Shipment',
  dialog_reject_text_second: 'with this vehicle request?',
  back_to_list_lsp: 'Back to list Logistic Provider',
  address: 'Address',
  no_active_order_title: 'No Active Order',
  no_active_order_shipment_title: 'No Active Order Shipment',
  no_active_order_text: 'Right now you don\'t have an active order',
  no_active_order_shipment_text: 'Right now you don\'t have an active order shipment',
  product_quantity_for: 'Product quantity for',
  in_suborder: 'in Suborder',
  specification: 'Specification',
  max_weight: 'Max Weight',
  back_to_order: 'Back to Order',
  review_order_shipment: 'Review Order Shipment',
  send_order_shipment: 'Send Order Shipment',
  send_order_shipment_text: 'are you sure you want to send order shipment?',
  select_order: 'Select Order',
  no_selected_order_title: 'No Selected Order',
  no_selected_order_text: 'Right now you don\'t have an selected order',
  shipping_dialog_text: ' Before Create order, Please select company first.',
  save_customer: 'Save Customer',
  cancel: 'Cancel',
  skip: 'Skip',
  select_customer: 'Select Customer',
  select_product: 'Select Product',
  select_shipping_company: 'Select Product'
}

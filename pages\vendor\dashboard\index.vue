<template>
  <v-row
    class="ma-0 mb-10 px-md-10 px-5 d-flex flex-lg-row flex-column-reverse"
  >
    <v-col class="pa-0 col-lg-5 col-12">
      <v-container class="white rounded pa-md-10 pa-5" fluid>
        <h4 class="mb-5">
          {{ $t('vendorDashboard.active_order') }}
        </h4>

        <v-text-field
          v-model="searchKey"
          outlined
          :label="$t('vendorDashboard.search')"
          append-icon="mdi-magnify"
          hide-details
          class="mb-5"
          @keydown.enter="searchShipments"
          @keyup.delete="searchShipments"
        />

        <div v-if="isLoadingVendor">
          <div v-for="i in 3" :key="i">
            <v-skeleton-loader
              type="image"
              style="border-radius: 0 !important"
            />
          </div>
        </div>
        <div v-else>
          <v-list v-if="vendorData?.active_order?.length !== 0" class="pa-0">
            <v-card class="pa-5 mt-5 rounded v-sheet--outlined">
              <v-card-actions class="pa-0">
                <v-expansion-panels accordion flat class="pa-0 custom-panel">
                  <v-expansion-panel
                    v-for="(item, i) in vendorData?.active_order || []"
                    :key="i"
                    class="pa-0 ma-5"
                  >
                    <v-expansion-panel-header
                      class="pa-0"
                      @click="trackColors = []; getDetailShipment(item?.id)"
                    >
                      <div class="d-flex mx-4">
                        <v-icon color="black" size="40" class="mr-5">
                          mdi-text-box-multiple
                        </v-icon>
                        <div class="d-block">
                          <p class="grey--text text-capitalize ma-0">
                            order shipment
                          </p>
                          <p class="subtitle-1 ma-0">
                            {{ item?.identity }}
                          </p>
                        </div>
                      </div>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content class="body-1 pa-0">
                      <v-divider class="mt-4" />
                      <div
                        v-for="(subitem, j) in item?.track"
                        :key="j"
                        class="d-flex mr-4 mt-5 align-center"
                      >
                        <v-img
                          class="mr-5"
                          :src="
                            require(`~/assets/images/icon-select-image.png`)
                          "
                          max-width="35"
                          max-height="35"
                          position="center center"
                        />
                        <div class="d-block">
                          <p class="subtitle-1 text-capitalize ma-0">
                            {{ subitem?.driver.name }}
                          </p>
                          <p class="grey--text ma-0">
                            Track
                            <span :style="`color:${trackColors[j]}`">{{
                              subitem?.identity
                            }}</span>
                          </p>
                        </div>
                      </div>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-actions>
            </v-card>
          </v-list>

          <div
            v-if="vendorData?.active_order?.length == 0"
            class="d-flex flex-column align-center"
          >
            <h4 class="mb-5">
              {{ $t('vendorDashboard.no_active_order') }}
            </h4>
            <p class="ma-0 text-secondary body-1 text-center">
              {{ $t('vendorDashboard.message_dont_have_active_order') }}
            </p>
          </div>
        </div>
      </v-container>
    </v-col>

    <v-col class="pa-0 pl-lg-10 col-lg-7 col-12">
      <v-container
        fluid
        class="white rounded pa-5 mb-3 d-flex justify-space-around"
      >
        <v-row class="py-3 px-5 align-center justify-start">
          <v-col class="pa-0 d-flex justify-start">
            <v-icon size="40" color="black" class="ml-4 mr-8">
              mdi-text-box-multiple
            </v-icon>
            <div class="d-flex align-center">
              <h1 class="mr-5 success--text">
                {{ vendorData?.request_shipment }}
              </h1>

              <div class="d-flex flex-column align-center">
                <p class="subtitle-1 mb-1">
                  {{ $t('vendorDashboard.request_order') }}
                </p>
                <v-btn text depressed :to="localePath('/vendor/order-shipment/create-order')" class="pa-0">
                  <p class="text-secondary body-1 ma-0 mr-2">
                    {{ $t('vendorDashboard.see_detail') }}
                  </p>
                  <v-icon>mdi-chevron-right</v-icon>
                </v-btn>
              </div>
            </div>
          </v-col>

          <v-col class="pa-0 d-flex justify-sm-start justify-start">
            <div class="d-flex align-center">
              <v-icon color="black" size="40" class="ml-4 mr-8">
                mdi-text-box-multiple
              </v-icon>
              <h1 class="mr-5 primary--text">
                {{ vendorData?.shipment_canceled }}
              </h1>

              <div class="d-flex flex-column align-center">
                <p class="subtitle-1 mb-1">
                  {{ $t('vendorDashboard.shipment_cancelled') }}
                </p>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>

      <v-responsive v-if="isLoadingDetailShipment" aspect-ratio="1">
        <v-skeleton-loader type="image" />
      </v-responsive>

      <v-responsive
        v-else
        :aspect-ratio="getAspectRatio"
        class="mb-10"
        style="z-index: 0"
      >
        <custom-map
          :latitude="centerLatLng?.lat"
          :longitude="centerLatLng?.lng"
          :polyline="polyline"
          :zoom="zoomMap"
          class="custom-map"
          @on-set-track-colors="trackColors = $event"
        >
          <template #marker>
            <div v-if="liveTracking">
              <v-rotated-marker
                v-for="(marker, i) in liveTracking?.vehicle_device"
                :key="i"
                :lat-lng="[marker?.lat, marker?.lng]"
                :rotation-angle="marker?.course"
              >
                <l-icon
                  :icon-size="[64, 64]"
                  :icon-url="require('~/assets/icons/driver.svg')"
                  :icon-anchor="[32, 32]"
                />

                <custom-map-popup
                  :driver-avatar="marker?.icon_url"
                  :driver-name="marker?.driver_name"
                  :plate-number="marker?.plate_number"
                  :identity-track="marker?.order_identity"
                  :status="marker?.status"
                  :estimate-time="marker?.eta"
                />
              </v-rotated-marker>

              <l-marker
                v-for="marker in liveTracking?.driver_device"
                :key="marker.id"
                :lat-lng="[marker.latitude, marker.longitude]"
              >
                <l-icon
                  :icon-size="[32, 32]"
                  :icon-anchor="[16, 16]"
                >
                  <v-sheet
                    color="white"
                    width="100%"
                    height="100%"
                    class="d-flex align-center justify-center pa-1"
                    style="border-radius: 100%; box-shadow: 0 0 5px rgba(0, 0, 0, 0.25)"
                  >
                    <v-img
                      v-if="marker.track?.vehicle_detail?.driver?.user?.avatar_url"
                      width="100%"
                      height="100%"
                      :src="marker.track?.vehicle_detail?.driver?.user?.avatar_url"
                      style="border-radius: 100%"
                    />

                    <v-icon
                      v-else
                      color="primary"
                      size="28"
                    >
                      mdi-account-circle
                    </v-icon>
                  </v-sheet>
                </l-icon>

                <l-popup>
                  <div class="d-flex align-center">
                    <v-img
                      width="24px"
                      contain
                      :src="marker.track?.driver?.user?.avatar_url"
                      class="mr-2"
                    />
                    <p>{{ marker.track?.driver?.user?.name }}</p>
                  </div>
                </l-popup>
              </l-marker>
            </div>

            <l-marker
              v-for="(marker, i) in markers"
              :key="i"
              :lat-lng="[parseFloat(marker.lat), parseFloat(marker.lng)]"
            >
              <l-icon v-if="marker.type === 'PICKUP'" :icon-anchor="[20, 40]">
                <v-icon size="40" color="info">
                  mdi-map-marker
                </v-icon>
              </l-icon>

              <l-icon v-else :icon-anchor="[20, 40]">
                <v-icon size="40" color="success">
                  mdi-map-marker
                </v-icon>
              </l-icon>

              <l-popup>
                <div>
                  <p
                    class="subtitle-3 mb-1"
                    :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                  >
                    {{ marker.type }}
                  </p>
                  <p class="caption ma-0 text--primary">
                    {{ marker.name }}
                  </p>
                </div>
              </l-popup>
            </l-marker>

            <l-marker
              v-for="marker in driverMarker"
              :key="marker.id"
              :lat-lng="marker.latLng"
            >
              <l-icon :icon-anchor="[20, 40]">
                <v-icon
                  color="primary"
                  size="40"
                  style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                >
                  mdi-map-marker-account
                </v-icon>
              </l-icon>
            </l-marker>
          </template>
        </custom-map>
      </v-responsive>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '@/components/shipping-company/CustomMap.vue'
import CustomMapPopup from '~/components/CustomMapPopup.vue'
import { LiveTracking, Route, Shipment } from '~/types/shipment'
import {
  defaultLat, defaultLng,
  generateCenterLatLng,
  generateDriverMarker,
  generatePolyline,
  zoom
} from '~/utils/functions'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'DashboardPage',

  components: {
    CustomMap,
    CustomMapPopup
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  beforeRouteLeave (to, from, next) {
    if (this.activeInterval) {
      clearInterval(this.activeInterval)
    }
    this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
    next()
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    searchKey: '',
    selectedTrackId: '',
    selectedRoutes: [] as Route[],
    polyline: null as Polyline | null,
    markers: [] as { lat: number, lng: number, type: string, name: string }[],
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4,
    orderIsExist: true,
    windowWidth: 0,
    trackColors: [] as string[],
    activeInterval: null as any
  }),

  computed: {
    getAspectRatio () {
      let aspectRatio = 0

      if (this.windowWidth >= 600 && this.windowWidth < 1280) {
        aspectRatio = 16 / 9
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    shipmentDetail () {
      return this.$store.getters['shipment/detailShipment'] as Shipment | null
    },
    vendorData () {
      return this.$store.getters['dashboard/dataVendor']
    },
    isLoadingVendor () {
      return this.$store.getters['dashboard/isLoadingVendor']
    },
    isLoadingDetailShipment (): Boolean {
      return this.$store.getters['shipment/isLoadingDetail']
    },
    liveTracking (): LiveTracking {
      return this.$store.getters['dashboard/liveTracking'] as LiveTracking
    },
    driverMarker (): object[] {
      return generateDriverMarker(this.polyline?.fms!)
    }
  },

  watch: {
    shipmentDetail () {
      if (this.shipmentDetail && this.shipmentDetail.tracks.length > 0) {
        this.selectedTrackId = this.shipmentDetail.tracks[0].id
        this.selectedRoutes = this.shipmentDetail.tracks[0].routes

        this.setupDirections()

        this.polyline = generatePolyline(this.shipmentDetail.tracks)
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)

        this.$store.dispatch('dashboard/getLiveTracking', {
          shipmentId: this.shipmentDetail.id
        })

        this.recurringTracking()
      }
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', this.$t('vendorDashboard.dashboard'))
    this.getVendorData()
  },

  beforeDestroy () {
    clearInterval(this.activeInterval)
  },

  methods: {
    getVendorData (searchColumns = '', searchKey = '') {
      this.$store.dispatch('dashboard/getItemVendor', {
        searchColumns,
        searchKey
      })
    },

    searchShipments () {
      this.getVendorData('identity' as any, this.searchKey)
    },

    setupDirections () {
      const markers = [] as any

      this.shipmentDetail?.tracks?.forEach((track) => {
        track.routes?.forEach((route) => {
          const lat = parseFloat(route.pickup_drop_off_location_point?.latitude)
          const lng = parseFloat(route.pickup_drop_off_location_point?.longitude)
          const type = route.type
          const name = route.pickup_drop_off_location_point?.name

          if (lat && lng && type) {
            markers.push({
              lat,
              lng,
              type,
              name
            })
          }
        })
      })

      this.markers = markers
    },

    getDetailShipment (id: string) {
      this.$store.dispatch('shipment/getItemDetailDashboardVendor', {
        id
      })
    },

    recurringTracking () {
      if (this.activeInterval) {
        clearInterval(this.activeInterval)
      }
      this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
      this.activeInterval = setInterval(() => {
        this.$store.dispatch('dashboard/getLiveTracking', {
          shipmentId: this.shipmentDetail!.id
        })
      }, 30000)
    }
  }
})
</script>

<style scoped>
.custom-map >>> .leaflet-popup { width: auto !important; }
.custom-map >>> .leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 8px 64px 0 rgba(10, 10, 10, 0.24);
}
.custom-map >>> .leaflet-popup-content { margin: 0; padding: 8px 16px; }
</style>

import { Middleware } from '@nuxt/types'

const middleware: Middleware = (context) => {
  if (context.$auth.loggedIn) {
    // get user
    const user = context.$auth.user?.data as any

    if (user !== null) {
      if (user.role === 'ADMIN') {
        return context.redirect('/admin/dashboard')
      } else if (user.role === 'SHIPMENT_COMPANY') {
        return context.redirect('/shipping-company/form-profile')
      } else if (user.role === 'VENDOR') {
        return context.redirect('/vendor/dashboard')
      } else if (user.role === 'LOGISTIC_SERVICE_PROVIDER') {
        return context.redirect('/logistic-service-provider/dashboard')
      } else {
        return context.redirect('/profile/form-profile')
      }
    }
  }
}

export default middleware

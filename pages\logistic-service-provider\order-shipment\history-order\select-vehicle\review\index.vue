<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 pb-10"
  >
    <v-row>
      <v-col>
        <v-btn
          text
          outlined
          @click="goBack"
        >
          <v-icon text>
            mdi-chevron-left
          </v-icon> Back to Order
        </v-btn>

        <v-responsive :aspect-ratio="setAspectRatio" class="mt-5 mb-lg-0 mb-10 pa-0 rounded" style="z-index: 0">
          <custom-map
            :latitude="centerLatLng.lat"
            :longitude="centerLatLng.lng"
            :zoom="zoomMap"
          >
            <template #marker>
              <l-marker
                v-for="(marker, i) in markers"
                :key="i"
                :lat-lng="[marker.lat, marker.lng]"
              >
                <l-icon
                  v-if="marker.type === 'PICKUP'"
                  :icon-anchor="[20, 30]"
                >
                  <v-icon
                    size="40"
                    color="info"
                  >
                    mdi-map-marker
                  </v-icon>
                </l-icon>

                <l-icon
                  v-else
                  :icon-anchor="[20, 30]"
                >
                  <v-icon
                    size="40"
                    color="success"
                  >
                    mdi-map-marker
                  </v-icon>
                </l-icon>

                <l-popup>
                  <div>
                    <p
                      class="subtitle-3 mb-1"
                      :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                    >
                      {{ marker.type }}
                    </p>
                    <p class="caption ma-0 text--primary">
                      {{ marker.name }}
                    </p>
                  </div>
                </l-popup>
              </l-marker>
            </template>
          </custom-map>
        </v-responsive>

        <v-col class="d-md-flex pa-0 mt-5">
          <div class="col-sm-12 col-md-4 pa-0">
            <div class="white rounded pa-md-10 pa-5 mr-2 ">
              <h4 class="mb-5">
                {{ $t('lspCreateShipment.calculation') }}
              </h4>

              <v-divider />

              <v-container fluid class="pa-0 mb-3">
                <h4 class="mb-2">
                  Your Delivery Order
                </h4>
                <h4 class="mb-2 red--text">
                  {{ detailShipment?.orders[0]?.identity }}
                </h4>

                <v-divider />
              </v-container>

              <v-container fluid class="pa-0">
                <v-container fluid class="pa-0 mb-6 d-flex justify-space-between align-start">
                  <div>
                    <p class="body-1 mb-1 text-secondary">
                      Remaining Weight :
                    </p>
                    <h2 class="blue--text">
                      {{ formatNumber(selectedOrder?.remaining_weight ?? 0) }} KG
                    </h2>
                    <div class="d-flex mt-5">
                      <p class="body-1 mb-1 text-secondary">
                        total weight :
                      </p>
                      <p class="subtitle-1 ml-1">
                        {{ formatNumber(detailShipment?.total_weight ?? 0) }} KG
                      </p>
                    </div>
                  </div>
                </v-container>
              </v-container>

              <v-divider />

              <h5 class="my-5">
                Detail Vendor
              </h5>
              <div v-for="(vehicle, index) of checkedVehicles" :key="index" class="pa-2 my-3">
                <div class="d-flex mb-3">
                  <h5>
                    Vendor :
                  </h5>
                  <h5 class="ml-3">
                    {{ vehicle?.vendor?.name }}
                  </h5>
                </div>
                <h5>
                  {{ $t('lspCreateShipment.name') }} :
                </h5>
                {{ vehicle.name }}
                <h5 class="mt-2">
                  {{ $t('lspCreateShipment.features') }} :
                </h5>
                <div class="pa-0">
                  <div
                    v-for="feature in vehicle.vehicle_features"
                    :key="feature.id"
                    class="pa-2 ma-1 my-1 d-inline-flex caption"
                    style="
                      border-color: #cfcccc;
                      border-style: solid;
                      border-radius: 4px;
                  "
                  >
                    {{ feature.name.toUpperCase() }}
                  </div>
                </div>
                <div class="pa-0">
                  <h5 class="mt-2">
                    {{ $t('lspCreateShipment.type') }}
                  </h5>

                  <p class="mb-0">
                    {{ vehicle.vehicle_type?.name }}
                  </p>
                  <h5 class="mt-2">
                    {{ $t('lspCreateShipment.quantity') }}
                  </h5>
                  {{ vehicle.quantity }}
                </div>
                <v-divider class="my-3" />
              </div>
              <v-dialog v-model="isShowSubmitDialog" max-width="600px">
                <v-card class="pa-md-10 pa-5">
                  <v-form ref="form">
                    <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
                      <h4>
                        {{ $t('sendOrderShipmentDialog.dialog_send_title') }}
                      </h4>

                      <v-icon color="black" @click="isShowSubmitDialog = false">
                        mdi-close
                      </v-icon>
                    </v-card-title>

                    <p class="subtitle-1">
                      Pickup Order Estimation
                    </p>
                    <v-row>
                      <v-col>
                        <v-menu
                        ref="menu"
                        v-model="menuDateRangePickup"
                        max-width="290"
                        :close-on-content-click="false"
                        transition="slide-y-transition"
                        offset-y
                      >
                        <template #activator="{ on, attrs }">
                          <v-text-field
                            v-model="dateRangePickup"
                            outlined
                            clearable
                            readonly
                            label="Estimation Date"
                            v-bind="attrs"
                            append-icon="mdi-calendar-range"
                            v-on="on"
                          >
                            Select Date Range
                          </v-text-field>
                        </template>

                        <v-date-picker
                          v-model="dateRangePickup"
                          no-title
                          :allowed-dates="allowedDatePickupDropoff"
                          color="primary"
                          @input="updateNextDay"
                        >
                          <v-btn text color="primary" @click="menuDateRangePickup = false">
                            Save
                          </v-btn>
                          <v-btn text color="primary" @click="menuDateRangePickup = false">
                            Cancel
                          </v-btn>
                        </v-date-picker>
                      </v-menu>
                      </v-col>

                      <v-col>
                        <vue-timepicker
                          v-model="timePickup"
                          fixed-dropdown-button
                          close-on-complete
                          manual-input
                          auto-scroll
                          drop-direction="up"
                          input-width="100%"
                          placeholder="Estimation Time"
                          input-class="vtimepicker"
                          style="z-index: 1000;"
                          @input="validatePickupTime"
                        >
                          <template #dropdownButton>
                            <v-icon>mdi-clock-time-four-outline</v-icon>
                          </template>
                        </vue-timepicker>
                        <p v-if="timePickupMessage" class="error--text mt-1">{{ timePickupMessage }}</p>
                      </v-col>
                    </v-row>

                    <p class="subtitle-1">
                      Dropoff Order Estimation
                    </p>
                    <v-row class="ma-0 pa-0">
                      <v-col class="ma-0 pa-0">
                        <v-menu
                          ref="menu"
                          v-model="menuDateRangeDropoff"
                          max-width="290"
                          :close-on-content-click="false"
                          transition="slide-y-transition"
                          offset-y
                        >
                          <template #activator="{ on, attrs }">
                            <v-text-field
                              v-model="dateRangeDropoff"
                              outlined
                              clearable
                              readonly
                              label="Estimation Date"
                              v-bind="attrs"
                              append-icon="mdi-calendar-range"
                              v-on="on"
                            >
                            </v-text-field>
                          </template>
                          <v-date-picker
                            v-model="dateRangeDropoff"
                            no-title
                            :allowed-dates="allowedDatePickupDropoff"
                            color="primary"
                            @input="updateNextDay"
                          >
                            <v-btn text color="primary" @click="menuDateRangeDropoff = false">
                              Save
                            </v-btn>
                            <v-btn text color="primary" @click="menuDateRangeDropoff = false">
                              Cancel
                            </v-btn>
                          </v-date-picker>
                        </v-menu>
                      </v-col>

                      <v-col class="ma-0 pa-0 ml-6">
                        <vue-timepicker
                          :value="timeDropoff"
                          fixed-dropdown-button
                          close-on-complete
                          manual-input
                          auto-scroll
                          drop-direction="down"
                          input-width="100%"
                          placeholder="Estimation Time"
                          input-class="vtimepicker"
                          style="z-index: 1000;"
                          @input="timeDropoff = $event"
                        >
                          <template #dropdownButton>
                            <v-icon>mdi-clock-time-four-outline</v-icon>
                          </template>
                        </vue-timepicker>
                      </v-col>
                    </v-row>

                    <v-row class="ma-0 pa-0">
                      <v-col class="ma-0 pa-0">
                        <p class="caption text-secondary">
                          Recommended Date: 
                          <span class="success--text" v-if="estimationDate !== 'NaN-NaN-NaN'">{{ estimationDate }}</span>
                          <span class="success--text" v-else>-</span>
                        </p>
                      </v-col>
                      
                      <v-col class="ma-0 pa-0 ml-6">
                        <p class="caption text-secondary">
                          Recommended Time:
                          <span class="success--text" v-if="estimationTime && timePickup && estimationTime !== 'NaN:NaN'">{{ estimationTime }}</span>
                          <span class="success--text" v-else>-</span>
                        </p>
                      </v-col>
                    </v-row>

                    <v-expansion-panels flat>
                    <v-expansion-panel elevation="0" class="pa-0 mb-1">
                      <v-expansion-panel-header>
                        <p class="subtitle-1">Optional</p>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>

                        <div v-for="(vehicle, index) of checkedVehicles" :key="index" class="pa-2 my-3">
                          <div class="d-flex mb-3">
                            <v-icon @click="toggleNoteField(index)">
                              mdi-plus
                            </v-icon>
                            <h5 class="ml-2">
                              Add {{ vehicle?.vendor?.name }} Note
                            </h5>
                          </div>
                          <v-textarea
                            v-if="vehicle.showNote"
                            v-model="note[index]"
                            outlined
                            :label="$t('sendOrderShipmentDialog.label_note')"
                            hide-details
                            class="mb-5"
                            no-resize
                          />
                        </div>

                        <p class="red--text">
                          Expired Order {{ nextDay }} at 10.00
                        </p>
                        <div class="pa-0 d-flex">
                          <v-checkbox v-model="setExpired" class="ma-1" />
                          <p class="ma-2">
                            Set Expired manually?
                          </p>
                        </div>

                        <div v-if="setExpired" class="col-10">
                          <v-menu
                            ref="menu"
                            v-model="menuDateRange"
                            max-width="290"
                            :close-on-content-click="false"
                            transition="slide-y-transition"
                            offset-y
                          >
                            <template #activator="{ on, attrs }">
                              <v-text-field
                                v-model="dateRange"
                                outlined
                                clearable
                                readonly
                                label="Date and Time"
                                v-bind="attrs"
                                append-icon="mdi-calendar-range"
                                v-on="on"
                              >
                                Select Date Range
                              </v-text-field>
                            </template>

                            <v-date-picker
                              v-model="dateRange"
                              no-title
                              :allowed-dates="allowedDatesExpired"
                              color="primary"
                              @input="updateNextDay"
                            >
                              <v-btn text color="primary" @click="menuDateRange = false">
                                Save
                              </v-btn>
                              <v-btn text color="primary" @click="menuDateRange = false">
                                Cancel
                              </v-btn>
                            </v-date-picker>
                          </v-menu>
                        </div>
                      </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-expansion-panels>

                    <p class="body-1 mb-4 black--text mt-4">
                      Are you sure you want to send order shipment?
                    </p>

                    <v-card-actions class="pa-0">
                      <v-row class="ma-0">
                        <v-col class="mr-sm-5 mb-sm-0 mb-5 pa-0 col-sm-6 col-12">
                          <v-btn
                            elevation="0"
                            color="primary"
                            class="text-capitalize"
                            x-large
                            block
                            @click="sendShipmentOrder"
                          >
                            Send Order Shipment
                          </v-btn>
                        </v-col>
                        <v-col class="pa-0">
                          <v-btn
                            elevation="0"
                            outlined
                            color="primary"
                            class="text-capitalize ma-0"
                            x-large
                            block
                            @click="isShowSubmitDialog = false"
                          >
                            {{ $t('sendOrderShipmentDialog.button_cancel') }}
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-actions>
                  </v-form>
                </v-card>
                <template #activator="{ on, attrs }">
                  <div class="d-flex flex-column">
                    <v-btn
                      class="mt-5 mr-2"
                      text
                      outlined
                      color="primary"
                      @click="goBack"
                    >
                      <v-icon text>
                        mdi-chevron-left
                      </v-icon> Back to Order
                    </v-btn><v-btn
                      class="mt-5"
                      color="primary"
                      v-bind="attrs"
                      :loading="isLoadingForm"
                      @click="isShowSubmitDialog = true"
                      v-on="on"
                    >
                      {{ $t('lspCreateShipment.send_order_shipment') }}
                    </v-btn>
                  </div>
                </template>
              </v-dialog>
            </div>
          </div>
          <v-container class="white rounded col-sm-12 col-md-8 d-flex ml-md-2 ml-sm-0 mt-5 mt-md-0 mt-sm-5 pa-md-10 pa-5 flex-column">
            <h4>
              <v-icon
                color="black"
                class="mr-5"
              >
                mdi-text-box-multiple
              </v-icon>Detail Order Shipment
            </h4>
            <v-divider class="my-5" />
            <h4 class="mb-5">
              {{ $t('lspCreateShipment.select_order') }}
            </h4>
            <div class="d-flex">
              <v-card
                v-for="(order, index) of detailShipment?.orders"
                :key="index"
                class="pa-3 ma-2"
                outlined
                elevation="0"
                :style="selectedOrder?.id === order.id? `border: 1px solid #bf2a2a`:null"
                @click="selectOrder(order)"
              >
                <div class="d-flex flex-column">
                  <div class="body-1">
                    {{ order?.shipment_company?.name }}
                  </div>
                  <div class="subtitle-1">
                    {{ order?.identity }}
                  </div>
                </div>
              </v-card>
            </div>
            <v-divider class="my-5" />
            <detail-order-shipment
              v-if="selectedOrder"
              :is-selected-order-exist="isSelectedOrderExist"
              :selected-order="selectedOrder"
              :is-loading="isLoadingDetailOrder"
            />
          </v-container>
        </v-col>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { formatNumber } from '~/utils/functions'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import { Shipment } from '~/types/shipment'
import { Vehicle } from '~/types/vehicle'
import { Order } from '~/types/product'
import {
  generateCenterLatLng,
  colorType,
  zoomBetweenTwoPoints
} from '~/utils/functions'

export default Vue.extend({
  name: 'ReviewOrderShipmentPage',

  components: {
    CustomMap
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    nextDay: '',
    menuDateRangePickup: false as boolean,
    menuTimePickup: false as boolean,
    timePickup: '',
    menuDateRangeDropoff: false as boolean,
    menuTimeDropoff: false as boolean,
    timeDropoff: '',
    dateRangePickup: '' as any,
    dateRangeDropoff: '' as any,
    dateRange: '' as any,
    menuDateRange: false as boolean,
    setExpired: false,
    note: [],
    isShowSubmitDialog: false,
    timeSuggestion: null as any,
    estimationDate: null as any,
    estimationTime: null as any,
    isLoadingTimeSuggestion: false,
    timePickupMessage: '',
  }),

  computed: {
    zoomMap (): number {
      if (!this.markers) { return 0 }

      const firstPoint = this.markers[0]
      const lastPoint = this.markers[this.markers.length - 1]

      return zoomBetweenTwoPoints(firstPoint.lat, firstPoint.lng, lastPoint.lat, lastPoint.lng)
    },
    markers (): { lat: number, lng: number, type: string, name: string }[] | null {
      return this.selectedOrder
        ? this.selectedOrder.suborders?.map(suborder => ({
          lat: Number(suborder.pickup_drop_off_location_point.latitude),
          lng: Number(suborder.pickup_drop_off_location_point.longitude),
          type: suborder.type,
          name: suborder.pickup_drop_off_location_point.name
        })) ?? null
        : null
    },
    centerLatLng (): { lat: number, lng: number } {
      return this.markers
        ? generateCenterLatLng(this.markers)
        : { lat: 0, lng: 0 }
    },
    setAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 5 / 2
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },

    isLoadingForm (): boolean {
      return this.$store.getters['order/isLoadingForm']
    },

    isLoadingFormShipment (): boolean {
      return this.$store.getters['shipment/isLoadingForm']
    },

    isLoadingDetailOrder (): boolean {
      return this.$store.getters['order/isLoadingDetail']
    },

    isSelectedOrderExist (): boolean {
      return Object.keys(this.selectedOrder).length !== 0
    },

    checkedVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/checkedVehicles'] as Array<Vehicle>
    },

    detailShipment (): Shipment | null {
      return this.$store.getters['shipment/detailShipment']
    },

    selectedOrder (): Order {
      return this.$store.getters['order/selectedOrder']
    }
  },

  watch: {
    dateRange (newVal) {
      if (!newVal) {
        this.updateNextDay()
      }
    },
    dateRangePickup(newVal) {
      if (!newVal) {
        // Reset estimation when date is cleared
        this.estimationDate = null;
        this.estimationTime = null;
      }
      if (newVal) {
        this.getTimeSuggestion();
      }
    },
    timePickup(newVal) {
      if (!newVal) {
        // Reset estimation when time is cleared
        this.estimationTime = null;
      }
      if (newVal) {
        this.getTimeSuggestion();
      }
    },
    isShowSubmitDialog(val) {
      if (!val) {
        this.estimationDate = null
        this.estimationTime = null
      } else {
        this.estimationDate = null
        this.estimationTime = null
      }
    }
  },

  async mounted () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')

    if (!this.detailShipment) {
      this.$router.go(-2)
    } else {
      await this.selectOrder(this.detailShipment.orders[0])
    }
    this.updateNextDay()
    this.getTimeSuggestion()
  },

  methods: {
    formatNumber,
    toggleNoteField (index: number) {
      this.checkedVehicles[index].showNote = !this.checkedVehicles[index].showNote
      this.$forceUpdate()
    },

    async getTimeSuggestion() {
      this.isLoadingTimeSuggestion = true;

      const params = {
        suborder_id: this.selectedOrder?.suborders?.[0]?.id,
        dropoff_location: this.selectedOrder?.suborders?.[1]?.pickup_dropoff_location_point_id,
        pickup_location: this.selectedOrder?.suborders?.[0]?.pickup_dropoff_location_point_id
      };

      try {
        // Check if any param is null or undefined
        if (!params.suborder_id || !params.dropoff_location || !params.pickup_location) {
          this.timeSuggestion = null;
          this.estimationTime = '-';
          return;
        }

        const response = await this.$store.dispatch('pick-up-drop-off-location-point/getTimeSuggestion', params);
        this.timeSuggestion = response?.data;

        if (this.timeSuggestion?.[0]?.in_minutes) {
          if (this.timePickup) {
            // Parse the pickup date and time
            const [year, month, day] = this.dateRangePickup.split('-').map(Number);
            const [hours, minutes] = this.timePickup.split(':').map(Number);
            
            // Create Date object with the correct date and time
            const pickupDateTime = new Date(year, month - 1, day, hours, minutes, 0, 0);
            
            // Calculate new date by adding minutes
            const minutesToAdd = this.timeSuggestion[0].in_minutes;
            const newDate = new Date(pickupDateTime.getTime() + minutesToAdd * 60000);
            
            // Format the estimation date and time
            this.estimationDate = `${newDate.getFullYear()}-${String(newDate.getMonth() + 1).padStart(2, '0')}-${String(newDate.getDate()).padStart(2, '0')}`;
            this.estimationTime = `${String(newDate.getHours()).padStart(2, '0')}:${String(newDate.getMinutes()).padStart(2, '0')}`;
          } else {
            // If no pickup time selected, use pickup date and calculate time from minutes
            const [year, month, day] = this.dateRangePickup.split('-').map(Number);
            const pickupDate = new Date(year, month - 1, day);
            const totalMinutes = this.timeSuggestion[0].in_minutes;
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            
            this.estimationTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
            this.estimationDate = this.dateRangePickup;
          }
        } else {
          this.estimationTime = '-';
        }
      } catch (error) {
        console.error(error);
        this.estimationTime = '-';
      } finally {
        this.isLoadingTimeSuggestion = false;
      }
    },

    validatePickupTime(time: any) {
      this.timePickup = time;
      
      if (this.dateRangePickup) {
        const today = new Date();
        const selectedDate = new Date(this.dateRangePickup);
        if (selectedDate.toDateString() === today.toDateString()) {
          const currentHour = today.getHours();
          const currentMinute = today.getMinutes();
          const [selectedHour, selectedMinute] = this.timePickup.split(':').map(Number);

          if (selectedHour < currentHour || (selectedHour === currentHour && selectedMinute <= currentMinute)) {
            this.timePickupMessage = 'Time cannot be less than current time';
          } else {
            this.timePickupMessage = '';
          }
        } else {
          this.timePickupMessage = '';
        }
      }
    },

    async selectOrder (order: Order) {
      await this.$store.dispatch('order/getDetailOrder', { id: order.id })
    },

    goBack () {
      this.$router.back()
    },

    colorType (type: string) {
      return colorType(type)
    },

    sendShipmentOrder () {
      const payload = {
        id: this.$route.query.shipmentRitaseId,
        set_expired_at: this.dateRange,
        vendors: [] as {
          id: string;
          weight: number;
          vehicles: { vehicle_id: string; quantity: number }[];
        }[],
        estimation_date_pickup: this.dateRangePickup && this.timePickup 
          ? `${this.dateRangePickup} ${this.timePickup}:00` 
          : "",
        estimation_date_dropoff: this.dateRangeDropoff && this.timeDropoff 
          ? `${this.dateRangeDropoff} ${this.timeDropoff}:00` 
          : ""
      }

      const vendorIds = Array.from(new Set(this.checkedVehicles.map(v => v.vendor.id)))

      vendorIds.forEach((vendorId, index) => {
        const vendorObject = {
          id: vendorId,
          note: this.note[index] || '',
          weight: 0,
          vehicles: [] as { vehicle_id: string; quantity: number }[]
        }

        const vehiclesForVendor = this.checkedVehicles.filter(v => v.vendor_id === vendorId)
        vehiclesForVendor.forEach((vehicle) => {
          const vehicleData = {
            vehicle_id: vehicle.id,
            quantity: vehicle.quantity || 0
          }
          vendorObject.vehicles.push(vehicleData)
          if (vehicle.vendor.weight) {
            vendorObject.weight = Number(vehicle.vendor.weight)
          }
        })

        payload.vendors.push(vendorObject)
      })

      this.$store.dispatch('order/sendRitaseShipmentOrder', payload)

      this.isShowSubmitDialog = false
    },

    allowedDatesExpired (date: Date) {
      const today = new Date()
      const currentHour = today.getHours()
      
      today.setHours(0, 0, 0, 0)

      const selectedDate = new Date(date)
      selectedDate.setHours(0, 0, 0, 0)

      if (currentHour < 10) {
        return selectedDate >= today
      }

      const tomorrow = new Date()
      tomorrow.setDate(today.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)

      return selectedDate >= tomorrow
    },

    allowedDates (date: Date) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const tomorrow = new Date()
      tomorrow.setDate(today.getDate())
      tomorrow.setHours(0, 0, 0, 0)

      const selectedDate = new Date(date)

      return selectedDate >= tomorrow
    },

    allowedDatePickupDropoff (date: Date) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const tomorrow = new Date()
      tomorrow.setDate(today.getDate())
      tomorrow.setHours(0, 0, 0, 0)

      const selectedDate = new Date(date)

      return selectedDate >= tomorrow
    },

    updateNextDay () {
      const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      if (this.dateRange) {
        const selectedDate = new Date(this.dateRange)
        const today = new Date()
        const isToday = selectedDate.toDateString() === today.toDateString()
        
        if (isToday) {
          if (today.getHours() >= 10) {
            this.nextDay = 'Tomorrow'
          } else {
            this.nextDay = 'Today'
          }
        } else {
          this.nextDay = daysOfWeek[selectedDate.getDay()]
        }
      } else {
        const today = new Date()
        if (today.getHours() >= 10) {
          this.nextDay = 'Tomorrow'
        } else {
          this.nextDay = 'Today'
        }
      }
    }
  }
})

</script>

<style lang="scss" scoped> </style>

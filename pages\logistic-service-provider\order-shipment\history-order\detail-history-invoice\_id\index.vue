<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="mb-10 pa-0">
      <div class="mb-sm-10 mb-5 d-flex align-center">
        <v-btn
          text
          plain
          class="text-capitalize pa-0"
          @click="navigateBack"
        >
          <v-icon
            class="mr-3 custom-icon"
          >
            mdi-chevron-left
          </v-icon>
          {{ $t('lspHistoryShipment.back_to_invoice') }}
        </v-btn>
      </div>

      <detail-history-invoice-loading v-if="isLoadingDetailInvoice" />

      <detail-invoice-lsp
        v-else
        :invoice-shipment="dataInvoiceShipment"
        :selected-invoice="selectedInvoice"
        :selected-order="selectedOrder"
        :pickup-sub-order="pickupSubOrder"
        :drop-off-sub-order="dropOffSubOrder"
        :products="products"
        :total-fee="totalFee"
        @on-select-order="selectedOrder = $event"
      >
        <template #date-invoice>
          {{ $moment(dataInvoiceShipment?.invoice?.shipment?.created_at).format('DD MMMM YYYY') }}
        </template>
        <template #invoice-number>
          <div>
            <p class="caption text-secondary mb-2">
              {{ $t('lspHistoryShipment.invoice_number') }}
            </p>
            <h3>{{ dataInvoiceShipment?.invoice?.shipment?.identity }}</h3>
          </div>
        </template>

        <template #download-invoice>
          <v-btn
            x-large
            outlined
            class="text-capitalize"
            :loading="isLoadingDownloadInvoice"
            style="border: 1px solid #CFCCCC;"
            @click="downloadInvoice"
          >
            <v-icon>
              mdi-file-download
            </v-icon>
            <p class="subtitle-1 ma-0">
              {{ $t('lspHistoryShipment.download_invoice') }}
            </p>
          </v-btn>
        </template>

        <template #header>
          <div>
            <p class="subtitle-1">
              {{ $t('lspHistoryShipment.select_order') }}
            </p>
            <v-item-group mandatory>
              <v-row class="ma-n3">
                <v-col
                  v-for="order in dataInvoiceShipment?.orders"
                  :key="order.id"
                  class="pa-3 col-auto"
                >
                  <v-item
                    v-slot="{ active, toggle }"
                    @change="selectedOrder = order"
                  >
                    <v-card
                      flat
                      outlined
                      :input-value="active"
                      class="py-2 px-5"
                      @click="toggle"
                    >
                      <p class="subtitle-1 mb-1">
                        {{ order.shipment_company?.name }}
                      </p>
                      <p class="ma-0 caption">
                        {{ order.identity }}
                      </p>
                    </v-card>
                  </v-item>
                </v-col>
              </v-row>
            </v-item-group>
          </div>
        </template>

        <template #order-number>
          <p class="caption mb-2 text-secondary">
            {{ $t('lspHistoryShipment.order_number') }}
          </p>
          <h3 class="mb-10">
            {{ selectedOrder?.identity }}
          </h3>
        </template>

        <template #shipping-cost>
          <h4>{{ selectedInvoice?.cost ?? 0 | toCurrency }}</h4>
        </template>

        <template #additional-fee>
          <v-container
            v-for="fee in selectedInvoice?.fees"
            :key="fee.id"
            fluid
            class="mb-2 pa-0 d-flex align-center justify-space-between"
          >
            <p class="ma-0 body-1 text-secondary">
              {{ fee.description }}
            </p>
            <p class="ma-0 body-1 black--text">
              {{ parseInt(fee.cost) | toCurrency }}
            </p>
          </v-container>
        </template>

        <template #total-cost>
          <h3 class="text-primary">
            {{ totalFee + parseInt(selectedInvoice?.cost) | toCurrency }}
          </h3>
        </template>
      </detail-invoice-lsp>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailHistoryInvoiceLoading from '~/components/loading/DetailHistoryInvoiceLoading.vue'
import DetailInvoiceLsp from '~/components/DetailInvoiceLsp.vue'
import { Fee, InvoiceDetail, InvoiceOrder } from '~/types/invoice'
import { Order, Product, SubOrder } from '~/types/product'

export default Vue.extend({

  name: 'DetailHistoryInvoicePage',

  components: {
    DetailHistoryInvoiceLoading,
    DetailInvoiceLsp
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    selectedOrder: null as Order | null,
    selectedInvoice: null as InvoiceDetail | null,
    pickupSubOrder: [] as SubOrder[],
    dropOffSubOrder: [] as SubOrder[],
    products: [] as Product[],
    totalFee: 0 as number
  }),

  computed: {
    dataInvoiceShipment (): InvoiceOrder | null {
      return this.$store.getters['logistic-service-provider/invoice-shipment/detailData'].item as InvoiceOrder | null
    },

    isLoadingDetailInvoice () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingDetail']
    },

    isLoadingDownloadInvoice () {
      return this.$store.getters['invoice-details/isLoadingDownloadInvoice']
    }
  },

  watch: {
    selectedOrder () {
      if (this.selectedOrder) {
        const invoiceDetails = this.dataInvoiceShipment?.invoice_details as InvoiceDetail[]

        invoiceDetails.forEach((item: InvoiceDetail) => {
          if (item.order_id === this.selectedOrder?.id) {
            this.selectedInvoice = item
          }
        })

        const subOrders = this.selectedInvoice?.order.suborders as SubOrder[]
        this.pickupSubOrder = []
        this.dropOffSubOrder = []
        this.products = []

        subOrders.forEach((item: SubOrder) => {
          if (item.type === 'PICKUP') {
            this.pickupSubOrder?.push(item)
          } else {
            this.dropOffSubOrder?.push(item)
          }
        })

        this.products = this.pickupSubOrder?.map((suborder: SubOrder) => suborder?.products).flat()

        const fees = this.selectedInvoice?.fees as Fee[]
        this.totalFee = 0

        fees.forEach((fee: Fee) => {
          if (fee.cost == null) {
            this.totalFee = 0
          }

          this.totalFee += parseInt(fee.cost ?? '0')
        })
      }
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'History Order Shipment')
  },

  mounted () {
    this.getInvoiceShipment()
  },

  methods: {
    async getInvoiceShipment () {
      const response = await this.$store.dispatch('logistic-service-provider/invoice-shipment/getItemDetail', {
        id: this.$route.params.id
      })

      this.selectedOrder = response?.orders[0]
    },

    async downloadInvoice () {
      await this.$store.dispatch('invoice-details/downloadInvoiceDetails', {
        id: this.selectedInvoice?.id,
        number: this.selectedOrder?.identity
      })
    },
    navigateBack () {
      this.$router.push({
        path: this.localePath('/logistic-service-provider/order-shipment/history-order'),
        query: {
          tab: '1'
        }
      })
    }
  }
})
</script>

<style lang="scss" scoped>
.v-item--active {
  color: #EF3434;
  border: 1px solid #EF3434;
}
.custom-icon {
  transition: 0s !important;
}
</style>

<template>
  <v-container fluid class="pa-0 px-10 mb-10">
    <div class="mb-sm-10 mb-5 d-flex align-center">
      <v-btn
        text
        plain
        class="text-capitalize pa-0"
        @click="$router.back()"
      >
        <v-icon class="mr-3 custom-icon">
          mdi-chevron-left
        </v-icon>
        {{ $t('vendorHistoryOrder.back_to_list') }}
      </v-btn>
    </div>

    <detail-history-order-loading v-if="isLoadingShipmentDetail" />

    <detail-history-order-shipment
      v-else
      from="VENDOR"
      :vehicles="vehicles"
      :driver="drivers"
      :vehicle-detail="vehicleDetail"
      :center-position="centerLatLng"
      :polyline="polyline"
      :markers="markers"
      :zoom="zoomMap"
      :shipment-detail="shipmentDetail"
      :is-total-weight-vendor="true"
      :is-total-weight="false"
      :selected-routes="selectedRoutes"
      :shipment-id="shipmentDetail?.id"
      :is-loading-shipment-detail="isLoadingShipmentDetail"
      detail-invoice-url="/vendor/order-shipment/history-order/detail-invoice-order/"
      @on-select-track="updateRoutes($event)"
    >
      <template #log-change-vehicle>
        <v-container v-if="shipmentDetail?.shipment_vehicle_log.length > 0"
                     class="white rounded flex-column ma-0 pa-0 mt-8"
        >
          <h4 class="mb-4">
            Log Changes Vehicle
          </h4>
          <v-col v-for="(logChangeVehicle, i) in shipmentDetail.shipment_vehicle_log" :key="i"
                 class="d-flex justify-space-between mb-8 ma-0 pa-0"
                 style="border-bottom: 2px solid #1b1f24;"
          >
            <v-col class="pa-0">
              <p class="ma-0 pa-0">From</p>
              <p class="subtitle-1 text--bold ma-0 pa-0">
                {{ logChangeVehicle?.old_vehicle_detail?.vehicle?.name }}
              </p>
              <p class="ma-0 pa-0">{{ logChangeVehicle?.old_vehicle_detail?.plate_number }}</p>
              <p class="ma-0 pa-0">{{ logChangeVehicle?.old_driver?.user?.name }}</p>
              <p class="subtitle-1 text--bold ma-0 pa-0">
                Lampiran
              </p>

              <div v-if="logChangeVehicle?.shipment_vehicle_photo[0]?.file.split('.')[1] === 'pdf'">
                <a :href="logChangeVehicle?.shipment_vehicle_photo[0]?.files_url" target="_blank">Open File</a>
              </div>
              <image-component
                v-else
                :image="logChangeVehicle?.shipment_vehicle_photo[0]?.files_url"
                class="zoom-image zoom-image:hover"
                max-width="100"
                min-width="100"
              />

              <p class="subtitle-1 text--bold mt-3 ma-0 pa-0">
                Waktu
              </p>
              <p class="ma-0 pa-0">{{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}</p>
            </v-col>
            <v-col class="ml-5 pa-0">
              <p class="ma-0 pa-0">To</p>
              <p class="subtitle-1 text--bold ma-0 pa-0">
                {{ logChangeVehicle?.new_vehicle_detail?.vehicle?.name }}
              </p>
              <p class="ma-0 pa-0">{{ logChangeVehicle?.new_vehicle_detail?.plate_number }}</p>
              <p class="ma-0 pa-0">{{ logChangeVehicle?.new_driver?.user?.name }}</p>
              <p class="subtitle-1 text--bold ma-0 pa-0">
                Note
              </p>
              <p>{{ logChangeVehicle?.note }}</p>
            </v-col>
          </v-col>
        </v-container>
      </template>

      <detail-history-order-shipment />
    </detail-history-order-shipment>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Route, Shipment } from '~/types/shipment'
import DetailHistoryOrderLoading from '~/components/loading/DetailHistoryOrderLoading.vue'
import DetailHistoryOrderShipment from '~/components/DetailHistoryOrderShipment.vue'
import { defaultLat, defaultLng, generateCenterLatLng, generatePolyline, zoom } from '~/utils/functions'
import { VehicleDetail } from '~/types/vehicle'
import { Driver } from '~/types/driver'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'DetailHistoryOrderPage',

  components: {
    DetailHistoryOrderLoading,
    DetailHistoryOrderShipment
  },
  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    dialog: false,
    expanded: [],
    searchKey: '',
    selectedTrackId: '',
    selectedRoutes: [] as any,
    polyline: null as Polyline | null,
    markers: [] as { lat: number, lng: number, type: string, name: string }[],
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4 as number,
    trackIndex: 0
  }),

  computed: {
    shipmentDetail () {
      return this.$store.getters['shipment/detailShipment'] as Shipment
    },
    isLoadingShipmentDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    },
    vehicles () {
      return this.$store.getters['vehicle/data'].items
    },
    vehicleDetail () {
      return this.$store.getters['vehicle/details/data'].items as VehicleDetail[]
    },
    drivers () {
      return this.$store.getters['vehicle/drivers/data'].items as Driver[]
    }
  },

  watch: {
    shipmentDetail () {
      this.updateRoutes(this.trackIndex)
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'History Order Shipment')
  },

  mounted () {
    this.$store.dispatch('shipment/getItemDetail', {
      id: this.$route.params.id,
      type: 'detail'
    })
    this.getDataVehicles()
    this.getVehicleDetailData()
    this.getDriverData({})
  },

  methods: {
    updateRoutes (index: number) {
      if (this.shipmentDetail) {
        this.selectedTrackId = this.shipmentDetail?.tracks?.[index]?.id
        this.selectedRoutes = this.shipmentDetail?.orders?.[index]?.suborders
        
        this.setupDirections()

        this.polyline = generatePolyline(this.shipmentDetail.tracks)
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)
      }
    },
    setupDirections () {
      const markers = [] as any

      this.shipmentDetail?.tracks?.forEach((track) => {
        track.routes?.forEach((route) => {
          if (!route.pickup_drop_off_location_point) {
            return
          }

          const lat = parseFloat(route.pickup_drop_off_location_point.latitude)
          const lng = parseFloat(route.pickup_drop_off_location_point.longitude)
          const type = route.type
          const name = route.pickup_drop_off_location_point.name

          markers.push({
            lat,
            lng,
            type,
            name
          })
        })
      })

      this.markers = markers
    },

    getDataVehicles () {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/getItems', {
        filterColumns: 'vendor_id',
        filterKeys: user?.vendor?.id,
        entries: '-1'
      })
    },

    getVehicleDetailData () {
      this.$store.dispatch('vehicle/details/getItems')
    },

    getDriverData ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/drivers/getItems', {
        mode: 'simplified',
        sortColumn: filter?.sortColumn,
        sortType: filter?.sortType,
        filterKeys: user?.vendor?.id,
        entries: -1
      })
    }
  }
})
</script>

<style scoped lang="scss">
.f-resize {
  font-size: 16px !important;
}
.chip-info {
  background: #e6f4f8 !important;
}
@media (min-width: 280px) and (max-width: 576px) {
  .f-resize {
    font-size: 12px !important;
  }
  .f-resize-16 {
    font-size: 16px !important;
  }
  .f-resize-14 {
    font-size: 14px !important;
  }
  .icon-resize {
    font-size: 16px !important;
  }
}

.zoom-image {
  transition: transform 0.3s;
}

.zoom-image:hover {
  transform: scale(5.0);
}
</style>

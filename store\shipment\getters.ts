import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { ShipmentState } from './state'

export const getters: GetterTree<ShipmentState, RootState> = {
  dataShipment (state) {
    return {
      items: state.dataShipment,
      totalPage: state.totalPage,
      page: state.page,
      totalData: state.totalData
    }
  },

  dataShipmentActiveOrders (state) {
    return {
      items: state.dataShipmentActiveOrders,
      totalPage: state.totalPage,
      page: state.page,
      totalData: state.totalData
    }
  },

  dataShipmentRitase (state) {
    return state.detailShipmentRitase
  },

  dataShipmentProposed (state) {
    return {
      items: state.dataShipmentProposed,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataShipmentVendor (state) {
    // type GroupedByUserId = {
    //   [key: string]: ShipmentVendor[];
    // };
    // const resItems: any = []
    // const groupedByShipmentID = state.dataShipmentVendor?.reduce<GroupedByUserId>((acc, item) => {
    //   if (!acc[item.shipment_id]) {
    //     acc[item.shipment_id] = []
    //   }
    //   item.is_disabled = true
    //   if (acc[item.shipment_id].length === 0) {
    //     item.is_disabled = false
    //   }
    //   acc[item.shipment_id].push(item)
    //   return acc
    // }, {})

    // for (const shipmentID in groupedByShipmentID) {
    //   groupedByShipmentID[shipmentID].sort((a, b) => {
    //     const aa = a.ritase_identity?.toString() ?? '0'
    //     const bb = b.ritase_identity?.toString() ?? '0'

    //     return aa.localeCompare(bb)
    //   })
    // }

    // Object.values(groupedByShipmentID ?? {}).forEach((value) => {
    //   value.forEach((val) => {
    //     resItems.push(val)
    //   })
    // })
    return {
      items: state.dataShipmentVendor,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataShipmentVendorProposed (state) {
    const shipmentVendor: any = []
    // this.dataShipmentProposed.items.filter((d: any) => d.status === 'PROPOSED' && d.status === 'DISPATCH' && d.status === 'ON_PROGRESS' && d.id === shipment.shipment_id)
    state.dataShipmentProposed?.map((item: any) => (
      // console.log('item', state.dataShipmentProposed)
      item.shipment_vendors.map((shipment: any) => (
        // const quantity += shipment.vehicles[0].pivot.quantity
        shipmentVendor.push({
          id: shipment.id,
          id_shipment: item.id,
          name: item.logistics_service_provider.name,
          identity: item.orders[0]?.identity,
          created_at: shipment.created_at,
          updated_at: shipment.updated_at,
          type: shipment.type,
          vehicle_type: shipment.vehicles.length,
          ritase_identity: shipment.ritase_identity === null ? 0 : shipment.ritase_identity,
          type_vehicle_count: item.type_vehicle_count,
          unit: shipment.vehicles.map((d: any) => parseInt(d.pivot.quantity, 10)).reduce((a: any, b: any) => a + b, 0),
          shipment_vendors: item.shipment_vendors,
          tracks: item.tracks,
          tracks_count: item.tracks_count,
          vehicle_detail_count: item.vehicle_detail_count,
          pkms_mill: item.orders[0]?.suborders[0]?.pickup_drop_off_location_point?.name,
          refenery: item.orders[0]?.suborders[1]?.pickup_drop_off_location_point?.name
        })
      ))
    ))
    return {
      items: shipmentVendor,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataShipmentDispatch (state) {
    return {
      items: state.dataShipmentDispatch,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  detailShipment (state) {
    return state.detailShipment
  },

  dataResponseAccept (state) {
    return state.responseAccept
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingShipmentVendor (state) {
    return state.isLoadingShipmentVendor
  },

  trackItem (state) {
    return state.trackItem
  },

  isLoadingTrack (state) {
    return state.isLoadingTrack
  },

  isLoadingTrackForm (state) {
    return state.isLoadingTrackForm
  },

  dialogDetailData (state) {
    return state.dialogItem
  },
  isLoadingDialog (state) {
    return state.isLoadingDialog
  },

  isLoadingShipmentLoadmore (state) {
    return state.isLoadingShipmentLoadmore
  },

  isLoadingDownload (state) {
    return state.isLoadingDownload
  },

  idManualLoadWeight (state) {
    return state.idManualLoadWeight
  }
}

export default getters

<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 pb-10"
  >
    <button-back :title="String($t('lspCreateShipment.back_to_order'))" class="mb-10" />

    <select-vendor-vehicle :calculation-order="calculationOrder" :order-number="checkedOrders[0]?.identity" />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'LogisticServiceProviderCreateOrderPage',

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  computed: {
    checkedOrders () {
      return this.$store.getters['order/checkedOrders']
    },

    calculationOrder () {
      return this.$store.getters['order/calculationOrder']
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')

    if (this.checkedOrders.length === 0) {
      this.$router.push(this.localePath('/logistic-service-provider/order-shipment/create-shipment'))
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

//* {
//  border: 1px solid;
//}
</style>
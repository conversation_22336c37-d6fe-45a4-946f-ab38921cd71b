import { ActionTree, GetterTree, MutationTree } from 'vuex'
import { Personalize } from '~/types/user'

export interface RootState {
  domain: string,
  personalization: Personalize | null,
}

export const state = () : RootState => ({
  domain: '',
  personalization: null
})

export const getters: GetterTree<RootState, RootState> = {
  domain: state => state.domain,
  personalization: state => state.personalization
}

export const mutations : MutationTree<RootState> = {
  SET_DOMAIN: (state, domain) => {
    state.domain = domain
  },

  SET_PERSONALIZATION: (state, personalization) => {
    state.personalization = personalization
  }
}

export const actions: ActionTree<RootState, RootState> = {
  async nuxtServerInit ({
    commit,
    dispatch
  }: any, context) {
    commit('SET_DOMAIN', context.req.headers.host)

    await dispatch('logistic-service-provider/personalize/getPersonalize', {
      domain: context.req.headers.host
    })

    const personalization = context.store.getters['logistic-service-provider/personalize/data'] as Personalize

    commit('SET_PERSONALIZATION', personalization)
  }
}

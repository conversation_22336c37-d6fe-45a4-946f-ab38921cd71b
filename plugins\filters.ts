import Vue from 'vue'

Vue.filter('toCurrency', function (value: number | null) {
  const formatter = new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    currencyDisplay: 'symbol',
    maximumFractionDigits: 2,
    useGrouping: true
  })

  if (value === null || value === undefined) {
    return formatter.format(0)
  }

  const formatted = formatter.format(value)
  return formatted.replace(/\s/g, '')
})

Vue.filter('toHighlight', function (words: string, query: string) {
  return words.replace(query, '<span class="text-highlight-plate">' + query + '</span>')
})

Vue.filter('toThousand', function (value: number) {
  return new Intl.NumberFormat(['ban', 'id']).format(value)
})

<template>
  <v-container fluid>
    <div v-if="isLoadingShipmentDetail || isLoadingPlayBack">
      <v-sheet v-if="$vuetify.breakpoint.xs" height="300">
        <v-skeleton-loader type="image" height="150" style="border-radius: 0 !important;" />
        <v-skeleton-loader type="image" height="150" style="border-radius: 0 !important;" />
      </v-sheet>
      <v-sheet v-if="!$vuetify.breakpoint.xs" height="600">
        <div v-for="i in 3" :key="i">
          <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
        </div>
      </v-sheet>
    </div>
    <v-row v-else class="d-flex flex-grow-1">
      <v-col>
        <v-responsive :aspect-ratio="setAspectRatio">
          <div class="d-flex justify-space-between" style="position: absolute; top: 0; left: 45px; z-index: 1000; width: 100%;">
            <div class="pa-1 mt-2">
              <div class="rounded white pa-1">
                <button-back title="Back to Detail" />
              </div>
            </div>
            <div class="white rounded pa-2 mt-2 mr-14">
              <v-col>
                <v-row class="d-flex pa-2 align-item-center">
                  <v-icon color="black" size="20">
                    mdi-account
                  </v-icon>
                  <p class="pa-0 subtitle-2 ma-0 mx-2">
                    {{ shipmentDetail.driver }}
                  </p>
                  <v-icon color="black" size="18">
                    mdi-circle
                  </v-icon>
                  <p class="pa-0 subtitle-2 ma-0 mx-2">
                    {{ shipmentDetail.plate_number }}
                  </p>
                </v-row>
                <v-divider class="my-4 pa-0" />
                <v-row class="d-flex pa-2 align-item-center justify-space-between">
                  <p class="pa-0 caption ma-0">
                    Mill
                  </p>
                  <p class="pa-0 caption ma-0">
                    Refinery
                  </p>
                </v-row>
                <v-divider class="my-4" />
                <v-row class="d-flex pa-2 align-item-center justify-space-between">
                  <v-col class="pa-0">
                    <p class="pa-0 subtitle-1 ma-0 mx-2 font-bold">
                      {{ shipmentDetail.mill_identity }}
                    </p>
                    <p class="caption pa-0 ma-0 mx-2">
                      {{ shipmentDetail.mill_name }}
                    </p>
                  </v-col>
                  <v-icon color="black" size="40">
                    mdi-arrow-right
                  </v-icon>
                  <v-col class="pa-0">
                    <p class="pa-0 subtitle-1 ma-0 mx-2 font-bold">
                      {{ shipmentDetail.refinery_identity }}
                    </p>
                    <p class="caption pa-0 ma-0 mx-2">
                      {{ shipmentDetail.refinery_name }}
                    </p>
                  </v-col>
                </v-row>
              </v-col>
            </div>
          </div>
          <div class="pa-0" style="height: 100%">
            <div v-if="dataPlayback && dataPlayback.length > 0" class="pa-0" style="height: 100%">
              <custom-map
                :latitude="centerLatLng?.lat"
                :longitude="centerLatLng?.lng"
                :zoom="zoomMap"
                :bounds="mapBounds"
              >
                <template #marker>
                  <div>
                    <v-rotated-marker
                      v-for="(marker, i) in markers"
                      :key="i"
                      :lat-lng="marker.latlng"
                      :rotation-angle="marker?.course"
                    >
                      <l-icon
                        :icon-size="[40, 40]"
                        :icon-url="require('~/assets/icons/arrow-up.svg')"
                        :color="marker.color"
                      />
                    </v-rotated-marker>
                    <l-marker
                      v-for="(marker, index) in poiData"
                      :key="index"
                      :lat-lng="[marker.lat, marker.long]"
                    >
                      <l-icon
                        :icon-anchor="[12, 12]"
                        :icon-size="[24, 24]"
                        :icon-url="marker.icon_url"
                      />
                      <l-popup>
                        <div>
                          <p class="ma-0 subtitle-2">
                            {{ marker.name }}
                          </p>
                          <v-divider class="my-2" />
                          <p class="ma-0 caption black--text">
                            {{ marker.description }}
                          </p>
                        </div>
                      </l-popup>
                    </l-marker>
                    <div v-if="carMarker">
                      <v-rotated-marker
                        v-for="(marker, i) in carMarker"
                        :key="i"
                        :lat-lng="marker.latlng"
                        :rotation-angle="marker?.course"
                      >
                        <l-icon
                          :icon-anchor="[32, 32]"
                          :icon-size="[64, 64]"
                          :icon-url="require('~/assets/icons/driver.svg')"
                        />
                      </v-rotated-marker>
                    </div>
                  </div>
                </template>
                <template #polyline>
                  <l-polyline
                    v-for="(segment, index) in polylineSegments"
                    :key="index"
                    :lat-lngs="segment.latlngs"
                    :color="segment.color"
                    :weight="5"
                    :opacity="1"
                  />
                </template>
              </custom-map>
              <div style="position: absolute; width: 100%; bottom: 0; z-index: 1000; background-color: rgba(255, 255, 255, 0.8);">
                <v-expansion-panels v-model="isPanelOpen" class="pa-1 mb-1">
                  <v-expansion-panel>
                    <v-expansion-panel-header>
                      <div v-if="!isPanelOpen" style="width: 100px; text-align: right;">
                        Show
                      </div>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <playback-control
                        :class="{ 'pointer-events-none': positions.length < 1 }"
                        :playback-data="playbackData"
                        :chart-data="chart"
                        :positions="positions.length"
                        @on-change-step="generateMarkers($event)"
                      />
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
            </div>
          </div>
        </v-responsive>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { LatLngBounds, LatLngTuple } from 'leaflet'
// import { positionDum } from './data'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import PlaybackControl from '~/components/playback/Control.vue'
import {
  defaultLat,
  defaultLng,
  generateCenterLatLng,
  zoom
} from '~/utils/functions'
import ButtonBack from '~/components/ButtonBack.vue'
import { Playback, PointOfInterest, Shipment } from '~/types/shipment'

interface Polyline {
  latlngs: object[],
  options: object[]
}

interface PolylineSegment {
  latlngs: LatLngTuple[]
  color: string
}

interface Marker {
  latlng: LatLngTuple
  color: any
  course: number
}

export default Vue.extend({
  name: 'PlayBackPage',

  components: { CustomMap, PlaybackControl, ButtonBack },

  props: {
    additionalMarkers: {
      type: Array,
      default: () => []
    }
  },

  data: () => ({
    polyline: null as Polyline | null,
    polylineSegments: [] as PolylineSegment[],
    markers: [] as Marker[],
    mapBounds: null as LatLngBounds | null,
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4,
    leaflet: null,
    positions: [] as Playback[],
    isLoading: false,
    playbackData: [] as {
      id: number
      lat: number
      lng: number
      course: number
      speed: number
      timestamp: string
    }[],
    chart: {
      labels: [] as string[],
      datasets: [{ label: 'Speed (km/h)', data: [] as number[] }]
    },
    carMarker: [] as Marker[],
    map: {
      markers: [],
      center: { lat: 0, lng: 0 }
    },
    markerGroup: null as any | null,
    isPanelOpen: false as boolean
  }),

  computed: {
    setAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 2.21
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    dataPlayback () : Playback[] {
      return this.$store.getters['playback/data']
    },
    poiData () : PointOfInterest[] {
      return this.$store.getters['playback/poiData']
    },
    isLoadingPlayBack () {
      return this.$store.getters['playback/isLoading']
    },
    shipmentDetail () {
      const items = this.$store.getters['shipment/detailShipment'] as Shipment | null
      const result = items?.tracks?.filter((t: any) => t.id === this.$route?.query?.trackid)
      return {
        driver: result?.[0]?.driver?.user.name,
        plate_number: result?.[0]?.vehicle_detail?.plate_number,
        mill_identity: items?.orders?.[0]?.suborders?.[0]?.pickup_drop_off_location_point.identity,
        mill_name: items?.orders?.[0]?.suborders?.[0]?.pickup_drop_off_location_point.name,
        refinery_identity: items?.orders?.[0]?.suborders?.[1]?.pickup_drop_off_location_point.identity,
        refinery_name: items?.orders?.[0]?.suborders?.[1]?.pickup_drop_off_location_point.name,
        outDateTimeMill: result?.[0]?.weight_bridges?.wb_date_out_pickup,
        outDateTimeRefinery: result?.[0]?.weight_bridges?.wb_date_out_dropoff
      }
    },
    isLoadingShipmentDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    }
  },

  watch: {
    dataPlayback (newVal) {
      if (newVal.length > 0) {
        this.fetchPosition()
      }
    }
  },

  mounted () {
    this.getDetailShipment()
  },

  methods: {
    generatePlaybackProps () {
      this.playbackData.splice(
        0,
        this.positions.length,
        ...this.positions.map(p => ({
          id: p.order,
          lat: p.latitude,
          lng: p.longitude,
          course: p.course,
          speed: p.speed * 1.852,
          timestamp: p.timestamp
        }))
      )
    },
    generateChartProps () {
      this.chart.labels = [...this.positions.map(p => this.$moment(p.timestamp).format('HH:mm:ss'))]
      this.chart.datasets[0].data = [...this.positions.map(p => p.speed * 1.852)] as number[]
    },
    generateMap () {
      const markers = this.positions.map((pos) => {
        return {
          lat: isNaN(pos.latitude) ? 0 : pos.latitude,
          lng: isNaN(pos.longitude) ? 0 : pos.longitude
        }
      })

      const speeds = this.positions.map((p: Playback) => p.speed)
      const minSpeed = Math.min(...speeds)
      const maxSpeed = Math.max(...speeds)
      this.positions.forEach((_, index) => {
        if (index < this.positions.length - 1) {
          const start = this.positions[index]
          const end = this.positions[index + 1]
          const segmentSpeed = (start.speed + end.speed) / 2
          const segmentColor = this.decToHex(segmentSpeed, minSpeed, maxSpeed)

          this.polylineSegments.push({
            latlngs: [
              [start.latitude, start.longitude],
              [end.latitude, end.longitude]
            ],
            color: segmentColor
          })
        }
      })

      let lastAddedIndex = -1
      const distanceThreshold = 10000
      this.positions.forEach((position, index) => {
        if (
          index === 0 ||
          index % 3 === 0 ||
          (index > 0 &&
            this.distanceBetween(this.positions[lastAddedIndex], position) > distanceThreshold)
        ) {
          const markerColor = this.decToHex(position.speed, minSpeed, maxSpeed)

          this.markers.push({
            latlng: [position.latitude, position.longitude],
            color: markerColor,
            course: position.course
          })
          lastAddedIndex = index
        }
      })
      this.centerLatLng = generateCenterLatLng(markers)
      this.zoomMap = zoom(markers)
    },
    distanceBetween (pos1: any, pos2: any) {
      const lat1 = pos1.latitude
      const lon1 = pos1.longitude
      const lat2 = pos2.latitude
      const lon2 = pos2.longitude

      const R = 6371e3
      const φ1 = (lat1 * Math.PI) / 180
      const φ2 = (lat2 * Math.PI) / 180
      const Δφ = ((lat2 - lat1) * Math.PI) / 180
      const Δλ = ((lon2 - lon1) * Math.PI) / 180

      const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

      return R * c
    },
    decToHex (value: any, min: any, max: any) {
      const normalized = (value - min) / (max - min)
      const red = Math.round(normalized * 255)
      const green = Math.round((1 - normalized) * 255)
      return `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}00`
    },
    fetchPosition () {
      this.positions.splice(0, this.positions.length, ...this.dataPlayback.reduce((result: any[], item, i, array) => {
        if (item.timestamp !== array[i - 1]?.timestamp) {
          result.push({ ...item })
        }
        return result
      }, []))

      this.generateChartProps()
      this.generatePlaybackProps()
      this.generateMap()
    },
    generateMarkers (index: number) {
      if (this.positions[index]) {
        const carMark = {
          speed: this.positions[index].speed,
          lat: this.positions[index].latitude,
          lng: this.positions[index].longitude,
          course: this.positions[index].course
        }

        const speeds = this.positions.map((p: Playback) => p.speed)
        const minSpeed = Math.min(...speeds)
        const maxSpeed = Math.max(...speeds)
        const color = this.decToHex(carMark.speed, minSpeed, maxSpeed)

        this.carMarker = [{
          latlng: [carMark.lat, carMark.lng],
          color,
          course: carMark.course
        }]
        this.centerLatLng = {
          lat: carMark.lat,
          lng: carMark.lng
        }
      }
    },
    async getDetailShipment () {
      const res = await this.$store.dispatch('shipment/getItemDetail', {
        id: this.$route.params.id
      })
      if (res) {
        this.getPlayback()
        this.getPointOfInterest()
        window.scrollTo(0, 0)
      }
    },
    getPlayback () {
      this.$store.dispatch('playback/getPlayback', {
        id: this.$route.query.trackid,
        fromDate: this.$moment(this.shipmentDetail.outDateTimeMill).format('YYYY-MM-DD'),
        fromTime: this.$moment(this.shipmentDetail.outDateTimeMill).format('HH:mm:ss'),
        toDate: this.$moment(this.shipmentDetail.outDateTimeRefinery).format('YYYY-MM-DD'),
        toTime: this.$moment(this.shipmentDetail.outDateTimeRefinery).format('HH:mm:ss')
      })
    },
    getPointOfInterest () {
      this.$store.dispatch('playback/getPointOfInterest')
    }

  }
})
</script>

<style scoped>
.pointer-events-none {
  pointer-events: none;
}
</style>

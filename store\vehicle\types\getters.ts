import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleTypeState } from './state'

export const getters: GetterTree<VehicleTypeState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingDelete (state) {
    return state.isLoadingDelete
  }
}

export default getters

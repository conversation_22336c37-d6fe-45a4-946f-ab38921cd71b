<template>
  <v-container fluid class="pa-0">
    <v-container
      fluid
      class="mb-10 pa-0 d-flex justify-space-between align-center"
    >
      <h3 v-if="vehicle">
        {{ $t('vehicleForm.update_vehicle') }}
      </h3>

      <h3 v-else>
        {{ $t('vehicleForm.add_vehicle') }}
      </h3>

      <v-icon
        color="secondary"
        @click="$emit('on-click-close')"
      >
        mdi-close
      </v-icon>
    </v-container>

    <v-form v-model="isValid" ref="form">
      <image-select
        :avatar-url="vehicle?.photo_url"
        :clear-image="dialog"
        :is-loading-form-clear-image="isLoadingDelete"
        @on-image-selected="onSelectPhoto($event)"
        @on-clear-image="removeImage"
      />

      <v-text-field
        v-model="form.name"
        flat
        outlined
        clearable
        persistent-hint
        :label="$t('vehicleForm.label_vehicle_name')"
        :hint="$t('vehicleForm.hint_vehicle_name')"
        :rules="[ruleRequired]"
        class="mt-10 mb-6"
      />

      <v-autocomplete
        v-model="form.vehicleTypeId"
        outlined
        clearable
        hide-details
        :label="$t('vehicleForm.label_vehicle_type')"
        :menu-props="{ closeOnContentClick: true, contentClass: 'custom-dropdown' }"
        :items="dataVehicleTypes.map(vt => {
          return {
            text: vt.name,
            value: vt.id
          }
        })"
        :rules="[ruleRequired]"
      >
        <template #append-item>
          <v-container fluid class="pa-2 white elevation-4" style="position: sticky; bottom: 0">
            <slot name="tab-activator" />
          </v-container>
        </template>
      </v-autocomplete>

      <p class="subtitle-1 my-6">
        {{ $t('vehicleForm.specification') }}
      </p>

      <v-combobox
        :value="form.featureIds"
        multiple
        outlined
        clearable
        hide-details
        :label="$t('vehicleForm.label_features')"
        :loading="isLoadingFormVehicleFeature"
        :items="dataVehicleFeatures.map(vf => {
          return {
            text: vf.name,
            value: vf.id
          }
        })"
        :rules="[ruleRequired]"
        class="mb-6"
        @change="onChangeVehicleFeature($event)"
      />

      <v-row class="ma-0 mx-n3">
        <v-col class="pa-3">
          <v-text-field
            v-model="form.length"
            type="number"
            hide-spin-buttons
            outlined
            clearable
            :label="$t('vehicleForm.label_length')"
            hint="CM"
            persistent-hint
            :rules="[ruleRequired]"
            @input="calculateVolume"
          />
        </v-col>

        <v-col class="pa-3">
          <v-text-field
            v-model="form.width"
            type="number"
            hide-spin-buttons
            outlined
            clearable
            :label="$t('vehicleForm.label_width')"
            hint="CM"
            persistent-hint
            :rules="[ruleRequired]"
            @input="calculateVolume"
          />
        </v-col>

        <v-col class="pa-3">
          <v-text-field
            v-model="form.height"
            type="number"
            hide-spin-buttons
            outlined
            clearable
            :label="$t('vehicleForm.label_height')"
            hint="CM"
            persistent-hint
            :rules="[ruleRequired]"
            @input="calculateVolume"
          />
        </v-col>
      </v-row>

      <v-row class="ma-0 mx-n3">
        <v-col class="pa-3">
          <v-text-field
            :value="form.maxVolume"
            type="number"
            hide-spin-buttons
            readonly
            outlined
            :label="$t('vehicleForm.label_volume')"
            hint="CBM"
            persistent-hint
            :rules="[ruleRequired]"
          />
        </v-col>

        <v-col class="pa-3">
          <v-text-field
            v-model="form.maxWeight"
            type="number"
            hide-spin-buttons
            outlined
            clearable
            :label="$t('vehicleForm.label_weight')"
            hint="KG"
            persistent-hint
            :rules="[ruleRequired]"
          />
        </v-col>
      </v-row>
    </v-form>

    <v-row class="mx-n3 mb-n3 mt-1">
      <v-col class="pa-3">
        <v-btn
          v-if="vehicle"
          block
          x-large
          elevation="0"
          color="primary"
          class="text-capitalize"
          :loading="isLoadingFormVehicle"
          @click="updateVehicle"
        >
          <p class="subtitle-1 ma-0">
            {{ $t('vehicleForm.button_update') }}
          </p>
        </v-btn>

        <v-btn
          v-else
          block
          x-large
          elevation="0"
          color="primary"
          class="text-capitalize"
          :loading="isLoadingFormVehicle"
          :disabled="!isValid"
          @click="createVehicle"
        >
          <p class="subtitle-1 ma-0">
            {{ $t('vehicleForm.button_add') }}
          </p>
        </v-btn>
      </v-col>
      <v-col class="pa-3">
        <v-btn
          block
          x-large
          outlined
          color="primary"
          class="text-capitalize"
          @click="$emit('on-click-close')"
        >
          <p class="subtitle-1 ma-0">
            {{ $t('vehicleForm.button_cancel') }}
          </p>
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageSelect from '~/components/ImageSelect.vue'
import { Vehicle, VehicleFeature, VehicleType } from '~/types/vehicle'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'VehicleForm',

  components: { ImageSelect },

  props: {
    vehicle: {
      type: Object as () => Vehicle | null,
      default: null
    },
    vendorId: {
      type: String,
      default: ''
    },
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isValid: false as boolean,
    form: {
      id: '' as string,
      name: '' as string,
      photo: null as object | null,
      type: '' as string,
      length: '' as string,
      width: '' as string,
      height: '' as string,
      maxVolume: '' as string,
      maxWeight: '' as string,
      vehicleTypeId: '' as string,
      featureIds: [] as object[]
    },
    clearForm: false as boolean
  }),

  computed: {
    dataVehicleTypes (): VehicleType[] {
      return this.$store.getters['vehicle/types/data'].items
    },

    dataVehicleFeatures (): VehicleFeature[] {
      return this.$store.getters['vehicle/features/data'].items
    },

    isLoadingFormVehicle (): Boolean {
      return this.$store.getters['vehicle/isLoadingForm']
    },

    isLoadingDelete (): Boolean {
      return this.$store.getters['vehicle/isLoadingDelete']
    },

    isLoadingFormVehicleFeature (): Boolean {
      return this.$store.getters['vehicle/features/isLoadingForm']
    },
    entriesMode (): any {
      return this.$store.getters['layout/entriesMode']
    }
  },

  watch: {
    dialog (currentValue: boolean) {
      const form = this.$refs.form as HTMLFormElement

      if (currentValue && !this.vehicle) {
        form.reset()
      }
    }
  },
  mounted () {
    if (this.vehicle) {
      const vehicle = this.vehicle as Vehicle

      this.form.id = vehicle.id
      this.form.name = vehicle.name
      this.form.vehicleTypeId = vehicle.vehicle_type_id
      this.form.featureIds = vehicle.vehicle_features.map((item) => {
        return { text: item.name, value: item.id }
      })
      this.form.length = vehicle.length
      this.form.width = vehicle.width
      this.form.height = vehicle.height
      this.form.maxVolume = vehicle.max_volume
      this.form.maxWeight = vehicle.max_weight
    }
  },

  methods: {
    ruleRequired (value: string | string[]) {
      return rules.required(value)
    },

    calculateVolume () {
      const length = parseInt(this.form.length)
      const width = parseInt(this.form.width)
      const height = parseInt(this.form.height)

      if (length && width && height) {
        this.form.maxVolume = ((length * width * height) / 1000000).toFixed(2)
      } else {
        this.form.maxVolume = ''
      }
    },

    onSelectPhoto (photo: object) {
      this.form.photo = photo
    },

    async createVehicle () {
      const response = await this.$store.dispatch('vehicle/createItem', {
        value: this.form,
        vendorId: this.vendorId,
        entries: this.entriesMode
      })

      if (response) {
        this.$emit('on-click-close')
        this.$emit('on-success-create')
      }
    },

    async updateVehicle () {
      const response = await this.$store.dispatch('vehicle/editItem', {
        value: this.form,
        vendorId: this.vendorId,
        entries: this.entriesMode
      })

      if (response) {
        this.$emit('on-click-close')
        this.$emit('on-success-update')
      }
    },

    async removeImage () {
      const response = await this.$store.dispatch('vehicle/removeImage', {
        vehicleId: this.vehicle?.id
      })

      if (response) {
        this.$emit('on-success-remove-image')
      }
    },

    async createVehicleFeature (name: string) {
      await this.$store.dispatch('vehicle/features/createItem', { name })
    },

    onChangeVehicleFeature (items: any[]) {
      const lastItem = items.slice(-1)[0]

      if (typeof lastItem === 'string') {
        this.form.featureIds.pop()
        this.createVehicleFeature(lastItem)
      } else {
        this.form.featureIds = items
      }
    }
  }
})
</script>

<style scoped>
.custom-dropdown .v-list {
  padding-bottom: 0;
}
</style>

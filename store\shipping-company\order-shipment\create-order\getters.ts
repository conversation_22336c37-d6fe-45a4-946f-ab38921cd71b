import { GetterTree } from 'vuex'
import { RootState } from '../../../index'
import { ShipmentCompanyCreateOrderState } from './state'

export const getters: GetterTree<ShipmentCompanyCreateOrderState, RootState> = {
  dataDraft (state) {
    return {
      itemDraft: state.itemDraft
    }
  },
  dataPublished (state) {
    return {
      itemPublished: state.itemPublished
    }
  },
  isLoading (state) {
    return state.isLoading
  },
  isLoadingForm (state) {
    return state.isLoadingForm
  },
  isLoadingDetail (state) {
    return state.isLoadingDetail
  },
  errorMessage (state) {
    return state.errorMessage
  },
  newOrderId (state) {
    return state.newOrderId
  }
}

export default getters

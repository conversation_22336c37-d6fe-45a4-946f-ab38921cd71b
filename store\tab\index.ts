import { ActionTree, GetterTree, MutationTree } from 'vuex'
import { RootState } from '../index'
export interface TabState {
  index: number
}
export const state = (): TabState => ({
  index: 0
})

export const getters: GetterTree<TabState, RootState > = {
  index (state) {
    return state.index
  }
}

export const mutations: MutationTree<TabState> = {
  SET_TAB_INDEX (state, index: number) {
    state.index = index
  }
}

export const actions: ActionTree<TabState, RootState> = {
  changeTab ({ commit }, payload: number) {
    commit('SET_TAB_INDEX', payload)
  }
}

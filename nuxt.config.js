export default {
  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    titleTemplate: '%s - LSI',
    title: 'Logistic Service Integrator',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: 'Efficiency your Shipment Every Time With Logistic Service Integrator.' },
      { name: 'format-detection', content: 'telephone=no' }
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      {
        rel: 'stylesheet',
        href: 'https://unpkg.com/leaflet@1.8.0/dist/leaflet.css',
        integrity:
          'sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ==',
        crossorigin: ''
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.googleapis.com'
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        crossorigin: ''
      },
      {
        href: 'https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700&display=swap',
        rel: 'stylesheet'
      }
    ]
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    '@/assets/scss/styles.scss',
    '@/assets/scss/font_styles.scss'
  ],

  ssr: false,

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    { src: '~/plugins/axios', ssr: false },
    { src: '~/plugins/vue-time-picker.ts', ssr: false },
    { src: '~/plugins/leaflet.ts', ssr: false, mode: 'client' },
    { src: '~/plugins/filters.ts', ssr: true },
    { src: '~/plugins/chart.ts', ssr: false },
    { src: '~/plugins/leaflet.client.ts', mode: 'client' },
    { src: '~/plugins/recaptcha-alt.ts', ssr: false, mode: 'client' }
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/typescript
    '@nuxt/typescript-build',
    // https://go.nuxtjs.dev/vuetify
    '@nuxtjs/vuetify',
    '@nuxtjs/device'
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',
    '@nuxtjs/auth-next',
    // https://go.nuxtjs.dev/pwa
    '@nuxtjs/pwa',
    'vue-toastification/nuxt',
    'nuxt-leaflet',
    '@nuxtjs/moment',
    'nuxt-i18n',
    [
      '@nuxtjs/firebase',
      {
        config: {
          apiKey: 'AIzaSyDzoAVviLhZa4kwgjXrmDltChuBlGu2Js0',
          authDomain: 'logistic-service-integrator.firebaseapp.com',
          databaseURL: 'https://logistic-service-integrator-default-rtdb.asia-southeast1.firebasedatabase.app',
          projectId: 'logistic-service-integrator',
          storageBucket: 'logistic-service-integrator.appspot.com',
          messagingSenderId: '814493655222',
          appId: '1:814493655222:web:50eff23c9a5c489f77c617',
          measurementId: 'G-3BEFCGNNQ8'
        },
        services: {
          database: true,
          messaging: {
            createServiceWorker: true,
            fcmPublicVapidKey: 'BA5gNBi55_piMRUifMq6cREvX1_zf6LGDEUh7UwWQqWlFbzmk-nLCLBwYlimxGyfN0QySmUwwtcf94B9eA62YuI'
          }
        }
      }
    ],
  ],

  moment: {
    timezone: true
  },

  toast: {
    position: 'top-right',
    duration: 2500
  },

  i18n: {
    lazy: true,
    langDir: 'locales/',
    locales: [
      {
        name: 'English',
        code: 'en',
        iso: 'en',
        file: 'en'
      },
      {
        name: 'Indonesia',
        code: 'id',
        iso: 'id',
        file: 'id'
      }
    ],
    defaultLocale: 'en'
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {
    // Workaround to avoid enforcing hard-coded localhost:3000: https://github.com/nuxt-community/axios-module/issues/308
    baseURL: process.env.BACKEND_URL
  },

  publicRuntimeConfig: {
    axios: {
      browserBaseURL: process.env.BACKEND_URL
    },
    appEnv: process.env.APP_ENV,
    vapidKey: process.env.VAPID_KEY,
    appHost: process.env.APP_HOST,
    recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY
  },

  privateRuntimeConfig: {
    axios: {
      baseURL: process.env.BACKEND_URL
    },
    appEnv: process.env.APP_ENV,
    vapidKey: process.env.VAPID_KEY,
    appHost: process.env.APP_HOST,
    recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY,
    recaptchaSecretKey: process.env.RECAPTCHA_SECRET_KEY
  },

  loading: {
    color: '#EF3434',
    height: '2px',
    throttle: 100
  },

  auth: {
    plugins: ['~/plugins/auth.ts'],
    redirect: {
      login: '/login',
      logout: '/login',
      callback: '/login',
      home: '/login'
    },
    strategies: {
      local: {
        token: {
          required: false,
          type: 'Bearer',
          maxAge: 24 * 60 * 60
        },
        user: {
          property: false,
          autoFetch: false
        },
        endpoints: {
          login: { url: '/v1/auth/login', method: 'post' },
          logout: { url: '/v1/auth/logout', method: 'post' },
          user: { url: '/v1/auth/profile', method: 'get' }
        },
        autoLogout: true
      }
    }
  },

  // PWA module configuration: https://go.nuxtjs.dev/pwa
  pwa: {
    manifest: {
      lang: 'en'
    }
  },

  // Vuetify module configuration: https://go.nuxtjs.dev/config-vuetify
  vuetify: {
    customVariables: ['~/assets/scss/variables.scss'],
    theme: {
      dark: false,
      options: { customProperties: true },
      themes: {
        dark: {
          primary: '#EF3434',
          info: '#0094BC',
          warning: '#F0CD4D',
          error: '#EF3434',
          success: '#2FA841'
        },
        light: {
          primary: '#EF3434',
          info: '#0094BC',
          warning: '#F0CD4D',
          error: '#EF3434',
          success: '#2FA841'
        }
      }
    },
    treeShake: true,
    defaultAssets: {
      font: {
        family: 'Poppins'
      }
    }
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    transpile: [
      'defu'
    ]
  }
}

<template>
  <v-dialog
    v-model="dialog"
    max-width="600"
    persistent
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex justify-space-between align-center">
        <h3>Update Vehicle</h3>
        <v-icon @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </v-container>

      <p class="mb-6 body-1 text-secondary">
        Assigned vehicles can be updated before delivery is executed
      </p>

      <v-container
        v-for="(track, i) in tracks"
        :key="track.id"
        fluid
        class="pa-0 mb-6"
      >
        <p class="subtitle-1 mb-4">
          {{ track.vehicle_detail?.vehicle?.name }}
        </p>

        <v-row v-if="dialog" class="ma-n3 d-flex">
          <v-col class="pa-3">
            <v-combobox
              v-model="form[i].vehicle"
              outlined
              :items="
                vehicleDetails.filter((vd) => vd.vehicle_id === track.vehicle_detail?.vehicle_id)
                  .map(vd => {
                    return { text: vd.plate_number, value: vd.id }
                  })
              "
              label="Vehicle"
              hide-details
            />
          </v-col>

          <v-col class="pa-3">
            <v-combobox
              v-model="form[i].driver"
              outlined
              :items="
                drivers.map(d => {
                  return { text: d.user?.name, value: d.id }
                })
              "
              label="Driver"
              hide-details
            />
          </v-col>
        </v-row>
      </v-container>

      <v-row class="mt-10 px-3">
        <v-btn
          :loading="isLoadingForm"
          x-large
          elevation="0"
          color="primary"
          class="mr-6 text-capitalize col-md-4 col-12"
          @click="$emit('on-click-update', form)"
        >
          <p class="ma-0 subtitle-1">
            Update Vehicle
          </p>
        </v-btn>

        <v-btn
          x-large
          elevation="0"
          outlined
          color="primary"
          class="text-capitalize col-md-4 col-12 mt-md-0 mt-5"
          @click="$emit('on-close-dialog')"
        >
          <p class="ma-0 subtitle-1">
            Cancel
          </p>
        </v-btn>
      </v-row>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Track } from '~/types/shipment'
import { VehicleDetail } from '~/types/vehicle'
import { Driver } from '~/types/driver'

interface TrackForm {
  id: string,
  vehicle: {
    value: string,
    text: string | undefined
  },
  driver: {
    value: string,
    text: string | undefined
  }
}

export default Vue.extend({
  name: 'UpdateTrackDialog',

  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    tracks: {
      type: Array as () => Track[],
      default: () => []
    },
    vehicleDetails: {
      type: Array as () => VehicleDetail[],
      default: () => []
    },
    drivers: {
      type: Array as () => Driver[],
      default: () => []
    },
    isLoadingVehicleDetail: {
      type: Boolean,
      default: true
    },
    isLoadingDriver: {
      type: Boolean,
      default: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    form: [] as TrackForm[]
  }),

  watch: {
    dialog () {
      if (this.dialog) {
        this.tracks?.forEach((track, index) => {
          this.form[index] = {
            id: track.id,
            vehicle: {
              text: track.vehicle_detail?.plate_number,
              value: track.vehicle_detail_id
            },
            driver: {
              text: track.driver?.user?.name,
              value: track.driver_id
            }
          }
        })
      }
    }
  }
})
</script>

<style scoped lang="scss"></style>

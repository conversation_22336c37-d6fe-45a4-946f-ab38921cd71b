import { MutationTree } from 'vuex'
import { ShipmentCompanyProductState } from './state'
import { ImportKey } from '~/types/import-key'

export const mutations: MutationTree<ShipmentCompanyProductState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },
  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },
  SET_PAGE (state, page: any) {
    state.page = page
  },
  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },
  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  },
  SET_IS_LOADING_FORM_CLEAR_IMAGE (state, isLoadingFormClearImage) {
    state.isLoadingFormClearImage = isLoadingFormClearImage
  },
  SET_ITEMS_PRODUCT_KEY (state, items: ImportKey[]) {
    state.itemsProductKey = items
  }
}

export default mutations

import Vue from 'vue'

declare global {
  interface Window {
    grecaptcha: any
    onRecaptchaLoaded: () => void
  }
}

export default function ({ app, $config }: any) {
  // Get reCAPTCHA site key from environment variables via runtime config
  const siteKey = $config.recaptchaSiteKey

  if (!siteKey) {
    console.warn('reCAPTCHA site key is not defined in environment variables')
    return
  }

  // Add recaptcha script to head
  const recaptchaScript = document.createElement('script')
  recaptchaScript.setAttribute('async', '')
  recaptchaScript.setAttribute('defer', '')
  recaptchaScript.setAttribute('src', `https://www.google.com/recaptcha/api.js?render=${siteKey}`)
  document.head.appendChild(recaptchaScript)

  // Create a global recaptcha instance
  Vue.prototype.$recaptcha = {
    execute: (action: string) => {
      return new Promise((resolve, reject) => {
        try {
          if (window.grecaptcha && window.grecaptcha.ready) {
            window.grecaptcha.ready(() => {
              window.grecaptcha.execute(siteKey, { action })
                .then((token: string) => {
                  resolve(token)
                })
                .catch((error: Error) => {
                  console.error('reCAPTCHA execution error:', error)
                  reject(error)
                })
            })
          } else {
            console.warn('grecaptcha not available yet')
            resolve('')
          }
        } catch (error) {
          console.error('reCAPTCHA error:', error)
          reject(error)
        }
      })
    }
  }
}

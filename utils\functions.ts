import { toastError, toastSuccess } from './toasts'
import { Track, TrackActiveOrder } from '~/types/shipment'
import { read, utils } from 'xlsx'

export const defaultLat = -0.954599
export const defaultLng = 119.790885

export function mergeDateTime (dateSelected: any, timeSelected: any) {
  const [year, month, day] = dateSelected.split('-')
  const [hours, minutes] = timeSelected.split(':')

  return new Date(year, month - 1, day, hours, minutes)
}

export function formatDateTime (dateTime: Date) {
  const year = dateTime.getFullYear()
  const month = String(dateTime.getMonth() + 1).padStart(2, '0')
  const day = String(dateTime.getDate()).padStart(2, '0')
  const hours = String(dateTime.getHours()).padStart(2, '0')
  const minutes = String(dateTime.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

export function generateDriverMarker (polylineFms: any[]) {
  const marker = [] as object[]

  if (polylineFms) {
    polylineFms.forEach((line) => {
      const lastLatLng = line.latLng.slice(-1)[0]

      if (lastLatLng) {
        marker.push({
          id: line.id,
          latLng: [parseFloat(lastLatLng[0]), parseFloat(lastLatLng[1])]
        })
      }
    })
  }

  return marker
}

export function formatNumber (value: number | null | undefined): string {
  if (value !== null && value !== undefined) {
    return value.toLocaleString()
  }
  return ''
}

export function generatePolyline (tracks: Track[]) {
  const polyline = {
    mapBox: [] as object[],
    fms: [] as object[]
  }

  tracks?.forEach((track) => {
    if (track?.directions) {
      polyline.mapBox.push({
        id: track?.id,
        latLng: track.directions
          .filter(direction => direction?.type === 'MAPBOX')
          .map((d) => {
            return [d.latitude, d.longitude]
          })
      })

      polyline.fms.push({
        id: track?.id,
        latLng: track.directions
          .filter(direction => direction?.type === 'FMS')
          .map((d) => {
            return [d.latitude, d.longitude]
          })
      })
    }
  })

  return polyline
}

export function generatePolylineActive (tracks: TrackActiveOrder[]) {
  const polyline = {
    mapBox: [] as object[],
    fms: [] as object[]
  }

  tracks?.forEach((track) => {
    if (track?.destinations) {
      polyline.mapBox.push({
        id: track?.id,
        latLng: track.destinations
          .filter(direction => direction?.type === 'MAPBOX')
          .map((d) => {
            return [d.lat, d.lng]
          })
      })

      polyline.fms.push({
        id: track?.id,
        latLng: track.destinations
          .filter(direction => direction?.type === 'FMS')
          .map((d) => {
            return [d.lat, d.lng]
          })
      })
    }
  })

  return polyline
}

export function colorType (type: string): string {
  if (type === 'PICKUP') {
    return 'bg-info-color text-info'
  } else if (type === 'DROPOFF') {
    return 'bg-success-color text-success'
  } else {
    return 'warning'
  }
}

export function getNewStatus (readAt: string): string {
  const fifteenMinutesInMs = 15 * 60 * 1000
  const isRead = readAt !== null
  const readTimestamp = isRead ? new Date(readAt.replace(' ', 'T') + 'Z').getTime() : null

  if (!isRead) {
    return 'new'
  } else {
    const currentTime = new Date().getTime()
    if (readTimestamp !== null && currentTime - readTimestamp > fifteenMinutesInMs) {
      return ''
    } else {
      return 'new'
    }
  }
}

export function checkIsNew (date: string): string {
  const fifteenMinutesInMs = 15 * 60 * 1000
  const isRead = date !== null
  const readTimestamp = isRead ? new Date(date).getTime() : null

  if (!isRead) {
    return 'new'
  } else {
    const currentTime = new Date().getTime()
    if (readTimestamp !== null && currentTime - readTimestamp > fifteenMinutesInMs) {
      return ''
    } else {
      return 'new'
    }
  }
}

export function styleContainerCollaboration (status: string | null) {
  if (status === 'COLLABORATE') {
    return 'color: #2FA841; background-color: #EAF6EC; border-radius: 5px 5px'
  } else if (status === 'BLOCK') {
    return 'color: #EF3434; background-color: #FDE0E0; border-radius: 5px 5px'
  } else if (status === 'REJECTED') {
    return 'color: #EF3434; background-color: #FDE0E0; border-radius: 5px 5px'
  } else if (status === 'PROPOSED') {
    return 'color: #ef9834; background-color: #fff5eb; border-radius: 5px 5px'
  }

  return ''
}

export function toastNetworkSuccess (context: any) {
  toastSuccess('Connected', context)
}

export function exceptionHandler (error: any, context: any) {
  try {
    if (error.message === 'Network Error') {
      // toastError('Network Error, Retrying', context)

      const initialDispatch = context.dispatch

      Object.defineProperty(context, 'dispatch', {
        value (name: string, payload: any) {
          context.commit('network-error/LAST_DISPATCHED_ACTION', {
            name,
            payload
          })
          return initialDispatch(...arguments)
        }
      })
    } else {
      if (error.response?.data?.message === 'Unauthorized') {
        return
      }

      if(context && error.response?.data?.message) {
        toastError(error.response?.data?.message, context)
      }
    }
  } catch (e) {
    console.error(e)
  }
}

export function generateCenterLatLng (
  markers: { lat: number, lng: number }[]
) {
  const reformattedLatitude = [] as number[]
  const reformattedLongitude = [] as number[]

  markers.forEach((marker: { lat: number, lng: number }) => {
    reformattedLatitude.push(marker.lat)
    reformattedLongitude.push(marker.lng)
  })

  return {
    lat: (Math.min(...reformattedLatitude) + Math.max(...reformattedLatitude)) / 2,
    lng: (Math.min(...reformattedLongitude) + Math.max(...reformattedLongitude)) / 2
  }
}

export function zoom (markers: { lat: number; lng: number }[]) {
  const lat =
    markers.reduce((a, b) => a + b.lat, 0) / markers.length
  const lng =
    markers.reduce((a, b) => a + b.lng, 0) / markers.length

  const latDiff =
    markers.reduce((a, b) => a + Math.abs(b.lat - lat), 0) /
    markers.length
  const lngDiff =
    markers.reduce((a, b) => a + Math.abs(b.lng - lng), 0) /
    markers.length

  const latZoom = Math.round(Math.log(360 / latDiff) / Math.LN2)
  const lngZoom = Math.round(Math.log(360 / lngDiff) / Math.LN2)

  return Math.min(latZoom, lngZoom, 15)
}

export function zoomBetweenTwoPoints (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
) {
  const latDiff = Math.abs(lat1 - lat2)
  const lngDiff = Math.abs(lng1 - lng2)

  const latZoom = Math.round(Math.log(360 / latDiff) / Math.LN2)
  const lngZoom = Math.round(Math.log(360 / lngDiff) / Math.LN2)

  return Math.min(latZoom, lngZoom, 15)
}

export function haversineCalculation (
  p1: { lat: number, lng: number },
  p2: { lat: number, lng: number }
): number {
  const r = 6371e3 as number

  const x1 = p1.lat * Math.PI / 180 as number
  const x2 = p2.lat * Math.PI / 180 as number

  const dx = Math.abs(p1.lat - p2.lat) * Math.PI / 180 as number
  const dy = Math.abs(p1.lng - p2.lng) * Math.PI / 180 as number

  const a =
    Math.pow(Math.sin(dx / 2), 2) +
    Math.cos(x1) * Math.cos(x2) * Math.pow(Math.sin(dy / 2), 2) as number

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)) as number

  return r * c
}

export async function sheetToJson (file: Blob): Promise<any[]> {
  const ab = await file.arrayBuffer()
  const wb = read(ab)
  const ws = wb.Sheets[wb.SheetNames[0]]
  return utils.sheet_to_json(ws)
}

export const rules = {
  required: (value: string | string[]) => {
    return (Array.isArray(value)
      ? value.length > 0
      : !!value) || 'Required.'
  },
  email: (value: string) => {
    const pattern =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return pattern.test(value) || 'Invalid e-mail.'
  },
  phoneMaxDigit: (value: string) => {
    const pattern = /^\d{0,15}$/
    return pattern.test(value) || 'Max 15 digit.'
  },
  domain: (value: string) => {
    const pattern = /^(?:[a-z0-9](?:[a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/

    return pattern.test(value) || 'Invalid domain.'
  },
  firstZeroPhoneNumber: (value: string) => {
    const pattern = /^0/
    return !pattern.test(value) || 'First digit cannot be 0.'
  },
  passwordMinLength: (value: string) => {
    const pattern = /^.{8,}$/
    return pattern.test(value) || 'Min 8 characters.'
  },
  passwordConfirm: (value: string, password: string) => {
    return value === password || 'Password not match.'
  },
  fileType: (file: Blob, type: string) => {
    return file?.type === type || 'File type not allowed'
  }
}

export function isPasswordExpired (lastUpdatedPassword: string | number | Date) {
  const passwordUpdateTime = new Date(lastUpdatedPassword).getTime()
  const currentTime = new Date().getTime()
  const timeDifferenceInDays = (currentTime - passwordUpdateTime) / (1000 * 3600 * 24)

  return timeDifferenceInDays >= 30
}

export function saveDateRange (dateRange: Date[] | Date) {
  let startDate: any | null = null
  let endDate: any | null = null

  if (dateRange instanceof Array) {
    if (dateRange.length > 1) {
      startDate = new Date(Math.min(...dateRange.map(date => new Date(date).getTime())))
      endDate = new Date(Math.max(...dateRange.map(date => new Date(date).getTime())))
    } else if (dateRange.length === 1) {
      startDate = new Date(dateRange[0])
    }
  } else {
    startDate = new Date(dateRange)
  }

  return {
    column: 'created_at',
    start: startDate,
    end: endDate
  }
}

export function getRandomColor () {
  return '#' + Math.floor(Math.random() * 16777215).toString(16)
}

export function formatToIDR (value: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    useGrouping: true
  }).format(value)
}

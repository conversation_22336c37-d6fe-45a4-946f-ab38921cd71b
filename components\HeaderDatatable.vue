<template>
  <v-container fluid class="pa-0 d-flex mb-10 justify-space-between flex-md-row flex-column">
    <div class="pa-0 d-flex flex-md-row flex-column">
      <div class="d-flex mr-md-5">
        <v-text-field
          v-model="searchKey"
          flat
          :label="$t('general.search')"
          append-icon="mdi-magnify"
          hide-details
          single-line
          solo
          height="52"
          background-color="white"
          @click:append="$emit('on-search-icon-click', searchKey)"
          @keydown.enter="$emit('on-search-icon-click', searchKey)"
        />

        <filter-menu
          v-if="defaultSortColumn"
          :default-sort-column="defaultSortColumn"
          :default-sort-type="defaultSortType"
          :sort-column-items="sortColumnItems"
          :sort-type-items="sortTypeItems"
          :default-filter-column="defaultFilterColumn"
          :default-filter-type="defaultFilterType"
          :filter-column-items="filterColumnItems"
          :filter-type-items="filterTypeItems"
          :sort-column-id="sortColumnId"
          :sort-type-id="sortTypeId"
          :filter-column-id="filterColumnId"
          :filter-type-id="filterTypeId"
          :is-sort-by="isSortBy"
          @on-filter-change="onFilterChange"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              color="white"
              elevation="0"
              large
              class="ml-5 text-capitalize custom-btn"
              x-large
              v-on="on"
              @click="onFilterTriggered"
            >
              <v-icon
                class="mr-3 custom-icon"
              >
                mdi-filter
              </v-icon>
              Filter
            </v-btn>
          </template>

          <template #filter-user>
            <slot name="filter-user" />
          </template>
        </filter-menu>

        <slot name="export-button" />
      </div>

      <div class="d-flex mt-5 mt-md-0">
        <slot name="data-button" />

        <slot name="toggle-button" />
      </div>
    </div>

    <slot name="button" />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'HeaderDatatable',

  props: {
    value: {
      type: String,
      default: null
    },
    defaultSortColumn: {
      type: String,
      default: null
    },

    defaultSortType: {
      type: String,
      default: null
    },

    sortColumnItems: {
      type: Object,
      default: null
    },

    sortTypeItems: {
      type: Object,
      default: null
    },

    defaultFilterColumn: {
      type: String || null,
      default: null
    },

    defaultFilterType: {
      type: String || null,
      default: null
    },

    filterColumnItems: {
      type: Object || null,
      default: null
    },

    filterTypeItems: {
      type: Object || null,
      default: null
    },

    sortColumnId: {
      type: String || null,
      default: null
    },

    sortTypeId: {
      type: String || null,
      default: null
    },

    filterColumnId: {
      type: String || null,
      default: null
    },

    filterTypeId: {
      type: String || null,
      default: null
    },

    isSortBy: {
      type: Boolean,
      default: true
    }
  },

  data: () => ({
    isShowDeleteDialog: false,
    searchKey: ''
  }),

  mounted () {
    this.searchKey = this.value
  },

  methods: {
    onFilterChange (value: any) {
      this.$emit('on-filter-change', value)
    },
    onFilterTriggered() {
      this.$emit('on-filter-triggered')
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: var(--v-primary-base) !important;
  color: white !important;
}

</style>

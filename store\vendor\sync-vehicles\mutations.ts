import { MutationTree } from 'vuex'
import { SyncState } from './state'

export const mutations: MutationTree<SyncState> = {
  SET_IS_LOADING_SUBMIT_SYNC (state, isLoadingSubmitSync) {
    state.isLoadingSubmitSync = isLoadingSubmitSync
  },

  SET_VEHICLES_FMS_RESULT (state, vehiclesFromFms) {
    state.items = vehiclesFromFms
  },

  SET_IS_LOADING_SUBMIT_SUCCESS (state, isLoadingSubmitSuccess) {
    state.isLoadingSubmitSuccess = isLoadingSubmitSuccess
  },

  RESET_STATE (state) {
    state.items = []
    state.isLoadingSubmitSync = false
    state.isLoadingSubmitSuccess = false
  }
}

export default mutations

<template>
  <v-container fluid class="pa-0">
    <div v-if="dataTableMode == '0'">
      <slot name="card-mode" />
    </div>

    <div v-else-if="dataTableMode == '1'">
      <slot name="data-table-mode" />
    </div>
  </v-container>
</template>

<script>
import Vue from 'vue'
export default Vue.extend({
  name: 'DisplayMode',

  computed: {
    dataTableMode () {
      return this.$store.getters['layout/dataTableMode']
    }
  },

  mounted () {
    this.$store.dispatch('layout/getDataTableMode')
  },

  methods: {}

})
</script>

<style scoped>

</style>

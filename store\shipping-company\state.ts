import { ShippingCompany } from '~/types/user'

export interface ShipmentCompanyState {
  isLoading: boolean
  isLoadingForm: boolean
  isLoadingDetail: boolean
  items: ShippingCompany[]
  item: ShippingCompany
  totalPage: number
  page: number
}

export const state = (): ShipmentCompanyState => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDetail: false,
  items: [],
  item: {} as ShippingCompany,
  totalPage: 1,
  page: 1
})

export default state

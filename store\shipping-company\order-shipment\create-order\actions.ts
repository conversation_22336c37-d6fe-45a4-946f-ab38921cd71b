import { ActionTree } from 'vuex'
import { RootState } from '../../../index'
import { ShipmentCompanyCreateOrderState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<ShipmentCompanyCreateOrderState, RootState> = {
  getDraftItem ({ commit, dispatch }) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/order', {
        params: {
          filter_columns: 'status',
          filter_keys: 'DRAFT'
        }
      })
      .then((response: any) => {
        if (response.data.data.length > 0) {
          commit('SET_DRAFT_ITEM', response.data.data[0])
          dispatch('getDetailOrder', response.data.data[0].id)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  getPublishedItems ({ commit, dispatch }) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/order', {
        params: {
          filter_columns: 'status',
          filter_keys: 'PUBLISHED'
        }
      })
      .then((response: any) => {
        commit('SET_PUBLISHED_ITEM', response.data.data)
        dispatch('getDraftItem')
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async getDetailOrder ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)
    return await this.$axios
      .get('/v1/order/' + payload)
      .then((response: any) => {
        commit('SET_DRAFT_ITEM', response.data.data)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async createOrder ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING', true)
    const data = {
      identity: payload.identityNumber,
      status: 'DRAFT',
      shipment_company_id: null
    }

    if (payload && payload.idSc) {
      data.shipment_company_id = payload.idSc
    }

    return await this.$axios
      .post('/v1/order', data)
      .then((response: any) => {
        commit('SET_DRAFT_ITEM', response.data.data)
        commit('SET_DRAFT_IS_EXIST', true)
        if (response.data.data.id) {
          dispatch('getDetailOrder', response.data.data.id)
          commit('SET_NEW_ORDER_ID', response.data.data.id)
        }
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async updateOrder ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    return await this.$axios.put(`v1/order/${payload.id}/identity`, {
      id: payload.id,
      identity: payload.identityNumber
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      return response.data.data
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async sendOrder ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    await this.$axios
      .put('/v1/order/' + payload.orderId + '/publish', {
        note: payload.note
      })
      .then((response: any) => {
        const user = this.$auth.user?.data as any

        commit('SET_PUBLISHED_ITEM', response.data.data)
        commit('SET_DRAFT_IS_EXIST', false)
        commit('SET_ERROR_MESSAGE', null)
        if (user.role === 'LOGISTIC_SERVICE_PROVIDER') {
          this.$router.push('/logistic-service-provider/order-shipment/create-shipment')
        } else if (user.role === 'SHIPMENT_COMPANY') {
          this.$router.push(this.localePath('/shipping-company/order-shipment/invoice-order'))
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_ERROR_MESSAGE', error.response.data)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }
}

export default actions

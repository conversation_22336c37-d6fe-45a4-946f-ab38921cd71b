<template>
  <div>
    <div v-if="label.length > 0" class="text-body">
      {{ label }}
    </div>
    <v-autocomplete
      :value="value"
      :items="items"
      :label="hint"
      :type="type"
      :prepend-inner-icon="prependInnerIcon"
      :required="isRequired"
      :rules="rules"
      :append-icon="appendIcon"
      flat
      solo
      :single-line="false"
      full-width
      outlined
      @input="$emit('input', $event)"
      @click:append="$emit('click:append')"
    />
  </div>
</template>

<script>
export default {
  name: 'CustomTextField',
  props: {
    appendIcon: {
      type: String,
      default: ''
    },
    rules: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'text'
    },
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    prependInnerIcon: {
      type: String,
      default: ''
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    items: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>

</style>

import { GetterTree } from 'vuex'
import { InvoiceAdditionalFeeState } from './state'

export const getters: GetterTree<InvoiceAdditionalFeeState, InvoiceAdditionalFeeState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export default getters

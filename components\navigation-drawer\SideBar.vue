<template>
  <v-navigation-drawer app fixed permanent width="300px" style="z-index: 99">
    <div class="my-10 mx-5 d-flex align-center">
      <v-img
        class="mr-5"
        :src="logo"
        max-width="50"
        max-height="50"
        position="center center"
      />
      <h4>
        {{ title }}
      </h4>
    </div>

    <slot name="navigation-list" />
  </v-navigation-drawer>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'SideBar',

  props: {
    title: {
      type: String,
      required: true
    },
    logo: {
      type: String,
      required: true
    }
  },

  methods: {
    logout () {
      this.$auth.logout()
    }
  }
})
</script>

<style lang="scss" scoped></style>

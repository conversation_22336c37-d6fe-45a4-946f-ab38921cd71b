<template>
  <v-container fluid class="pa-0 px-10 mb-10">
    <v-container fluid class="pa-10 white rounded">
      <h4 class="mb-6">
        Settings
      </h4>

      <v-list class="ma-n3">
        <v-list-item-group>
          <v-list-item
            class="pa-3"
            style="border-bottom: 1px solid #CFCCCC"
            @click="$router.push(localePath(`${$route.path}/manage-user`))"
          >
            <v-container fluid class="pa-0 d-flex justify-space-between align-center">
              <div>
                <p class="mb-2 subtitle-1 black--text">
                  Manage User
                </p>
                <p class="ma-0 body-1 text-secondary">
                  Managemeng of user LSI
                </p>
              </div>

              <v-icon>
                mdi-chevron-right
              </v-icon>
            </v-container>
          </v-list-item>
        </v-list-item-group>
      </v-list>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'SetupPaymentAdminTranstrackPage',

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  created () {
    this.$store.commit('layout/SET_TITLE', 'Manage System')
  }
})
</script>

<style scoped> </style>

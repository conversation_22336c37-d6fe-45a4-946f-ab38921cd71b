<template>
  <v-container v-resize="getWindowWidth" fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="mb-10 pa-0">
      <div class="mb-sm-10 mb-5 d-flex align-center">
        <v-btn
          link
          plain
          class="text-capitalize px-3"
          @click="
            $store.dispatch('tab/changeTab', 1)
              .then(() => { $router.back() })
          "
        >
          <v-icon
            color="black"
            class="mr-2"
          >
            mdi-chevron-left
          </v-icon>
          <p class="ma-0 subtitle-1">
            {{ $t('lspInvoiceShipment.back_to_list_invoice') }}
          </p>
        </v-btn>
      </div>

      <detail-invoice-order-loading v-if="isLoadingDetail" />

      <detail-invoice-vendor
        v-else
        :data-invoice="dataInvoice"
        :total-cost="totalAmount"
      >
        <template #download-invoice>
          <v-btn
            x-large
            outlined
            :loading="isLoadingDownloadInvoice"
            class="text-capitalize"
            style="border: 1px solid #CFCCCC;"
            @click="downloadInvoice"
          >
            <v-icon class="mr-3">
              mdi-file-download
            </v-icon>
            <p class="subtitle-1 ma-0">
              {{ $t('lspInvoiceShipment.download_invoice') }}
            </p>
          </v-btn>
        </template>

        <template #default="{ invoiceDetail }">
          <h4>{{ invoiceDetail.cost | toCurrency }}</h4>
        </template>

        <template #additional-fee>
          <v-container v-if="dataInvoice?.invoice?.fees.length === 0">
            <p class="body-1 ma-0">
              {{ $t('lspInvoiceShipment.no_additional_fee') }}
            </p>
          </v-container>

          <div v-else>
            <v-container
              v-for="fee in dataInvoice?.invoice?.fees"
              :key="fee.id"
              fluid
              class="mb-5 pa-0 d-flex align-center justify-space-between"
            >
              <p class="body-1 ma-0">
                {{ fee.description }}
              </p>
              <h4>{{ parseInt(fee.cost) | toCurrency }},-</h4>
            </v-container>
          </div>
        </template>
      </detail-invoice-vendor>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailInvoiceOrderLoading from '~/components/loading/DetailInvoiceOrderLoading.vue'
import DetailInvoiceVendor from '~/components/DetailInvoiceVendor.vue'
import { Fee, InvoiceDetail, InvoiceOrder } from '~/types/invoice'

export default Vue.extend({

  name: 'DetailInvoiceOrder',

  components: { DetailInvoiceOrderLoading, DetailInvoiceVendor },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],
  data: () => ({
    tabs: null,
    windowWidth: 0,
    expanded: [],
    form: {
      products: [{
        product_id: '',
        quantity: 0
      }]
    },
    cost: ''
  }),

  computed: {
    dataInvoice () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/detailData'].item as InvoiceOrder | null
    },

    totalAmount ():number {
      let amountCost = 0
      const invoiceDetails = this.dataInvoice?.invoice_details
      const additionalFees = this.dataInvoice?.invoice?.fees

      invoiceDetails?.forEach((item: InvoiceDetail) => {
        amountCost += Number(item.cost)
      })

      additionalFees?.forEach((item: Fee) => {
        amountCost += Number(item.cost)
      })

      return amountCost
    },
    isLoadingDetail () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingDetail']
    },

    isLoadingDownloadInvoice () {
      return this.$store.getters['invoice/isLoadingDownloadInvoice']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Invoice Order Shipment')
  },

  mounted () {
    this.$store.dispatch('logistic-service-provider/invoice-shipment/getItemDetail', {
      id: this.$route.params.id
    })
  },

  methods: {
    getWindowWidth () {
      this.windowWidth = window.innerWidth
    },

    addProductList () {
      this.form.products.push({
        product_id: '',
        quantity: 0
      })
    },

    removeProductList (index: any) {
      if (this.form.products.length > 1) {
        this.form.products.splice(index, 1)
      }
    },

    async downloadInvoice () {
      await this.$store.dispatch('invoice/downloadInvoices', {
        id: this.$route.params.id,
        number: this.dataInvoice?.invoice?.shipment?.identity
      })
    }
  }
})
</script>

<style lang="scss" scoped>

.custom-icon {
  transition: 0s !important;
}

.w-custom {
  max-width: 250px;
}

.border-grey {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

</style>

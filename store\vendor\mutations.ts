import { MutationTree } from 'vuex'
import { VendorState } from '~/store/vendor/state'

export const mutations: MutationTree<VendorState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEMS_VENDOR_REPORT (state, response: any) {
    state.itemsVendorReport = response.data
    state.totalPageVendorReport = response.meta.last_page
    state.pageVendorReport = response.meta.current_page
  },

  SET_VENDOR_ORDER_SHIPMENT (state, response: any) {
    state.itemsVendorOrderShipment = response
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_VENDOR_REPORT (state, isLoadingVendorReport) {
    state.isLoadingVendorReport = isLoadingVendorReport
  },

  SET_IS_LOADING_VENDOR_ORDER_SHIPMENT (state, isLoadingVendorOrderShipment) {
    state.isLoadingVendorOrderShipment = isLoadingVendorOrderShipment
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_FORM_STATUS (state, isLoadingFormStatus) {
    state.isLoadingFormStatus.accept = isLoadingFormStatus.accept
    state.isLoadingFormStatus.reject = isLoadingFormStatus.reject
  },

  SET_SELECTED_VENDOR (state, Vendor: any) {
    state.item = Vendor
  },

  UPDATE_STATUS_VENDOR (state, { vendor, id }) {
    const index = state.items.findIndex(item => item.id === id) as number

    const newList = [...state.items]

    newList[index] = vendor
    state.items = newList
  },

  SET_ITEM_EXPORT_EXCEL (state, itemExportExcel) {
    state.itemExportExcel = itemExportExcel
  },

  SET_IS_LOADING_EXPORT_EXCEL (state, isLoadingExportExcel) {
    state.isLoadingExportExcel = isLoadingExportExcel
  }
}

export default mutations

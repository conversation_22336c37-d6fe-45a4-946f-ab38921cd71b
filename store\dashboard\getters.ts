import { GetterTree } from 'vuex'
import { DashboardState } from './state'
import { colors } from '~/utils/colors'

export const getters: GetterTree<DashboardState, DashboardState> = {
  dataAdmin (state) {
    return state.itemAdmin
  },

  dataShipment (state) {
    return state.itemShipment
  },

  dataLsa (state) {
    return state.itemLsa
  },

  dataVendor (state) {
    return state.itemVendor
  },

  isLoadingAdmin (state) {
    return state.isLoadingAdmin
  },

  isLoadingShipmentCompany (state) {
    return state.isLoadingShipmentCompany
  },

  isLoadingLogisticServiceProvider (state) {
    return state.isLoadingLogisticServiceProvider
  },

  isLoadingVendor (state) {
    return state.isLoadingVendor
  },

  isLoadingLiveTracking (state) {
    return state.isLoadingLiveTracking
  },

  liveTracking (state) {
    return state.liveTracking
  },

  truckStatus (state) {
    return state.truckStatus
  },

  geofences (state) {
    return state.filteredItemsGeofences
  },

  AllLiveTracking (state) {
    return state.itemsAllLiveTracking
  },

  isLoadingAllLiveTracking (state) {
    return state.isLoadingAllLiveTracking
  },

  isLoadingTruckStatus (state) {
    return state.isLoadingTruckStatus
  },

  dataIncomingTruckStatus (state) {
    return {
      itemIncomingTruck: state.itemIncomingTruck,
      page: state.page,
      totalPage: state.totalPages,
      totalData: state.totalData
    }
  },

  isLoadingIncomingTruck (state) {
    return state.isLoadingIncomingTRuck
  },

  isLoadingExportAlertGeofencing (state) {
    return state.isLoadingExportAlertGeofencing
  },

  dataPerformanceVendor (state) {
    return {
      itemPerformanceVendor: state.itemPerformanceVendor,
      page: state.page,
      totalPage: state.totalPages
    }
  },

  dataPerformanceVendorRate (state) {
    return {
      itemPerformanceVendorRate: state.itemPerformanceVendorRate,
      pageVendorRate: state.pageVendorRate,
      totalPagesVendorRate: state.totalPagesVendorRate
    }
  },

  dataPerformanceDetailOrder (state) {
    return {
      itemPerformanceDetailOrder: state.itemPerformanceDetailOrder,
      pagePerformanceDetailOrder: state.pagePerformanceDetailOrder,
      totalPagePerformanceDetailOrder: state.totalPagePerformanceDetailOrder
    }
  },

  isLoadingPerformanceDetailOrder (state) {
    return state.isLoadingPerformanceDetailOrder
  },

  dataVendorVehicles (state) {
    return {
      items: state.itemVendorVehicles,
      totalPage: state.totalPageVehicle,
      page: state.pageVehicle
    }
  },

  isLoadingPerformanceVendor (state) {
    return state.isLoadingPerformanceVendor
  },

  isLoadingPerformanceVendorRate (state) {
    return state.isLoadingPerformanceVendorRate
  },

  isLoadingShippingStatusVehicle (state) {
    return state.isLoadingShippingStatusVehicle
  },

  isLoadingExportExcel (state) {
    return state.isLoadingExportExcel
  },

  isLoadingExportReportExcel (state) {
    return state.isLoadingExportReportExcel
  },

  itemsReportTransporter (state) {
    return state.itemsReportTransporter
  },

  isLoadingReportTransporter (state) {
    return state.isLoadingReportTransporter
  },

  itemsAlertGeofencing (state) {
    return {
      itemsAlertGeofencing: state.itemsAlertGeofencing,
      totalPageAlertGeofencing: state.totalPageAlertGeofencing,
      pageAlertGeofencing: state.pageAlertGeofencing
    }
  },

  isLoadingAlertGeofencing (state) {
    return state.isLoadingAlertGeofencing
  },

  totalWeightOrder (state) {
    return state.itemsReportTransporter.length > 0 ? state.itemsReportTransporter.reduce((sum: any, record: { total_netto_pickup: string }) => sum + parseFloat(record.total_netto_pickup), 0) : 0
  },

  pieChartData (state) {
    let totalRefineryWeight = 0

    const initPieChart = {
      labels: [] as any,
      datasets: [
        {
          label: 'Pie Dataset',
          backgroundColor: [] as any,
          data: [] as any
        }
      ]
    }

    if (state.itemsReportTransporter.length > 0) {
      state.itemsReportTransporter
        .filter((item: { total_netto_pickup: number }) => item.total_netto_pickup > 0)
        .forEach((item: { vendor: any; total_netto_pickup: any }, index: any) => {
          initPieChart.labels.push(item.vendor)
          initPieChart.datasets[0].backgroundColor.push(colors[index])
          initPieChart.datasets[0].data.push(item.total_netto_pickup)
        })
    }
    
    return initPieChart
  },

  barChartData (state) {
    const initBarChart = {
      labels: [] as any,
      datasets: [
        {
          type: 'bar',
          label: 'Total Pickup Weight',
          backgroundColor: '#3E4784',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Total Dropoff Weight',
          backgroundColor: '#0094BC',
          data: [] as any,
          order: 2
        },
        {
          type: 'line',
          label: 'Netto Loss Percentage',
          borderColor: '#FF4560',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        }
      ]
    }

    if (state.itemsReportTransporter.length > 0) {
      state.itemsReportTransporter.forEach((item: { vendor: any; total_netto_pickup: any, total_netto_dropoff: any, netto_loss_percentage: any }, index: any) => {
        initBarChart.labels.push(item.vendor)
        initBarChart.datasets[0].data.push(item.total_netto_pickup)
        initBarChart.datasets[1].data.push(item.total_netto_dropoff)
        initBarChart.datasets[2].data.push(item.netto_loss_percentage)
      })
    }
    

    return initBarChart
  },

  barChartDataTrips (state) {
    const initBarChartTrips = {
      labels: [] as any,
      datasets: [
        {
          type: 'bar',
          label: 'Delivery Order',
          backgroundColor: '#486945',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Trips Finished',
          backgroundColor: '#85D149',
          data: [] as any,
          order: 2
        },
      ]
    }

    if (state.itemsReportTransporter.length > 0) {
      state.itemsReportTransporter.forEach((item: { vendor: any; total_shipment_track_trip: any, total_order_trip: any }) => {
        initBarChartTrips.labels.push(item.vendor)
        initBarChartTrips.datasets[0].data.push(item.total_order_trip) 
        initBarChartTrips.datasets[1].data.push(item.total_shipment_track_trip)
      })
    }
    

    return initBarChartTrips
  },

  barChartDataAssignmentAccuracy (state) {
    const initBarChart = {
      labels: [] as any,
      datasets: [
        {
          type: 'bar',
          label: 'Request Unit',
          backgroundColor: '#486945',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Acc by Actual Coming',
          backgroundColor: '#85D149',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Acc by Transporter',
          backgroundColor: '#854A0E',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Auto WB',
          backgroundColor: '#FB6514',
          data: [] as any,
          order: 2
        },
        {
          type: 'line',
          label: 'Order Acc Rate',
          borderColor: '#3E4784',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        },
        {
          type: 'line',
          label: 'Order Commitment Rate',
          borderColor: '#EF3434',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        },
        {
          type: 'line',
          label: 'Order Accuracy Rate',
          borderColor: '#EE46BC',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        }
      ]
    }

    const vendorData = [
        {
            "vendor": "JAYA",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 10,
            "percentage": 20,
            "percentage_two": 80,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "SUPER",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 20,
            "percentage": 30,
            "percentage_two": 70,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "GUDANG GARAM",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 30,
            "percentage": 40,
            "percentage_two": 60,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
          "vendor": "SAMPOERNA",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 40,
          "percentage": 70,
          "percentage_two": 30,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
        {
          "vendor": "MILD",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 70,
          "percentage": 80,
          "percentage_two": 20,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
    ];

    if (state.itemsReportTransporter.length > 0) {
      state.itemsReportTransporter.forEach((item: { vendor: any; request_unit: any, acc_by_actual_coming: any, acc_by_transporter: any, auto_wb: any, order_acceptance_rate: any, order_commitment_rate: any, order_accuracy_rate: any }) => {
        initBarChart.labels.push(item.vendor)
        initBarChart.datasets[0].data.push(item.request_unit)
        initBarChart.datasets[1].data.push(item.acc_by_actual_coming)
        initBarChart.datasets[2].data.push(item.acc_by_transporter)
        initBarChart.datasets[3].data.push(item.auto_wb)
        initBarChart.datasets[4].data.push(item.order_acceptance_rate)
        initBarChart.datasets[5].data.push(item.order_commitment_rate)
        initBarChart.datasets[6].data.push(item.order_accuracy_rate)

      })
    }

    return initBarChart
  },
}

export default getters

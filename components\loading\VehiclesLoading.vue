<template>
  <v-col class="pa-0 ma-0">
    <v-col v-for="i in 3" :key="i" class="col-12">
      <v-card elevation="0" class="pa-5 overflow-hidden">
        <v-row>
          <v-col class="pa-0 mt-5 mb-5 col-md-3 col-12 d-flex justify-center">
            <div class="text-center justify-center">
              <div>
                <v-skeleton-loader type="image" width="140" height="140" class="mt-2 mb-5" />
                <v-skeleton-loader type="text" width="100%" />
                <v-skeleton-loader type="text" width="50%" class="d-inline-flex" />
                <v-skeleton-loader type="text" width="100%" />
              </div>
            </div>
          </v-col>
          <v-col class="pa-5 col-md-9 col-12">
            <v-row class="pa-5 justify-space-between">
              <v-col class="pa-0 col-sm-7 col-12 mb-2">
                <v-skeleton-loader type="heading" />
                <v-row class="mt-2 pl-3">
                  <v-skeleton-loader type="text" width="40%" />
                </v-row>
              </v-col>
              <div>
                <v-row>
                  <v-skeleton-loader v-if="$vuetify.breakpoint.smAndUp" type="image" width="100" height="40" />
                </v-row>
              </div>
            </v-row>
            <div>
              <v-col>
                <div class="text-center">
                  <v-skeleton-loader type="text" width="100%" class="mt-10" />
                  <v-skeleton-loader type="text" width="75%" class="d-inline-flex mt-2 mb-2" />
                  <v-skeleton-loader type="text" width="40%" class="d-inline-flex" />
                </div>
              </v-col>
            </div>
          </v-col>
        </v-row>
      </v-card>
    </v-col>
  </v-col>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'VehiclesLoading'
})
</script>

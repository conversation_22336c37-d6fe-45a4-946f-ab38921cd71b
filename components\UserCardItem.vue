<template>
  <v-card outlined class="pa-5 fill-height">
    <v-card-title class="mb-5 pa-0 d-flex align-start justify-space-between">
      <div class="col-10 pa-0 d-flex align-start">
        <image-component v-if="user.avatar_url" :image="user.avatar_url" max-width="40" max-height="40" class="mr-5" />

        <v-avatar
          v-else
          color="primary"
          rounded
          size="40"
          class="mr-5"
        >
          <v-icon color="white">
            mdi-account
          </v-icon>
        </v-avatar>
        <v-badge dot :color="Bo<PERSON>an(user.is_active === false) ? 'primary' : 'success'" />

        <div>
          <h4 class="custom-card-title mb-2">
            <div class="d-flex">
              <div class="mx-2">
                {{ user.name }}
              </div>
              <div v-if="isHasNew" class="d-flex align-center pl-2 red--text">
                <span>{{ checkNew(user.created_at) }}</span>
              </div>
            </div>
          </h4>
          <p
            class="body-1 ma-0 d-flex align-center"
          >
            <v-icon
              size="16"
              color="black"
              class="mr-2"
            >
              mdi-phone
            </v-icon>
            +{{ user.phone_country_code }} {{ user.phone_number }}
          </p>
        </div>
      </div>

      <v-menu
        v-if="isHasEdit"
        v-model="menu"
        bottom
        transition="slide-y-transition"
      >
        <template #activator="{on, attrs}">
          <v-btn
            icon
            v-bind="attrs"
            v-on="on"
          >
            <v-icon color="black">
              mdi-dots-vertical
            </v-icon>
          </v-btn>
        </template>

        <v-list>
          <user-form-item
            :user="user"
            :is-loading-form="isLoadingForm"
            :is-loading-form-clear-image="isLoadingFormClearImage"
            :dialog="dialogUpdateUser"
            :is-account-setting-page="true"
            @on-click-save="onClickSave"
            @on-clear-image="$emit('on-clear-image', user.id)"
            @on-close-dialog="onCloseUpdateDialog"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                key="edit"
                v-bind="attrs"
                v-on="on"
                @click="onOpenUpdateDialog"
              >
                <v-list-item-title>
                  {{ $t('userCardItem.edit') }}
                </v-list-item-title>
              </v-list-item>
            </template>
          </user-form-item>
          <v-dialog
            v-if="isHasDialogActiveUser && user.is_active === false"
            v-model="dialogActiveUser"
            persistent
            max-width="600px"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                key="active"
                v-bind="attrs"
                v-on="on"
                @click="onOpenActiveDialog"
              >
                <v-list-item-title>
                  Active
                </v-list-item-title>
              </v-list-item>
            </template>
            <v-card>
              <v-card-title class="text-h6 lighten-2">
                Activate Driver
              </v-card-title>

              <v-card-text>
                Are you sure want to Active this User?
              </v-card-text>

              <v-divider />

              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="primary"
                  text
                  :loading="isLoadingForm"
                  @click="onClickActive"
                >
                  {{ $t('userCardItem.button_yes') }}
                </v-btn>
                <v-btn
                  color="primary"
                  @click="onCloseActiveDialog"
                >
                  {{ $t('userCardItem.button_cancel') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- <v-dialog
            v-if="isHasDialogDeleteUser"
            v-model="dialogDeleteUser"
            persistent
            max-width="600px"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                key="delete"
                v-bind="attrs"
                v-on="on"
                @click="onOpenDeleteDialog"
              >
                <v-list-item-title>
                  {{ $t('userCardItem.delete') }}
                </v-list-item-title>
              </v-list-item>
            </template>
            <v-card>
              <v-card-title class="text-h6 lighten-2">
                {{ $t('userCardItem.confirm_delete_title') }} {{ user.name }}
              </v-card-title>

              <v-card-text>
                {{ $t('userCardItem.confirm_delete_description') }}
              </v-card-text>

              <v-divider />

              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="primary"
                  text
                  :loading="isLoadingForm"
                  @click="onClickDelete"
                >
                  {{ $t('userCardItem.button_yes') }}
                </v-btn>
                <v-btn
                  color="primary"
                  @click="onCloseDeleteDialog"
                >
                  {{ $t('userCardItem.button_cancel') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog> -->
          <v-list-item
            key="session"
            v-bind="attrs"
            v-on="on"
            @click="onClickEndSession"
          >
            <v-list-item-title>
              End Session
            </v-list-item-title>
          </v-list-item>
          <v-dialog
            v-if="isHasDialogPermissionUser"
            v-model="dialogPermissionUser"
            persistent
            max-width="600px"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                key="delete"
                v-bind="attrs"
                v-on="on"
                @click="onOpenPermissionDialog"
              >
                <v-list-item-title>
                  Permission
                </v-list-item-title>
              </v-list-item>
            </template>
            <v-card class="pa-5">
              <v-card-title class="text-h6 lighten-2 d-flex justify-space-between">
                <h4>User Permission</h4>

                <v-icon color="black" @click="$emit('on-close-permission-dialog')">
                  mdi-close
                </v-icon>
              </v-card-title>

              <v-row class="pa-3 ma-1 d-flex mt-3">
                <v-autocomplete
                  v-model="permissionId"
                  outlined
                  clearable
                  :items="permissionUserId.map(item => {
                    return { text: item.name, value: item.id }
                  })"
                  label="Permission"
                />
              </v-row>

              <v-card-actions>
                <v-btn
                  color="primary"
                  class="text-capitalize"
                  :loading="isLoadingForm"
                  @click="onClickPermission"
                >
                  Save
                </v-btn>
                <v-btn
                  color="primary"
                  class="ml-5 text-capitalize"
                  outlined
                  text
                  @click="onClosePermissionDialog"
                >
                  Cancel
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-list>
      </v-menu>
    </v-card-title>

    <v-card-text class="pa-0 black--text">
      <p class="body-1 mb-2 d-flex align-center">
        <v-icon
          color="black"
          size="16"
          class="mr-2"
        >
          mdi-email
        </v-icon>
        {{ user.email }}
      </p>

      <p class="body-1 ma-0 d-flex align-center">
        <v-icon
          color="black"
          size="16"
          class="mr-2"
        >
          mdi-lock
        </v-icon>
        ********
      </p>
      <p class="body-1 mt-5">
        {{ user?.permission?.name }}
      </p>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '~/types/user'
import { checkIsNew } from '~/utils/functions'

export default Vue.extend({
  name: 'UserCardItem',

  props: {
    user: {
      type: Object as () => User,
      required: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    isLoadingFormClearImage: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    dialogUpdateUser: {
      type: Boolean,
      default: false
    },
    dialogDeleteUser: {
      type: Boolean,
      default: false
    },
    dialogActiveUser: {
      type: Boolean,
      default: false
    },
    isHasDialogDeleteUser: {
      type: Boolean,
      default: false
    },
    isHasDialogActiveUser: {
      type: Boolean,
      default: false
    },
    isHasNew: {
      type: Boolean,
      default: false
    },
    isHasEdit: {
      type: Boolean,
      default: true
    },
    isHasDialogPermissionUser: {
      type: Boolean,
      default: false
    },
    dialogPermissionUser: {
      type: Boolean,
      default: false
    },
    permissionUserId: {
      type: Array as () => User[],
      required: true
    }
  },

  data: () => ({
    menu: false,
    isShowFormPassword: false,
    permissionId: null
  }),

  watch: {
    dialogUpdateUser: {
      handler () {
        if (this.dialogUpdateUser) {
          this.menu = true
        }
      },
      immediate: true
    }
  },

  methods: {
    onClickSave (formValue: any) {
      this.$emit('on-click-edit', formValue, this.index)
    },
    onClickDelete () {
      this.$emit('on-click-delete', this.user.id, this.index)
    },
    onClickActive () {
      this.$emit('on-click-active', this.user.id, this.index)
    },
    onClickPermission () {
      this.$emit('on-click-permission', this.user.id, this.index, this.permissionId)
    },
    onClickEndSession () {
      this.$emit('on-click-end-session', this.user.id, this.index)
    },
    onOpenUpdateDialog () {
      this.$emit('on-open-update-dialog')
    },
    onCloseUpdateDialog () {
      this.$emit('on-close-update-dialog')
    },
    onOpenDeleteDialog () {
      this.$emit('on-open-delete-dialog')
    },
    onCloseDeleteDialog () {
      this.$emit('on-close-delete-dialog')
    },
    checkNew (date: string) {
      return checkIsNew(date)
    },
    onOpenActiveDialog () {
      this.$emit('on-open-active-dialog')
    },
    onCloseActiveDialog () {
      this.$emit('on-close-active-dialog')
    },
    onOpenPermissionDialog () {
      this.$emit('on-open-permission-dialog')
    },
    onClosePermissionDialog () {
      this.$emit('on-close-permission-dialog')
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-card-title {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}
</style>

module.exports = {
  add_lsp: 'Add Logistic Provider',
  empty_message_title: 'You don\'t have a registered Logistic Provider yet.',
  empty_message_description: 'You don\'t have an Logistic Provider list at this time. Register Logistic Provider on the button below.',
  back_to_list_lsp: 'Back to list Logistic Provider',
  address: 'Address',
  add_user: 'Add User',
  empty_message_title_lsp_user: 'Logistic Provider don\'t have a user.',
  empty_message_description_lsp_user: 'Now, Logistic Provider don\'t have an user. Add user now.',
  empty_message_title_lsp_sc: 'Logistic Provider don\'t have a Collaboration with Product Owner.',
  empty_message_description_lsp_sc: 'Sorry, at this time Logistic Provider don\'t have a collaboration with Product Owner, Invite the Product Owner to work with Logistic Provider.',
  empty_message_title_lsp_vendor: 'Logistic Provider don\'t have a Collaboration with Vendor.',
  empty_message_description_lsp_vendor: 'Sorry, at this time Logistic Provider don\'t have a collaboration with <PERSON>end<PERSON>, Invite the Vendor to work with Logistic Provider.',
  user: 'Users'

}

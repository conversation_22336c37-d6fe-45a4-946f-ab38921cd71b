<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-row class="d-flex mt-4 mb-8">
      <v-btn tile text class="ml-2" @click="$router.back()">
        <v-icon left color="black">
          mdi-chevron-left
        </v-icon>
        {{ $t('lspListVendor.back_to_list_vendor') }}
      </v-btn>
    </v-row>
    <v-row class="mb-8 mx-0 pa-5 rounded white">
      <div class="d-flex flex-row align-center mr-4">
        <div class="d-flex flex-row align-center">
          <v-avatar rounded size="80" class="mr-2">
            <image-component v-if="company?.logo_url" :image="company?.logo_url" max-width="40" max-height="40" />

            <v-icon v-else color="secondary">
              mdi-domain
            </v-icon>
          </v-avatar>
          <div>
            <h4 class="text-wrap mb-2">
              {{ company?.name }}
            </h4>
            <div class="d-flex align-center">
              <block-dialog
                type-label="Vendor"
                :name="company?.name"
                :status="company?.status"
                :is-loading-form="isLoadingFormStatus"
                :dialog="dialogBlockUnblock"
                @on-click-yes="updateCollaborationStatus"
                @on-close-dialog="dialogBlockUnblock = false"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    elevation="0"
                    class="mr-2 custom-btn"
                    outlined
                    depressed
                    style="height: 35px; width: 35px"
                    v-bind="attrs"
                    v-on="on"
                    @click="dialogBlockUnblock = true"
                  >
                    <v-icon v-if="company?.status === 'COLLABORATE'">
                      mdi-block-helper
                    </v-icon>
                    <v-icon v-else>
                      mdi-lock-open
                    </v-icon>
                  </v-btn>
                </template>
              </block-dialog>
              <div
                class="caption px-3 py-2"
                :style="styleContainerCollaboration()"
              >
                {{ status }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <v-divider vertical />
      <div
        class="flex flex-column justify-content-center align-items-center ma-8"
      >
        <div class="body-1 text-secondary">
          {{ $t('lspListVendor.address') }}
        </div>
        <div class="body-1 black--text">
          {{ company?.address }}
        </div>
      </div>
    </v-row>

    <tab-component>
      <template #tab>
        <v-tab class="subtitle-1 text-capitalize">
          Vehicle
        </v-tab>
<!--        <v-tab class="subtitle-1 text-capitalize">-->
<!--          Driver-->
<!--        </v-tab>-->
      </template>

      <template #tab-item>
        <v-tab-item>
          <v-container fluid class="pa-0">
            <header-datatable
              default-sort-column="name"
              default-sort-type="asc"
              :sort-column-items="sortColumnItems"
              :sort-type-items="sortTypeItems"
              @on-filter-change="getVehicles({ filter: $event })"
              @on-search-icon-click="getVehicles({ searchKey: $event })"
            />
            <vehicles-loading v-if="isLoadingVehicles" />
            <v-col v-else class="pa-0">
              <v-col
                v-for="item in vehicles.items"
                :key="item.id"
                class="col-12 px-0 py-4"
              >
                <vehicle-card-item :vehicle="item" :is-has-menu="false" />
              </v-col>
            </v-col>
            <v-row class="mt-4 mb-4" justify="end">
              <v-pagination
                v-model="vehicles.page"
                :length="vehicles.totalPage"
                :total-visible="5"
                @input="getVehicles({ page: $event })"
              />
            </v-row>
          </v-container>
        </v-tab-item>

<!--        <v-tab-item>-->
<!--          <v-container fluid class="pa-0">-->
<!--            <header-datatable-->
<!--              default-sort-column="created_at"-->
<!--              default-sort-type="desc"-->
<!--              :sort-column-items="sortColumnDrivers"-->
<!--              :sort-type-items="sortTypeDrivers"-->
<!--              sort-column-id="sort_column"-->
<!--              sort-type-id="sort_type"-->
<!--              @on-search-icon-click="getData({searchKey: $event})"-->
<!--              @on-filter-change="getData({filter: $event, page: $route.query?.page})"-->
<!--            />-->
<!--            <v-data-table-->
<!--              :loading="isLoadingDrivers"-->
<!--              loading-text="Loading... Please wait"-->
<!--              :headers="tableHeaders"-->
<!--              :items="drivers.items"-->
<!--              :page.sync="page"-->
<!--              :single-expand="singleExpand"-->
<!--              :expanded.sync="expanded"-->
<!--              :items-per-page="-1"-->
<!--              disable-sort-->
<!--              hide-default-footer-->
<!--              class="pa-md-10 pa-5"-->
<!--              style=""-->
<!--              @page-count="pageCount = $event"-->
<!--            >-->
<!--              <template #item.user.name="{ item }">-->
<!--                <div class="d-flex align-center">-->
<!--                  <image-component-->
<!--                    :image="item.user?.avatar_url ?? ''"-->
<!--                    min-width="36"-->
<!--                    max-width="36"-->
<!--                    class="mr-5"-->
<!--                  />-->
<!--                  <p class="ma-0 body-2">-->
<!--                    {{ item.user.name }}-->
<!--                  </p>-->
<!--                </div>-->
<!--              </template>-->

<!--              <template #item.user.phone_number="{ item }">-->
<!--                +{{ item.user.phone_country_code }} {{ item.user.phone_number }}-->
<!--              </template>-->

<!--              <template #item.driver_license="{ item, index }">-->
<!--                <detail-driver-license-menu-->
<!--                  :menu="menuDriverLicense[index]"-->
<!--                  :driver-name="item.user.name"-->
<!--                  :licenses="item.licenses"-->
<!--                  @on-click-close="$set(menuDriverLicense, index, false)"-->
<!--                >-->
<!--                  <template #activator="{ on, attrs }">-->
<!--                    <v-btn-->
<!--                      v-bind="attrs"-->
<!--                      text-->
<!--                      small-->
<!--                      :disabled="item.licenses.length === 0"-->
<!--                      class="px-3 mx-n3 text-capitalize"-->
<!--                      v-on="on"-->
<!--                      @click="menuDriverLicense = []; $set(menuDriverLicense, index, true)"-->
<!--                    >-->
<!--                      <p v-if="item.licenses.length > 0" class="caption ma-0 mr-2">-->
<!--                        <span v-for="(license, i) in item.licenses" :key="license.id">-->
<!--                          SIM {{ license.license_category.name }}{{ i !== item.licenses.length - 1 ? ', ' : '' }}-->
<!--                        </span>-->
<!--                      </p>-->
<!--                      <p v-else class="caption ma-0 mr-2">-->
<!--                        No License-->
<!--                      </p>-->
<!--                      <v-icon size="16" color="black">-->
<!--                        mdi-chevron-right-->
<!--                      </v-icon>-->
<!--                    </v-btn>-->
<!--                  </template>-->
<!--                </detail-driver-license-menu>-->
<!--              </template>-->

<!--              <template #item.status="{ item }">-->
<!--                <v-chip-->
<!--                  label-->
<!--                  :class="item.driver_blocked_by.length > 0 || item.blocked_at ? 'chip-primary' : 'chip-success'"-->
<!--                >-->
<!--                  <p-->
<!--                    class="subtitle-1 ma-0 text-primary"-->
<!--                    :class="item.driver_blocked_by.length > 0 || item.blocked_at ? 'text-primary' : 'text-success'"-->
<!--                  >-->
<!--                    {{ item.driver_blocked_by.length > 0 || item.blocked_at ? 'Block' : 'Active' }}-->
<!--                  </p>-->
<!--                </v-chip>-->
<!--              </template>-->

<!--              <template #item.detail="{ item, index}">-->
<!--                <v-menu-->
<!--                  bottom-->
<!--                  transition="slide-y-transition"-->
<!--                >-->
<!--                  <template #activator="{on, attrs}">-->
<!--                    <v-btn-->
<!--                      v-if="item.blocked_at === null"-->
<!--                      icon-->
<!--                      v-bind="attrs"-->
<!--                      v-on="on"-->
<!--                    >-->
<!--                      <v-icon color="black">-->
<!--                        mdi-dots-vertical-->
<!--                      </v-icon>-->
<!--                    </v-btn>-->
<!--                  </template>-->

<!--                  <v-list>-->
<!--                    <block-unblock-driver-dialog-->
<!--                      :driver-name="item.user.name"-->
<!--                      :is-blocked="Boolean(item.driver_blocked_by.length > 0)"-->
<!--                      :dialog="dialogBlockUnblockDriver[index]"-->
<!--                      :is-loading="isLoadingFormDriver"-->
<!--                      @on-click-close="$set(dialogBlockUnblockDriver, index, false)"-->
<!--                      @on-click-activate="blockUnblockDriver(item.id, 'unblock')"-->
<!--                      @on-click-block="blockUnblockDriver(item.id, 'block')"-->
<!--                    >-->
<!--                      <template #activator="{ on, attrs }">-->
<!--                        <v-list-item-->
<!--                          v-bind="attrs"-->
<!--                          v-on="on"-->
<!--                          @click="$set(dialogBlockUnblockDriver, index, true)"-->
<!--                        >-->
<!--                          <v-list-item-title class="d-flex align-center">-->
<!--                            <v-icon size="20" color="black" class="mr-2">-->
<!--                              {{ item.driver_blocked_by.length ? 'mdi-check' : 'mdi-block-helper' }}-->
<!--                            </v-icon>-->
<!--                            <p class="body-1 ma-0">-->
<!--                              {{ item.driver_blocked_by.length > 0 ? 'Activate' : 'Block' }}-->
<!--                            </p>-->
<!--                          </v-list-item-title>-->
<!--                        </v-list-item>-->
<!--                      </template>-->
<!--                    </block-unblock-driver-dialog>-->
<!--                  </v-list>-->
<!--                </v-menu>-->
<!--              </template>-->
<!--            </v-data-table>-->
<!--            <v-row class="mt-4 mb-4" justify="end">-->
<!--              <pagination-component-->
<!--                :page="drivers.page"-->
<!--                :total-page="drivers.totalPage"-->
<!--                class="mr-3"-->
<!--                page-id="page"-->
<!--                @on-change-page="getData({-->
<!--                  page: $event,-->
<!--                  filter: {-->
<!--                    sortColumn: $route.query?.sort_column,-->
<!--                    sortType: $route.query?.sort_type-->
<!--                  }-->
<!--                })"-->
<!--              />-->
<!--            </v-row>-->
<!--          </v-container>-->
<!--        </v-tab-item>-->
      </template>
    </tab-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import VehicleCardItem from '~/components/VehicleCardItem.vue'
import { styleContainerCollaboration } from '~/utils/functions'
import BlockDialog from '~/components/BlockDialog.vue'
import VehiclesLoading from '~/components/loading/VehiclesLoading.vue'
import { Vendor } from '~/types/user'
import TabComponent from '~/components/TabComponent.vue'
import { Driver } from '~/types/driver'
import BlockUnblockDriverDialog from '~/components/BlockUnblockDriverDialog.vue'

export default Vue.extend({
  name: 'VendorDetailPage',
  components: {
    BlockDialog,
    VehicleCardItem,
    VehiclesLoading,
    BlockUnblockDriverDialog,
    TabComponent
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    tab: null,
    searchKey: '',
    sortType: 'asc',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    sortColumnDrivers: {
      name: {
        label: 'Created',
        value: ''
      }
    },
    sortTypeDrivers: {
      asc: {
        label: 'First Created',
        value: 'asc'
      },
      desc: {
        label: 'Last Created',
        value: 'desc'
      }
    },
    dialogBlockUnblock: false,
    dialogBlockUnblockDriver: [] as boolean[],
    tableHeaders: [
      { text: 'National Identity', value: 'user.national_identity' },
      { text: 'Full Name', value: 'user.name' },
      { text: 'Phone Number', value: 'user.phone_number' },
      { text: 'Driver License', value: 'driver_license' },
      { text: 'Status', value: 'status' },
      { text: '', value: 'detail' }
    ],
    expanded: [],
    singleExpand: true,
    page: 1,
    pageCount: 0,
    formDriverLicense: [{
      driver_license_id: null,
      license_category_id: null,
      license_number: null
    }] as {
      driver_license_id: string | null,
      license_category_id: string | null,
      license_number: number | null
    }[],
    menuDriverLicense: [] as boolean[]
  }),
  computed: {
    company () {
      return this.$store.getters['vendor/selectedVendor'] as Vendor | null
    },
    vehicles () {
      return this.$store.getters['vehicle/data']
    },
    isLoadingVehicles () {
      return this.$store.getters['vehicle/isLoading']
    },

    isLoadingFormStatus () {
      return this.$store.getters['vendor/isLoadingFormStatus']
    },

    status () {
      const company = this.$store.getters['vendor/selectedVendor'] as Vendor | null

      if (!this.company) {
        return
      }

      return company?.status
    },

    isLoadingDrivers (): boolean {
      return this.$store.getters['vehicle/drivers/isLoading']
    },

    drivers (): { items: Driver[], page: number, totalPage: number } {
      return this.$store.getters['vehicle/drivers/data']
    },

    isLoadingFormDriver (): boolean {
      return this.$store.getters['vehicle/drivers/isLoadingForm']
    }
  },
  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Transporter')

    this.getVehicles({})
    this.getVendorDetail()
    this.getData({
      page: this.$route.query?.page as string
    })
  },
  methods: {
    getVendorDetail () {
      this.$store.dispatch('vendor/getVendor', {
        id: this.$route.params.detail
      })
    },

    getVehicles ({
      page = 1,
      searchKey = '',
      filter = { sortColumn: 'name', sortType: 'asc' }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('vehicle/getItems', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'vendor_id',
        filterKeys: this.$route.params.detail,
        page
      })
    },

    async updateCollaborationStatus () {
      const status =
        this.company?.status === 'COLLABORATE' ? 'BLOCK' : 'COLLABORATE'
      await this.$store.dispatch('vendor/updateCollaborationStatus', {
        id: this.$route.params.detail,
        status
      }).then(() => {
        this.getVendorDetail()
      }).then(() => {
        this.getVehicles({})
      })
      this.dialogBlockUnblock = false
    },

    styleContainerCollaboration () {
      if (!this.company) {
        return
      }

      return styleContainerCollaboration(this.company?.status)
    },

    getData ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('vehicle/drivers/getItems', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterKeys: this.$route.params.detail,
        page
      })
    },

    onChangeDriverLicense (driver: Driver, formValues: { license_category_id: string | null, license_number: number | null }[]) {
      this.formDriverLicense = formValues.map((
        value: { license_category_id: string | null, license_number: number | null },
        index: number
      ) => {
        return {
          driver_license_id: driver.licenses[index].id,
          license_category_id: value.license_category_id,
          license_number: value.license_number
        }
      })
    },

    async blockUnblockDriver (id: string, type: string) {
      const res = await this.$store.dispatch('vehicle/drivers/blockUnblockDriver', { id, type })

      if (res) {
        this.dialogBlockUnblockDriver = []

        this.getData({
          page: this.$route.query?.page as string,
          filter: {
            sortColumn: (this.$route.query?.sort_column as string),
            sortType: (this.$route.query?.sort_type as string)
          }
        })
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.chip-success {
  background-color: #EAF6EC !important;
}

.chip-primary {
  background-color: #FDE0E0 !important;
}
</style>

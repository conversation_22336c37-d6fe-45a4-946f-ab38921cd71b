import { ActionTree } from 'vuex'
import { ProfileLogisticShipmentCompanyState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<ProfileLogisticShipmentCompanyState, ProfileLogisticShipmentCompanyState> = {
  getFormLSP ({ commit }) {
    commit('SET_IS_LOADING', true)
    this.$axios.get('/v1/profile/logistics-service-providers').then((response: any) => {
      commit('SET_LOGISTIC_SERVICE_PROVIDER', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  getFormVendor ({ commit }) {
    commit('SET_IS_LOADING', true)
    this.$axios.get('/v1/profile/vendors').then((response: any) => {
      commit('SET_VENDOR', response.data.data)
    }).catch((error: any) => {
      exception<PERSON>and<PERSON>(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async saveFormLSP ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.name)
    formData.append('domain', payload.domain)
    formData.append('address', payload.address)
    formData.append('_method', 'PUT')

    if (payload.logo) {
      formData.append('logo', payload.logo)
    }

    if (payload.primaryColor) {
      formData.append('primary_color', payload.primaryColor)
    }

    if (payload.secondaryColor) {
      formData.append('secondary_color', payload.secondaryColor)
    }

    if (payload.registerBanner) {
      formData.append('register_banner', payload.registerBanner)
    }

    await this.$axios.post('/v1/profile/logistics-service-providers', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      this.$auth.fetchUser()

      this.$router.push('/logistic-service-provider/dashboard')
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  async saveFormVendor ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.name)
    formData.append('address', payload.address)
    formData.append('_method', 'PUT')

    if (payload.logo) {
      formData.append('logo', payload.logo)
    }

    await this.$axios.post('/v1/profile/vendors', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      this.$auth.fetchUser()

      this.$router.push('/vendor/dashboard')
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }
}

export default actions

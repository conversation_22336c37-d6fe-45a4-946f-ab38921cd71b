<template>
  <v-container>
    <v-container fluid class="mb-10 pa-0">
      <div class="d-flex flex-sm-row justify-space-between flex-column align-sm-center align-start">
        <div class="d-flex align-center ma-sm-0 mb-5">
          <h3>
            Number Order <span v-if="!isLoadingOrder" class="info--text"> {{ dataDraft.itemDraft?.identity }} </span>
            <v-skeleton-loader v-else type="heading" width="400" class="d-inline-flex mt-1" />
          </h3>
          <slot name="menu-dots" />
          <slot name="update-order-dialog" />
          <slot name="select-order" />
          <slot name="create-order" />
        </div>

        <div class="d-flex">
          <slot name="activator-import" />
          <slot name="send-order-shipment-dialog" />
        </div>
      </div>
    </v-container>

    <v-row class="ma-0 d-flex flex-lg-row flex-column">
      <v-col class="mr-lg-10 mb-lg-0 mb-10 pa-0">
        <v-container
          class="pa-md-10 pa-5 white rounded d-flex flex-column align-center"
        >
          <v-container class="pa-0 d-flex align-center justify-space-between">
            <h3>Pickup Order</h3>

            <slot name="create-pickup-order-dialog" :is-empty="false" :dialog-sub-order-index="0" />
          </v-container>

          <v-container v-if="!isLoadingDetail" class="ma-0 pa-0">
            <v-container
              v-if="totalSubOrder.pickUp > 0"
              class="pa-0"
            >
              <v-container
                v-for="(subOrder, i) in dataDraft.itemDraft.suborders"
                :key="subOrder.id"
                class="pa-0"
              >
                <slot name="sub-order-pickup-item" :sub-order="subOrder" :index="i" />
              </v-container>
            </v-container>

            <v-container
              v-else
              class="mt-10 pa-0 d-flex flex-column align-center"
            >
              <h4 class="mb-5">
                {{ $t('createOrderSection.section_title_pickup') }}
              </h4>

              <p class="text-secondary mb-10 text-center body-1">
                {{ $t('createOrderSection.section_description_pickup') }}
              </p>

              <slot name="create-pickup-order-dialog" :is-empty="true" :dialog-sub-order-index="1" />
            </v-container>
          </v-container>

          <v-container v-else>
            <v-sheet height="350" class="pt-10">
              <v-container class="d-flex justify-space-between pa-0">
                <v-skeleton-loader type="heading" width="250" class="mb-8" />
                <v-skeleton-loader type="image" width="55" height="55" />
              </v-container>
              <v-skeleton-loader type="image" height="540" class="mt-10" style="border-radius: 0 !important;" />
            </v-sheet>
          </v-container>
        </v-container>
      </v-col>

      <v-col class="pa-0">
        <v-container
          class="pa-md-10 pa-5 white rounded d-flex flex-column align-center"
        >
          <v-container class="pa-0 d-flex align-center justify-space-between">
            <h3>Drop Off Order</h3>

            <slot name="create-drop-order-dialog" :is-empty="false" :dialog-sub-order-index="0" />
          </v-container>

          <v-container v-if="!isLoadingDetail" class="ma-0 pa-0">
            <v-container
              v-if="totalSubOrder.dropOff > 0"
              class="pa-0"
            >
              <v-container
                v-for="(subOrder, i) in dataDraft.itemDraft.suborders"
                :key="subOrder.id"
                class="pa-0"
              >
                <slot name="sub-order-drop-item" :sub-order="subOrder" :index="i" />
              </v-container>
            </v-container>

            <v-container
              v-else
              class="mt-10 pa-0 d-flex flex-column align-center"
            >
              <h4 class="mb-5">
                {{ $t('createOrderSection.section_title_drop_off') }}
              </h4>

              <p class="text-secondary mb-10 text-center body-1">
                {{ $t('createOrderSection.section_description_pickup') }}
              </p>

              <slot name="create-drop-order-dialog" :is-empty="true" :dialog-sub-order-index="1" />
            </v-container>
          </v-container>

          <v-container v-else>
            <v-sheet height="350" class="pt-10">
              <v-container class="d-flex justify-space-between pa-0">
                <v-skeleton-loader type="heading" width="250" class="mb-8" />
                <v-skeleton-loader type="image" width="55" height="55" />
              </v-container>
              <v-skeleton-loader type="image" height="540" class="mt-10" style="border-radius: 0 !important;" />
            </v-sheet>
          </v-container>
        </v-container>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Order } from '~/types/product'

export default Vue.extend({
  name: 'CreateOrderSection',

  props: {
    isLoadingDetail: {
      type: Boolean,
      required: true
    },
    isLoadingOrder: {
      type: Boolean,
      required: true
    },
    dataDraft: {
      type: Object as () => {itemDraft: Order},
      required: true
    },
    totalSubOrder: {
      type: Object as () => {
        pickUp: number
        dropOff: number
      },
      default: () => ({
        pickUp: 0,
        dropOff: 0
      })
    }
  }
})
</script>

<style scoped>

</style>

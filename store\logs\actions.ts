import { ActionTree } from 'vuex'
import { RootState } from '..'
import { LogsState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<LogsState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/logs', {
        params: {
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          search_columns: 'body',
          sort_column: 'created_at',
          sort_type: 'DESC',
          filter_columns: 'type',
          filter_keys: payload.filter_keys,
          filter_date_column: payload.filter_date_column,
          filter_date_start: payload.filter_date_start,
          filter_date_end: payload.filter_date_end,
          commodity: payload.commodity,
          pickup: payload.pickup,
          dropoff: payload.dropoff,
          transporter: payload.transporter,
          driver: payload.driver,
          plate_number: payload.plate_number,
          page: payload.page == null ? '' : payload.page,
          entries: payload?.entries ? payload.entries : 9
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async refreshWeighbridge({ commit }, payload: { 
    id: string,
    log_ids: string[], 
    start_date: string, 
    end_date: string, 
    do: string 
  }) {
    commit('SET_ITEM_LOADING', { id: payload.id, isLoading: true })
    
    return await this.$axios.post('/v1/logs/weighbridge-push', {
      log_ids: payload.log_ids || [],
      start_date: payload.start_date || '',
      end_date: payload.end_date || '',
      do: payload.do
    }, {
      headers: {
        'x-api-key': 'ubmRyhcB3KpuaMrIkSq7HpK6tb0I6oWgOY4YAD31UUPSvD2sVZ'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_ITEM_LOADING', { id: payload.id, isLoading: false })
      })
  }
}

export default actions

<template>
  <div>
    <div v-if="isSelectedOrderExist" class="bg-color d-flex flex-column pa-3 mt-5 rounded">
      <a class="caption grey--text">Note</a>
      <a class="black--text font-italic">{{ selectedOrder?.note }}</a>
    </div>

    <v-progress-linear
      v-if="isLoading"
      indeterminate
      color="primary"
      class="my-5"
    />

    <div v-if="isSelectedOrderExist" class="mt-8 mb-5 pa-10" style="border: 1px solid darkgray">
      <v-row class="justify-lg-space-between mb-5">
        <h4>{{ $t('lspCreateShipment.number_order') }}</h4>
        <h4>{{ selectedOrder.identity }}</h4>
      </v-row>

      <v-row v-for="(suborder, index) in selectedOrder.suborders" :key="index" class="pa-5 my-5" style="border: 1px solid darkgray">
        <v-col class="pa-0">
          <v-row class="ma-0 justify-space-between align-center">
            <div :class="colorType(suborder.type)" class="bg-info-color px-3 py-1 text-info rounded  font-weight-bold">
              {{ suborder.type }}
            </div>
          </v-row>
          <div class="col-sm-12 col-xl-10 col-lg-8 col-12 pa-0 mt-5">
            <div class="d-flex justify-space-between">
              <div class="d-block">
                <h4 class="font-weight-medium black--text">
                  {{ $t('lspCreateShipment.product') }}
                </h4>
                <p
                  v-for="(product, j) in suborder.products"
                  :key="j"
                  class="my-1"
                >
                  {{ product?.name }}
                </p>
              </div>
              <div class="d-block">
                <h4 class="font-weight-medium black--text">
                  {{ $t('lspCreateShipment.quantity') }}
                </h4>
                <p
                  v-for="(product, j) in suborder.products"
                  :key="j"
                  class="my-1"
                >
                  {{ formatNumber(product.pivot.quantity) }} {{ product.unit }}
                </p>
              </div>
            </div>
          </div>
          <div class="col-sm-12 col-xl-10 col-lg-8 col-12 pa-0 mt-5">
            <div class="d-flex justify-space-between">
              <div class="d-block">
                <h4 class="font-weight-medium black--text">
                  {{ $t('lspCreateShipment.estimation') }}
                </h4>
                <p class="my-1 red--text">
                  {{ $moment(suborder.estimation_date).format('DD-MM-yyyy') }}
                </p>
              </div>
              <div class="d-block">
                <h4 class="font-weight-medium black--text text-left">
                  {{ $t('lspCreateShipment.location') }}
                </h4>
                <p class="my-1">
                  {{ suborder.pickup_drop_off_location_point?.name }}
                </p>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>

    <div v-if="!isSelectedOrderExist" class="mt-8 d-flex flex-column align-center">
      <h4 class="mb-2">
        {{ $t('lspCreateShipment.no_selected_order_title') }}
      </h4>
      <p class="ma-0 text-secondary body-1 text-center">
        {{ $t('lspCreateShipment.no_selected_order_text') }}
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Order } from '~/types/product'
import { colorType, formatNumber } from '~/utils/functions'

export default Vue.extend({
  name: 'DetailOrderShipment',

  props: {
    isLoading: {
      type: Boolean,
      default: false
    },

    isSelectedOrderExist: {
      type: Boolean,
      default: false,
      required: true
    },

    selectedOrder: {
      type: Object as () => Order,
      required: true
    }
  },

  methods: {
    formatNumber,
    colorType (type: string):string {
      return colorType(type)
    }
  }
})
</script>

<style scoped>

</style>

<template>
  <v-container fluid class="pa-0 px-10 pb-10 d-flex flex-column align-end">
    <header-datatable
      default-sort-column="created_at"
      default-sort-type="desc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      default-filter-column="status"
      default-filter-type="DRAFT|PUBLISHED"
      :sort-column-id="tabs === 0 ? 'sort_column_lsa' : 'sort_column_vendor'"
      :sort-type-id="tabs === 0 ? 'sort_type_lsa' : 'sort_type_vendor'"
      @on-search-icon-click="
        tabs === 0 ?
          getDataInvoiceLsa({searchKey: $event}) :
          getDataInvoiceVendor({searchKey: $event})"
      @on-filter-change="
        tabs === 0 ?
          getDataInvoiceLsa({filter: $event, page: $route.query?.page_lsa}) :
          getDataInvoiceVendor({filter: $event, page: $route.query?.page_vendor})"
    >
      <template #toggle-button>
        <toggle-button />
      </template>
    </header-datatable>

    <tab-component>
      <template #tab>
        <v-tab class="subtitle-1 text-capitalize">
          Detail Invoice
        </v-tab>
        <v-tab class="subtitle-1 text-capitalize">
          Invoice from Vendor
        </v-tab>
      </template>

      <template #tab-item>
        <display-mode style="background-color: #F0F0F0;">
          <template #card-mode>
            <v-container
              fluid
              class="pa-0"
              style="background-color: #F0F0F0; "
            >
              <v-tab-item style="background-color: #F0F0F0; " class="pa-0 mt-5">
                <invoice-loading v-if="isLoadingInvoice" />
                <v-container v-else fluid class="pa-0 mb-10">
                  <v-row v-if="dataInvoiceLsa.items" class="ma-n5">
                    <v-col
                      v-for="(item, i) in dataInvoiceLsa.items"
                      :key="i"
                      class="pa-5 col-lg-4 col-sm-6 col-12"
                    >
                      <invoice-card-item
                        :id="item.id"
                        :identity="item.shipment?.identity"
                        :read-at="item.read_at"
                        :date="$moment(item.created_at).format('DD MMMM YYYY')"
                        :detail-route="'/logistic-service-provider/order-shipment/invoice-order/create-invoice'"
                      />
                    </v-col>
                  </v-row>

                  <v-row v-else>
                    <v-col class="justify-center align-center fill-height">
                      <empty-placeholder
                        hero="empty-placeholder.svg"
                        :message-title="`${ $t('lspInvoiceShipment.invoice_empty_title') }`"
                        :message-description="`${ $t('lspInvoiceShipment.invoice_empty_desc') }`"
                      />
                    </v-col>
                  </v-row>
                </v-container>

                <pagination-component
                  :page="dataInvoiceLsa.page"
                  :total-page="dataInvoiceLsa.totalPage"
                  page-id="page_lsa"
                  class="float-end"
                  @on-change-page="getDataInvoiceLsa({
                    page: $event,
                    filter: {
                      sortColumn: $route.query?.sort_column_lsa,
                      sortType: $route.query?.sort_type_lsa
                    }
                  })"
                />
              </v-tab-item>
            </v-container>

            <v-container fluid class="pa-0" style="background-color: #F0F0F0;">
              <v-tab-item style="background-color: #F0F0F0;" class="pa-0 mt-5">
                <invoice-loading v-if="isLoadingVendor" />
                <v-container v-else fluid class="pa-0 mb-10">
                  <v-row v-if="dataInvoiceVendor.vendorItems" class="ma-n5">
                    <v-col
                      v-for="(item, i) in dataInvoiceVendor.vendorItems"
                      :key="i"
                      class="pa-5 col-lg-4 col-sm-6 col-12"
                    >
                      <detail-invoice-card-item
                        :id="item.id"
                        :lsa-image="item.shipment?.vendor.logo_url"
                        :identity="item.shipment?.identity"
                        :lsa-name="item.shipment?.vendor.name"
                        :read-at="item.read_at"
                        :date="$moment(item.created_at).format('DD MMMM YYYY')"
                        :detail-route="'/logistic-service-provider/order-shipment/invoice-order/detail'"
                      />
                    </v-col>
                  </v-row>
                  <v-row v-else>
                    <v-col class="justify-center align-center fill-height">
                      <empty-placeholder
                        hero="empty-placeholder.svg"
                        :message-title="`${ $t('lspInvoiceShipment.invoice_empty_title') }`"
                        :message-description="`${ $t('lspInvoiceShipment.invoice_empty_desc') }`"
                      />
                    </v-col>
                  </v-row>
                </v-container>

                <pagination-component
                  :page="dataInvoiceVendor.page"
                  :total-page="dataInvoiceVendor.totalPage"
                  page-id="page_vendor"
                  class="float-end"
                  @on-change-page="getDataInvoiceVendor({
                    page: $event,
                    filter: {
                      sortColumn: $route.query?.sort_column_vendor,
                      sortType: $route.query?.sort_type_vendor
                    }
                  })"
                />
              </v-tab-item>
            </v-container>
          </template>

          <template #data-table-mode>
            <v-container
              fluid
              class="pa-0 mt-3"
              style="background-color: #f0f0f0"
            >
              <v-tab-item style="background-color: #F0F0F0; " class="pa-0 mt-5">
                <div v-if="dataInvoiceLsa.items">
                  <v-data-table
                    :loading="isLoadingInvoice"
                    loading-text="Loading... Please wait"
                    :headers="tableInvoiceHeaders"
                    :items="dataInvoiceLsa.items"
                    :page.sync="page"
                    :single-expand="singleExpand"
                    :expanded.sync="expanded"
                    :items-per-page="-1"
                    hide-default-footer
                    class="pa-md-10 pa-5"
                    style=""
                    @page-count="pageCount = $event"
                  >
                    <template #item.date="{ item }">
                      {{ $moment(item.created_at).format('DD MMMM YYYY') }}
                    </template>
                    <template #item.status="{ item }">
                      <v-chip
                        v-if="item.status === 'PROPOSED'"
                        label
                        class="chip-success font-weight-medium"
                      >
                        <p class="ma-0 subtitle-1 text-success">
                          {{ item.status }}
                        </p>
                      </v-chip>
                      <v-chip
                        v-else-if="item.status === 'REJECT'"
                        label
                        class="chip-danger font-weight-medium"
                      >
                        <p class="ma-0 subtitle-1 text-primary">
                          {{ item.status }}
                        </p>
                      </v-chip>
                      <v-chip v-else label class="chip-success font-weight-medium">
                        <p class="ma-0 subtitle-1 text-info">
                          {{ item.status }}
                        </p>
                      </v-chip>
                    </template>
                    <template #item.detail="{ item }">
                      <v-btn
                        class="font-weight-medium pa-0 text-capitalize"
                        text
                        plain
                        @click="
                          $router.push(
                            '/logistic-service-provider/order-shipment/invoice-order/create-invoice/' + `${item.id}`
                          )
                        "
                      >
                        Detail
                        <v-icon> mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </div>
                <div v-else>
                  <div class="justify-center align-center fill-height">
                    <empty-placeholder
                      hero="empty-placeholder.svg"
                      :message-title="`${ $t('lspInvoiceShipment.invoice_empty_title') }`"
                      :message-description="`${ $t('lspInvoiceShipment.invoice_empty_desc') }`"
                    />
                  </div>
                </div>
                <pagination-component
                  :page="dataInvoiceLsa.page"
                  :total-page="dataInvoiceLsa.totalPage"
                  page-id="page_lsa"
                  class="float-end mt-5"
                  @on-change-page="getDataInvoiceLsa({page: $event})"
                />
              </v-tab-item>
            </v-container>

            <v-container
              fluid
              class="pa-0 mt-3"
              style="background-color: #f0f0f0"
            >
              <v-tab-item style="background-color: #F0F0F0; " class="pa-0 mt-5">
                <div v-if="dataInvoiceVendor.vendorItems">
                  <v-data-table
                    :loading="isLoadingVendor"
                    loading-text="Loading... Please wait"
                    :headers="tableHeaders"
                    :items="dataInvoiceVendor.vendorItems"
                    :page.sync="page"
                    :single-expand="singleExpand"
                    :expanded.sync="expanded"
                    :items-per-page="-1"
                    hide-default-footer
                    class="pa-md-10 pa-5"
                    style=""
                    @page-count="pageCount = $event"
                  >
                    <!--                      <template #item.image="{ item, index }">-->
                    <!--                        <image-component :image="item.vendor?.logo_url" max-width="50" max-height="50" class="mr-5" />-->
                    <!--                      </template>-->
                    <template #item.date="{ item }">
                      {{ $moment(item.created_at).format('DD MMMM YYYY') }}
                    </template>
                    <template #item.status="{ item }">
                      <v-chip
                        v-if="item.status === 'PROPOSED'"
                        label
                        class="chip-success font-weight-medium"
                      >
                        <p class="ma-0 subtitle-1 text-success">
                          {{ item.status }}
                        </p>
                      </v-chip>
                      <v-chip
                        v-else-if="item.status === 'REJECT'"
                        label
                        class="chip-danger font-weight-medium"
                      >
                        <p class="ma-0 subtitle-1 text-primary">
                          {{ item.status }}
                        </p>
                      </v-chip>
                      <v-chip v-else label class="chip-success font-weight-medium">
                        <p class="ma-0 subtitle-1 text-info">
                          {{ item.status }}
                        </p>
                      </v-chip>
                    </template>
                    <template #item.detail="{ item }">
                      <v-btn
                        class="font-weight-medium pa-0 text-capitalize"
                        text
                        plain
                        @click="
                          $router.push(
                            '/logistic-service-provider/order-shipment/invoice-order/detail/' + `${item.id}`
                          )
                        "
                      >
                        Detail
                        <v-icon> mdi-chevron-right</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </div>
                <div v-else>
                  <div class="justify-center align-center fill-height">
                    <empty-placeholder
                      hero="empty-placeholder.svg"
                      :message-title="`${ $t('lspInvoiceShipment.invoice_empty_title') }`"
                      :message-description="`${ $t('lspInvoiceShipment.invoice_empty_desc') }`"
                    />
                  </div>
                </div>
                <pagination-component
                  :page="dataInvoiceVendor.page"
                  :total-page="dataInvoiceVendor.totalPage"
                  page-id="page_vendor"
                  class="float-end mt-5"
                  @on-change-page="getDataInvoiceVendor({page: $event})"
                />
              </v-tab-item>
            </v-container>
          </template>
        </display-mode>
      </template>
    </tab-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import InvoiceCardItem from '~/components/logistic-service-provider/InvoiceCardItem.vue'
import DetailInvoiceCardItem from '~/components/logistic-service-provider/DetailInvoiceCardItem.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import InvoiceLoading from '~/components/loading/InvoiceLoading.vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import ToggleButton from '~/components/ToggleButton.vue'
import DisplayMode from '~/components/DisplayMode.vue'
import ImageComponent from '~/components/ImageComponent.vue'
import TabComponent from '~/components/TabComponent.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'LogisticServiceProviderInvoiceOrderPage',

  components: {
    InvoiceCardItem,
    DetailInvoiceCardItem,
    EmptyPlaceholder,
    InvoiceLoading,
    HeaderDatatable,
    ToggleButton,
    DisplayMode,
    ImageComponent,
    TabComponent,
    PaginationComponent
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    tabs: 0,
    expanded: [],
    dialog: false,
    singleExpand: true,
    button: true,
    page: 1,
    pageCount: 0,
    sortColumnItems: {
      createdAt: {
        label: 'Date',
        value: 'created_at'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    tableHeaders: [
      // {
      //   text: 'Image',
      //   value: 'image'
      // },
      {
        text: 'Order Shipment',
        value: 'shipment.identity'
      },
      {
        text: 'Vendor Name',
        value: 'shipment.vendor.name'
      },
      {
        text: 'Date',
        value: 'date'
      },
      {
        text: 'Status',
        value: 'status'
      },
      {
        text: '',
        value: 'detail'
      }
    ],

    tableInvoiceHeaders: [
      {
        text: 'Order Shipment',
        value: 'shipment.identity'
      },
      {
        text: 'Date',
        value: 'date'
      },
      {
        text: '',
        value: 'detail'
      }
    ]
  }),

  computed: {
    dataInvoiceLsa () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/data']
    },

    dataInvoiceVendor () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/vendorData']
    },

    isLoadingInvoice () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoading']
    },

    isLoadingVendor () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingVendor']
    },
    tabIndex (): number {
      return this.$store.getters['tab/index']
    }
  },

  watch: {
    tabIndex: {
      handler () {
        this.tabs = this.tabIndex
        if (this.tabs === 1 && !this.isLoadingVendor) {
          this.getDataInvoiceVendor({})
        }
      },
      immediate: true
    }
  },

  mounted () {
    this.getDataInvoiceVendor({
      page: this.$route.query?.page_vendor as string
    })
    this.getDataInvoiceLsa({
      page: this.$route.query?.page_lsa as string
    })
  },

  created () {
    this.$store.commit('layout/SET_TITLE', `${this.$t('lspInvoiceShipment.invoice_order_shipment')}`)
  },

  methods: {
    getDataInvoiceVendor ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.$store.dispatch('logistic-service-provider/invoice-shipment/getVendorItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'invoice_from',
        filterKeys: 'VENDOR',
        searchColumns: 'shipment.identity',
        searchKey,
        page
      })
    },

    getDataInvoiceLsa ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.$store.dispatch('logistic-service-provider/invoice-shipment/getItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'invoice_from,status',
        filterKeys: 'LOGISTIC_SERVICE_PROVIDER,DRAFT',
        searchColumns: 'shipment.identity',
        searchKey,
        page
      })
    }
  }

})
</script>
<style scoped lang="scss">

.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}
</style>

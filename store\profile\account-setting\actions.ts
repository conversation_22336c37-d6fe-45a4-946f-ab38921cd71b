import { ActionTree } from 'vuex'
import { ProfileAccountSettingState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<ProfileAccountSettingState, ProfileAccountSettingState> = {
  getItem ({ commit }) {
    commit('SET_IS_LOADING', true)
    this.$axios.get('/v1/auth/profile').then((response: any) => {
      commit('SET_ITEM', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async updatePassword ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.name)
    formData.append('email', payload.email)
    formData.append('password', payload.password)
    formData.append('password_confirm', payload.password_confirmation)
    if (payload.password_old) {
      formData.append('password_old', payload.password_old)
    }
    formData.append('phone_country_code', payload.phone_country_code)
    formData.append('phone_number', payload.phone_number)
    formData.append('_method', 'PUT')

    await this.$axios.post('/v1/auth/profile', formData, {
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      this.$auth.fetchUser()
      if (response.data.data.role === 'LOGISTIC_SERVICE_PROVIDER') {
        this.$router.push('/logistic-service-provider/dashboard')
      } else if (response.data.data.role === 'VENDOR') {
        this.$router.push('/vendor/dashboard')
      }
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }
}

export default actions

import { MutationTree } from 'vuex'
import { InvoiceState } from './state'

export const mutations: MutationTree<InvoiceState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_ITEM (state, item: any) {
    state.item = item
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  },

  SET_IS_LOADING_DETAIL_FORM (state, isLoadingDetailForm) {
    state.isLoadingDetailForm = isLoadingDetailForm
  },

  SET_IS_LOADING_DOWNLOAD_INVOICE (state, isLoadingDownloadInvoice) {
    state.isLoadingDownloadInvoice = isLoadingDownloadInvoice
  },

  SET_BLOB_INVOICE (state, blobInvoice) {
    state.blobInvoice = blobInvoice
  }
}

export default mutations

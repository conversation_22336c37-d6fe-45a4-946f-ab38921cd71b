<template>
  <v-dialog
    v-model="dialog"
    max-width="540px"
    persistent
  >
    <template #activator="{ on, attrs }">
      <v-btn
        v-if="shipment"
        :disabled="disabled"
        elevation="0"
        color="primary"
        class="text-capitalize col-md-4 col-12 mt-md-0 mt-5"
        v-bind="attrs"
        x-large
        outlined
        v-on="on"
        @click="$emit('on-dialog-open', shipment)"
      >
        {{ btnName }}
      </v-btn>
      <v-btn
        v-else-if="order"
        :disabled="disabled"
        elevation="0"
        color="primary"
        class="text-capitalize ml-3"
        v-bind="attrs"
        large
        outlined
        v-on="on"
        @click="$emit('on-dialog-open', order)"
      >
        {{ btnName }}
      </v-btn>
    </template>

    <v-card class="pa-10">
      <v-form>
        <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
          <h4>
            {{ dialogTitle }}
          </h4>

          <v-icon color="black" @click="dialog = false; $emit('on-dialog-close')">
            mdi-close
          </v-icon>
        </v-card-title>

        <div class="text-center">
          <p>
            {{ $t('lspCreateShipment.dialog_reject_text_first') }}
            <span v-if="order == null" class="font-weight-bold">#{{ shipment?.shipment?.orders[0]?.identity }}</span>
            <span v-else class="font-weight-bold">#{{ order.identity }}</span>
            {{ $t('lspCreateShipment.dialog_reject_text_second') }}
          </p>
        </div>

        <div v-if="order == null" class="text-center">
          <p class="font-weight-bold">
            {{ shipment.tracks?.map((track) => track.vehicle_detail?.vehicle?.name).join(', ') }}
          </p>
        </div>

        <v-card-actions class="mt-10 pa-0 d-flex justify-start">
          <v-row class="ma-0">
            <v-col class="mr-5 pa-0">
              <v-btn
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                @click="dialog = false; $emit('on-dialog-close')"
              >
                {{ $t('general.cancel') }}
              </v-btn>
            </v-col>
            <v-col class="pa-0">
              <v-btn
                v-if="order == null"
                elevation="0"
                outlined
                color="primary"
                class="text-capitalize ma-0"
                x-large
                block
                :loading="isLoadingDetail"
                @click="onClickRejectShipment"
              >
                {{ $t('lspCreateShipment.reject') }} Order
              </v-btn>
              <v-btn
                v-else
                elevation="0"
                outlined
                color="primary"
                class="text-capitalize ma-0"
                x-large
                block
                :loading="isLoadingDetail"
                @click="onClickRejectOrder"
              >
                {{ $t('lspCreateShipment.reject') }} Order
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Shipment } from '~/types/shipment'
import { Order } from '~/types/product'
export default Vue.extend({
  name: 'RejectOrderShipmentDialog',

  props: {
    dialogTitle: {
      type: String,
      default: ''
    },
    shipment: {
      type: Object as () => Shipment || undefined,
      default: undefined
    },
    order: {
      type: Object as () => Order || undefined || null,
      default: undefined
    },
    btnName: {
      type: String,
      default: ''
    },
    isLoadingDetail: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    onClickRejectShipment () {
      this.$emit('on-reject-order', this.shipment)
    },

    onClickRejectOrder () {
      this.$emit('on-reject-order', this.order)
    }
  }
})
</script>

<style lang="scss" scoped>
</style>

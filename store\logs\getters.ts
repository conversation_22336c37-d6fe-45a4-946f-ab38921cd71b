import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { LogsState } from './state'

export const getters: GetterTree<LogsState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingRefreshWB (state) {
    return state.isLoadingRefreshWB
  },

  isItemLoading: (state: LogsState) => (id: string): boolean => {
    return !!state.loadingItems?.[id]
  }
}

export default getters

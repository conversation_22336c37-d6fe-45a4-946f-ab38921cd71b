const general = require('./general.js')
const login = require('./login.js')
const loginAdmin = require('./loginAdmin.js')
const profile = require('./profile.js')

const acceptOrderShipmentDialog = require('./vendor/components/accept-order-shipment-dialog.js')
const historyInvoiceCardItem = require('./vendor/components/history-invoice-card-item.js')
const invoiceCardItem = require('./vendor/components/invoice-card-item.js')
const requestLsaCardItem = require('./vendor/components/request-lsa-card-item.js')
const sendOrderShipmentDialog = require('./send-order-shipment-dialog.js')
const detailHistoryInvoice = require('./components/detail-history-invoice.js')
const vehicleCard = require('./components/vehicle-card.js')
const createOrderSection = require('./components/create-order-section.js')
const userFormItem = require('./components/user-form-item.js')
const userCardItem = require('./components/user-card-item.js')
const imageSelect = require('./components/image-select.js')
const addDriverDialog = require('./components/add-driver-dialog.js')
const blockDialog = require('./components/block-dialog.js')
const companyFormItem = require('./components/company-form-item.js')
const driverCardItem = require('./components/driver-card-item.js')
const filterMenu = require('./components/filter-menu.js')
const notificationList = require('./components/notification-list.js')
const productManagement = require('./components/product-management.js')
const scCardItem = require('./components/sc-card-item.js')
const userRoleFormItem = require('./components/user-role-form-item.js')
const vehicleForm = require('./components/vehicle-form.js')
const vehicleFormDialog = require('./components/vehicle-form-dialog.js')
const vehiclePlateForm = require('./components/vehicle-plate-form.js')
const vehiclePlateFormAddDialog = require('./components/vehicle-plate-form-add-dialog.js')
const vehicleTypeForm = require('./components/vehicle-type-form.js')
const vehiclePlateFormUpdateDialog = require('./components/vehicle-plate-form-update-dialog.js')

const adminDashboard = require('./admin/dashboard.js')
const adminLsp = require('./admin/lsp.js')
const adminSc = require('./admin/sc.js')
const adminVendor = require('./admin/vendor.js')

const lspDashboard = require('./logistic-shipment-provider/dashboard.js')
const lspCreateShipment = require('./logistic-shipment-provider/create-shipment.js')
const lspInvoiceShipment = require('./logistic-shipment-provider/invoice-shipment.js')
const lspHistoryShipment = require('./logistic-shipment-provider/history-shipment.js')
const lspCustomer = require('./logistic-shipment-provider/customer.js')
const lspListVendor = require('./logistic-shipment-provider/list-vendor.js')
const lspRequestVendor = require('./logistic-shipment-provider/request-vendor.js')
const lspUsers = require('./logistic-shipment-provider/users.js')
const lspSidebar = require('./logistic-shipment-provider/sidebar.js')
const lspDetailScLocation = require('./logistic-shipment-provider/components/detail-sc-location')
const lspLocationFormDialog = require('./logistic-shipment-provider/components/location-form-dialog')

const scRegistration = require('./register-shipping-company.js')
const scSidebar = require('./shipping-company/sidebar.js')
const scDashboard = require('./shipping-company/dashboard.js')
const scCreateOrder = require('./shipping-company/create-order.js')
const scInvoiceOrder = require('./shipping-company/invoice-order.js')
const scHistoryOrder = require('./shipping-company/history-order.js')
const scProduct = require('./shipping-company/product.js')
const scUsers = require('./shipping-company/users.js')
const scSubOrderCardItem = require('./shipping-company/components/sub-order-card-item.js')
const scCreateOrderDialog = require('./shipping-company/components/create-order-dialog.js')
const scFormProductDialog = require('./shipping-company/components/form-product-dialog.js')
const scFormProductItems = require('./shipping-company/components/form-product-items.js')
const scInvoiceCardItem = require('./shipping-company/components/invoice-card-item.js')

const vendorDashboard = require('./vendor/dashboard.js')
const vendorCreateOrder = require('./vendor/create-order.js')
const vendorVehicles = require('./vendor/vehicles.js')
const vendorDrivers = require('./vendor/drivers.js')
const vendorLsp = require('./vendor/lsp.js')
const vendorUsers = require('./vendor/users.js')
const vendorMenu = require('./vendor/menu.js')
const vendorHistoryOrder = require('./vendor/history-order.js')
const vendorInvoiceOrder = require('./vendor/invoice-order.js')
const vendorRegistration = require('./register-vendor.js')
const vendorSuccessAcceptShipment = require('./vendor/success-accept-shipment.js')

module.exports = {
  sendOrderShipmentDialog,
  general,
  profile,
  login,
  loginAdmin,

  acceptOrderShipmentDialog,
  historyInvoiceCardItem,
  invoiceCardItem,
  requestLsaCardItem,
  detailHistoryInvoice,
  vehicleCard,
  createOrderSection,
  userFormItem,
  userCardItem,
  imageSelect,
  addDriverDialog,
  blockDialog,
  companyFormItem,
  driverCardItem,
  filterMenu,
  notificationList,
  productManagement,
  scCardItem,
  userRoleFormItem,
  vehicleForm,
  vehicleFormDialog,
  vehiclePlateForm,
  vehiclePlateFormAddDialog,
  vehicleTypeForm,
  vehiclePlateFormUpdateDialog,

  adminDashboard,
  adminLsp,
  adminSc,
  adminVendor,

  lspDashboard,
  lspCreateShipment,
  lspInvoiceShipment,
  lspHistoryShipment,
  lspCustomer,
  lspListVendor,
  lspRequestVendor,
  lspSidebar,
  lspUsers,
  lspDetailScLocation,
  lspLocationFormDialog,

  scRegistration,
  scSidebar,
  scDashboard,
  scCreateOrder,
  scInvoiceOrder,
  scHistoryOrder,
  scProduct,
  scUsers,
  scSubOrderCardItem,
  scCreateOrderDialog,
  scFormProductDialog,
  scFormProductItems,
  scInvoiceCardItem,

  vendorDashboard,
  vendorCreateOrder,
  vendorVehicles,
  vendorDrivers,
  vendorLsp,
  vendorUsers,
  vendorMenu,
  vendorHistoryOrder,
  vendorInvoiceOrder,
  vendorRegistration,
  vendorSuccessAcceptShipment
}

import { ActionTree } from 'vuex'
import { GeoLocationState } from './state'
import { exceptionH<PERSON><PERSON> } from '~/utils/functions'
import { GeoLocation } from '~/types/geo-location'

export const actions: ActionTree<GeoLocationState, GeoLocationState> = {
  searchByName ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/location/search?location=' + payload.searchKey)
      .then((response: any) => {
        const data = response.data as GeoLocation[]

        commit('SET_ITEMS', data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

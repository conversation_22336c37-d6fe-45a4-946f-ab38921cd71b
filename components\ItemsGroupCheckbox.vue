<template>
  <v-list class="fill-height overflow-y-auto pa-0">
    <v-list-item-group
      v-model="selectedItems"
      multiple
      color="primary"
    >
      <template v-for="(item, i) in items">
        <v-list-item
          :key="i + '_' + item"
          :value="item"
        >
          <template #default="{ active }">
            <v-list-item-action v-if="item.value !== 'isCategory'">
              <v-checkbox :input-value="active" />
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                <div v-if="item.value === 'isCategory'" class="d-flex justify-space-between">
                  <p class="subtitle-1 ma-0">
                    {{ item.text }}
                  </p>
                  <p class="ma-0">
                    {{ item.length }} data
                  </p>
                </div>
                <div v-else>
                  {{ item.text }}
                </div>
              </v-list-item-title>
              <v-list-item-subtitle>
                {{ item.subtitle }}
              </v-list-item-subtitle>
            </v-list-item-content>
          </template>
        </v-list-item>
      </template>
    </v-list-item-group>
  </v-list>
</template>

<script lang="ts">
import Vue from 'vue'

interface Item {
  value: string | number | null
  text: string
  subtitle: string | null
}

export default Vue.extend({
  name: 'ItemsGroupCheckbox',

  props: {
    items: {
      type: Array as () => Item[],
      default: () => []
    },
    isSelectedAll: {
      type: Boolean,
      default: false
    },
    resetSelectedItems: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    selectedItems: [] as (string | Item)[]
  }),

  watch: {
    isSelectedAll (isSelected) {
      if (isSelected) {
        this.selectedItems = this.items?.map(item => item.value ? item : item.text)
      } else {
        this.selectedItems = []
      }
    },

    selectedItems (items) {
      this.$emit('on-select-item', items)
    },

    resetSelectedItems (isReset) {
      if (isReset) {
        this.selectedItems = []
      }
    }
  }
})
</script>

<style scoped> </style>

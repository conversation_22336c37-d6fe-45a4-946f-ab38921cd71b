<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10 d-flex align-end justify-space-between flex-column">
    <header-datatable
      default-sort-column="name"
      default-sort-type="asc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-filter-change="getUsers({filter: $event, page: $route.query?.page})"
      @on-search-icon-click="getUsers({searchKey: $event})"
    >
      <template #button>
        <user-form-item
          :is-loading-form="isLoadingUserForm"
          :dialog="dialogCreateUser"
          :clear-form="clearForm"
          @on-click-save="createUser"
          @on-close-dialog="
            dialogCreateUser = false
            clearForm = true
          "
        >
          <template #activator="{ on, attrs }">
            <v-btn
              v-if="$vuetify.breakpoint.xs"
              depressed
              color="primary"
              height="52"
              v-bind="attrs"
              block
              class="mb-4 mt-5"
              v-on="on"
              @click="
                dialogCreateUser = true
                clearForm = false
              "
            >
              {{ $t('scUsers.button_add') }}
            </v-btn>
            <v-btn
              v-else
              depressed
              color="primary"
              height="52"
              v-bind="attrs"
              v-on="on"
              @click="
                dialogCreateUser = true
                clearForm = false
              "
            >
              {{ $t('scUsers.button_add') }}
            </v-btn>
          </template>
        </user-form-item>
      </template>
    </header-datatable>

    <v-container fluid class="pa-0 mb-10">
      <users-loading v-if="isLoading" />
      <v-row
        v-else
        class="ma-n5 pa-3"
      >
        <v-row v-if="data.items.length !== 0">
          <v-col
            v-for="(user, index) in data.items"
            :key="index"
            class="pa-5 col-lg-4 col-sm-6 col-12"
          >
            <user-card-item
              :user="user"
              :is-loading-form="isLoadingUserForm"
              :is-loading-form-clear-image="isLoadingUserFormClearImage"
              :index="index"
              :is-has-new="true"
              :dialog-update-user="dialogUpdateUser[index]"
              :dialog-delete-user="dialogDeleteUser[index]"
              @on-click-delete="deleteUser"
              @on-click-edit="editUser"
              @on-clear-image="clearImage($event, index)"
              @on-open-update-dialog="$set(dialogUpdateUser, index, true)"
              @on-close-update-dialog="$set(dialogUpdateUser, index, false)"
              @on-open-delete-dialog="$set(dialogDeleteUser, index, true)"
              @on-close-delete-dialog="$set(dialogDeleteUser, index, false)"
            />
          </v-col>
        </v-row>

        <v-row v-else>
          <v-col class="justify-center align-center fill-height">
            <empty-placeholder
              hero="empty-placeholder.svg"
              :message-title="$t('scUsers.empty_placeholder_title')"
              :message-description="$t('scUsers.empty_placeholder_description')"
            />
          </v-col>
        </v-row>
      </v-row>
    </v-container>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getUsers({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserCardItem from '~/components/UserCardItem.vue'
import UsersLoading from '~/components/loading/UsersLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'ShipmentCompanyUsersPage',

  components: {
    UserCardItem,
    UsersLoading,
    EmptyPlaceholder,
    PaginationComponent
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    searchKey: '',
    page: 1,
    filterOptions: false,
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      },
      email: {
        label: 'Email',
        value: 'email'
      },
      phoneNumber: {
        label: 'Phone Number',
        value: 'phone_number'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateUser: false,
    dialogUpdateUser: [] as Array<Boolean>,
    dialogDeleteUser: [] as Array<Boolean>,
    clearForm: false
  }),

  computed: {
    getScId () {
      return this.$auth.$state.user.data.shipment_company_id
    },

    data () {
      return this.$store.getters['users/data']
    },

    isLoading () {
      return this.$store.getters['users/isLoading']
    },

    isLoadingUserForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingUserFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', this.$t('scUsers.page_title'))
    this.getUsers({
      page: this.$route.query?.page as string
    })
  },

  methods: {
    clearImage (id: string, index: number) {
      this.$store.dispatch('users/removeAvatar', id)
        .then(() => {
          this.getUsers({})
        })
        .then(() => {
          this.$set(this.dialogUpdateUser, index, true)
        })
    },
    getUsers ({ page = '', searchKey = '', filter = { sortColumn: 'name', sortType: 'asc' } }) {
      this.searchKey = searchKey

      this.$store.dispatch('users/getItems', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterKeys: this.$auth.$state.user.data.shipment_company_id,
        role: this.$auth.$state.user.data.role,
        page
      })
    },

    async createUser (value: any) {
      const response = await this.$store.dispatch('users/createItem', {
        value,
        selectedId: this.$auth.$state.user.data.shipment_company_id,
        role: this.$auth.$state.user.data.role
      })
      if (response) {
        this.dialogCreateUser = false
        this.clearForm = true
        this.getUsers({
          page: this.$route.query?.page as string
        })
      }
    },
    async editUser (value: any, i: any) {
      const response = await this.$store.dispatch('users/editItem', {
        value,
        selectedId: this.$auth.$state.user.data.shipment_company_id,
        role: this.$auth.$state.user.data.role
      })

      if (response) {
        this.$set(this.dialogUpdateUser, i, false)
        this.getUsers({
          page: this.$route.query?.page as string
        })
      }
    },
    async deleteUser (id: any, i: any) {
      const response = await this.$store.dispatch('users/deleteItem', {
        id,
        selectedId: this.$auth.$state.user.data.shipment_company_id,
        role: this.$auth.$state.user.data.role
      })

      if (response) {
        this.$set(this.dialogDeleteUser, i, false)
        this.getUsers({
          page: this.$route.query?.page as string
        })
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: .28s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}

.custom-btn:hover .custom-icon{
  color: white !important;
}
</style>

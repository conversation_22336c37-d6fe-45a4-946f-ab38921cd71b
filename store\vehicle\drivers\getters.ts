import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleDriversState } from './state'

export const getters: GetterTree<VehicleDriversState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataFilter (state) {
    return {
      items: state.itemFilters,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export default getters

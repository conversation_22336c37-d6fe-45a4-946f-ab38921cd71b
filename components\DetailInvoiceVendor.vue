<template>
  <v-container fluid class="white rounded pa-10">
    <v-container fluid class="mb-5 pa-0 d-flex justify-space-between align-start">
      <div>
        <p class="caption text-secondary mb-2">
          {{ $t('lspHistoryShipment.invoice_number') }}
        </p>
        <h3>{{ dataInvoice?.invoice?.shipment?.identity }}</h3>
      </div>

      <div class="text-right">
        <p class="body-1 mb-5">
          {{ $moment(dataInvoice?.invoice?.shipment?.created_at).format('DD-MM-YYYY') }}
        </p>
        <div v-show="$vuetify.breakpoint.smAndUp">
          <slot name="download-invoice" />
        </div>
      </div>
    </v-container>

    <div class="row pa-3">
      <div class="column mr-0 mr-md-10">
        <p class="subtitle-1 mb-2">
          {{ $t('lspHistoryShipment.from') }}
        </p>
        <div class="d-flex align-center">
          <image-component
            :image="dataInvoice?.invoice?.shipment?.vendor?.logo_url"
            min-width="60"
            max-width="60"
            class="mr-5"
          />
          <h4>{{ dataInvoice?.invoice?.shipment?.vendor?.name }}</h4>
        </div>
      </div>

      <div class="column ml-0 ml-md-10 mt-5 mt-sm-0">
        <p class="subtitle-1 mb-2">
          {{ $t('lspHistoryShipment.to') }}
        </p>
        <div class="d-flex align-center">
          <image-component
            :image="dataInvoice?.invoice?.shipment?.logistics_service_provider?.logo_url"
            min-width="60"
            max-width="60"
            class="mr-5"
          />
          <h4>{{ dataInvoice?.invoice?.shipment?.logistics_service_provider?.name }}</h4>
        </div>
      </div>
    </div>

    <v-divider class="my-5" />

    <p class="subtitle-1 mb-5">
      Detail Order Shipment
    </p>

    <div class="mb-5 d-flex">
      <div class="mr-10">
        <p class="body-1 mb-1">
          Volume:
        </p>
        <h1>{{ dataInvoice?.invoice?.shipment?.total_volume }} CBM</h1>
      </div>

      <div class="ml-10">
        <p class="body-1 mb-1">
          {{ $t('lspHistoryShipment.total_weight') }}:
        </p>
        <h1>{{ formatNumber(dataInvoice?.invoice?.shipment?.total_weight) }} KG</h1>
      </div>
    </div>

    <div class="d-flex">
      <div>
        <p class="caption ma-0">
          {{ $t('lspHistoryShipment.total_length') }}:
        </p>
        <p class="subtitle-1 ma-0">
          {{ dataInvoice?.invoice?.shipment?.total_dimension_length }} CM
        </p>
      </div>
      <div class="mx-10">
        <p class="caption ma-0">
          {{ $t('lspHistoryShipment.total_width') }}:
        </p>
        <p class="subtitle-1 ma-0">
          {{ dataInvoice?.invoice?.shipment?.total_dimension_width }} CM
        </p>
      </div>
      <div>
        <p class="caption ma-0">
          {{ $t('lspHistoryShipment.total_height') }}:
        </p>
        <p class="subtitle-1 ma-0">
          {{ dataInvoice?.invoice?.shipment?.total_dimension_height }} CM
        </p>
      </div>
    </div>

    <v-divider class="my-5" />

    <p class="subtitle-1 mb-5">
      Detail Vendor
    </p>

    <h3 class="mb-5">
      {{ dataInvoice?.invoice?.shipment?.vendor?.name }}
    </h3>

    <v-container
      v-for="invoiceDetail in dataInvoice?.invoice_details"
      :key="invoiceDetail.id"
      fluid
      class="pa-0"
    >
      <v-container fluid class="pa-0 d-flex justify-space-between">
        <div class="d-flex">
          <div class="mr-10 d-flex flex-column align-center">
            <v-img
              :src="invoiceDetail.vehicle_detail?.vehicle?.photo_url"
              min-width="120"
              max-width="120"
              aspect-ratio="1"
              contain
              class="mb-2"
            />
            <p class="caption ma-0">
              {{ $t('lspHistoryShipment.distance') }}:
            </p>
            <p class="subtitle-1 ma-0">
              {{ invoiceDetail.total_odometer ?? 'Belum ada' }} KM
            </p>
          </div>

          <div>
            <h4 class="mb-1">
              {{ invoiceDetail.vehicle_detail?.vehicle?.name }}
            </h4>
            <p class="body-1 mb-5">
              {{ invoiceDetail.vehicle_detail?.plate_number }}
            </p>
            <p class="caption mb-1">
              {{ $t('lspHistoryShipment.features') }}:
            </p>
            <div class="d-flex">
              <v-chip
                v-for="vehicleFeature in invoiceDetail.vehicle_detail?.vehicle?.vehicle_features"
                :key="vehicleFeature.id"
                class="text-wrap"
                label
                outlined
              >
                {{ vehicleFeature.name }}
              </v-chip>
            </div>
          </div>
        </div>

        <div v-show="$vuetify.breakpoint.mdAndUp">
          <slot :invoice-detail="invoiceDetail" />
        </div>
      </v-container>
      <div v-show="$vuetify.breakpoint.smAndDown" class="text-right mt-4">
        <slot :invoice-detail="invoiceDetail" />
      </div>
      <v-divider class="my-5" />
    </v-container>

    <slot name="additional-fee" />

    <v-divider class="my-6" />

    <v-row class="pa-0 d-flex align-start align-sm-center flex-column flex-sm-row justify-space-between">
      <v-col class="pb-0">
        <p class="body-1 ma-0" style="color:red;">
          {{ $t('lspHistoryShipment.the_total_amount_that_lsp_must_be_paid') }}
        </p>
      </v-col>

      <v-col class="pt-0">
        <h3 class="text-primary text-right">
          {{ totalCost ?? 0 | toCurrency }}
        </h3>
      </v-col>
    </v-row>

    <div v-if="isHasAction" class="d-flex mt-10">
      <select-summary-vendor-send-invoice-dialog
        :dialog="showSummary"
        :total-cost-dialog="totalCost"
        @on-close-dialog="showSummary = false"
        @on-send="showSummary = true"
      >
        <template #activator="{ on }">
          <v-btn
            x-large
            color="primary"
            elevation="0"
            class="text-capitalize mr-6"
            :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
            v-on="on"
            @click="showSummary = true"
          >
            {{ $t('vendorInvoiceOrder.send_invoice') }}
          </v-btn>
        </template>
      </select-summary-vendor-send-invoice-dialog>
      <v-btn
        x-large
        outlined
        color="primary"
        :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
        class="text-capitalize"
      >
        {{ $t('vendorInvoiceOrder.cancel') }}
      </v-btn>
    </div>

    <div class="d-flex justify-center align-center mt-4">
      <div v-show="$vuetify.breakpoint.xs">
        <slot name="download-invoice" />
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import { InvoiceOrder } from '~/types/invoice'
import SelectSummaryVendorSendInvoiceDialog from '~/components/vendor/SelectSummaryVendorSendInvoiceDialog.vue'
import { formatNumber } from '../utils/functions'

export default Vue.extend({
  name: 'DetailInvoiceVendor',
  methods: { formatNumber },

  components: {
    ImageComponent,
    SelectSummaryVendorSendInvoiceDialog
  },

  props: {
    dataInvoice: {
      type: Object as () => InvoiceOrder | null,
      default: null
    },
    totalCost: {
      type: Number || null,
      default: null
    },
    isHasAction: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    showSummary: false
  })
})
</script>

<style scoped lang="scss">
.v-chip {
  height: auto !important;
}

.v-chip .v-chip__content {
  max-width: 100%;
  height: auto;
  min-height: 32px;
  white-space: pre-wrap;
}
</style>

<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 pb-10"
  >
    <div>
      <div v-if="checkedOrders.length > 0" class="d-flex justify-center align-center">
        <div class="d-flex flex-column flex-md-row flex-lg-row white rounded pa-md-5 pa-5 align-md-center align-lg-center" style="position:fixed; z-index: 1; bottom: 0; box-shadow: 0 10px 25px 0px rgb(0 0 0 / 0.25);">
          <div class="px-md-5 px-lg-5 px-0">
            <h4>
              {{ $t('lspCreateShipment.calculation') }}
            </h4>
            <h5>
              {{ $t('lspCreateShipment.sub_order_your_choice') }}
            </h5>
          </div>

          <v-divider vertical />

          <div class="px-md-5 px-lg-5 px-0 py-2">
            <div class="d-flex justify-space-between">
              <!-- <div class="pr-md-8">
                <h5 class="font-weight-light">
                  Volume :
                </h5>
                <h2>
                  {{ +(calculationOrder.totalVolume).toFixed(2) }} CBM
                </h2>
              </div> -->
              <div>
                <h5 class="font-weight-light">
                  {{ $t('lspCreateShipment.total_weight') }} :
                </h5>
                <h2>
                  {{ formatNumber(+(calculationOrder.totalWeight).toFixed(2)) }} KG
                </h2>
              </div>
            </div>

            <!-- <div class="d-flex justify-space-between">
              <div class="d-flex flex-column">
                <a class="font-weight-light caption black--text">
                  {{ $t('lspCreateShipment.total_length') }}
                </a>
                <a class="font-weight-bold black--text">
                  {{ calculationOrder.totalLength }} CM
                </a>
              </div>
              <div class="d-flex flex-column mx-3">
                <a class="font-weight-light caption black--text">
                  {{ $t('lspCreateShipment.total_width') }}
                </a>
                <a class="font-weight-bold black--text">
                  {{ calculationOrder.totalWidth }} CM
                </a>
              </div>
              <div class="d-flex flex-column">
                <a class="font-weight-light caption black--text">
                  {{ $t('lspCreateShipment.total_height') }}
                </a>
                <a class="font-weight-bold black--text">
                  {{ calculationOrder.totalHeight }} CM
                </a>
              </div>
            </div> -->
          </div>

          <v-divider vertical />

          <v-btn
            v-if="tab===0"
            color="primary"
            class="mx-md-5 mx-lg-5 mx-0 my-2"
            depressed
            @click="goToSelectVehicle"
          >
            Select Transporter
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>

          <h5 v-if="tab === 1" class="mt-5">
            {{ selectedShipment?.vendor?.name }}
          </h5>

          <vehicles-detail-shipment v-if="tab === 1 && selectedShipment" :shipment="selectedShipment" :selected-track-id="selectedTrackId" @on-track-selected="selectedRoutes = $event.routes" />
        </div>
      </div>
      <v-row>
        <v-col>
          <v-responsive :aspect-ratio="setAspectRatio" class="mb-lg-0 mb-10 pa-0 rounded" style="z-index: 0">
            <custom-map
              :zoom="zoomMap"
              :latitude="centerMap?.lat"
              :longitude="centerMap?.lng"
            >
              <template #marker>
                <div
                  v-for="suborder in selectedOrder?.suborders"
                  :key="suborder.id + 'marker'"
                >
                  <l-marker
                    v-if="suborder.pickup_drop_off_location_point"
                    :lat-lng="[
                      Number(suborder.pickup_drop_off_location_point?.latitude ?? 0),
                      Number(suborder.pickup_drop_off_location_point?.longitude ?? 0)
                    ]"
                  >
                    <l-icon
                      v-if="suborder.type === 'PICKUP'"
                      :icon-anchor="[20, 30]"
                    >
                      <v-icon
                        size="40"
                        color="info"
                      >
                        mdi-map-marker
                      </v-icon>
                    </l-icon>

                    <l-icon
                      v-else
                      :icon-anchor="[20, 30]"
                    >
                      <v-icon
                        size="40"
                        color="success"
                      >
                        mdi-map-marker
                      </v-icon>
                    </l-icon>

                    <l-popup ref="popup">
                      <div>
                        <p
                          class="subtitle-3 mb-1"
                          :style="suborder.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                        >
                          {{ suborder.type }}
                        </p>
                        <p class="caption ma-0 text--primary">
                          {{ suborder.pickup_drop_off_location_point.name }}
                        </p>
                      </div>
                    </l-popup>
                  </l-marker>
                </div>
              </template>
            </custom-map>
          </v-responsive>
          <v-col class="d-md-flex pa-0 mt-5">
            <div class="col-sm-12 col-md-4 pa-0">
              <div class="white rounded pa-md-10 pa-5 mr-2 mb-5">
                <div class="d-flex flex-column flex-xl-row justify-space-between mb-2">
                  <h4 class="mb-5">
                    <v-icon
                      color="black"
                      class="mr-5"
                    >
                      mdi-text-box-multiple
                    </v-icon>{{ $t('lspCreateShipment.request_order_shipment') }}
                  </h4>

                  <select-shipping-company-dialog :dialog="showSelectCustomer" @on-close-dialog="showSelectCustomer = false">
                    <template #activator="{ on }">
                      <v-btn
                        class="ml-xl-5"
                        outlined
                        color="primary my-2"
                        v-on="on"
                        @click="showSelectCustomer = true"
                      >
                        <v-icon
                          class="mr-5"
                        >
                          mdi-plus
                        </v-icon>
                        ORDER
                      </v-btn>
                    </template>
                  </select-shipping-company-dialog>
                </div>

                <v-divider />

                <v-tabs v-model="tab" class="mb-5 mt-5">
                  <v-tab>{{ $t('lspCreateShipment.request') }}</v-tab>
                  <!-- <v-tab>{{ $t('lspCreateShipment.rejected') }}</v-tab> -->
                </v-tabs>

                <v-tabs-items v-model="tab">
                  <v-tab-item>
                    <v-text-field
                      outlined
                      :label="$t('general.search')"
                      append-icon="mdi-magnify"
                      hide-details
                      class="mb-5 mt-2"
                      @change="searchOrders($event)"
                    />
                    <div v-if="isLoadingOrder">
                      <v-sheet>
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                      </v-sheet>
                    </div>

                    <div v-else>
                      <v-list v-if="isOrdersExist" class="pa-0">
                        <v-list-item-group color="primary">
                          <v-list-item
                            v-for="order in orders.items"
                            :key="order.id"
                            class="pa-5 my-3 rounded v-sheet--outlined d-flex"
                            :disabled="isLoadingDetailOrder"
                            :input-value="selectedOrder?.id === order.id"
                            @click="getDetailOrder(order)"
                          >
                            <v-list-item-action>
                              <v-checkbox
                                :input-value="order.is_selected"
                                @change="
                                  $store.commit('order/SET_SELECTED_ORDER_ITEM', order);
                                  if (order.is_selected) {
                                    $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', order.shipment_company)
                                  } else {
                                    $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', null)
                                  }
                                "
                              />
                            </v-list-item-action>
                            <v-list-item-content class="pa-0">
                              <v-list-item-title class="pa-0 d-flex flex-column text-wrap">
                                <div>
                                  {{ order.shipment_company?.name }}
                                  <span class="pl-2 red--text">{{ checkNew(order.created_at) }}</span>
                                </div>
                              </v-list-item-title>
                              <v-list-item-subtitle class="pa-0 d-flex flex-column">
                                {{ order.identity }}
                              </v-list-item-subtitle>
                            </v-list-item-content>
                            <v-list-item-action-text v-if="order.published_at">
                              {{ $moment(order.published_at).format('DD-MM-yyyy') }}
                            </v-list-item-action-text>
                          </v-list-item>
                        </v-list-item-group>
                      </v-list>

                      <div v-if="!isOrdersExist" class="d-flex flex-column align-center">
                        <h4 class="mb-5">
                          {{ $t('lspCreateShipment.no_active_order_title') }}
                        </h4>
                        <p class="ma-0 text-secondary body-1 text-center">
                          {{ $t('lspCreateShipment.no_active_order_text') }}
                        </p>
                      </div>
                    </div>
                  </v-tab-item>
                  <v-tab-item>
                    <v-text-field
                      outlined
                      label="Search"
                      append-icon="mdi-magnify"
                      hide-details
                      class="mb-5  mt-2"
                      @change="searchShipments($event)"
                    />

                    <div v-if="isLoadingShipment">
                      <v-sheet>
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                        <v-skeleton-loader class="mt-2" type="image" height="40" />
                      </v-sheet>
                    </div>

                    <div v-else>
                      <v-list v-if="isShipmentsExist" class="pa-0">
                        <v-list-item-group color="primary">
                          <v-list-item
                            v-for="shipment in shipments"
                            :key="shipment.id"
                            class="pa-5 mt-5 rounded v-sheet--outlined"
                            @click="selectedRejectedShipment = shipment; getDetailShipment(shipment.id)"
                          >
                            <v-list-item-content class="pa-0">
                              <v-list-item-title class="pa-0 d-flex flex-column">
                                {{ shipment.orders[0].identity }}
                              </v-list-item-title>
                              <v-list-item-subtitle class="pa-0 d-flex flex-column">
                                {{ shipment.vendor?.name }}
                              </v-list-item-subtitle>
                            </v-list-item-content>
                          </v-list-item>
                        </v-list-item-group>
                      </v-list>
                      <div v-else class="d-flex flex-column align-center">
                        <h4 class="mb-5">
                          {{ $t('lspCreateShipment.no_active_order_shipment_title') }}
                        </h4>
                        <p class="ma-0 text-secondary body-1 text-center">
                          {{ $t('lspCreateShipment.no_active_order_shipment_text') }}
                        </p>
                      </div>
                    </div>
                  </v-tab-item>
                </v-tabs-items>
              </div>
            </div>
            <v-container class="white rounded col-sm-12 col-md-8 d-flex ml-md-2 ml-sm-0 mt-5 mt-md-0 mt-sm-5 pa-md-10 pa-5 flex-column">
              <div class="d-flex justify-space-between align-center mb-5">
                <h4>
                  <v-icon
                    color="black"
                    class="mr-5"
                  >
                    mdi-text-box-multiple
                  </v-icon>Detail Order Shipment
                </h4>
                <div v-if="checkedOrders.length > 0">
                  <v-btn
                    outlined
                    elevation="0"
                    color="primary"
                    class="pa-2 ma-2"
                    @click="
                      $router.push(localePath(`/logistic-service-provider/order-shipment/create-shipment/create-order?id=${checkedOrders[0]?.id}`))
                    "
                  >
                    <v-icon>
                      mdi-pencil
                    </v-icon>
                    <p class="pa-2 mt-3">
                      Edit Order
                    </p>
                  </v-btn>
                </div>
                <!-- <reject-order-shipment-dialog
                  v-if="tab === 0 && isSelectedOrderExist"
                  :btn-name="`${ $t('lspCreateShipment.reject')}`"
                  :order="selectedOrder"
                  :dialog-title="`${ $t('lspCreateShipment.dialog_reject_title')}`"
                  :is-loading-detail="isLoadingDetailOrder"
                  :dialog="rejectOrderDialog"
                  @on-dialog-open="rejectOrderDialog = true"
                  @on-reject-order="cancelOrder"
                  @on-dialog-close="rejectOrderDialog = false"
                /> -->
                <h4 v-if="tab === 1">
                  {{ selectedShipment?.identity }}
                </h4>
              </div>
              <v-divider />

              <detail-order-shipment
                v-if="tab===0 && selectedOrder"
                :is-selected-order-exist="isSelectedOrderExist"
                :selected-order="selectedOrder"
                :is-loading="isLoadingDetailOrder"
              />

              <timeline-detail-shipment v-if="tab === 1 && selectedRejectedShipment" :selected-routes="selectedRoutes" />
            </v-container>
          </v-col>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import { Route, Shipment } from '~/types/shipment'
import { toastError } from '~/utils/toasts'
import {
  colorType,
  defaultLat,
  defaultLng,
  checkIsNew,
  zoom,
  generateCenterLatLng,
  formatNumber
} from '~/utils/functions'
import VehiclesDetailShipment from '~/components/detail-shipment/VehiclesDetailShipment.vue'
import TimelineDetailShipment from '~/components/detail-shipment/TimelineDetailShipment.vue'
import RejectOrderShipmentDialog from '~/components/RejectOrderShipmentDialog.vue'
import { Order, SubOrder } from '~/types/product'
import SelectShippingCompanyDialog from '~/components/SelectShippingCompanyDialog.vue'

export default Vue.extend({
  name: 'LogisticServiceProviderCreateOrderPage',

  components: {
    TimelineDetailShipment,
    VehiclesDetailShipment,
    CustomMap,
    RejectOrderShipmentDialog,
    SelectShippingCompanyDialog
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    tab: 0,
    selectedTrackId: '' as string | undefined,
    selectedRoutes: [] as Route[] | undefined,
    rejectOrderDialog: false,
    selectedRejectedShipment: null,
    showSelectCustomer: false
  }),

  computed: {
    markers (): { lat: number, lng: number }[] {
      if (!this.selectedOrder) { return [] }

      const subOrders: SubOrder[] = this.selectedOrder.suborders as SubOrder[]

      return subOrders.map(suborder => ({
        lat: Number(suborder.pickup_drop_off_location_point.latitude),
        lng: Number(suborder.pickup_drop_off_location_point.longitude)
      }))
    },

    centerMap (): { lat: number, lng: number } {
      if (this.markers.length === 0) {
        return { lat: defaultLat, lng: defaultLng }
      }
      const center = generateCenterLatLng(this.markers)
      return { lat: center.lat, lng: center.lng }
    },

    zoomMap (): number {
      if (this.markers.length === 0) { return 4 }
      return zoom(this.markers)
    },

    isLoadingDetailOrder (): boolean {
      return this.$store.getters['order/isLoadingDetail']
    },

    isLoadingShipment (): boolean {
      return this.$store.getters['shipment/isLoading']
    },

    isLoadingOrder (): boolean {
      return this.$store.getters['order/isLoading']
    },

    isSelectedOrderExist (): boolean {
      return this.selectedOrder !== null
    },

    isOrdersExist (): boolean {
      return this.orders.items.length !== 0
    },

    isShipmentsExist (): boolean {
      return this.shipments?.length !== 0
    },
    tabIndex (): number {
      return this.$store.getters['tab/index']
    },
    setAspectRatio (): number {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 5 / 2
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },

    orders (): { items: Order[], page: number, totalPage: number } {
      return this.$store.getters['order/data']
    },

    selectedOrder (): Order | null {
      return this.$store.getters['order/selectedOrder'] as Order | null
    },

    checkedOrders (): Order[] {
      return this.$store.getters['order/checkedOrders']
    },

    selectedShipment (): Shipment | null {
      return this.$store.getters['shipment/detailShipment'] as Shipment | null
    },

    shipments (): Shipment[] {
      return this.$store.getters['shipment/dataShipment'].items
    },

    calculationOrder (): {
      totalLength: number,
      totalWidth: number,
      totalHeight: number,
      totalVolume: number,
      totalWeight: number
      } {
      return this.$store.getters['order/calculationOrder']
    }

  },

  watch: {
    tabIndex: {
      handler () {
        this.tab = this.tabIndex
        if (this.tab === 1 && !this.isLoadingShipment) {
          // this.getShipments()
        }
      },
      immediate: true
    },

    selectedShipment () {
      const tracks = this.selectedShipment?.tracks || []

      if (this.selectedShipment && tracks.length > 0) {
        this.selectedTrackId = tracks.at(0)?.id
        this.selectedRoutes = tracks.at(0)?.routes
      }
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')

    this.$store.commit('order/SET_SELECTED_ORDER', null)

    this.getOrders({
      filterColumns: 'status,shipments',
      filterKeys: 'PUBLISHED,<_null>'
    })

    this.$store.dispatch('vehicle/clearVehicles')

    // this.getShipments()
  },

  methods: {
    formatNumber,
    searchOrders (key: string) {
      this.getOrders({
        searchColumns: 'identity,shipment_company.name' as any,
        searchKey: key,
        filterColumns: 'status,shipments',
        filterKeys: 'PUBLISHED,<_null>'
      })
    },

    getOrders ({
      searchColumns = '',
      searchKey = '',
      filterColumns = '',
      filterKeys = '',
      page = -1,
      perPage = 10
    }) {
      this.$store.dispatch('order/getOrders', {
        searchColumns,
        searchKey,
        filterColumns,
        filterKeys,
        page,
        perPage
      })
    },

    colorType (type: string):string {
      return colorType(type)
    },

    // searchShipments (key: string) {
    //   this.getShipments('identity,vendor.name' as any, key)
    // },

    // getShipments (searchColumns = '', searchKey = '', filterColumns = 'status', filterKeys = 'REJECT', page = 1, perPage = 10) {
    //   this.$store.dispatch('shipment/getItems', {
    //     searchColumns,
    //     searchKey,
    //     filterColumns,
    //     filterKeys,
    //     page,
    //     perPage
    //   })
    // },

    getDetailOrder (order : Order | null) {
      if (order?.id === this.selectedOrder?.id) { return null }
      this.$store.dispatch('order/getDetailOrder', {
        id: order?.id
      })
    },

    getDetailShipment (id: string) {
      this.$store.dispatch('shipment/getItemDetail', { id })
    },

    async cancelOrder (order : Order | null) {
      await this.$store.dispatch('order/cancelOrder', {
        id: order?.id
      })

      this.$store.commit('order/SET_SELECTED_ORDER', null)

      this.getOrders({
        filterColumns: 'status,shipments',
        filterKeys: 'PUBLISHED,<_null>'
      })
      // this.getShipments()

      this.rejectOrderDialog = false
    },

    goToSelectVehicle () {
      if (this.checkedOrders.length === 0) {
        toastError('Please select order', this)
        return
      }
      this.$router.push(this.localePath('/logistic-service-provider/order-shipment/create-shipment/select-vehicle'))
    },

    checkNew (date: string) {
      return checkIsNew(date)
    },
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

//* {
//  border: 1px solid;
//}
</style>

<template>
  <create-order-page-component :id="$auth.$state.user.data.shipment_company_id" :title="$t('scCreateOrder.page_title')">
    <template #dialog-order-number>
      <add-order-number-dialog
        :id="$auth.$state.user.data.shipment_company_id"
        :dialog="showDialogOrderNumber"
        @on-close-dialog="showDialogOrderNumber = false"
      />
    </template>
  </create-order-page-component>
</template>

<script lang="ts">
import Vue from 'vue'
import CreateOrderPageComponent from '~/components/create-order-page-component/index.vue'
import AddOrderNumberDialog from '~/components/AddOrderNumberDialog.vue'

export default Vue.extend({
  name: 'CreateOrderPage',

  components: {
    CreateOrderPageComponent,
    AddOrderNumberDialog
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    showDialogOrderNumber: true
  })

})
</script>

<style scoped lang="scss">
</style>

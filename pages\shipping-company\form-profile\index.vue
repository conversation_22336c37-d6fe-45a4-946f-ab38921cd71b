<template>
  <v-container fluid class="pa-0 py-md-10 d-flex align-center justify-center" style="min-height: 100vh; position: relative">
    <v-img
      v-if="$vuetify.breakpoint.mdAndUp"
      :src="require(`~/assets/images/form-profile-bg-sc.png`)"
      max-width="200"
      style="position: absolute; top: 0; left: 0"
    />
    <v-img
      v-if="$vuetify.breakpoint.mdAndUp"
      :src="require(`~/assets/images/form-profile-bg-sc.png`)"
      max-width="200"
      style="position: absolute; bottom: 0; right: 0; rotate: 180deg"
    />

    <v-card class="pa-10 col-12 col-md-6 col-lg-5">
      <v-btn
        text
        x-large
        color="primary"
        class="text-capitalize mb-5"
        :loading="isLoadingLogout"
        @click="logout"
      >
        <v-icon size="20" color="primary" class="mr-2">
          mdi-logout
        </v-icon>
        <p class="subtitle-1 text-primary ma-0">
          Logout
        </p>
      </v-btn>

      <div class="mb-10">
        <h1>Your Profile</h1>
        <p class="body-1 ma-0">
          Insert your profile Product Owner information here.
        </p>
      </div>

      <v-form class="mb-10">
        <image-select
          label="Logo"
          :image-url="dataSc?.logo_url"
          @on-image-selected="form.logo = $event"
        />

        <v-text-field
          v-model="form.name"
          :loading="isLoadingSc"
          outlined
          hide-details
          label="Company Name"
          class="mb-5"
        />
        <v-textarea
          v-model="form.address"
          :loading="isLoadingSc"
          outlined
          hide-details
          no-resize
          label="Address"
        />
      </v-form>

      <v-btn
        :loading="isLoadingFormSc"
        block
        x-large
        color="primary"
        class="text-capitalize"
        @click="saveProfile"
      >
        Save Profile
      </v-btn>
    </v-card>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageSelect from '~/components/ImageSelect.vue'

export default Vue.extend({
  name: 'ShipmentCompanyFormProfilePage',

  components: { ImageSelect },

  middleware: 'auth',

  data: () => ({
    form: {
      logo: null,
      name: '',
      address: ''
    },
    isLoadingLogout: false
  }),

  computed: {
    dataSc () {
      return this.$store.getters['profile/shipping-company/shippingCompany']
    },
    isLoadingSc () {
      return this.$store.getters['profile/shipping-company/isLoading']
    },
    isLoadingFormSc () {
      return this.$store.getters['profile/shipping-company/isLoadingForm']
    }
  },

  watch: {
    dataSc () {
      this.form.name = this.dataSc?.name
      this.form.address = this.dataSc?.address
    }
  },

  mounted () {
    this.$store.dispatch('profile/shipping-company/getData')
  },

  methods: {
    onImageSelected (value: any) {
      this.form.logo = value
    },
    saveProfile () {
      const domainLsp = this.$store.getters.domain

      this.$store.dispatch('profile/shipping-company/saveData', {
        name: this.form.name,
        address: this.form.address ?? '',
        logo: this.form.logo,
        domainLsp
      })
    },
    async logout () {
      this.isLoadingLogout = true
      await this.$auth.logout()
      this.isLoadingLogout = false
    }
  }
})
</script>

<style lang="scss" scoped> </style>

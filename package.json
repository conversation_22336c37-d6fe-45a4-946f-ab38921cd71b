{"name": "lsi-frontend", "version": "1.3.276", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.ts,.vue\" --ignore-path .gitignore .", "lint": "yarn lint:js", "lintfix": "yarn lint:js --fix", "test": "npx playwright test"}, "dependencies": {"@mdi/js": "^7.0.96", "@nuxtjs/auth-next": "5.0.0-1648802546.c9880dc", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/composition-api": "^0.33.0", "@nuxtjs/firebase": "^8.2.2", "@nuxtjs/i18n": "^7.3.0", "@nuxtjs/moment": "^1.6.1", "@nuxtjs/pwa": "^3.3.5", "chart.js": "^3.8.0", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-zoom": "^2.0.1", "core-js": "^3.19.3", "file-saver": "^2.0.5", "firebase": "^9.17.1", "leaflet": "^1.9.4", "leaflet-polylinedecorator": "^1.6.0", "leaflet-rotate": "^0.2.8", "leaflet.gridlayer.googlemutant": "^0.8.0", "moment-timezone": "^0.5.37", "nuxt": "^2.15.8", "nuxt-i18n": "^6.28.1", "nuxt-leaflet": "^0.0.27", "uuidv4": "^6.2.13", "vue": "^2.6.14", "vue-chartjs": "^4.0.0", "vue-lottie-ts": "^0.1.2", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "vue-toastification": "^1.7.14", "vue2-leaflet": "^2.7.1", "vue2-leaflet-googlemutant": "^2.0.0", "vue2-leaflet-rotatedmarker": "^1.0.9", "vue2-timepicker": "^1.1.6", "vuetify": "^2.6.1", "webpack": "^4.46.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz"}, "devDependencies": {"@babel/eslint-parser": "^7.16.3", "@nuxt/types": "^2.15.8", "@nuxt/typescript-build": "^2.1.0", "@nuxtjs/device": "^2.1.0", "@nuxtjs/eslint-config-typescript": "^8.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/vuetify": "^1.12.3", "@playwright/test": "^1.30.0", "@types/file-saver": "^2.0.5", "@types/lodash": "^4.17.16", "@vue/test-utils": "^1.3.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^27.4.4", "eslint": "^8.4.1", "eslint-plugin-nuxt": "^3.1.0", "eslint-plugin-vue": "^8.2.0", "sass": "^1.53.0", "sass-loader": "10", "ts-jest": "^27.1.1", "vue-jest": "^3.0.4"}}
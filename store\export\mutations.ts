import { MutationTree } from 'vuex'
import { ExportState } from './state'

export const mutations: MutationTree<ExportState> = {
  SET_ITEM_EXPORT_RESULT (state, response: any) {
    state.itemExportResult = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_IS_LOADING_EXPORT (state, isLoadingExport) {
    state.isLoading = isLoadingExport
  },

  SET_IS_LOADING (state, isLoadingDelete) {
    state.isLoadingDelete = isLoadingDelete
  }
}

export default mutations

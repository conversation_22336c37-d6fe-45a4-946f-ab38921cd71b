<template>
  <v-container fluid class="pa-0">
    <v-row class="ma-0 mx-n5 mb-10">
      <v-col class="col-12 col-lg-6 pa-0 px-5 mb-10 mb-lg-0 d-flex flex-column">
        <v-container fluid class="pa-0 mb-2 d-flex align-center justify-space-between">
          <v-checkbox
            v-model="isSelectedAll"
            :disabled="
              type === 'IMPORT' ? plateNumbers.length === 0 :
              type === 'CATEGORIZE' ? vehicleDetails.length === 0 :
              type === 'SYNC' ? vehiclesFromFms.length === 0 : true
            "
            label="Select All"
            hide-details
            class="ma-0"
          />

          <v-btn
            :disabled="!isSelectedAll"
            text
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="isSelectedAll = !isSelectedAll"
          >
            Unselect All
          </v-btn>
        </v-container>

        <v-container
          fluid
          class="pa-3 mb-6 rounded d-flex flex-column"
          style="border: 1px solid #CFCCCC; min-height: 320px !important; max-height: 320px !important;"
        >
          <v-autocomplete
            v-if="isSelectedCategory"
            v-model="selectedCategory"
            outlined
            hide-details
            small-chips
            large
            deletable-chips
            :items="categoryNames"
            label="Select Group"
            item-value="value"
            item-text="text"
            multiple
            dense
            class="mb-3"
          />
          <v-text-field
            v-model="searchKey"
            outlined
            label="Search Plate"
            append-icon="mdi-magnify"
            hide-details
            clearable
            class="mb-3"
          />
          <items-group-checkbox
            :items="reformattedPlateNumbers"
            :is-selected-all="isSelectedAll"
            :reset-selected-items="resetSelectedItems"
            @on-select-item="selectedPlateNumbers = $event; resetSelectedItems = false"
          />
        </v-container>

        <v-combobox
          v-model="selectedVehicle"
          outlined
          hide-details
          :items="vehicles?.map(item => {
            return { text: item.name, value: item.id }
          })"
          label="Select Vehicle"
          class="flex-grow-0"
        />
      </v-col>

      <v-col class="col-12 col-lg-6 pa-0 px-5 d-flex flex-column">
        <v-container fluid class="pa-0 mb-4 d-flex align-center justify-space-between">
          <h4>Vehicle Selected Category</h4>
          <v-btn
            text
            color="primary"
            class="subtitle-1
            text-capitalize"
            :disabled="appliedVehicle.length === 0"
            @click="deleteVehicleDetails()"
          >
            Delete All
          </v-btn>
        </v-container>

        <v-item-group v-model="selectedAppliedVehicle" class="d-flex mb-4">
          <v-row class="ma-n2 d-flex">
            <v-col v-for="item in appliedVehicleName" :key="item.id" class="pa-2 col-3">
              <v-item v-slot="{ active, toggle }">
                <v-btn
                  active-class="primary--text"
                  :input-value="active"
                  block
                  outlined
                  rounded
                  color="primary"
                  class="subtitle-1 text-capitalize"
                  @click="toggle"
                >
                  {{ item.label }}
                </v-btn>
              </v-item>
            </v-col>
          </v-row>
        </v-item-group>
        <v-container
          fluid
          class="pa-3 rounded overflow-y-auto"
          style="border: 1px solid #CFCCCC; min-height: 360px !important; max-height: 360px !important;"
        >
          <v-chip
            v-for="(item, index) in appliedVehicle.at(selectedAppliedVehicle)?.plate_numbers"
            :key="index"
            color="primary"
            text-color="white"
            class="mr-2 mb-2"
            close
            @click:close="deleteVehicleDetail(item.id)"
          >
            {{ item.value }}
          </v-chip>
        </v-container>
      </v-col>
    </v-row>

    <v-row class="ma-0 mx-n3">
      <v-col class="col-6 col-lg-auto pa-0 px-3">
        <v-btn
          block
          x-large
          depressed
          color="primary"
          class="subtitle-1 text-capitalize"
          @click="onClickApply"
        >
          {{ buttonLabel }}
        </v-btn>
      </v-col>

      <v-col class="col-6 col-lg-auto pa-0 px-3">
        <v-btn
          v-if="appliedVehicle.length > 0 && type === 'IMPORT'"
          block
          x-large
          outlined
          color="primary"
          class="subtitle-1 text-capitalize"
          @click="$router.back()"
        >
          Save & Exit
        </v-btn>

        <v-btn
          v-else
          block
          x-large
          outlined
          color="primary"
          class="subtitle-1 text-capitalize"
          @click="
            type === 'IMPORT' ? $router.back() : onClickCancel()"
        >
          Cancel
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { uuid } from 'uuidv4'
import { Vehicle, VehicleDetail, VehicleFromFms } from '~/types/vehicle'
import ItemsGroupCheckbox from '~/components/ItemsGroupCheckbox.vue'

interface Item {
  value: string | number | null
  text: string
  subtitle: string | null
}

export default Vue.extend({
  name: 'FormCategorizeVehicle',

  components: { ItemsGroupCheckbox },

  props: {
    type: {
      type: String,
      default: ''
    },
    importedPlateNumbers: {
      type: Array as () => string[],
      default: () => []
    },
    buttonLabel: {
      type: String,
      default: ''
    },
    vehicles: {
      type: Array as () => Vehicle[],
      default: () => []
    },
    isSelectedCategory: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isSelectedAll: false,
    resetSelectedItems: false as boolean,
    searchKey: '',
    selectedCategory: '',
    selectedVehicle: null as Item | null,
    selectedPlateNumbers: [] as Item[],
    appliedVehicle: [] as {
      vehicle_id: string,
      vehicle_name: string,
      plate_numbers: { id: string, value: string}[]
    }[],
    selectedAppliedVehicle: -1
  }),

  computed: {
    isLoadingVehicle (): boolean {
      return this.$store.getters['vehicle/isLoading']
    },

    appliedVehicleName (): { id: string, label: string }[] {
      return this.appliedVehicle.map((v) => {
        return { id: v.vehicle_id, label: v.vehicle_name }
      })
    },

    vehiclesFromFms (): Record<string, VehicleFromFms[]> {
      const vehicles = this.$store.getters['vendor/sync-vehicles/syncedVehicles'] as Record<string, VehicleFromFms[]>
      const selectedCategories = this.selectedCategory as any
      const filteredVehicles: Record<string, VehicleFromFms[]> = {}

      if (this.searchKey) {
        Object.entries(vehicles).forEach(([category, vehicleList]) => {
          const filteredList = vehicleList.filter(v => v.plate_number?.toLowerCase().includes(this.searchKey.toLowerCase()))
          if (filteredList.length > 0) {
            filteredVehicles[category] = filteredList
          }
        })
      } else {
        Object.assign(filteredVehicles, vehicles)
      }

      if (selectedCategories.length > 0) {
        Object.keys(filteredVehicles).forEach((category) => {
          if (!selectedCategories.includes(category)) {
            delete filteredVehicles[category]
          }
        })
      }

      return Object.keys(filteredVehicles).length > 0 ? filteredVehicles : vehicles
    },

    plateNumbers (): string[] {
      const plateNumbers = this.importedPlateNumbers as string[]

      if (this.searchKey) {
        return plateNumbers.filter(pn => pn.toLowerCase().includes(this.searchKey?.toLowerCase()))
      }

      return plateNumbers
    },

    vehicleDetails (): VehicleDetail[] {
      const vehicles = this.$store.getters['vehicle/details/data'].items as VehicleDetail[]

      if (this.searchKey) {
        return vehicles.filter(v => v.plate_number?.toLowerCase().includes(this.searchKey?.toLowerCase()))
      }

      return vehicles.filter(v => v.vehicle_id == null)
    },

    reformattedPlateNumbers (): Item[] {
      let items = [] as Item[] as any

      if (this.type === 'CATEGORIZE') {
        items = this.vehicleDetails?.map((item: VehicleDetail) => {
          return { value: item.id, text: item.plate_number, subtitle: item.driver?.user.name as string }
        })
      } else if (this.type === 'IMPORT') {
        items = this.plateNumbers?.map((item: string) => {
          return { value: item.replaceAll(' ', ''), text: item, subtitle: null }
        })
      } else if (this.type === 'SYNC') {
        items = Object.entries(this.vehiclesFromFms)?.flatMap(([category, vehicles]) => {
          const categoryItem = {
            value: 'isCategory',
            text: category,
            subtitle: '',
            length: vehicles.length
          }

          const vehicleItems = vehicles?.map((item: VehicleFromFms) => ({
            value: item.id,
            text: item.plate_number || '',
            subtitle: item.driver?.user.name || '',
            category: categoryItem.text
          }))

          return [categoryItem, ...vehicleItems]
        })
      }

      return items
    },

    categoryNames (): Item[] {
      const vehicles = this.$store.getters['vendor/sync-vehicles/syncedVehicles'] as Record<string, VehicleFromFms[]>
      return Object.keys(vehicles).map(category => ({
        value: `${category}`,
        text: category,
        subtitle: null
      }))
    }
  },

  methods: {
    onClickApply () {
      if (this.type === 'CATEGORIZE') {
        this.applyPlateNumbersCategorize()
      } else if (this.type === 'IMPORT') {
        this.applyPlateNumbersImport()
      } else if (this.type === 'SYNC') {
        this.applyPlateNumbersSync()
      }

      this.searchKey = ''
    },

    onClickCancel () {
      this.appliedVehicle = []
      this.isSelectedAll = false
      this.resetSelectedItems = true
      this.$emit('on-click-cancel')
    },

    async applyPlateNumbersCategorize () {
      const selectedVehicle = this.selectedVehicle as Item | null

      const response = await this.$store.dispatch('vehicle/addItem', {
        id: selectedVehicle?.value,
        vehicle_detail_ids: this.selectedPlateNumbers.map(item => item.value)
      })

      if (response) {
        const existedVehicle = this.appliedVehicle.find(v => v.vehicle_id === selectedVehicle?.value)

        if (existedVehicle) {
          existedVehicle.plate_numbers.push(...this.selectedPlateNumbers.map((item) => {
            return { id: item.value, value: item.text }
          }) as {id: string, value: string}[])
        } else {
          this.appliedVehicle.push({
            vehicle_id: selectedVehicle!.value as string,
            vehicle_name: selectedVehicle!.text,
            plate_numbers: this.selectedPlateNumbers.map((item) => {
              return { id: item.value, value: item.text }
            }) as { id: string, value: string }[]
          })
        }

        this.resetSelectedItems = true
        this.selectedAppliedVehicle = this.appliedVehicle.length - 1
        this.selectedPlateNumbers = []
        this.selectedVehicle = null

        this.$emit('on-success-add-vehicle-detail')
      }
    },

    async applyPlateNumbersImport () {
      const selectedVehicle = this.selectedVehicle as { value: string, text: string} | null

      const formValues = {
        vehicleId: selectedVehicle?.value,
        vehicleDetails: this.selectedPlateNumbers.map((item) => {
          return { plate_numbers: item.text }
        })
      }

      const response = await this.$store.dispatch('vehicle/details/createItem', { formValues })

      if (response) {
        const existedVehicle = this.appliedVehicle.find(v => v.vehicle_id === selectedVehicle?.value)

        if (existedVehicle) {
          existedVehicle.plate_numbers.push(...this.selectedPlateNumbers.map((item) => {
            return { id: item.value, value: item.text }
          }) as {id: string, value: string}[])
        } else {
          this.appliedVehicle.push({
            vehicle_id: selectedVehicle!.value,
            vehicle_name: selectedVehicle!.text,
            plate_numbers: response.data.map((item: VehicleDetail) => {
              return { id: item.id, value: item.plate_number }
            }) as { id: string, value: string }[]
          })
        }

        this.$emit('on-success-add-vehicle-detail', this.selectedPlateNumbers)

        this.resetSelectedItems = true
        this.selectedAppliedVehicle = this.appliedVehicle.length - 1
        this.selectedPlateNumbers = []
        this.selectedVehicle = null
      }
    },

    async applyPlateNumbersSync () {
      const selectedVehicle = this.selectedVehicle as { value: string, text: string } | null

      const formValues = {
        vehicleId: selectedVehicle?.value,
        vehicleDetails: this.selectedPlateNumbers
          .filter(item => item.value !== 'isCategory')
          .map((item) => {
            return {
              id: uuid(),
              plate_numbers: item.text,
              driver_ids: null,
              fms_identity: item.value
            }
          })
      }

      const response = await this.$store.dispatch('vehicle/details/createItem', { formValues })

      if (response) {
        const existedVehicle = this.appliedVehicle.find(v => v.vehicle_id === selectedVehicle?.value)

        if (existedVehicle) {
          existedVehicle.plate_numbers.push(...formValues.vehicleDetails.map((item) => {
            return { id: item.id, value: item.plate_numbers }
          }) as {id: string, value: string}[])
        } else {
          this.appliedVehicle.push({
            vehicle_id: selectedVehicle!.value,
            vehicle_name: selectedVehicle!.text,
            plate_numbers: formValues.vehicleDetails.map((item) => {
              return { id: item.id, value: item.plate_numbers }
            }) as { id: string, value: string }[]
          })
        }

        this.resetSelectedItems = true
        this.selectedAppliedVehicle = this.appliedVehicle.length - 1
        this.selectedPlateNumbers = []
        this.selectedVehicle = null
      }
    },

    async deleteVehicleDetail (vehicleDetailId: string) {
      const response = await this.$store.dispatch('vehicle/details/deleteItem', {
        vehicleDetailId
      })

      if (response) {
        const vehicleDetailIndex = this.appliedVehicle[this.selectedAppliedVehicle].plate_numbers
          .findIndex(v => v.id === vehicleDetailId)

        this.appliedVehicle[this.selectedAppliedVehicle].plate_numbers.splice(vehicleDetailIndex, 1)

        if (this.appliedVehicle[this.selectedAppliedVehicle].plate_numbers.length === 0) {
          this.appliedVehicle.splice(this.selectedAppliedVehicle, 1)
          this.selectedAppliedVehicle = -1
        }

        this.$emit('on-success-delete-vehicle-detail')
      }
    },

    async deleteVehicleDetails () {
      const vehicleDetailIds = this.appliedVehicle.at(this.selectedAppliedVehicle)?.plate_numbers.map((pn) => {
        return pn.id
      }) as string[]

      const response = await this.$store.dispatch('vehicle/details/deleteItems', {
        vehicleDetailIds
      })

      if (response) {
        this.appliedVehicle.splice(this.selectedAppliedVehicle, 1)
      }
    }
  }
})
</script>

<style scoped lang="scss"> </style>

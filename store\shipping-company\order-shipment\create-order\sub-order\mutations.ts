import { MutationTree } from 'vuex'
import { ShipmentCompanyCreateOrderSubOrderState } from './state'

export const mutations: MutationTree<ShipmentCompanyCreateOrderSubOrderState> =
  {
    SET_ITEMS (state, items: any) {
      state.itemsPickUp = []
      state.itemsDropOff = []

      for (let i = 0; i < items.length; i++) {
        if (items[i].type === 'PICKUP') {
          state.itemsPickUp.push(items[i])
        } else {
          state.itemsDropOff.push(items[i])
        }
      }
    },

    SET_PICK_UP_ITEMS (state, items: any) {
      state.itemsPickUp = items
    },

    SET_DROP_OFF_ITEMS (state, items: any) {
      state.itemsDropOff = items
    },

    SET_IS_LOADING (state, isLoading: any) {
      state.isLoading = isLoading
    },

    SET_IS_LOADING_FORM (state, isLoadingForm: any) {
      state.isLoadingForm = isLoadingForm
    }
  }

export default mutations

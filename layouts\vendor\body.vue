<template>
  <v-app>
    <network-error-overlay v-if="isNetworkError" />
    <side-bar
      v-if="$vuetify.breakpoint.smAndUp"
      :logo="
        $auth.$state.user?.data.vendor?.logo_url || require(`~/assets/images/logo-lsi.png`)
      "
      :title="$auth.$state.user?.data.vendor?.name"
    >
      <template #navigation-list>
        <navigation-list />
      </template>
    </side-bar>

    <head-bar
      v-if="$vuetify.breakpoint.smAndUp"
      account-setting-url="/profile/account-setting"
    />

    <mobile-head-bar
      v-else
      account-setting-url="/profile/account-setting"
      :logo="require(`~/assets/images/logo-lsi.png`)"
    >
      <template #navigation-list>
        <navigation-list />
      </template>
    </mobile-head-bar>

    <v-main class="bg-color">
      <nuxt />
    </v-main>
  </v-app>
</template>

<script lang="ts">
import Vue from 'vue'
import SideBar from '~/components/navigation-drawer/SideBar.vue'
import HeadBar from '~/components/head-bar/HeadBar.vue'
import MobileHeadBar from '~/components/head-bar/MobileHeadBar.vue'
import NavigationList from '~/components/vendor/NavigationList.vue'
import { toastNetworkSuccess } from '~/utils/functions'

export default Vue.extend({
  name: 'LayoutScBody',

  components: { SideBar, HeadBar, MobileHeadBar, NavigationList },

  data: () => ({
    snackbar: false
  }),

  computed: {
    isNetworkError (): Boolean {
      return this.$store.getters['network-error/isNetworkError']
    }
  },

  watch: {
    $route: {
      handler (currentRoute) {
        let childPath = ''

        if (currentRoute.path.includes('invoice-order')) {
          childPath = '/badge-vendor-invoice-order'
          this.$store.dispatch('firebase/setDataRealtime', childPath)
        } else if (currentRoute.path.includes('create-order')) {
          childPath = '/badge-vendor-request-shipment'
          this.$store.dispatch('firebase/setDataRealtime', childPath)
        }

        if (!currentRoute.path.includes('success-accept-shipment')) {
          this.$store.commit('shipment/SET_RESPONSE_ACCEPT', null)
        }
      },
      immediate: true
    },

    isNetworkError () {
      if (!this.isNetworkError) {
        toastNetworkSuccess(this)
      }
    }
  },

  mounted () {
    if (
      Notification.permission === 'default' ||
      Notification.permission === 'denied'
    ) {
      this.snackbar = true
    }

    const user = this.$auth.user as any
    const userData = user.data as any
    const firebasePath = `/users/${userData.id}`

    this.$store.dispatch('firebase/getDataRealtime', {
      path: firebasePath,
      appEnv: this.$config.appEnv
    })
  },

  methods: {
    requestNotification () {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          this.snackbar = false
        }
      })
    }
  }
})
</script>

<style lang="scss"></style>

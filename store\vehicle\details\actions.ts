import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleDetailsState } from './state'
import { toastError, toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'
import { VehicleDetail } from '~/types/vehicle'

export const actions: ActionTree<VehicleDetailsState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/vehicle-detail', {
        params: {
          page: payload == null ? 1 : payload.page,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          mode: payload?.mode,
          entries: -1
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
        const items = response.data.data as VehicleDetail[]
        for (const item of items) {
          if (item?.vehicle_id === null) {
            commit('SET_IS_HAS_NULL_VEHICLE_ID', true)
            break
          } else {
            commit('SET_IS_HAS_NULL_VEHICLE_ID', false)
          }
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .post('/v1/vehicle-detail', {
        vehicle_id: payload.formValues.vehicleId,
        vehicle_details: payload.formValues.vehicleDetails
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return response.data
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return null
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async editItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .put(`/v1/vehicle-detail/${payload.formValues.plateNumberId}`, {
        plate_number: payload.formValues.plateNumber,
        driver_id: payload.formValues.driverId,
        vehicle_id: payload.formValues.vehicleId
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    return await this.$axios
      .delete('/v1/vehicle-detail/' + payload.vehicleDetailId)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .delete('v1/vehicle-detail', {
        params: {
          vehicle_detail_ids: payload.vehicleDetailIds
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  getImportVehicleKeys ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/vehicle-detail/import')
      .then((response: any) => {
        commit('SET_VEHICLE_KEYS', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => commit('SET_IS_LOADING', false))
  },

  async bulkImportVehicles ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const reformattedFormValues = [] as object[]

    try {
      payload.formValues.forEach((value: any) => {
        reformattedFormValues.push({
          name: value.name,
          type_name: value['vehicle_types.name'] || value.vehicle_type,
          features_name: (value['vehicle_features.name'] || value.features).split(/,\s*|\s*,\s*/),
          length: value.length,
          width: value.width,
          height: value.height,
          max_volume: value.max_volume,
          max_weight: value.max_weight
        })
      })
    } catch (e) {
      toastError('Required Data is Empty', this)
      commit('SET_IS_LOADING_FORM', false)
      return false
    }

    return await this.$axios.post('/v1/vehicle-detail/import', {
      vehicles_details: reformattedFormValues
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => commit('SET_IS_LOADING_FORM', false))
  },

  getVehicleDetail ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/vehicle-detail/'+ payload.id)
      .then((response: any) => {
        commit('SET_DETAIL_ITEM', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },
}

export default actions

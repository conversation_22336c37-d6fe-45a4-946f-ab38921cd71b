<template>
  <v-container fluid class="pa-0 px-5 px-sm-10 mb-10">
    <v-container fluid class="pa-0">
      <header-datatable
        default-sort-column="created_at"
        default-sort-type="desc"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        :is-sort-by="false"
        @on-search-icon-click="getData({searchKey: $event})"
        @on-filter-change="getData({filter: $event, page: $route.query?.page})"
      >
        <template #filter-user>
          <v-card
            elevation="0"
            class="pa-5 d-flex justify-start flex-column"
          >
            <p class="subtitle-1 mb-5">
              Filter
            </p>
            <p class="subtitle-1 mb-5">
              User
            </p>
            <v-container class="pa-0">
              <v-row class="ma-0 d-flex">
                <v-autocomplete
                  v-model="selectedUsersId"
                  outlined
                  hide-details
                  placeholder="Select User"
                  :loading="isLoadingUser"
                  :items="listUsers.items.map(vt => {
                    return {
                      text: vt.name,
                      value: vt.id
                    }
                  })"
                  item-value="item"
                  item-text="text"
                  multiple
                  dense
                />
              </v-row>
              <v-row class="ma-0 d-flex mt-3">
                <v-menu
                  ref="menu"
                  v-model="menuDateRange"
                  max-width="280"
                  :close-on-content-click="false"
                  transition="slide-y-transition"
                  offset-y
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="dateRange"
                      outlined
                      clearable
                      label="Date and Time"
                      v-bind="attrs"
                      append-icon="mdi-calendar-range"
                      v-on="on"
                      @click:clear="getData({})"
                    >
                      Select Date Range
                    </v-text-field>
                  </template>

                  <v-date-picker
                    v-model="dateRange"
                    range
                    no-title
                    color="primary"
                  >
                    <v-btn text color="primary" @click="menuDateRange = false">
                      Save
                    </v-btn>
                    <v-btn text color="primary" @click="menuDateRange = false">
                      Cancel
                    </v-btn>
                  </v-date-picker>
                </v-menu>
              </v-row>
              <v-btn
                block
                elevation="0"
                color="primary"
                class="text-capitalize mt-3"
                x-large
                @click="applyFilter"
              >
                Apply Filter
              </v-btn>
              <v-btn
                color="transparent"
                elevation="0"
                class="mt-5 pa-0 text-capitalize text-secondary"
                @click="resetFilter"
              >
                reset
              </v-btn>
            </v-container>
          </v-card>
        </template>
      </header-datatable>
      <v-data-table
        :loading="isLoadingLogs"
        loading-text="Loading... Please wait"
        :headers="tableHeaders"
        :items="logs.itemLogs"
        :page.sync="page"
        :items-per-page="-1"
        disable-sort
        hide-default-footer
        class="pa-md-10 pa-5"
        style=""
        @page-count="pageCount = $event"
      >
        <template #item="{item, index}">
          <tr>
            <td>{{ index + 1 }}</td>
            <td>{{ item.user?.name }}</td>
            <td>{{ item.module }}</td>
            <td>{{ item.action }}</td>
            <td>{{ $moment(item.created_at).format('DD/MM/yyyy HH:mm') }}</td>
          </tr>
        </template>
      </v-data-table>
      <v-row class="mt-4 mb-4" justify="end">
        <pagination-component
          :page="logs.pageLogs"
          :total-page="logs.totalPageLogs"
          class="mr-3"
          page-id="page"
          @on-change-page="handlePaginationChange"
        />
      </v-row>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '~/types/user'
import { saveDateRange } from '~/utils/functions'

export default Vue.extend({
  name: 'SettingPage',

  components: {
  },

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  data: () => ({
    searchKey: '',
    menuDateRange: false as boolean,
    dateRange: [] as Date[],
    page: 0,
    pageCount: 0,
    tableHeaders: [
      { text: 'No', value: '' },
      { text: 'User', value: '' },
      { text: 'Module', value: '' },
      { text: 'Activity', value: '' },
      { text: 'Date & Time', value: '' }
    ],
    selectedUsersId: null as any
  }),

  computed: {
    logs (): { itemLogs: User[], pageLogs: number, totalPageLogs: number } {
      return this.$store.getters['users/dataLogs']
    },
    listUsers (): User[] {
      return this.$store.getters['users/data']
    },
    isLoadingLogs (): boolean {
      return this.$store.getters['users/isLoading']
    },
    isLoadingUser (): boolean {
      return this.$store.getters['users/isLoading']
    }

  },

  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'User Activity Log'
    )
    this.getUsers()
    this.getData({ page: this.$route.query?.page as string })
  },

  methods: {

    async getData ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.searchKey = searchKey
      await this.$store.dispatch('users/getLogs', {
        searchColumns: 'user.name',
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        entries: 10,
        page
      })
    },

    getUsers () {
      return this.$store.dispatch('users/getItems', {
        entries: -1
      })
    },

    async applyFilter ({
      page = '',
      filterDate = { column: 'created_at', start: null as null | Date, end: null as null | Date },
      filter = {
        sortColumn: '',
        sortType: 'desc',
        filterColumn: 'user.name',
        filterType: ''
      }
    }) {
      let filterDateStart = '' as any
      let filterDateEnd = '' as any

      if (filterDate.start !== null && filterDate.end !== null) {
        filterDateStart = filterDate.start || new Date()
        filterDateEnd = filterDate.end || new Date()
      }

      if (this.dateRange) {
        const { start, end } = saveDateRange(this.dateRange)
        filterDateStart = start ? new Date(start.toISOString().substring(0, 10) + ' 01:00:00') : filterDateStart
        filterDateEnd = end ? new Date(end.toISOString().substring(0, 10) + ' 23:00:00') : filterDateEnd
      }

      const selectedUsersId = this.selectedUsersId ?? []

      await this.$store.dispatch('users/getLogs', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: filter.filterColumn,
        filterKeys: selectedUsersId.join('|'),
        filterDateColumn: filterDate.column,
        filterDateStart,
        filterDateEnd,
        searchKey: this.searchKey,
        page
      })
    },

    resetFilter () {
      this.selectedUsersId = null as string[] | null
      this.dateRange = []
      this.getData({})
    },

    handlePaginationChange (pageEvent: any) {
      const filterVariabel = (this.selectedUsersId || this.dateRange)

      if (filterVariabel) {
        this.applyFilter({ page: pageEvent })
      } else {
        this.getData({ page: pageEvent })
      }
    }
  }
})
</script>

<style scoped lang="scss"> </style>

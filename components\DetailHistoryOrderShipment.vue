<template>
  <v-container fluid class="pa-0">
    <v-dialog
      v-model="isShowDialogRemoveVehicle"
      persistent
      width="780"
    >
      <v-card class="pa-md-10 pa-5">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Remove Vehicle</h4>

          <v-icon color="black" @click="isShowDialogRemoveVehicle = false">
            mdi-close
          </v-icon>
        </v-card-title>

        <v-row>
          <v-col cols="3">
            <v-img
              v-show="!$vuetify.breakpoint.xs"
              :src="selectedTrack?.vehiclePhoto"
              aspect-ratio="1"
              class="mr-5"
              contain
              max-width="80"
            />
          </v-col>
          <v-col>
            <v-row>
              <v-col cols="4">
                <span class="body-1">Order Number:</span>
              </v-col>
              <v-col><span class="subtitle-2">{{ shipmentDetail?.orders[0]?.identity }}</span></v-col>
            </v-row>
            <v-row>
              <v-col cols="4">
                <span class="body-1">Vehicle Type:</span>
              </v-col>
              <v-col><span class="subtitle-2">{{ selectedTrack?.vehicle }}</span></v-col>
            </v-row>
            <v-row>
              <v-col cols="4">
                <span class="body-1">Plate Number:</span>
              </v-col>
              <v-col><span class="subtitle-2">{{ selectedTrack?.plateNumber }}</span></v-col>
            </v-row>
            <v-row>
              <v-col cols="4">
                <span class="body-1">Status:</span>
              </v-col>
              <v-col><span class="subtitle-2">{{ selectedTrack?.statusTruck }}</span></v-col>
            </v-row>
          </v-col>
        </v-row>

        <div class="d-flex mt-5">
          <v-btn
            :loading="isLoadingTrackForm"
            class="text-capitalize"
            color="primary"
            depressed
            @click="onRemoveVehicle"
          >
            Remove
          </v-btn>
          <v-btn
            class="ml-5 text-capitalize"
            color="primary"
            depressed
            outlined
            @click="isShowDialogRemoveVehicle = false"
          >
            Cancel
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <!-- Handle LOGISTIC_SERVICE_PROVIDER role -->
    <div v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'" class="white rounded">
      <order-header
        :shipment-detail="shipmentDetail"
        :center-position="centerPosition"
        :zoom="zoom"
        :polyline="polyline"
        :markers="markers"
        :is-loading-shipment-detail="isLoadingShipmentDetail"
        :shipment-id="shipmentId"
        :selected-routes="selectedRoutes"
      />
      <div class="white rounded">
        <weight-bridge-component
          :shipment-detail="shipmentDetail"
          :is-loading-shipment-detail="isLoadingShipmentDetail"
          :vehicles="vehicles"
          :vehicle-detail="vehicleDetail"
          :driver="driver"
          :is-loading-drivers="isLoadingDrivers"
          :is-loading-vehicles="isLoadingVehicles"
          :is-loading-vehicle-details="isLoadingVehicleDetails"
          @delete-vehicle="dialogDeleteVehicle"
          @on-change-vehicle="handleChangeVehicle"
          @change-vendor="navigateToChangeVendor"
        />
      </div>
    </div>

    <!-- Handle VENDOR role -->
    <div v-else-if="userRole === 'VENDOR'" class="white rounded">
      <order-header
        :shipment-detail="shipmentDetail"
        :center-position="centerPosition"
        :zoom="zoom"
        :polyline="polyline"
        :markers="markers"
        :is-loading-shipment-detail="isLoadingShipmentDetail"
        :shipment-id="shipmentId"
        :selected-routes="selectedRoutes"
      />
      <div class="white rounded">
        <weight-bridge-component
          :shipment-detail="shipmentDetail"
          :is-loading-shipment-detail="isLoadingShipmentDetail"
          :vehicles="vehicles"
          :vehicle-detail="vehicleDetail"
          :driver="driver"
          :is-loading-drivers="isLoadingDrivers"
          :is-loading-vehicles="isLoadingVehicles"
          :is-loading-vehicle-details="isLoadingVehicleDetails"
          @delete-vehicle="dialogDeleteVehicle"
          @on-change-vehicle="handleChangeVehicle"
          @change-vendor="navigateToChangeVendor"
        />
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import OrderHeader from '~/components/logistic-service-provider/OrderHeader.vue'
import WeightBridgeComponent from '~/components/logistic-service-provider/WeightBridgeComponent.vue'
import TimelineDetailShipment from '~/components/detail-shipment/TimelineDetailShipment.vue'
import { Route, Shipment, Track } from '~/types/shipment'
import { formatDateTime, formatNumber, generateDriverMarker, mergeDateTime, rules } from '~/utils/functions'
import { Vehicle, VehicleDetail } from '~/types/vehicle'
import { Driver, LicenseCategory } from '~/types/driver'
import { toastError } from '~/utils/toasts'
import { login } from '~/locales/en'
import VehiclePlateFormDialog from '~/components/VehiclePlateFormCreateDialog.vue'
import AddDriverDialog from '~/components/AddDriverDialog.vue'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'DetailHistoryOrderShipment',

  components: {
    ImageComponent,
    CustomMap,
    TimelineDetailShipment,
    VehiclePlateFormDialog,
    AddDriverDialog,
    OrderHeader,
    WeightBridgeComponent
  },

  props: {
    centerPosition: {
      type: Object as () => { lat: number, lng: number },
      default: () => {
      }
    },
    zoom: {
      type: Number,
      default: 16
    },
    polyline: {
      type: Object as () => Polyline,
      default: () => ({
        mapBox: [],
        fms: []
      })
    },
    markers: {
      type: Array,
      default: () => []
    },
    shipmentDetail: {
      type: Object as () => Shipment | null,
      default: null
    },
    selectedRoutes: {
      type: Array as () => Route[],
      default: []
    },
    isLoadingShipmentDetail: {
      type: Boolean,
      default: false
    },
    detailInvoiceUrl: {
      type: String,
      default: ''
    },
    from: {
      type: String,
      required: true
    },
    isChangeVendor: {
      type: Boolean,
      default: false
    },
    shipmentId: {
      type: String || null,
      default: null
    },
    isTotalWeightVendor: {
      type: Boolean,
      default: false
    },
    isTotalWeight: {
      type: Boolean,
      default: true
    },
    vehicles: {
      type: Array as () => (Vehicle | null)[],
      default: null
    },
    vehicleDetail: {
      type: Array as () => Array<VehicleDetail>,
      default: () => []
    },
    driver: {
      type: Array as () => Array<Driver>,
      default: () => []
    },
    isLoadingDrivers: {
      type: Boolean,
      default: false
    },
    isLoadingVehicles: {
      type: Boolean,
      default: false
    },
    isLoadingVehicleDetails: {
      type: Boolean,
      default: false
    }

  },

  data: () => ({
    uploadFile: null as any,
    selectedVehicleDetailId: '',
    selectedTrack: null as any,
    textNote: null as any,
    filePhoto: [] as any[],
    dateSelected: '' as any,
    timeSelected: '' as any,
    selectedDriver: '',
    selectedVehicleDetail: '',
    selectedVehicle: null as string[] | null,
    isSelectedVehicle: [] as any[],
    isSelectedSC: [] as any[],
    isSelectedOrder: [] as any[],
    currentTab: 0,
    maxRemainingTime: 6 * 60 * 60 * 1000,
    displayWeighBridge: false,
    displayAllWeighBridge: false,
    displayDataReportTravelled: false,
    dataWeighBridges: [] as any,
    dataReportTravelled: [] as any,
    totalPickupWeight: 0,
    totalDropoffWeight: 0,
    weightDifference: 0,
    weightDifferencePercentage: 0,
    mergedData: [] as any,
    mergedDataRitase: [] as any,
    formattedDuration: '',
    dialogChangeVehicle: false as boolean,
    dialogWeightBridge: false as boolean,
    dialogAllWeightBridge: false as boolean,
    isShowDateMenu: false,
    isShowTimeMenu: false,
    isShowDialogRemoveVehicle: false,
    isShowDialogMoveVehicle: false,
    moveVehicleType: undefined,
    dialogCreatePlate: false,
    dialogCreateDriver: false,
    clearForm: false,
    minDate: null,
    maxDate: new Date().toISOString().slice(0, 10)
  }),

  computed: {
    userRole (): string {
      return this.$auth.$state.user.data.role
    },
    setAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 3
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    driverMarker (): object[] {
      return generateDriverMarker(this.polyline?.fms)
    },

    isLoadingDownload (): boolean {
      return this.$store.getters['shipment/isLoadingDownload']
    },

    isLoadingTrackForm (): boolean {
      return this.$store.getters['shipment/isLoadingTrackForm']
    },

    weighBrightDetail (): any {
      return this.shipmentDetail?.orders[0]
    },

    differenceNet (): number {
      const pickup = this.dataWeighBridges?.filteredWeightBridges?.find(
        (item: { type: string }) => item.type === 'PICKUP'
      )
      const dropoff = this.dataWeighBridges?.filteredWeightBridges?.find(
        (item: { type: string }) => item.type === 'DROPOFF'
      )

      if (!pickup || !dropoff) {
        return 0
      }

      const pickupWeight = parseFloat(pickup.weight)
      const dropoffWeight = parseFloat(dropoff.weight)

      return dropoffWeight - pickupWeight
    },

    differencePercentage (): number {
      const pickup = this.dataWeighBridges?.filteredWeightBridges?.find(
        (item: { type: string }) => item.type === 'PICKUP'
      )
      const dropoff = this.dataWeighBridges?.filteredWeightBridges?.find(
        (item: { type: string }) => item.type === 'DROPOFF'
      )

      if (!pickup || !dropoff) {
        return 0
      }

      const pickupWeight = parseFloat(pickup.weight)
      const dropoffWeight = parseFloat(dropoff.weight)
      const weightDifference = dropoffWeight - pickupWeight

      return (
        (weightDifference / pickupWeight) * 100
      )
    },

    statusList () {
      const list = [] as any
      this.shipmentDetail?.tracks.forEach((track) => {
        if (track.status) {
          list.push(track.status)
        }
      })
      return [...new Set(list)]
    },

    getTotalRitase () {
      const lastShipmentVendor = this.shipmentDetail?.shipment_vendors[this.shipmentDetail?.shipment_vendors.length - 1]
      return lastShipmentVendor?.ritase_identity
    },

    moveVehiclesOrder () {
      return this.$store.getters['shipment/move-vehicles/order']
    },

    moveVehiclesSelectedTracks (): Track[] {
      return this.$store.getters['shipment/move-vehicles/selectedTracks']
    },

    vendorId (): String {
      return this.$auth.$state.user.data.vendor_id
    },

    licenseCategories (): LicenseCategory[] {
      return this.$store.getters['vehicle/drivers/license-category/items']
    },

    isLoadingFormDriver (): boolean {
      return this.$store.getters['vehicle/drivers/isLoadingForm']
    }

  },

  watch: {
    dialogChangeVehicle (newVal) {
      if (!newVal) {
        this.selectedVehicle = []
        this.selectedVehicleDetailId = ''
        this.selectedVehicleDetail = ''
        this.selectedDriver = ''
        this.textNote = ''
        this.filePhoto = []
      }
    }
  },

  mounted () {
    if (this.weighBrightDetail) {
      this.differenceAllNet()
    }
    this.mergeArrays()
    this.getReportedAt()
    this.mergeArrayRitase()
    if (this.$auth.$state.user.data.role === 'VENDOR') {
      this.getLicenseCategories()
    }
  },

  methods: {
    ruleRequired (value: string) {
      return rules.required(value)
    },
    formatNumber,
    // eslint-disable-next-line camelcase
    clickChangeVehicle (vehicle_detail_id: any) {
      // eslint-disable-next-line camelcase
      this.selectedVehicleDetailId = vehicle_detail_id
    },
    downloadDispatchNote () {
      this.$store.dispatch('shipment/downloadDispatchNote', {
        shipmentId: this.shipmentDetail?.id,
        shipmentIdentity: this.shipmentDetail?.identity
      })
    },

    // eslint-disable-next-line camelcase
    clickWeighBridge (vehicle_detail_id: any) {
      // eslint-disable-next-line camelcase
      this.selectedVehicleDetailId = vehicle_detail_id
      if (
        this.shipmentDetail &&
        this.shipmentDetail.orders &&
        this.shipmentDetail.orders[0].weight_bridges
      ) {
        const filteredWeightBridges =
          this.shipmentDetail.orders[0].weight_bridges.filter(
            (
              // eslint-disable-next-line camelcase
              bridge: { vehicle_detail_id: any }
              // eslint-disable-next-line camelcase
            ) => bridge.vehicle_detail_id === vehicle_detail_id
          )

        this.dataWeighBridges = { filteredWeightBridges }

        this.displayWeighBridge = true
        this.displayAllWeighBridge = false
      }
    },

    // eslint-disable-next-line camelcase
    getDataReportTravelled (vehicle_detail_id: any) {
      if (this.shipmentDetail !== null) {
        // eslint-disable-next-line camelcase
        const filteredTracks = this.shipmentDetail.tracks.filter((track: { vehicle_detail_id: any }) => track.vehicle_detail_id === vehicle_detail_id)

        this.dataReportTravelled = { filteredTracks }
        this.displayDataReportTravelled = true
      }
    },

    differenceAllNet () {
      const pickups = this.weighBrightDetail?.weight_bridges?.filter(
        (item: { type: string }) => item.type === 'PICKUP'
      )
      const dropoffs = this.weighBrightDetail?.weight_bridges?.filter(
        (item: { type: string }) => item.type === 'DROPOFF'
      )

      this.totalPickupWeight = pickups.reduce(
        (acc: number, pickup: { weight: string }) =>
          acc + parseFloat(pickup.weight),
        0
      )

      this.totalDropoffWeight = dropoffs.reduce(
        (acc: number, dropoff: { weight: string }) =>
          acc + parseFloat(dropoff.weight),
        0
      )

      this.weightDifference = this.totalDropoffWeight - this.totalPickupWeight

      this.weightDifferencePercentage =
        (this.weightDifference / this.totalPickupWeight) * 100
    },

    clickAllWeighBridge () {
      this.displayAllWeighBridge = true
      this.displayWeighBridge = false
    },

    getColor (createdAt: Date) {
      const tomorrow9AM = this.$moment().add(1, 'day').set({ hour: 9, minute: 0, second: 0, millisecond: 0 })
      const diffInHours = tomorrow9AM.diff(createdAt, 'hours')

      return diffInHours > 24 ? 'ma-0 body-1 red--text font-weight-bold' : 'ma-0 body-1'
    },

    remainingHours (createdAt: Date) {
      const currentTime = new Date()

      const tomorrow9AM = new Date(currentTime)
      if (tomorrow9AM.getHours() > 9) {
        tomorrow9AM.setDate(currentTime.getDate() + 1)
      }
      tomorrow9AM.setHours(9, 0, 0, 0)

      const shipmentTime = new Date(createdAt)

      const elapsedTime = tomorrow9AM.getTime() - shipmentTime.getTime()
      const remainingHours = Math.floor(elapsedTime / 3600000)
      const formattedHours = String(remainingHours).padStart(2, '0')
      return formattedHours
    },

    remainingMinutes (createAt: Date) {
      const currentTime = new Date().getTime()
      const shipmentTime = this.$moment(createAt).toDate().getTime()
      const elapsedTime = currentTime - shipmentTime
      const remainingTime = Math.abs(this.maxRemainingTime - elapsedTime)

      const minutes = Math.floor((remainingTime % 3600000) / 60000)

      const formattedMinutes = String(minutes).padStart(2, '0')
      return `${formattedMinutes}`
    },

    // eslint-disable-next-line camelcase
    navigateToChangeVendor (track: any, id: string) {
      // eslint-disable-next-line camelcase
      const shipmentVendorIds = track?.vendor_id
      this.$store.commit('vehicle/SET_WEIGHT_VENDOR', {
        // eslint-disable-next-line camelcase
        vendorWeight: this.shipmentDetail?.orders[0]?.remaining_weight
      })
      this.$router.push({
        path: `${this.$route.path}/change-vendor`,
        query: { shipmentVendorIds, id }
      })
    },

    dialogDeleteVehicle (item: any) {
      this.isShowDialogRemoveVehicle = true
      this.selectedTrack = item
    },

    mergeArrays () {
      const mergedData: any[] = []

      this.shipmentDetail?.shipment_vendors?.forEach((shipmentVendor: { vendor_id: any, type?: string }) => {
        if (shipmentVendor?.type === 'GENERAL') {
          const matchingTracks = this.shipmentDetail?.tracks?.filter(track => track?.shipment_vendor?.type === shipmentVendor?.type && track?.vehicle_detail?.vendor_id === shipmentVendor?.vendor_id) ?? []

          if (matchingTracks.length > 0) {
            const mergedItem = {
              ...shipmentVendor,
              tracks: matchingTracks
            }
            mergedData.push(mergedItem)
          }
        }
      })

      this.mergedData = mergedData
    },

    mergeArrayRitase () {
      const ritaseShipments = this.shipmentDetail?.tracks?.filter((track) => {
        return track.shipment_vendor && track.shipment_vendor?.type === 'RITASE'
      })
      if (ritaseShipments && ritaseShipments.length > 0) {
        const ritaseMap = new Map()

        ritaseShipments.forEach((shipment) => {
          const ritaseIdentity = shipment.shipment_vendor.ritase_identity
          if (ritaseMap.has(ritaseIdentity)) {
            ritaseMap.get(ritaseIdentity).push(shipment)
          } else {
            ritaseMap.set(ritaseIdentity, [shipment])
          }
        })

        const mergedRitaseArray = Array.from(ritaseMap.values())
        this.mergedDataRitase = mergedRitaseArray
      }
    },

    getReportedAt () {
      this.formattedDuration = '0'

      if (
        this.shipmentDetail &&
    this.shipmentDetail.tracks &&
    this.shipmentDetail.tracks.length > 0
      ) {
        // eslint-disable-next-line array-callback-return
        this.shipmentDetail.tracks.some((track) => {
          if (track.routes && track.routes.length > 0) {
            const shipmentHistory = track.routes.find(route => route.shipment_history)

            if (shipmentHistory && shipmentHistory.shipment_history?.reported_at) {
              let timeDifference

              if (this.shipmentDetail?.status === 'FINISHED') {
                const updatedTime = new Date(this.shipmentDetail.updated_at).getTime()
                const reportedTime = new Date(shipmentHistory.shipment_history.created_at).getTime()

                timeDifference = reportedTime - updatedTime
                timeDifference = Math.abs(timeDifference)

                this.formattedDuration = this.formatTime(timeDifference)
              } else {
                const reportedTime = new Date().getTime()
                const updateTime = new Date(shipmentHistory.shipment_history.created_at).getTime()

                timeDifference = updateTime - reportedTime
                timeDifference = Math.abs(timeDifference)

                this.formattedDuration = this.formatTime(timeDifference)
              }
            }
            return shipmentHistory
          }
        })
      }
    },

    formatTime (timeDifference: number) {
      const hours = Math.floor(timeDifference / (1000 * 60 * 60))
      const minutes = Math.floor(
        (timeDifference % (1000 * 60 * 60)) / (1000 * 60)
      )

      return `${hours} Hours ${minutes} Minutes`
    },

    isVehicleDetailVisible (shipmentDetail: any, track: any) {
      const status = shipmentDetail?.shipment_vendors?.find((e: any) => e?.vendor?.id === track?.shipment_vendor?.vendor_id)?.status
      return status !== 'PROPOSED' && status !== 'REJECT'
    },

    isVehicleDetailVisibleRitase (shipmentDetail: any, track: any) {
      const status = shipmentDetail?.shipment_vendors
        ?.find((e: any) => e?.vendor?.id === track?.shipment_vendor?.vendor_id && e?.ritase_identity !== null)
        ?.status
      return status !== 'PROPOSED' && status !== 'REJECT'
    },

    async changeVehicle () {
      let fileArray = []

      if (Array.isArray(this.filePhoto)) {
        fileArray = this.filePhoto
      } else if (this.filePhoto) {
        fileArray.push(this.filePhoto)
      }

      const date = this.dateSelected ? formatDateTime(mergeDateTime(this.dateSelected, this.timeSelected)) : ''
      const files = fileArray || []

      const res = await this.$store.dispatch('shipment/changeVehicle', {
        id: this.shipmentDetail?.id,
        old_vehicle_detail_id: this.selectedVehicleDetailId,
        new_vehicle_detail_id: this.selectedVehicleDetail,
        driver_id: this.selectedDriver,
        note: this.textNote,
        date,
        files
      })

      if (res) {
        this.dialogChangeVehicle = false
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id
        })
      }
    },

    async onRemoveVehicle () {
      const result = await this.$store.dispatch('shipment/removeTrack', {
        id: this.selectedTrack?.id
      })

      if (result) {
        this.$emit('on-remove-track')
         this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id,
          type: 'detail'
        })
        this.isShowDialogRemoveVehicle = false
      }
    },

    onChangesToSelectVehicleMode () {
      try {
        const shipmentCompanyId = this.shipmentDetail?.orders?.[0]?.suborders?.[0]?.products?.[0].shipment_company_id

        this.$store.commit('shipment/move-vehicles/SET_MOVETYPE', 'MOVE_DO')
        this.$store.commit('shipment/move-vehicles/SET_ORDER', this.shipmentDetail?.orders[0])
        this.$store.commit('shipment/move-vehicles/SET_SHIPPING_COMPANY_ID', shipmentCompanyId)

        this.isShowDialogMoveVehicle = false
      } catch (e) {
        toastError('Terjadi kesalahan', this)
      }
    },

    onSelectTracks (track: Track) {
      const selectedTracks = this.$store.getters['shipment/move-vehicles/selectedTracks']

      if (selectedTracks.map((e: any) => e.id).includes(track?.id)) {
        const updatedTracks = selectedTracks.filter((t: any) => t.id !== track.id)
        this.$store.commit('shipment/move-vehicles/SET_TRACKS', updatedTracks)
      } else {
        this.$store.commit('shipment/move-vehicles/SET_TRACKS', [...selectedTracks, track])
      }
    },

    getEnterMillDate (bridge: any) {
      const alerts = bridge.vehicle_details.alerts
      const location = bridge.location

      const filteredAlerts = alerts
        .filter((alert: { type: string; pickup_drop_off_location_point: { identity: any } }) => alert.type === 'zone_in' && alert.pickup_drop_off_location_point.identity === location)
        .sort((a: { created_at: string }, b: { created_at: string }) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      return filteredAlerts.length > 0 ? filteredAlerts[0].alert_time : ''
    },

    getExitMillDate (bridge: any) {
      const alerts = bridge.vehicle_details.alerts
      const location = bridge.location

      const filteredAlerts = alerts
        .filter((alert: { type: string; pickup_drop_off_location_point: { identity: any } }) => alert.type === 'zone_out' && alert.pickup_drop_off_location_point.identity === location)
        .sort((a: { created_at: string }, b: { created_at: string }) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      return filteredAlerts.length > 0 ? filteredAlerts[0].alert_time : ''
    },

    getAllEnterMillDate (weightBridge: any) {
      const alerts = weightBridge?.vehicle_details?.alerts || []
      const location = weightBridge?.location

      const filteredAlerts = alerts
        .filter((alert: { 
          type: string; 
          pickup_drop_off_location_point: { identity: any } | null 
        }) => {
          return alert.type === 'zone_in' && 
                 alert?.pickup_drop_off_location_point?.identity === location
        })
        .sort((a: { created_at: string }, b: { created_at: string }) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )

      return filteredAlerts.length > 0 ? filteredAlerts[0].alert_time : ''
    },

    getAllExitMillDate (weightBridge: any) {
      const alerts = weightBridge?.vehicle_details?.alerts || []
      const location = weightBridge?.location

      const filteredAlerts = alerts
        .filter((alert: { 
          type: string; 
          pickup_drop_off_location_point: { identity: any } | null 
        }) => {
          return alert.type === 'zone_out' && 
                alert?.pickup_drop_off_location_point?.identity === location
        })
        .sort((a: { created_at: string }, b: { created_at: string }) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )

      return filteredAlerts.length > 0 ? filteredAlerts[0].alert_time : ''
    },

    async createItemDriver (value: any) {
      const user = this.$auth.user?.data as any
      const response = await this.$store.dispatch('vehicle/drivers/createItem', {
        value,
        vendorId: user.vendor.id
      })
      if (response) {
        this.dialogCreateDriver = false
        this.clearForm = true

        this.getDriver({})
      }
    },

    getDriver ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/drivers/getItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterKeys: user.vendor.id,
        entries: -1
      })
    },

    getDataVehicleDetails () {
      this.$store.dispatch('vehicle/details/getItems')
    },

    getLicenseCategories () {
      this.$store.dispatch('vehicle/drivers/license-category/getItems')
    },

    handleChangeVehicle(params: { dialog: boolean; item: any }) {
      this.$emit('update-change-vehicle', { dialog: params.dialog, item: params.item })
    }
  }
})
</script>

<style scoped lang="scss">
.v-item--active {
  color: #EF3434;
  border: 1px solid #EF3434;
}

.chip-success {
  background-color: #EAF6EC !important;
}

.chip-danger {
  background: #FDE0E0 !important;
}

h3 {
  color: #0D0000;
}

.fixed-append-item {
  position: sticky;
  bottom: 0;
  background-color: white;
  z-index: 1;
  padding: 10px;
  border-top: 1px solid #ccc;
}
</style>

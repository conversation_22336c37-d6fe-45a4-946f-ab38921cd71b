import { MutationTree } from 'vuex'
import { ShipmentCompanyInvoiceOrderState } from './state'

export const mutations: MutationTree<ShipmentCompanyInvoiceOrderState> = {
  SET_ITEM (state, item) {
    state.item = item
  },

  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  }
}

export default mutations

<template>
  <v-container fluid class="pa-0 px-5 px-md-10 mb-10">
    <div class="d-flex justify-space-between align-center mb-10">
      <v-btn
        link
        plain
        class="text-capitalize px-3"
        @click="
          $store.dispatch('tab/changeTab', 0)
            .then(() => { $router.back() })
        "
      >
        <v-icon
          color="black"
          class="mr-2"
        >
          mdi-chevron-left
        </v-icon>
        <p class="ma-0 subtitle-1">
          {{ $t('lspInvoiceShipment.back_to_list_invoice') }}
        </p>
      </v-btn>
      <select-summary-send-invoice-dialog
        :dialog="showSummary"
        @on-close-dialog="showSummary = false"
        @on-send="showSummary = false"
      >
        <template #activator="{ on }">
          <v-btn
            x-large
            color="primary"
            class="elevation-0 text-capitalize"
            :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
            v-on="on"
            @click="showSummary = true"
          >
            {{ $t('lspInvoiceShipment.send_invoice') }}
          </v-btn>
        </template>
      </select-summary-send-invoice-dialog>
    </div>

    <v-sheet v-if="isLoadingDetail" height="1150" color="pa-7 overflow-hidden">
      <v-container class="d-flex justify-space-between pa-0 mb-2">
        <v-skeleton-loader type="text" width="150" />
        <v-skeleton-loader type="text" width="150" />
      </v-container>
      <v-skeleton-loader type="heading" width="450" class="mb-10" />
      <v-skeleton-loader type="text" width="150" class="mb-4" />
      <div class="d-flex flex-row">
        <v-skeleton-loader type="image" width="150" height="75" />
        <v-skeleton-loader type="image" width="150" height="75" class="ml-5" />
        <v-skeleton-loader type="image" width="150" height="75" class="ml-5" />
      </div>
      <v-divider class="mt-7 mb-5" />
      <div class="d-flex flex-column">
        <v-skeleton-loader type="text" width="100" class="mb-3" />
        <v-skeleton-loader type="heading" width="300" class="mb-10" />
      </div>
      <v-skeleton-loader type="heading" width="500" class="mb-5" />
      <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
      <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
      <v-skeleton-loader type="image" height="50" style="border-radius: 0 !important;" class="mt-5" />
      <v-skeleton-loader type="image" width="175" height="50" style="border-radius: 0 !important;" class="mt-5" />
      <v-divider class="mt-5 mb-6" />
      <v-skeleton-loader type="text" class="mb-10" />
      <v-skeleton-loader type="image" width="150" height="40" />
    </v-sheet>

    <detail-invoice-lsp
      v-else
      :invoice-shipment="dataInvoiceShipment"
      :selected-invoice="selectedInvoice"
      :selected-order="selectedOrder"
      :pickup-sub-order="pickupSubOrder"
      :drop-off-sub-order="dropOffSubOrder"
      :products="products"
      :total-fee="totalAmount"
      @on-select-order="selectedOrder = $event"
    >
      <template #date-invoice>
        {{ $moment(dataInvoiceShipment?.invoice?.shipment?.created_at).format('DD MMMM YYYY') }}
      </template>
      <template #invoice-number>
        <div>
          <p class="caption text-secondary mb-2">
            {{ $t('lspInvoiceShipment.invoice_number') }}
          </p>
          <h3>{{ dataInvoiceShipment?.invoice?.shipment?.identity }}</h3>
        </div>
      </template>

      <template #header>
        <div>
          <p class="subtitle-1">
            {{ $t('lspInvoiceShipment.select_order') }}
          </p>
          <v-item-group mandatory :value="currentTab">
            <v-row class="ma-n3">
              <v-col
                v-for="(order, index) in dataInvoiceShipment?.orders"
                :key="order.id"
                class="pa-3 col-auto"
              >
                <v-item
                  v-slot="{ active, toggle }"
                  :value="index"
                  @change="selectedOrder = order; currentTab = index"
                >
                  <v-card
                    flat
                    outlined
                    :input-value="active"
                    class="py-2 px-5"
                    @click="toggle"
                  >
                    <p class="subtitle-1 mb-1">
                      {{ order.shipment_company?.name }}
                    </p>
                    <p class="ma-0 caption">
                      {{ order.identity }}
                    </p>
                  </v-card>
                </v-item>
              </v-col>
            </v-row>
          </v-item-group>
        </div>
      </template>

      <template #order-number>
        <p class="caption mb-2 text-secondary">
          {{ $t('lspInvoiceShipment.order_number') }}
        </p>
        <h3 class="mb-10">
          {{ selectedOrder?.identity }}
        </h3>
      </template>

      <template #shipping-cost>
        <v-text-field
          v-model="form.costs"
          outlined
          label="Shipping Cost"
          prefix="Rp"
          :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
          hide-details
          @keydown.enter="onClickSaveInvoice(
            selectedInvoice?.id,
            selectedInvoice?.invoice_id,
            selectedInvoice?.order_id,
            selectedInvoice?.total_odometer
          )"
        />
      </template>

      <template #additional-fee>
        <v-row
          v-for="(additionalCost, i) in form.additional_costs"
          :key="i"
          class="ma-0 mb-6 d-flex"
        >
          <v-col class="pa-0 col-auto">
            <v-text-field
              v-model="form.additional_costs[i].description"
              outlined
              label="Description"
              hide-details
              :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
              class="mr-6"
            />
          </v-col>
          <v-col class="pa-0 col-auto">
            <v-text-field
              v-model="form.additional_costs[i].cost"
              outlined
              prefix="Rp."
              label="Amount"
              type="number"
              hide-details
              :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
              class="mr-6"
            />
          </v-col>
          <v-col class="pa-0 col-auto">
            <v-btn
              x-large
              color="primary"
              elevation="0"
              class="text-capitalize mr-4"
              :loading="form.additional_costs[i].isLoading"
              :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
              @click="submitAdditionalCost(i, additionalCost.id)"
            >
              <p class="subtitle-1 ma-0">
                Save Cost
              </p>
            </v-btn>
          </v-col>
          <v-col class="pa-0 col-auto">
            <v-btn
              x-large
              outlined
              color="primary"
              elevation="0"
              class="text-capitalize mr-4"
              :loading="form.additional_costs[i].isLoadingDelete"
              :disabled="additionalCost.id === null || dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
              @click="deleteAdditionalCost(i, additionalCost.id)"
            >
              <p class="subtitle-1 ma-0">
                Delete Cost
              </p>
            </v-btn>
          </v-col>
          <!--          <v-col class="pa-0 col-auto">-->
          <!--            <v-btn-->
          <!--              outlined-->
          <!--              fab-->
          <!--              :disabled="additionalCost.id !== null"-->
          <!--              class="rounded"-->
          <!--              style="border: 1px solid #CFCCCC"-->
          <!--              @click="removeAdditionalFeeList(i)"-->
          <!--            >-->
          <!--              <v-icon>mdi-close</v-icon>-->
          <!--            </v-btn>-->
          <!--          </v-col>-->
        </v-row>

        <v-btn
          x-large
          outlined
          class="text-capitalize px-4 black--text"
          style="border: 1px solid #CFCCCC"
          :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
          @click="addAdditionalFeeList"
        >
          <v-icon class="mr-3">
            mdi-plus
          </v-icon>
          <p class="ma-0 body-1">
            {{ $t('lspInvoiceShipment.add_additional_fee') }}
          </p>
        </v-btn>
      </template>

      <template #total-cost>
        <h3 class="text-primary">
          {{ totalAmount | toCurrency }}
        </h3>
      </template>

      <template #save-button>
        <v-btn
          x-large
          color="primary"
          class="elevation-0 text-capitalize mt-10"
          :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED'"
          @click="onClickSaveInvoice(
            selectedInvoice?.id,
            selectedInvoice?.invoice_id,
            selectedInvoice?.order_id,
            selectedInvoice?.total_odometer
          )"
        >
          {{ $t('lspInvoiceShipment.save_invoice') }}
        </v-btn>
      </template>
    </detail-invoice-lsp>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailInvoiceLsp from '~/components/DetailInvoiceLsp.vue'
import { Fee, InvoiceDetail, InvoiceOrder } from '~/types/invoice'
import { Order, Product, SubOrder } from '~/types/product'
import SelectSummarySendInvoiceDialog from '~/components/logistic-service-provider/SelectSummarySendInvoiceDialog.vue'

export default Vue.extend({
  name: 'LogisticServiceProviderCreateInvoiceOrderPage',

  components: {
    DetailInvoiceLsp,
    SelectSummarySendInvoiceDialog
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    selectedInvoiceDetail: null as InvoiceDetail | null,
    selectedInvoice: null as InvoiceDetail | null,
    selectedOrder: null as Order | null,
    pickupSubOrder: [] as SubOrder[],
    dropOffSubOrder: [] as SubOrder[],
    products: [] as Product[],
    totalFee: 0 as number,
    showSummary: false,
    form: {
      additional_costs: [] as any[],
      costs: 0 as number
    },
    currentTab: 0
  }),

  computed: {
    dataInvoiceShipment () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/detailData'].item as InvoiceOrder | null
    },

    totalAmount (): number {
      let amountCost = 0

      amountCost += Number(this.form.costs)

      for (const items of this.form.additional_costs) {
        if (items.cost === null) {
          amountCost += 0
          continue
        }
        amountCost += Number(`${items.cost}`)
      }

      return amountCost
    },

    isLoadingDetail (): Boolean {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingDetail']
    },

    isLoadingPublish (): Boolean {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingPublish']
    },

    isShippingCostHasEmpty (): Boolean {
      if (!this.dataInvoiceShipment) { return true }
      const invoiceDetails = this.dataInvoiceShipment?.invoice_details as InvoiceDetail[]

      return invoiceDetails.filter((item: InvoiceDetail) => {
        if (item.cost === null || item.cost === undefined || item.cost! === '0') {
          return true
        }

        return false
      }).length > 0
    }
  },

  watch: {
    selectedOrder () {
      if (this.selectedOrder) {
        const invoiceDetails = this.dataInvoiceShipment?.invoice_details as InvoiceDetail[]

        invoiceDetails.forEach((item: InvoiceDetail) => {
          if (item.order_id === this.selectedOrder?.id) {
            this.selectedInvoice = item
          }
        })

        const subOrders = this.selectedInvoice?.order.suborders as SubOrder[]
        this.pickupSubOrder = []
        this.dropOffSubOrder = []
        this.products = []

        subOrders.forEach((item: SubOrder) => {
          if (item.type === 'PICKUP') {
            this.pickupSubOrder?.push(item)
          } else {
            this.dropOffSubOrder?.push(item)
          }
        })

        this.products = this.pickupSubOrder?.map((suborder: SubOrder) => suborder?.products).flat()

        const fees = this.selectedInvoice?.fees as Fee[]

        this.form.additional_costs = fees.map((item:any) => ({
          id: item.id,
          description: item.description,
          cost: item.cost
        }))

        this.form.costs = Number(this.selectedInvoice?.cost)
      }
    }
    // currentTab () {
    //   if (this.selectedOrder) {
    //     this.dataInvoiceShipment?.orders = index
    //   }
    // }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Invoice Order Shipment')
  },

  mounted () {
    this.getInvoiceDetail()
  },

  methods: {
    async getInvoiceDetail () {
      const response = await this.$store.dispatch('logistic-service-provider/invoice-shipment/getItemDetail', {
        id: this.$route.params.id
      })

      this.selectedOrder = response?.orders[this.currentTab]
    },

    onClickSaveInvoice (id: string, invoiceId: string, orderId: string, totalOdometer: any) {
      this.updateFee(id, invoiceId, orderId, totalOdometer)
    },

    updateFee (id: string | undefined, invoiceId : string | undefined, orderId: string | undefined, totalOdometer: any) {
      this.$store.dispatch('logistic-service-provider/invoice-shipment/updateItem', {
        id,
        cost: this.form.costs,
        invoice_id: invoiceId,
        order_id: orderId,
        total_odometer: totalOdometer
      })
    },

    addAdditionalFeeList () {
      this.form.additional_costs.push({
        id: null,
        description: null,
        cost: null,
        isLoading: false,
        isLoadingDelete: false
      })
    },

    removeAdditionalFeeList (index: number) {
      this.form.additional_costs.splice(index, 1)
    },

    async getAdditionalCost ({ page = 1 }) {
      await this.$store.dispatch('invoice/additional-fee/getItems', {
        filterColumns: 'invoice_id',
        filterKeys: this.$route.params.id,
        page
      })
    },

    async submitAdditionalCost (i: number, id: string | null) {
      this.$set(this.form.additional_costs[i], 'isLoading', true)

      let response = false

      if (id === null) {
        response = await this.$store.dispatch('invoice/additional-fee/createItem', {
          invoice_id: this.$route.params.id,
          description: this.form.additional_costs[i].description,
          cost: this.form.additional_costs[i].cost,
          invoice_detail_id: this.selectedInvoice?.id
        })
      } else {
        response = await this.$store.dispatch('invoice/additional-fee/updateItem', {
          id,
          invoice_id: this.$route.params.id,
          description: this.form.additional_costs[i].description,
          cost: this.form.additional_costs[i].cost,
          invoice_detail_id: this.selectedInvoice?.id
        })
      }

      if (response) {
        await this.getInvoiceDetail()
      }

      this.$set(this.form.additional_costs[i], 'isLoading', false)
    },

    async deleteAdditionalCost (i: number, id: string) {
      this.$set(this.form.additional_costs[i], 'isLoadingDelete', true)

      const response = await this.$store.dispatch('invoice/additional-fee/deleteItem', {
        id
      })

      if (response) {
        await this.getInvoiceDetail()
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.v-item--active {
  color: #EF3434;
  border: 1px solid #EF3434;
}
tbody {
  tr:hover {
    background-color: transparent !important;
  }
}

.border-none {
  border: none
}

.active-selected{
  border:1px solid #bf2a2a !important;
  border-radius: 6px !important;
}

.active-published {
  border: 1px solid #2FA841 !important;
  border-radius: 6px !important;
}
</style>

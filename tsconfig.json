{
  "compilerOptions": {
    "resolveJsonModule": true,
    "target": "ES2018",
    "module": "ESNext",
    "moduleResolution": "Node",
    "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"],
    "esModuleInterop": true,
    "allowJs": true,
    "sourceMap": true,
    "strict": true,
    "noEmit": true,
    "jsx": "preserve",
    // "noImplicitAny": false,
    "experimentalDecorators": true,
    "importHelpers": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./*"],
      "@/*": ["./*"]
    },
    "types": [
      "@types/jest",
      "@nuxtjs/moment",
      "@nuxt/types",
      "@nuxtjs/axios",
      "@types/node",
      "@nuxt/vue-app",
      "nuxt-leaflet",
      "@nuxtjs/device",
      "@nuxtjs/auth-next",
      "vuetify",
      "vue-chartjs/legacy",
      "chart.js",
      "@types/file-saver",
      "@nuxtjs/i18n",
      "vue-toastification",
      "vue2-timepicker",
      "@nuxtjs/firebase"
    ]
  },
  "exclude": ["node_modules", ".nuxt", "dist"]
}

import { VendorOrderShipment } from '~/types/dashboard'
import { Vendor } from '~/types/user'

export interface VendorState {
  items: Vendor[]
  item: Vendor | null
  itemsVendorReport: Vendor[],
  itemsVendorOrderShipment: VendorOrderShipment[]
  itemExportExcel: Vendor | null,
  page: number
  pageVendorReport: number
  totalPage: number
  totalPageVendorReport: number
  isLoading: boolean
  isLoadingVendorReport: boolean
  isLoadingVendorOrderShipment: boolean
  isLoadingForm: boolean
  isLoadingFormStatus: {
    accept: boolean,
    reject: boolean
  },
  isLoadingExportExcel: boolean,
}

export const state = (): VendorState => ({
  items: [],
  item: null,
  itemsVendorReport: [],
  itemsVendorOrderShipment: [],
  itemExportExcel: null,
  page: 1,
  pageVendorReport: 1,
  totalPage: 1,
  totalPageVendorReport: 1,
  isLoading: false,
  isLoadingVendorReport: false,
  isLoadingVendorOrderShipment: false,
  isLoadingForm: false,
  isLoadingFormStatus: {
    accept: false,
    reject: false
  },
  isLoadingExportExcel: false
})

export default state

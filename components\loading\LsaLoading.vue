<template>
  <v-row class="ma-n5">
    <v-col v-for="i in 3" :key="i" class="pa-5 col-lg-4 col-sm-6 col-12">
      <v-sheet class="pa-5 overflow-hidden" height="175">
        <div class="d-flex">
          <div class="d-flex flex-row" style="width: 100%">
            <v-skeleton-loader type="image" height="50" width="50" class="mt-3" />
              <v-skeleton-loader type="list-item-two-line" width="100%" />
          </div>
        </div>
        <v-skeleton-loader type="text" width="100" class="mt-5" />
        <v-skeleton-loader type="text" />
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'LsaLoading'
})
</script>

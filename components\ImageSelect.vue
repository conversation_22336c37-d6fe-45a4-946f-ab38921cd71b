<template>
  <div class="mb-5 d-flex flex-column align-center">
    <input
      ref="file"
      type="file"
      class="d-none"
      accept="image/png, image/jpeg, image/bmp"
      @change="onChangeFile"
    >

    <div class="custom-img-btn" @click="$refs.file.click()">
      <v-img
        :src="url || avatarUrl ? url || avatarUrl : require(`assets/images/icon-select-image.png`)"
        min-width="160"
        max-width="160"
        contain
        class="mb-1"
      />

      <p class="ma-0 caption black--text text-center">
        {{ $t('imageSelect.text_activator') }}
      </p>
    </div>

    <v-btn
      v-if="url || avatarUrl"
      :loading="isLoadingFormClearImage"
      :disabled="isLoadingFormClearImage"
      depressed
      color="primary"
      class="mt-2 text-capitalize"
      @click="
        onClearImage
      "
    >
      <v-icon
        size="20"
        class="mr-2"
      >
        mdi-delete
      </v-icon>
      {{ $t('imageSelect.button_clear') }}
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ImageSelect',

  props: {
    avatarUrl: {
      type: String,
      default: ''
    },
    clearImage: {
      type: Boolean,
      default: false
    },
    isLoadingFormClearImage: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    url: '' as string | null
  }),

  watch: {
    clearImage () {
      const inputFile = this.$refs.file as HTMLInputElement
      if (this.clearImage) {
        inputFile.value = ''
        this.url = null
      }
    }
  },

  methods: {
    onChangeFile (event : any) {
      const file = event.target.files[0]

      if (!file) {
        return
      }

      this.url = URL.createObjectURL(file)
      this.$emit('on-image-selected', file)
    },
    onClearImage () {
      const fileInput = this.$refs.file as HTMLInputElement

      fileInput.value = ''
      this.url = null

      if (this.avatarUrl) {
        this.$emit('on-clear-image')
      }
    }
  }
})
</script>

<style scoped lang="scss">
.custom-img-btn {
  cursor: pointer;
  transition: .28s;
}
.custom-img-btn:hover {
  opacity: .85;
}
</style>

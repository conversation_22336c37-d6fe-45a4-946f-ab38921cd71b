<template>
  <v-container
    fluid
    class="pa-0 d-flex flex-column align-end"
  >
    <header-datatable
      default-sort-column="name"
      default-sort-type="asc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      sort-column-id="sort_column_product"
      sort-type-id="sort_type_product"
      @on-filter-change="getItems({filter: $event, page: $route.query?.page_product})"
      @on-search-icon-click="getItems({searchKey: $event})"
    >
      <template #data-button>
        <menu-export-import
          :route-import="`${$route.path}/import-data${role === 'SHIPMENT_COMPANY' ? '' : `/${id}`}`"
          query-import-type="PRODUCT"
        />
      </template>

      <template #button>
        <form-product-dialog
          :submit-type="`create`"
          :is-loading-form="isLoadingFormProduct"
          :dialog="dialogProduct"
          :clear-form="clearForm"
          @on-click-close="dialogProduct = false; clearForm = true"
          @on-click-add="createItem"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              v-if="$vuetify.breakpoint.xs"
              v-bind="attrs"
              depressed
              color="primary"
              class="text-capitalize mt-5"
              x-large
              v-on="on"
              @click="dialogProduct = true; clearForm = false"
            >
              {{ $t('lspCustomer.add_product') }}
            </v-btn>
            <v-btn
              v-else
              v-bind="attrs"
              depressed
              color="primary"
              class="text-capitalize"
              x-large
              v-on="on"
              @click="dialogProduct = true; clearForm = false"
            >
              {{ $t('lspCustomer.add_product') }}
            </v-btn>
          </template>
        </form-product-dialog>
      </template>
    </header-datatable>

    <v-container fluid class="pa-0 mb-10">
      <product-loading v-if="isLoadingProduct" />

      <v-container v-else fluid class="pa-0">
        <v-row v-if="data.items.length !== 0" class="ma-n5">
          <v-col
            v-for="(item, i) in data.items"
            :key="item.id"
            :class="`pa-5 ` + classGrid"
          >
            <product-card-item
              :index="parseInt(i)"
              :product="item"
              :is-loading-form="isLoadingFormProduct"
              :dialog-product="dialogCardProduct[i]"
              :dialog-delete-product="dialogDeleteProduct[i]"
              @on-open-edit-dialog="$set(dialogCardProduct, i, true)"
              @on-close-edit-dialog="$set(dialogCardProduct, i, false)"
              @on-open-delete-dialog="$set(dialogDeleteProduct, i, true)"
              @on-close-delete-dialog="$set(dialogDeleteProduct, i, false)"
              @on-click-save="updateItem"
              @on-click-delete="deleteItem"
            />
          </v-col>
        </v-row>

        <v-row v-else>
          <v-col class="justify-center align-center fill-height">
            <empty-placeholder
              hero="empty-placeholder.svg"
              :message-title="$t('productManagement.empty_title')"
              :message-description="$t('productManagement.empty_text')"
            />
          </v-col>
        </v-row>
      </v-container>
    </v-container>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page_product"
      @on-change-page="getItems({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column_product,
          sortType: $route.query?.sort_type_product
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import MenuExportImport from '~/components/MenuExportImport.vue'
import FormProductDialog from '~/components/shipping-company/FormProductDialog.vue'
import ProductCardItem from '~/components/ProductCardItem.vue'
import ProductLoading from '~/components/loading/ProductLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'ProductManagement',

  components: {
    MenuExportImport,
    FormProductDialog,
    ProductCardItem,
    ProductLoading,
    EmptyPlaceholder,
    PaginationComponent
  },

  props: {
    id: {
      type: String,
      required: true
    },
    classGrid: {
      type: String,
      required: true
    }
  },

  data: () => ({
    searchKey: '',
    filterOptions: false,
    sortColumn: 'name',
    sortType: 'asc',
    sortColumnItems: {
      identity: {
        label: 'Identity',
        value: 'identity'
      },
      name: {
        label: 'Name',
        value: 'name'
      },
      productType: {
        label: 'Product Type',
        value: 'product_type'
      },
      unitType: {
        label: 'Unit Type',
        value: 'unit_type'
      },
      unit: {
        label: 'Unit',
        value: 'unit'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    clearForm: false,
    dialogProduct: false,
    dialogCardProduct: [] as Array<Boolean>,
    dialogDeleteProduct: [] as Array<Boolean>
  }),

  computed: {
    role (): string {
      return this.$auth.$state.user.data.role
    },
    data () {
      return this.$store.getters['shipping-company/product/data']
    },
    isLoadingProduct () {
      return this.$store.getters['shipping-company/product/isLoading']
    },
    isLoadingFormProduct () {
      return this.$store.getters['shipping-company/product/isLoadingForm']
    },
    isLoadingUserFormClearImage () {
      return this.$store.getters['shipping-company/product/isLoadingFormClearImage']
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', this.$t('scProduct.page_title'))
    this.getItems({
      page: this.$route.query?.page_product as string
    })
  },

  methods: {
    clearImage (id: string, index: number) {
      this.$store.dispatch('product/removePhoto', id)
        .then(() => {
          this.getItems({})
        })
        .then(() => {
          this.$set(this.dialogCardProduct, index, true)
        })
    },
    getItems ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('shipping-company/product/getItems', {
        searchColumns: 'identity,name,product_type,unit,unit_type',
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'shipment_company_id',
        filterKeys: this.id,
        page
      })
    },
    async createItem (values: any) {
      const response = await this.$store.dispatch('shipping-company/product/createItem', {
        values,
        idSc: this.id
      })

      if (response) {
        this.dialogProduct = false
        this.clearForm = true

        this.getItems({
          page: this.$route.query?.page_product as string
        })
      }
    },
    async updateItem (values: any, i: any) {
      const response = await this.$store.dispatch('shipping-company/product/updateItem', {
        values,
        idSc: this.id
      })

      if (response) {
        this.$set(this.dialogCardProduct, i, false)

        this.getItems({
          page: this.$route.query?.page_product as string
        })
      }
    },
    async deleteItem (id: any, i: any) {
      const response = await this.$store.dispatch('shipping-company/product/deleteItem', { id, idSc: this.id })
      if (response) {
        this.$set(this.dialogDeleteProduct, i, false)

        this.getItems({
          page: this.$route.query?.page_product as string
        })
      }
    }
  }
})
</script>

<style scoped> </style>

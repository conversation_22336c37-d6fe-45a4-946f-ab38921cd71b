import { Driver } from './driver'

export interface Personalize {
  name: string,
  logo: string | null,
  logo_url: string | null,
  primary_color: string | null,
  secondary_color: string | null,
  register_banner: string | null,
  register_banner_url: string | null,
}

export interface Vendor {
  weight: number
  id: string
  name: string
  address: string
  logo: string | null
  read_at: Date | null
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  status: string | null
  logo_url: string | null
}
export interface LogisticsServiceProvider {
  id: string
  name: string
  address: string
  collaborated_at: string | null
  logo: string | null
  register_banner: string | null
  domain: string
  session_last_identity: number
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  status: string | null
  logo_url: string | null
  register_banner_url: string | null
}

export interface ShippingCompany {
  id: string
  name: string
  address: string
  logo: string | null
  read_at: Date | null
  status: string
  logistics_service_provider_id: string
  session_last_order_identity: number
  pickup_dropoff_location_points_count: number
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  logo_url: string | null
  products_count: number | null
}

export interface User {
  id: string
  nationalIdentity: string
  name: string
  email: string
  avatar: string | null
  phone_country_code: string
  phone_number: string
  role: string
  fcm_token: string | null
  email_verified_at: Date | null
  logistics_service_provider_id: string | null
  shipment_company_id: string | null
  vendor_id: string | null
  driver_id: string | null
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  avatar_url: string | null
  driver: Driver | null
  shipment_company: ShippingCompany | null
  vendor: Vendor | null
  logistics_service_provider: LogisticsServiceProvider | null
}

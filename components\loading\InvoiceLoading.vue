<template>
  <v-row class="d-flex">
    <v-col
      v-for="i in 3"
      :key="i"
      class="pa-5 col-lg-4 col-sm-6 col-12"
    >
      <v-sheet height="165" class="pa-5 overflow-hidden">
        <div class="d-flex">
        <div class="d-flex flex-row " style="width: 100%">
          <v-skeleton-loader type="image" height="50" width="50" class="mt-3"/>
          <div class="d-flex flex-column" style="width: 100%">
            <v-skeleton-loader type="list-item-two-line" width="100%" />
          </div>
        </div>
        </div>
        <v-container class="d-flex justify-space-between pl-0 pr-0">
          <div class="d-flex flex-column">
            <v-skeleton-loader type="text" width="150"  />
            <v-skeleton-loader type="text" width="100" class="mt-2" />
          </div>
          <v-skeleton-loader type="image" width="150" height="35"  />
        </v-container>
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'InvoiceLoading'
})

</script>

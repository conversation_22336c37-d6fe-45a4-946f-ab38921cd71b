<template>
  <div class="pa-0 mt-10">
    <v-skeleton-loader v-show="headingTabs" type="heading" class="mb-5" />
    <v-sheet height="300" class="pa-10" style="border-radius: 5px">
      <v-skeleton-loader type="table-row-divider@4" />
    </v-sheet>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TableOrderShipmentLoading',
  props: {
    headingTabs: {
      type: Boolean,
      default: true
    }
  }
})
</script>

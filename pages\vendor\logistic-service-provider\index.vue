<template>
  <v-container fluid class="pa-0 px-md-10 px-5 pb-10 d-flex flex-column align-end">
    <v-container fluid class="pa-0 d-flex justify-space-between align-start flex-sm-row flex-column">
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-filter-change="getDataLSPs({ filter: $event, page: $route.query?.page })"
        @on-search-icon-click="getDataLSPs({ searchKey: $event })"
      >
        <template #toggle-button>
          <toggle-button />
        </template>
        <template #button>
          <v-btn
            v-if="$vuetify.breakpoint.xs"
            depressed
            color="primary"
            class="text-capitalize mt-5"
            x-large
            :to="localePath('/vendor/logistic-service-provider/requests')"
          >
            {{ $t('vendorLsp.find_lsp') }}
          </v-btn>
          <v-btn
            v-else
            depressed
            color="primary"
            class="text-capitalize"
            x-large
            :to="localePath('/vendor/logistic-service-provider/requests')"
          >
            {{ $t('vendorLsp.find_lsp') }}
          </v-btn>
        </template>
      </header-datatable>
    </v-container>

    <display-mode class="mb-10">
      <template #card-mode>
        <v-container fluid class="pa-0">
          <lsa-loading v-if="isLoadingLSP" />
          <v-row v-else class="ma-n5 pa-3">
            <v-row v-if="logisticServiceProviders.items.length !== 0">
              <v-col
                v-for="item in logisticServiceProviders.items"
                :key="item.id"
                class="pa-5 col-lg-4 col-sm-6 col-12"
              >
                <lsp-card-item :lsp="item" />
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-placeholder.svg"
                  :message-title="$t('vendorLsp.empty_message_title')"
                  :message-description="$t('vendorLsp.empty_message_description')"
                />
              </v-col>
            </v-row>
          </v-row>
        </v-container>
      </template>

      <template #data-table-mode>
        <v-container
          fluid
          class="pa-0 mt-3"
          style="background-color: #f0f0f0"
        >
          <div v-if="logisticServiceProviders.items">
            <v-data-table
              :loading="isLoadingLSP"
              loading-text="Loading... Please wait"
              :headers="tableHeaders"
              :items="logisticServiceProviders.items"
              :page.sync="page"
              :single-expand="singleExpand"
              :expanded.sync="expanded"
              :items-per-page="-1"
              hide-default-footer
              class="pa-md-10 pa-5"
              style=""
              @page-count="pageCount = $event"
            >
              <template #item.image="{ item }">
                <v-avatar rounded size="40" class="mr-5">
                  <img
                    v-if="item.logo_url"
                    :src="item.logo_url"
                    alt="logo"
                  >
                  <v-icon v-else size="40">
                    mdi-domain
                  </v-icon>
                </v-avatar>
              </template>
            </v-data-table>
          </div>
          <div v-else>
            <div class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-placeholder.svg"
                :message-title="$t('vendorLsp.empty_message_title')"
                :message-description="$t('vendorLsp.empty_message_description')"
              />
            </div>
          </div>
        </v-container>
      </template>
    </display-mode>

    <pagination-component
      :page="logisticServiceProviders.page"
      :total-page="logisticServiceProviders.totalPage"
      page-id="page"
      @on-change-page="getDataLSPs({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import LspCardItem from '~/components/LspCardItem.vue'
import LsaLoading from '~/components/loading/LsaLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import ToggleButton from '~/components/ToggleButton.vue'
import DisplayMode from '~/components/DisplayMode.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'LogisticServiceProviderPage',

  components: {
    LspCardItem,
    LsaLoading,
    EmptyPlaceholder,
    ToggleButton,
    DisplayMode,
    PaginationComponent
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      },
      address: {
        label: 'Address',
        value: 'address'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    singleExpand: true,
    expanded: [],
    button: true,
    page: 1,
    pageCount: 0,
    tableHeaders: [
      { text: 'Logistic Provider Image', value: 'image' },
      { text: 'Logistic Provider Name', value: 'name' },
      { text: 'Location', value: 'address' },
      { text: 'Status', value: 'status' }
    ]
  }),

  computed: {
    logisticServiceProviders () {
      return this.$store.getters['vendor/logistic-service-provider/data']
    },
    isLoadingLSP () {
      return this.$store.getters['vendor/logistic-service-provider/isLoading']
    }
  },

  mounted () {
    this.getDataLSPs({
      page: this.$route.query?.page as string
    })
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Cooperation Logistic Provider')
  },

  methods: {
    getDataLSPs ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: '',
        sortType: ''
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vendor/logistic-service-provider/getItems', {
        vendorId: user.id,
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'vendors.status',
        filterKeys: 'COLLABORATE',
        page
      })
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}
</style>

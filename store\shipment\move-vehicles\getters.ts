import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { MoveVehiclesState } from '~/store/shipment/move-vehicles/state'

export const getters: GetterTree<MoveVehiclesState, RootState> = {
  order (state) {
    return state.order
  },

  selectedTracks (state) {
    return state.selectedTracks
  },

  selectedShippingCompanyId (state) {
    return state.selectedShippingCompanyId
  },

  moveType (state) {
    return state.moveType
  },

  isLoading (state) {
    return state.isLoadingForm
  }
}

export default getters

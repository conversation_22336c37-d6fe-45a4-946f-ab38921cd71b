<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column align-end"
  >
    <header-datatable
      default-sort-column="created_at"
      default-sort-type="desc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-search-icon-click="getListInvoiceDetail({searchKey: $event})"
      @on-filter-change="getListInvoiceDetail({filter: $event, page: $route.query?.page})"
    />

    <v-container fluid class="pa-0 mb-10">
      <invoice-loading v-if="isLoading" />
      <v-row
        v-else
        class="ma-n5 pa-3"
      >
        <v-row v-if="data.items.length !== 0">
          <v-col
            v-for="(item, i) in data.items"
            :key="i"
            class="pa-5 col-lg-4 col-sm-6 col-12"
          >
            <invoice-card-item
              :id="item.id"
              :identity="item.order.identity"
              :read-at="item.read_at"
              :date="$moment(item.created_at).format('DD MMM YYYY')"
              detail-route="/shipping-company/order-shipment/invoice-order"
            />
          </v-col>
        </v-row>
        <v-row v-else>
          <v-col class="justify-center align-center fill-height">
            <empty-placeholder
              hero="empty-placeholder.svg"
              :message-title="$t('scInvoiceOrder.empty_placeholder_title')"
              :message-description="$t('scInvoiceOrder.empty_placeholder_description')"
            />
          </v-col>
        </v-row>
      </v-row>
    </v-container>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getListInvoiceDetail({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import InvoiceCardItem from '~/components/shipping-company/InvoiceCardItem.vue'
import InvoiceLoading from '~/components/loading/InvoiceLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'InvoiceOrderPage',

  components: {
    HeaderDatatable,
    InvoiceCardItem,
    InvoiceLoading,
    EmptyPlaceholder,
    PaginationComponent
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    searchKey: '',
    sortColumn: 'order.identity',
    sortType: 'asc',
    sortColumnItems: {
      date: {
        label: 'Date',
        value: 'created_at'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    }
  }),

  computed: {
    data () {
      return this.$store.getters['invoice-details/data']
    },

    isLoading () {
      return this.$store.getters['invoice-details/isLoading']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', this.$t('scInvoiceOrder.page_title'))
  },

  mounted () {
    this.getListInvoiceDetail({
      page: this.$route.query?.page as string
    })
  },

  methods: {
    getListInvoiceDetail ({
      page = '',
      searchKey = '',
      filter = { sortColumn: 'created_at', sortType: 'desc' }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('invoice-details/getItems', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'order.status',
        filterKeys: 'PUBLISHED',
        entries: 12,
        page
      })
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}
</style>

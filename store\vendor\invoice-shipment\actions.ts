import { ActionTree } from 'vuex'
import { RootState } from '~/store'
import { InvoiceShipmentState } from '~/store/vendor/invoice-shipment/state'
import { toastSuccess } from '~/utils/toasts'
import { exception<PERSON>and<PERSON> } from '~/utils/functions'

export const actions: ActionTree<InvoiceShipmentState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/invoices', {
      params: {
        page: payload.page,
        search_columns: payload?.searchColumns,
        search_key: payload?.searchKey,
        filter_keys: payload?.filterKeys,
        filter_columns: payload?.filterColumns
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  getVendorItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING_VENDOR', true)

    this.$axios.get('/v1/invoices', {
      params: {
        page: payload.page,
        search_columns: payload?.searchColumns,
        search_key: payload?.searchKey,
        filter_keys: payload?.filterKeys,
        filter_columns: payload?.filterColumns
      }
    }).then((response: any) => {
      commit('SET_VENDOR_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE_VENDOR', response.data.meta.last_page)

      commit('SET_PAGE_VENDOR', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_VENDOR', false)
    })
  },

  async getItemDetail ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)
    return await this.$axios.get(`v1/invoices/${payload.id}`).then((response: any) => {
      commit('SET_ITEM', response.data.data)

      return response.data.data
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_DETAIL', false)
    })
  },

  createItem ({ state, commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_PUBLISH', true)
    this.$axios.put(`v1/invoices/${payload.id}/publish`, {
      id: payload.id
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      dispatch('getItemDetail', { id: payload.id, page: state.page })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_PUBLISH', false)
    })
  },

  updateItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    const data = {
      cost: payload.cost,
      invoice_id: payload.invoice_id,
      order_id: payload.order_id,
      total_odometer: payload.total_odometer,
      vehicle_detail_id: undefined
    }

    if (payload.vehicle_detail_id) {
      data.vehicle_detail_id = payload.vehicle_detail_id
    }

    this.$axios
      .put('/v1/invoice-details/' + payload.id, data)
      .then((response: any) => {
        commit('SET_IS_LOADING_DETAIL', false)
        toastSuccess(response.data.message, this)
        dispatch('getItemDetail', { id: payload.invoice_id, page: state.page })
      }).catch((error: any) => {
        exceptionHandler(error, this)
      }).finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  }
}

export default actions

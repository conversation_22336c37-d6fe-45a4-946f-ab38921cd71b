<template>
  <v-container fluid class="pa-0">
    <v-container fluid class="mb-10 pa-0 d-flex align-center">
      <v-text-field
        v-model="searchKey"
        :loading="isLoadingLocation"
        flat
        solo
        :label="$t('lspDetailScLocation.search')"
        hide-details
        append-icon="mdi-magnify"
        height="56"
        class="mr-5"
        @input="getDataLocation"
      />

      <menu-export-import
        :route-import="`${$route.path}/import-data/${idSc}`"
        query-import-type="LOCATION"
      />

      <v-menu
        v-if="$vuetify.breakpoint.mdAndDown"
        v-model="menu"
        offset-y
        auto
        transition="slide-y-transition"
      >
        <template #activator="{ on, attrs }">
          <v-btn
            fab
            elevation="0"
            color="white"
            class="rounded custom-btn-hover"
            :on="on"
            :attrs="attrs"
            @click="menu = true"
          >
            <v-icon>
              mdi-dots-vertical
            </v-icon>
          </v-btn>
        </template>

        <v-list>
          <location-form-dialog
            :dialog="dialogCreateLocation.icon"
            :clear-form="clearFormLocation.create.icon"
            :is-loading-form="isLoadingFormLocation"
            @on-close-dialog="
              dialogCreateLocation.icon = false
              clearFormLocation.create.icon = true
            "
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                v-bind="attrs"
                v-on="on"
                @click="
                  dialogCreateLocation.icon = true
                  clearFormLocation.create.icon = false
                "
              >
                <v-icon class="mr-3">
                  mdi-plus
                </v-icon>
                <p class="ma-0 body-1">
                  {{ $t('lspDetailScLocation.add_location') }}
                </p>
              </v-list-item>
            </template>
          </location-form-dialog>

          <location-form-dialog
            :location="selectedLocation"
            :dialog="dialogUpdateLocation"
            :is-loading-form="isLoadingFormLocation"
            @on-close-dialog="dialogUpdateLocation = false"
            @on-click-save="updateLocation"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                :disabled="selectedLocation === null"
                v-bind="attrs"
                v-on="on"
                @click="dialogUpdateLocation = true"
              >
                <v-icon class="mr-3">
                  mdi-pencil
                </v-icon>
                <p class="ma-0 body-1">
                  {{ $t('lspDetailScLocation.edit_location') }}
                </p>
              </v-list-item>
            </template>
          </location-form-dialog>

          <!-- <v-dialog
            v-model="dialogDeleteLocation"
            max-width="600px"
            style="z-index: 999"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                :disabled="selectedLocation === null"
                fab
                elevation="0"
                color="white"
                class="ma-0 rounded custom-btn-hover"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon class="mr-3">
                  mdi-delete
                </v-icon>
                <p class="ma-0 body-1">
                  {{ $t('lspDetailScLocation.delete_location') }}
                </p>
              </v-list-item>
            </template>

            <v-card>
              <v-card-title class="text-h6 lighten-2">
                {{ $t('lspDetailScLocation.confirm_delete_title') }} {{ selectedLocation?.name }}
              </v-card-title>

              <v-card-text>
                {{ $t('lspDetailScLocation.confirm_delete_text') }}
              </v-card-text>

              <v-divider />

              <v-card-actions>
                <v-spacer />
                <v-btn
                  color="primary"
                  text
                  :loading="isLoadingFormLocation"
                  @click="deleteLocation(selectedLocation.id)"
                >
                  {{ $t('lspDetailScLocation.yes') }}
                </v-btn>
                <v-btn
                  color="primary"
                  @click="dialogDeleteLocation = false"
                >
                  {{ $t('lspDetailScLocation.cancel') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog> -->
        </v-list>
      </v-menu>

      <div v-else class="d-flex">
        <location-form-dialog
          :dialog="dialogCreateLocation.icon"
          :is-loading-form="isLoadingFormLocation"
          :clear-form="clearFormLocation.create.icon"
          @on-close-dialog="
            dialogCreateLocation.icon = false
            clearFormLocation.create.icon = true
          "
          @on-click-add="createLocation($event, 'ICON')"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              fab
              elevation="0"
              color="white"
              class="mr-5 rounded custom-btn-hover"
              v-bind="attrs"
              v-on="on"
              @click="
                dialogCreateLocation.icon = true
                clearFormLocation.create.icon = false
              "
            >
              <v-icon>
                mdi-plus
              </v-icon>
            </v-btn>
          </template>
        </location-form-dialog>

        <location-form-dialog
          :location="selectedLocation"
          :dialog="dialogUpdateLocation"
          :is-loading-form="isLoadingFormLocation"
          @on-close-dialog="dialogUpdateLocation = false"
          @on-click-save="updateLocation"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              :disabled="selectedLocation === null"
              fab
              elevation="0"
              color="white"
              class="mr-5 rounded custom-btn-hover"
              v-bind="attrs"
              v-on="on"
              @click="dialogUpdateLocation = true"
            >
              <v-icon>
                mdi-pencil
              </v-icon>
            </v-btn>
          </template>
        </location-form-dialog>

        <!-- <v-dialog
          v-model="dialogDeleteLocation"
          max-width="600px"
          style="z-index: 999"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              :disabled="selectedLocation === null"
              fab
              elevation="0"
              color="white"
              class="ma-0 rounded custom-btn-hover"
              v-bind="attrs"
              v-on="on"
            >
              <v-icon>
                mdi-delete
              </v-icon>
            </v-btn>
          </template>

          <v-card>
            <v-card-title class="text-h6 lighten-2">
              {{ $t('lspDetailScLocation.confirm_delete_title') }} {{ selectedLocation?.name }}
            </v-card-title>

            <v-card-text>
              {{ $t('lspDetailScLocation.confirm_delete_text') }}
            </v-card-text>

            <v-divider />

            <v-card-actions>
              <v-spacer />
              <v-btn
                :loading="isLoadingFormLocation"
                color="primary"
                text
                @click="deleteLocation(selectedLocation.id)"
              >
                {{ $t('lspDetailScLocation.yes') }}
              </v-btn>
              <v-btn
                color="primary"
                @click="dialogDeleteLocation = false"
              >
                {{ $t('lspDetailScLocation.cancel') }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog> -->
      </div>
    </v-container>

    <v-container v-if="!isLoadingLocation" fluid class="pa-0 rounded white">
      <v-row class="ma-0 d-flex flex-column flex-lg-row">
        <v-col
          v-show="dataLocation.length > 0"
          :class="dataLocation.length > 0 ? 'd-flex pa-0 col-auto overflow-y-auto':''"
          style="max-height: 540px"
        >
          <v-list flat class="pa-10 pb-5 overflow-y-auto" width="100%">
            <v-list-item-group mandatory>
              <v-list-item
                v-for="(location, i) in dataLocation"
                :key="location.id"
                color="primary"
                class="mb-5 pa-0 text-capitalize rounded"
                :style="isSelectedLocation[i] ? `border: 1px solid #EF3434;` : 'border: 1px solid #CFCCCC;'"
                @click="
                  isSelectedLocation = [null]
                  $set(isSelectedLocation, i, !isSelectedLocation[i])
                  selectedLocation = location
                "
              >
                <v-container class="pa-5 d-flex justify-space-between">
                  <div class="d-flex flex-column align-start">
                    <a class="subtitle-1 mb-2 black--text">
                      {{ location?.name }}
                    </a>
                    <p class="caption ma-0 text-secondary">
                      {{ location.start_operation_hour }} - {{ location.end_operation_hour }} WIB
                    </p>
                    <div class="d-flex">
                      <a class="caption black--text">Type : </a>
                      <a class="caption text-info font-weight-bold">
                        {{ location.type.replace(' ', ' & ') }}
                      </a>
                    </div>
                  </div>
                  <v-icon color="black">
                    mdi-crosshairs-gps
                  </v-icon>
                </v-container>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-col>

        <v-col
          v-show="!(dataLocation.length > 0)"
          class="pa-0"
        >
          <v-container class="pa-10 d-flex flex-column align-center justify-center fill-height">
            <h4 class="mb-5">
              {{ $t('lspDetailScLocation.empty_location_title') }}
            </h4>
            <p class="body-1 mb-10 text-center">
              {{ $t('lspDetailScLocation.empty_location_text') }}
            </p>

            <location-form-dialog
              :show-component="false"
              :is-loading-form="isLoadingFormLocation"
              :dialog="dialogCreateLocation.text"
              :clear-form="clearFormLocation.create.text"
              @on-click-add="createLocation($event, 'TEXT')"
              @on-close-dialog="
                dialogCreateLocation.text = false
                clearFormLocation.create.text = true
              "
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  elevation="0"
                  x-large
                  color="primary"
                  class="text-capitalize"
                  v-bind="attrs"
                  v-on="on"
                  @click="
                    dialogCreateLocation.text = true
                    clearFormLocation.create.text = false
                  "
                >
                  {{ $t('lspDetailScLocation.create_location') }}
                </v-btn>
              </template>
            </location-form-dialog>
          </v-container>
        </v-col>

        <v-col
          class="pa-0"
          style="position: relative"
        >
          <v-responsive min-height="540" max-height="540" style="z-index: 1">
            <custom-map
              :latitude="
                !isNaN(parseFloat(selectedLocation?.latitude)) ?
                  parseFloat(selectedLocation?.latitude) :
                  defaultLatitude"
              :longitude="
                !isNaN(parseFloat(selectedLocation?.longitude)) ?
                  parseFloat(selectedLocation?.longitude) :
                  defaultLongitude
              "
            >
              <template v-if="selectedLocation !== null" #marker>
                <l-marker
                  :lat-lng="[
                    !isNaN(parseFloat(selectedLocation?.latitude)) ?
                      parseFloat(selectedLocation?.latitude) :
                      defaultLatitude,
                    !isNaN(parseFloat(selectedLocation?.longitude)) ?
                      parseFloat(selectedLocation?.longitude) :
                      defaultLongitude
                  ]"
                />
              </template>
            </custom-map>
          </v-responsive>

          <div
            v-if="selectedLocation !== null"
            class="white rounded pa-5 elevation-2"
            style="position: absolute; bottom: 20px; right: 20px; left: 20px; z-index: 999"
          >
            <p class="mb-2 caption">
              {{ $t('lspDetailScLocation.location') }}
            </p>
            <p class="ma-0 body-1">
              {{ selectedLocation?.address }}
            </p>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import CustomMap from '~/components/shipping-company/CustomMap.vue'
import LocationFormDialog from '~/components/logistic-service-provider/LocationFormDialog.vue'
import { PickupDropOffLocationPoint } from '~/types/product'
import { defaultLat, defaultLng } from '~/utils/functions'

export default Vue.extend({
  name: 'DetailScLocation',

  components: { CustomMap, LocationFormDialog },

  props: {
    idSc: {
      type: String || null,
      default: null
    }
  },

  data: () => ({
    menu: false,
    searchKey: '',
    isSelectedLocation: [] as any[],
    selectedLocation: null as PickupDropOffLocationPoint | null,
    dialogCreateLocation: {
      icon: false,
      text: false
    },
    dialogUpdateLocation: false,
    dialogDeleteLocation: false,
    clearFormLocation: {
      create: { icon: false, text: false },
      update: false
    },
    defaultLatitude: defaultLat,
    defaultLongitude: defaultLng
  }),

  computed: {
    dataLocation (): PickupDropOffLocationPoint[] {
      const locations = [...this.$store.getters['pick-up-drop-off-location-point/data'].items]

      return locations.sort((a: PickupDropOffLocationPoint, b: PickupDropOffLocationPoint) => {
        return a.name.localeCompare(b.name)
      })
    },

    isLoadingLocation (): Boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },
    isLoadingFormLocation (): Boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoadingForm']
    }
  },

  watch: {
    idSc: {
      handler () {
        this.getDataLocation()
      },
      immediate: true
    }
  },

  methods: {
    getDataLocation () {
      this.$store.dispatch('pick-up-drop-off-location-point/getItems', {
        idSc: this.idSc,
        searchKey: this.searchKey,
        entries: -1
      })
    },

    async createLocation (formValues: any, type: string) {
      const response = await this.$store.dispatch('pick-up-drop-off-location-point/createItem', {
        name: formValues.name,
        identity: formValues.identity,
        address: formValues.address,
        startOperationHour: formValues.startOperationHour,
        endOperationHour: formValues.endOperationHour,
        longitude: formValues.longitude,
        latitude: formValues.latitude,
        type: formValues.type.value,
        category: formValues.category,
        idSc: this.idSc
      })

      if (response) {
        switch (type) {
          case 'ICON' : this.clearFormLocation.create.icon = true; break
          case 'TEXT' : this.clearFormLocation.create.text = true; break
          default : break
        }
        this.dialogCreateLocation.icon = false
        this.dialogCreateLocation.text = false
      }
    },

    async updateLocation (formValues: any) {
      const response = await this.$store.dispatch('pick-up-drop-off-location-point/updateItem', {
        id: formValues.id,
        identity: formValues.identity,
        name: formValues.name,
        longitude: formValues.longitude,
        latitude: formValues.latitude,
        startOperationHour: formValues.startOperationHour,
        endOperationHour: formValues.endOperationHour,
        type: formValues.type?.value,
        address: formValues.address,
        category: formValues.category,
        idSc: this.idSc
      })

      if (response) {
        this.dialogUpdateLocation = false
      }
    },

    async deleteLocation (id: any) {
      await this.$store.dispatch('pick-up-drop-off-location-point/deleteItem', {
        idLocation: id,
        idSc: this.idSc
      })

      this.dialogDeleteLocation = false
    }
  }
})
</script>

<style scoped lang="scss"> </style>

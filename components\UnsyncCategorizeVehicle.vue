<template>
    <v-container fluid class="pa-0">
      <v-row class="ma-0 mx-n5 mb-10">
        <v-col class="col-12 col-lg-6 pa-0 px-5 mb-10 mb-lg-0 d-flex flex-column">
          <v-container fluid class="pa-0 mb-2 d-flex align-center justify-space-between">
            <v-checkbox
              v-model="isSelectedAll"
              label="Select All"
              hide-details
              class="ma-0"
            />
  
            <v-btn
              :disabled="!isSelectedAll"
              text
              color="primary"
              class="subtitle-1 text-capitalize"
              @click="isSelectedAll = !isSelectedAll"
            >
              Unselect All
            </v-btn>
          </v-container>
  
          <v-container
            fluid
            class="rounded d-flex flex-column"
            style="border: 1px solid #CFCCCC; min-height: 385px !important; max-height: 320px !important;"
          >
            <v-text-field
              v-model="searchKey"
              outlined
              label="Search Plate"
              append-icon="mdi-magnify"
              hide-details
              class="mb-3"
            />
            <items-group-checkbox
              :items="filteredPlateNumbers"
              :is-selected-all="isSelectedAll"
              :reset-selected-items="resetSelectedItems"
              @on-select-item="selectedPlateNumbers = $event; resetSelectedItems = false"
            />
          </v-container>
        </v-col>
  
        <v-col class="col-12 col-lg-6 pa-0 px-5 d-flex flex-column">
          <v-container fluid class="pa-0 mb-4 d-flex align-center justify-space-between">
            <h4>Vehicle Selected Category</h4>
            <v-btn
              text
              color="primary"
              class="subtitle-1
              text-capitalize"
              :disabled="appliedPlateNumbers.length === 0"
              @click="deleteVehicleDetails()"
            >
              Delete All
            </v-btn>
          </v-container>
          <v-container
            fluid
            class="pa-3 rounded overflow-y-auto"
            style="border: 1px solid #CFCCCC; min-height: 360px !important; max-height: 360px !important;"
          >
          <v-chip
            v-for="(item, index) in appliedPlateNumbers"
            :key="index"
            color="primary"
            text-color="white"
            class="mr-2 mb-2"
            close
            @click:close="deleteVehicleDetail(index)" 
        >
            {{ item.text }}
        </v-chip>
          </v-container>
        </v-col>
      </v-row>
  
      <v-row class="ma-0 mx-n3 align-center">
        <v-col class="d-flex">
        <v-btn
            class="mr-2"
            x-large
            depressed
            color="primary"
            @click="onClickApply"
        >
        {{ buttonLabel }}
        </v-btn>

        <v-btn
            x-large
            outlined
            color="primary"
            @click="onClickCancel"
            >
           Cancel
        </v-btn>

        <v-spacer></v-spacer>
        
        <v-btn
            x-large
            depressed
            color="primary"
            :disabled="appliedPlateNumbers.length === 0"
            @click="Unsync"
            >
           Unsync
        </v-btn>
    </v-col>
    </v-row>
    </v-container>
  </template>
  
  <script lang="ts">
  import Vue from 'vue'
  import { uuid } from 'uuidv4'
  import { Vehicle, VehicleDetail, VehicleFromFms } from '~/types/vehicle'
  import ItemsGroupCheckbox from '~/components/ItemsGroupCheckbox.vue'
  import { toastSuccess } from '~/utils/toasts'
  
  interface Item {
    value: string | number | null
    text: string
    subtitle: string | null
  }
  
  export default Vue.extend({
    name: 'UnsyncCategorizeVehicle',
  
    components: { ItemsGroupCheckbox },
  
    props: {
      type: {
        type: String,
        default: ''
      },
      importedPlateNumbers: {
        type: Array as () => string[],
        default: () => []
      },
      buttonLabel: {
        type: String,
        default: ''
      },
      vehicles: {
        type: Array as () => Vehicle[],
        default: () => []
      },
      isSelectedCategory: {
        type: Boolean,
        default: false
      }
    },
  
    data: () => ({
      isSelectedAll: false,
      resetSelectedItems: false as boolean,
      searchKey: '',
      appliedPlateNumbers: [] as any,
      selectedVehicle: null as Item | null,
      selectedPlateNumbers: [] as Item[],
    }),
  
    computed: {
      isLoadingVehicle (): boolean {
        return this.$store.getters['vehicle/isLoading']
      },

      vehicleDetail () {
        return this.$store.getters['vehicle/details/data'].items as VehicleDetail[]
      },

      isLoadingVehicleDetail () {
        return this.$store.getters['vehicle/details/isLoading'].items as VehicleDetail[]
      },
  
      reformattedPlateNumbers(): Item[] {
        const items = this.vehicleDetail.map((item: VehicleDetail) => {
            return { value: item.id, text: item.plate_number, subtitle: item.name };
        });
        return items;
       },

      filteredPlateNumbers(): Item[] {
        const filtered = this.reformattedPlateNumbers.filter(item => {
            const matches = item.text.toLowerCase().includes(this.searchKey.toLowerCase());
            return matches;
        });
            return filtered;
      },
    },

    mounted () {
      this.getVehicleDetailData()
    },
  
    methods:  {

        onClickApply() {
            this.appliedPlateNumbers = [...this.selectedPlateNumbers]
        },

        async Unsync() {
        const vehicleDetailIds = this.appliedPlateNumbers.map((item: { value: any }) => item.value);
        const payload = {
            vehicle_detail_id: vehicleDetailIds,
        };

        try {
            const response = await this.$store.dispatch('vendor/sync-vehicles/unSync', payload);
            console.log('Response from unSync action:', response);
            if (response) {
                this.getVehicleDetailData()
                toastSuccess('Success', this);
            }
                } catch (error) {
                    console.error('Error during unSync:', error);
                }
            },

        getVehicleDetailData () {
        this.$store.dispatch('vehicle/details/getItems', {
            mode: 'unsync',
            entries: -1
        })
        },
    
        onClickCancel () {
            this.isSelectedAll = false
            this.resetSelectedItems = true
            this.$emit('on-click-cancel')
        },
    
        deleteVehicleDetail(index: number) {
            this.appliedPlateNumbers.splice(index, 1);
        },
    
        async deleteVehicleDetails () {
            this.appliedPlateNumbers = [];
        }
    }
  })
  </script>
  
  <style scoped lang="scss"> </style>
  
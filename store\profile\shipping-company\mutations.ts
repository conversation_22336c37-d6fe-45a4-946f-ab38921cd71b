import { MutationTree } from 'vuex'
import { ProfileShipmentCompanyState } from './state'
import { ShippingCompany } from '~/types/user'

export const mutations: MutationTree<ProfileShipmentCompanyState> = {
  SET_SHIPMENT_COMPANY (state, shippingCompany: ShippingCompany) {
    state.shippingCompany = shippingCompany
  },
  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },
  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  }
}

export default mutations

import { MutationTree } from 'vuex'
import { InvoiceAdditionalFeeState } from './state'

export const mutations: MutationTree<InvoiceAdditionalFeeState> = {

  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }

}

export default mutations

import { MutationTree } from 'vuex'
import { VehicleDetailsState } from './state'
import { VehicleKey } from '~/types/vehicle'

export const mutations: MutationTree<VehicleDetailsState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },
  SET_DETAIL_ITEM (state, itemDetail: any) {
    state.itemDetail = itemDetail
  },
  SET_IS_HAS_NULL_VEHICLE_ID (state, isHasNullVehicleId: any) {
    state.isHasNullVehicleId = isHasNullVehicleId
  },

  SET_VEHICLE_KEYS (state, vehicleKeys: VehicleKey[]) {
    state.vehicleKeys = vehicleKeys
  },

  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  }
}

export default mutations

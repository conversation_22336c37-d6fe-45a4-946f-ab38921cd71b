import { Shipment, ShipmentActiveOrder, ShipmentVendor, Track } from '~/types/shipment'

export interface ShipmentState {
  isLoadingTrack: boolean
  isLoadingTrackForm: boolean
  isLoadingShipmentLoadmore: boolean
  isLoadingDialog: boolean
  trackItem: Track | null
  isLoading: boolean
  isLoadingForm: boolean
  isLoadingDetail: boolean
  isLoadingDownload: boolean
  isLoadingShipmentVendor: boolean
  dataShipment: Shipment[] | null
  dataShipmentActiveOrders: ShipmentActiveOrder[] | null
  dataShipmentProposed: Shipment[] | null
  dataShipmentDispatch: Shipment[] | null
  dataShipmentVendor: ShipmentVendor[] | null
  detailShipment: Shipment | null
  detailShipmentRitase: any[] | null
  totalPage: number,
  read_at: Date | null
  dialogItem: ShipmentVendor | null,
  responseAccept: object | null
  page: number
  totalData: number,
  idManualLoadWeight: number
}

export const state = () => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDetail: false,
  isLoadingDownload: false,
  dataShipment: null,
  dataShipmentActiveOrders: null,
  isLoadingTrack: true,
  isLoadingTrackForm: false,
  isLoadingShipmentLoadmore: false,
  isLoadingDialog: false,
  isLoadingShipmentVendor: false,
  dataShipmentProposed: null,
  dataShipmentVendor: null,
  dataShipmentDispatch: null,
  detailShipment: null,
  detailShipmentRitase: [],
  responseAccept: null,
  page: 1,
  totalPage: 1,
  trackItem: null,
  dialogItem: null as ShipmentVendor | null,
  totalData: 0,
  idManualLoadWeight: null
})

export default state

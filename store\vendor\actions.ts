import { ActionTree } from 'vuex'
import { RootState } from '~/store'
import { VendorState } from '~/store/vendor/state'
import { VendorOrderShipment } from '~/types/dashboard'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<VendorState, RootState> = {
  getVendors ({ commit }, payload) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/vendors', {
      params: {
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns: payload.searchColumns == null ? 'name' : payload.searchColumns,
        sort_column: payload.sortColumn == null ? 'name' : payload.sortColumn,
        sort_type: payload.sortType == null ? 'ASC' : payload.sortType,
        filter_columns: payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page == null ? '' : payload.page,
        entries: payload.entries == null ? null : payload.entries
      }
    }).then((response: any) => {
      commit('SET_RESULT', response.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  getVendor ({ commit }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios.get('/v1/vendors/' + payload.id).then((response: any) => {
      commit('SET_SELECTED_VENDOR', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  async updateCollaborationStatus ({ commit }, payload) {
    const isAccept = payload.status === 'COLLABORATE'
    commit('SET_IS_LOADING_FORM_STATUS', {
      accept: isAccept,
      reject: !isAccept
    })

    await this.$axios.post('/v1/vendors/' + payload.id + '/update-collaboration', {
      status: payload.status
    }
    ).then((response: any) => {
      commit('SET_SELECTED_VENDOR', response.data.data)

      commit('UPDATE_STATUS_VENDOR', {
        vendor: response.data.data,
        id: payload.id
      })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM_STATUS', false)
    })
  },

  async removeLogo ({
    commit,
    dispatch
  }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios.delete(`/v1/vendors/${payload}/logo`)
      .then(() => {
        dispatch('profile/getFormVendor', null, { root: true })
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  getVendorsReport ({ commit }, payload) {

    commit('SET_IS_LOADING_VENDOR_REPORT', true)

    this.$axios.get('/v1/vendor/report', {
      params: {
        type: payload.type,
        filter_by: payload.filterBy,
        product_owner: payload.productOwner,
        search: payload.search == null ? '' : payload.search,
        start_date: payload?.filterDateStart,
        end_date: payload?.filterDateEnd,
        vendor_id: payload?.vendorId,
        driver_id: payload.driverId,
        mill: payload.mill,
        refinery: payload.refinery,
        plate_number: payload.plateNumber,
        product: payload?.product,
        status: payload?.status,
        page: payload.page == null ? '' : payload.page
      }
    }).then((response: any) => {
      commit('SET_ITEMS_VENDOR_REPORT', response.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_VENDOR_REPORT', false)
    })
  },

  downloadExportExcel ({ commit }, payload: any) {
    commit('SET_IS_LOADING_EXPORT_EXCEL', true)

    this.$axios({
      method: 'get',
      url: '/v1/vendor/report/export-excel',
      params: {
        filter_by: payload.filterBy,
        type: payload.type,
        search: payload.search == null ? '' : payload.search,
        start_date: payload?.filterDateStart,
        end_date: payload?.filterDateEnd,
        vendor_id: payload?.vendorId,
        driver_id: payload.driverId,
        mill: payload.mill,
        refinery: payload.refinery,
        plate_number: payload.plateNumber,
        product: payload?.product,
        status: payload?.status,
        page: payload.page == null ? '' : payload.page
      },
      // responseType: 'blob'
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      // const url = window.URL.createObjectURL(new Blob([response.data]))

      // const link = document.createElement('a')
      // link.href = url
      // link.setAttribute('download', 'report_performance_vendor.xlsx')
      // document.body.appendChild(link)
      // link.click()

      // window.URL.revokeObjectURL(url)

      commit('SET_IS_LOADING_EXPORT_EXCEL', false)
    }).catch((error: any) => {
      exceptionHandler(error, this)
      commit('SET_IS_LOADING_EXPORT_EXCEL', false)
    })
  },

  getVendorOrderShipment ({ commit }, payload) {
    commit('SET_IS_LOADING_VENDOR_ORDER_SHIPMENT', true)

    this.$axios.get('/v1/vendor/report-order-shipment', {
      params: {
        start_date: payload?.filterDateStart,
        end_date: payload?.filterDateEnd,
        vendor_id: payload?.vendorId
      }
    }).then((response: any) => {
      commit('SET_VENDOR_ORDER_SHIPMENT', response.data.data)
    }).catch((error: any) => {
      commit('SET_VENDOR_ORDER_SHIPMENT', [] as VendorOrderShipment[])
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_VENDOR_ORDER_SHIPMENT', false)
    })
  }
}

export default actions

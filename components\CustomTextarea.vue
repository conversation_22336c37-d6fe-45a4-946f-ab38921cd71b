<template>
  <div>
    <div class="text-body">
      {{ label }}
    </div>
    <v-textarea
      class="no-shadow"
      :value="value"
      :label="hint"
      :hint="persistentHint"
      :persistent-hint="persistentHint != null"
      :type="type"
      :prepend-inner-icon="prependInnerIcon"
      :required="isRequired"
      :rules="rules"
      :append-icon="appendIcon"
      flat
      solo
      :loading="isLoading"
      :single-line="false"
      full-width
      outlined
      @input="$emit('input', $event)"
      @click:append="$emit('click:append')"
      @change="$emit('change', $event)"
      @keydown.enter="onEnter"
    />
  </div>
</template>

<script>
export default {
  name: 'CustomTextarea',
  props: {
    appendIcon: {
      type: String,
      default: ''
    },
    rules: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'text'
    },
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      required: true
    },
    hint: {
      type: String,
      default: ''
    },
    persistentHint: {
      type: String,
      default: ''
    },
    prependInnerIcon: {
      type: String,
      default: ''
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    onEnter (event) {
      event.preventDefault()

      this.$emit('on-enter', event.target.value)
    }
  }
}
</script>

<style scoped>

</style>

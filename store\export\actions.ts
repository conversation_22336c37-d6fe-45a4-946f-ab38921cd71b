import { ActionTree } from 'vuex'
import { ExportState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'
import { toastError } from '~/utils/toasts'

export const actions: ActionTree<ExportState, ExportState> = {

getExport ({ commit }, payload: any) {
    commit('SET_IS_LOADING_EXPORT', true)

    this.$axios
      .get('/v1/exports', {
        params: {
          entries: payload.entries == null ? '' : payload.entries,
          page: payload.page == null ? '' : payload.page,
        },
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      .then((response: any) => {
        if (response.data.success) {
          commit('SET_ITEM_EXPORT_RESULT', response.data)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_EXPORT', false)
      })
  },

  async deleteItem ({ commit }, payload: any) {
      commit('SET_IS_LOADING', true)
  
      await this.$axios
        .delete('/v1/exports/' + payload.id)
        .then((response: any) => {
          toastSuccess(response.data.message, this)
        })
        .catch((error: any) => {
          exceptionHandler(error, this)
        })
        .finally(() => {
          commit('SET_IS_LOADING', false)
        })
  },

}

export default actions

import { Track } from '~/types/shipment'
import { Vehicle } from '~/types/vehicle'
import { Order, SubOrder } from '~/types/product'

export interface MoveVehiclesState {
  moveType: string | undefined,
  order?: Order | undefined,
  newOrderIdentity?: string | undefined,
  selectedTracks: Track[],
  selectedAdditionalVehicles: Vehicle[],
  suborders: SubOrder[],
  selectedShippingCompanyId?: string | undefined,
  isLoadingForm: boolean
}

export const state = (): MoveVehiclesState => ({
  moveType: undefined,
  order: undefined,
  newOrderIdentity: undefined,
  selectedTracks: [],
  selectedAdditionalVehicles: [],
  suborders: [],
  selectedShippingCompanyId: undefined,
  isLoadingForm: false
})

export default state

<template>
  <v-card
    v-if="!isLoadingDetail"
    elevation="0"
    outlined
    class="mt-10 pa-5"
  >
    <v-row>
      <v-col cols="11">
        <div
          v-if="$vuetify.breakpoint.mdAndUp"
          class="mb-10"
        >
          <v-simple-table dense>
            <template #default>
              <thead>
                <tr>
                  <th id="identity_product" style="border: none" class="caption pa-0">
                    {{ $t('scSubOrderCardItem.identity_product') }}
                  </th>
                  <th id="product" style="border: none" class="caption pa-0">
                    {{ $t('scSubOrderCardItem.product') }}
                  </th>
                  <th id="quantity" style="border: none" class="caption pa-0">
                    {{ $t('scSubOrderCardItem.quantity') }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="product in subOrder.products"
                  :key="product.id"
                >
                  <td style="border: none" class="body-1 pa-0">
                    {{ product.identity }}
                  </td>
                  <td style="border: none" class="body-1 pa-0">
                    {{ product.name }}
                  </td>
                  <td style="border: none" class="body-1 pa-0">
                    {{ formatNumber(product.pivot.quantity) }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </div>

        <div
          v-else
        >
          <v-row
            v-for="product in subOrder.products"
            :key="product.id"
            class="ma-0"
          >
            <v-col class="mr-10 pa-0">
              <p class="caption mb-2 text-secondary">
                {{ product.identity }}
              </p>
              <p class="body-1 mb-2">
                {{ product.name }}
              </p>
            </v-col>

            <v-col class="pa-0">
              <p
                class="caption mb-2 text-secondary"
              >
                Quantity
              </p>
              <p
                class="body-1 mb-2"
              >
                {{ formatNumber(product.pivot.quantity) }}
              </p>
            </v-col>
          </v-row>
        </div>
      </v-col>
      <v-col cols="1" class="mt-1">
        <v-menu>
          <template
            #activator="{on, attrs}"
          >
            <v-icon
              color="black"
              v-bind="attrs"
              v-on="on"
            >
              mdi-dots-vertical
            </v-icon>
          </template>

          <v-list>
            <v-list-item link>
              <create-order-dialog
                :dialog-title="subOrder.type === 'PICKUP' ? 'Edit the Pickup Order' : 'Edit the Drop Off Order'"
                :sub-order-type="subOrder.type"
                :data-draft="dataDraft"
                :products="productDataForm"
                :locations="locationDataForm"
                :sub-order="subOrder"
                :is-loading-form="subOrderIsLoadingForm"
                :is-loading-form-product="isLoadingFormProduct"
                :dialog="dialogSubOrder"
                :date-range="{
                  minDate: new Date().toISOString().slice(0, 10),
                  maxDate: null,
                }"
                @on-click-add-product="onClickAddProduct"
                @on-click-save="onClickSave"
                @on-close-dialog="$emit('on-close-dialog', index)"
              >
                <template #activator="{ on, attrs }">
                  <v-list-item-title
                    v-bind="attrs"
                    v-on="on"
                    @click="$emit('on-open-dialog', index)"
                  >
                    {{ $t('scSubOrderCardItem.edit') }}
                  </v-list-item-title>
                </template>
              </create-order-dialog>
            </v-list-item>

            <!-- <v-dialog
              v-model="dialogDeleteSubOrder"
              max-width="600px"
            >
              <template #activator="{ on, attrs }">
                <v-list-item
                  key="delete"
                  v-bind="attrs"
                  v-on="on"
                  @click="onOpenDeleteDialog"
                >
                  <v-list-item-title>
                    {{ $t('scSubOrderCardItem.delete') }}
                  </v-list-item-title>
                </v-list-item>
              </template>
              <v-card>
                <v-card-title class="text-h6 lighten-2">
                  {{ $t('scSubOrderCardItem.delete_confirmation_title') + ' ' + subOrder.identity }}
                </v-card-title>

                <v-card-text>
                  {{ $t('scSubOrderCardItem.delete_confirmation_description') }}
                </v-card-text>

                <v-divider />

                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    color="primary"
                    text
                    :loading="subOrderIsLoadingForm"
                    @click="onClickDelete"
                  >
                    {{ $t('scSubOrderCardItem.button_yes') }}
                  </v-btn>
                  <v-btn
                    color="primary"
                    @click="onCloseDeleteDialog"
                  >
                    {{ $t('scSubOrderCardItem.button_cancel') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog> -->
          </v-list>
        </v-menu>
      </v-col>
    </v-row>

    <div class="my-8">
      <v-row
        class="ma-0"
      >
        <v-col class="mr-10 pa-0">
          <p class="caption mb-2 text-secondary">
            {{ $t('scSubOrderCardItem.operational_time') }}
          </p>
          <p class="body-1 mb-2">
            {{ subOrder.pickup_drop_off_location_point.start_operation_hour }} - {{ subOrder.pickup_drop_off_location_point.end_operation_hour }}
          </p>
        </v-col>

        <v-col
          class="pa-0"
        >
          <p
            class="caption mb-2 text-secondary"
          >
            {{ $t('scSubOrderCardItem.location') }}
          </p>
          <p
            class="body-1 mb-2"
          >
            {{ subOrder.pickup_drop_off_location_point.name }}
          </p>
        </v-col>
      </v-row>
    </div>

    <div>
      <p class="mb-2 caption text-secondary">
        {{ $t('scSubOrderCardItem.estimated') }} {{ subOrder.type === 'PICKUP' ? 'Pickup' : 'Drop Off' }}
      </p>
      <p class="ma-0 subtitle-1 text-primary">
        {{ $moment(subOrder.estimation_date).format('DD MMMM YYYY, HH:mm') }}
      </p>
    </div>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import CreateOrderDialog from '~/components/shipping-company/CreateOrderDialog.vue'
import { SubOrder } from '~/types/product'
import { formatNumber } from '~/utils/functions'

export default Vue.extend({
  name: 'SubOrderCardItem',

  components: { CreateOrderDialog },

  props: {
    isLoadingDetail: {
      type: Boolean,
      default: false
    },
    subOrder: {
      type: Object as () => SubOrder,
      required: true
    },
    productDataForm: {
      type: Array,
      default: () => [{ }]
    },
    locationDataForm: {
      type: Array,
      default: () => [{ }]
    },
    subOrderIsLoadingForm: {
      type: Boolean,
      default: false
    },
    isLoadingFormProduct: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    dialogSubOrder: {
      type: Boolean,
      default: false
    },
    dialogDeleteSubOrder: {
      type: Boolean,
      default: false
    },
    dataDraft: {
      type: Object as any,
      required: true
    },
  },

  data: () => ({ }),

  computed: { },

  methods: {
    formatNumber,
    onClickAddProduct (productFormValues: any) {
      this.$emit('on-click-add-product', productFormValues)
    },

    onClickSave (formValues: any) {
      this.$emit('on-click-save', formValues, this.index)
    },

    onClickDelete () {
      this.$emit('on-click-delete', this.subOrder.id, this.index)
    },
    onOpenDeleteDialog () {
      this.$emit('on-open-delete-dialog', this.index)
    },
    onCloseDeleteDialog () {
      this.$emit('on-close-delete-dialog', this.index)
    }
  }
})
</script>

<style lang="scss" scoped>
tbody {
  tr:hover {
    background-color: transparent !important;
  }
}
</style>

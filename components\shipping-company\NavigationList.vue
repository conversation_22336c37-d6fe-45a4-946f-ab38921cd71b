<template>
  <div class="mx-5 d-flex flex-column">
    <p class="mb-5 text-secondary">
      Menu
    </p>

    <v-list nav class="pa-0">
      <v-list-item-group color="primary">
        <v-list-item link :to="localePath('/shipping-company/dashboard')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('scSidebar.dashboard') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-group :value="true">
          <template #activator>
            <v-list-item-content class="pl-3 py-0">
              <v-list-item-title class="font-weight-medium">
                <p class="ma-0">
                  {{ $t('scSidebar.order_shipment') }}
                </p>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item
            link
            :to="localePath('/shipping-company/order-shipment/create-order')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('scSidebar.create_order') }}
              </p>
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            :to="localePath('/shipping-company/order-shipment/invoice-order')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none overflow-visible"
            >
              <v-badge dot bordered :value="isShowBadge?.invoiceOrder || false" class="ma-0">
                <p class="ma-0">
                  {{ $t('scSidebar.invoice_order') }}
                </p>
              </v-badge>
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            :to="localePath('/shipping-company/order-shipment/history-order')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('scSidebar.history_order') }}
              </p>
            </v-list-item-title>
          </v-list-item>
        </v-list-group>

        <v-list-item link :to="localePath('/shipping-company/product')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('scSidebar.product') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item link :to="localePath('/shipping-company/users')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('scSidebar.users') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <!--        <v-list-item link to="#" class="mb-5 pa-0" @click="logout">-->
        <!--          <v-list-item-title-->
        <!--            class="pl-5 font-weight-medium text-decoration-none"-->
        <!--          >-->
        <!--            <p class="ma-0">-->
        <!--              Logout-->
        <!--            </p>-->
        <!--          </v-list-item-title>-->
        <!--        </v-list-item>-->
      </v-list-item-group>
    </v-list>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'NavigationList',

  computed: {
    isShowBadge () {
      return this.$store.getters['firebase/dataSc']
    }
  },

  methods: {
    logout () {
      this.$auth.logout()
    }
  }
})
</script>

<style lang="scss" scoped>
p {
  font-weight: 600;
}
</style>

import { ActionTree } from 'vuex'
import { RootState } from '../../../index'
import { ShipmentCompanyInvoiceOrderState } from './state'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<ShipmentCompanyInvoiceOrderState, RootState> =
  {
    getItems ({ commit }, payload: any) {
      commit('SET_IS_LOADING', true)
      this.$axios
        .get('v1/order', {
          params: {
            page: payload == null ? 1 : payload.page,
            search_key: payload == null ? '' : payload.searchKey,
            search_columns:
              payload == null ? 'identity' : payload.searchColumns,
            sort_column: payload == null ? 'identity' : payload.sortColumn,
            sort_type: payload == null ? 'asc' : payload.sortType,
            filter_columns: payload.filterColumns,
            filter_keys: payload.filterKeys,
            entries: 9
          }
        })
        .then((response: any) => {
          commit('SET_ITEMS', response.data.data)
          commit('SET_TOTAL_PAGE', response.data.meta.last_page)
          commit('SET_PAGE', response.data.meta.current_page)
        })
        .catch((error: any) => {
          exceptionHandler(error, this)
        })
        .finally(() => {
          commit('SET_IS_LOADING', false)
        })
    },

    getItemDetail ({ commit }, payload: any) {
      commit('SET_IS_LOADING', true)
      this.$axios
        .get('v1/order/' + payload.id)
        .then((response: any) => {
          commit('SET_ITEM', response.data.data)
        })
        .catch((error: any) => {
          exceptionHandler(error, this)
        })
        .finally(() => {
          commit('SET_IS_LOADING', false)
        })
    }
  }

export default actions

import { MutationTree } from 'vuex'
import { OrderState } from './state'

export const mutations: MutationTree<OrderState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_SELECTED_ORDER (state, order: any) {
    state.selectedOrder = order
  },

  SET_SELECTED_ORDER_ITEM (state, order) {
    const index = state.items.findIndex(item => item.id === order.id)

    state.items[index].is_selected = !state.items[index].is_selected

    state.items = [...state.items]
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  },

  REMOVE_SELECTED_ORDER_ITEM_FROM_LIST (state, orderId) {
    const index = state.items.findIndex(item => item.id === orderId)

    state.items.splice(index, 1)
  }
}

export default mutations

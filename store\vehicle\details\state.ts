import { VehicleDetail, VehicleKey } from '~/types/vehicle'

export interface VehicleDetailsState {
  isLoading: boolean
  isLoadingForm: boolean
  items: VehicleDetail[]
  itemDetail: VehicleDetail[]
  totalPage: number
  page: number
  isHasNullVehicleId: boolean
  vehicleKeys: VehicleKey[]
}

export const state = () : VehicleDetailsState => ({
  isLoading: false,
  isLoadingForm: false,
  isHasNullVehicleId: false,
  items: [],
  itemDetail: [],
  totalPage: 1,
  page: 1,
  vehicleKeys: []
})

export default state

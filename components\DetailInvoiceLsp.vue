<template>
  <v-container fluid class="pa-10 white rounded">
    <v-container fluid class="mb-5 pa-0 d-flex justify-space-between align-start">
      <div style="width: 50%">
        <slot name="invoice-number" />
      </div>
      <div class="text-right">
        <p class="body-1 mb-5 text-secondary">
          <slot name="date-invoice" />
        </p>

        <div v-show="$vuetify.breakpoint.smAndUp">
          <slot name="download-invoice" />
        </div>
      </div>
    </v-container>

    <slot name="header" />

    <v-divider class="my-6" />

    <slot name="order-number" />

    <div class="d-flex mb-6">
      <v-icon size="24" color="black" class="mr-2">
        mdi-text-box
      </v-icon>
      <h3>Detail Order Shipment</h3>
    </div>

    <v-card flat outlined class="mb-6 pa-6">
      <v-simple-table dense class="mx-n4">
        <template #default>
          <thead>
            <tr>
              <th id="identity_product" class="text-left caption">
                {{ $t('lspHistoryShipment.identity_product') }}
              </th>
              <th id="product" class="text-left caption">
                {{ $t('lspHistoryShipment.product') }}
              </th>
              <th id="quantity" class="text-left caption">
                {{ $t('lspHistoryShipment.quantity') }}
              </th>
              <th id="weight" class="text-left caption">
                {{ $t('lspHistoryShipment.weight') }}
              </th>
              <th id="total_weight" class="text-left caption">
                {{ $t('lspHistoryShipment.total_weight') }}
              </th>
              <th id="volume" class="text-left caption">
                Volume
              </th>
              <th id="total_volume" class="text-left caption">
                Total Volume
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="product in products"
              :key="product?.id"
            >
              <td class="body-1">
                {{ product?.identity }}
              </td>
              <td class="body-1">
                {{ product?.name }} ({{ product?.unit }})
              </td>
              <td class="body-1">
                {{ product?.pivot?.quantity }}
              </td>
              <td class="body-1">
                {{ product?.weight }} KG
              </td>
              <td class="body-1">
                {{ product.weight * product.pivot.quantity }} KG
              </td>
              <td class="body-1">
                {{ product?.volume }} CBM
              </td>
              <td class="body-1">
                {{ product?.volume * product?.pivot?.quantity }} CBM
              </td>
            </tr>
          </tbody>
        </template>
      </v-simple-table>

      <v-divider class="my-6" />

      <v-row class="ma-n3">
        <v-col class="pa-3">
          <p class="subtitle-1 ma-0">
            {{ $t('lspHistoryShipment.location') }} Pickup
          </p>
          <ul>
            <li
              v-for="subOrder in pickupSubOrder"
              :key="subOrder.id"
            >
              <p class="ma-0 mt-2 body-1">
                {{ subOrder.pickup_drop_off_location_point?.name }}
              </p>
            </li>
          </ul>
        </v-col>

        <v-col class="pa-3">
          <p class="subtitle-1 ma-0">
            {{ $t('lspHistoryShipment.location') }} Drop Off
          </p>
          <ul>
            <li
              v-for="subOrder in dropOffSubOrder"
              :key="subOrder.id"
            >
              <p class="ma-0 mt-2 body-1">
                {{ subOrder.pickup_drop_off_location_point?.name }}
              </p>
            </li>
          </ul>
        </v-col>
      </v-row>

      <v-divider class="my-6" />

      <v-row class="pa-0 d-flex align-center justify-space-between">
        <v-col class="d-flex col-auto">
          <h4 class="mr-10">
            {{ $t('lspHistoryShipment.total_weight') }}
            <span class="text-primary">{{ selectedInvoice?.order?.total_weight }} KG</span>
          </h4>
          <h4 class="ml-10">
            Total Volume
            <span class="text-primary">{{ selectedInvoice?.order?.total_volume }} CBM</span>
          </h4>
        </v-col>

        <v-col class="col-auto">
          <slot name="shipping-cost" />
        </v-col>
      </v-row>
    </v-card>

    <v-container fluid class="pa-0">
      <slot name="additional-fee" />
    </v-container>

    <v-divider class="my-6" />

    <v-row class="pa-0 d-flex align-center justify-space-between">
      <v-col class="pb-0">
        <p class="ma-0 body-1" style="color:red;">
          {{ $t('lspHistoryShipment.the_total_amount_that_sc_must_be_paid') }}
        </p>
      </v-col>
      <v-col class="pt-0 text-right">
        <slot name="total-cost" />
      </v-col>
    </v-row>

    <slot name="save-button" />
    <div class="d-flex justify-center align-center mt-4">
      <div v-show="$vuetify.breakpoint.xs">
        <slot name="download-invoice" />
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { InvoiceDetail, InvoiceOrder } from '~/types/invoice'
import { Order, Product, SubOrder } from '~/types/product'

export default Vue.extend({
  name: 'DetailInvoiceLsp',

  props: {
    invoiceShipment: {
      type: Object as () => InvoiceOrder | null,
      default: null
    },
    selectedInvoice: {
      type: Object as () => InvoiceDetail | null,
      default: null
    },
    selectedOrder: {
      type: Object as () => Order | null,
      default: null
    },
    pickupSubOrder: {
      type: Array as () => SubOrder[] | null,
      default: null
    },
    dropOffSubOrder: {
      type: Array as () => SubOrder[] | null,
      default: null
    },
    products: {
      type: Array as () => Product[] | null,
      default: null
    },
    totalFee: {
      type: Number,
      default: 0
    }
  }
})
</script>

<style scoped lang="scss">
tbody {
  tr:hover {
    background-color: transparent !important;
  }
}
th, td {
  border: none !important;
}
</style>

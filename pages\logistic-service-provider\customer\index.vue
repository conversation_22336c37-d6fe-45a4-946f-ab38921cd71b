<template>
  <v-container fluid class="pa-0 px-5 px-md-10 mb-10">
    <customer-side-bar
      :drawer="customerNavigationDrawer"
      :is-page-mounted="pageMounted"
      :is-loading-shipment-companies="isLoadingSc"
      :is-loading-register-link="isLoadingRegisterLink"
      @on-copy="getRegisterLink"
      @on-get-detail-sc="dataDetailSc = $event"
      @on-page-mounted="pageMounted = $event"
      @on-close-drawer="customerNavigationDrawer = !customerNavigationDrawer"
    />

    <div v-if="isLoadingSc || isLoadingDetailSc">
      <v-sheet height="230" class="pa-5 mb-5">
        <div class="d-flex flex-row">
          <v-skeleton-loader type="image" width="120" height="120" />
          <v-container class="d-flex justify-space-between pa-0">
            <div class="d-flex flex-column">
              <v-skeleton-loader type="heading" width="600" class="mt-8 ml-6" />
              <v-skeleton-loader type="text" width="200" class="mt-3 ml-6" />
            </div>
            <v-skeleton-loader type="image" width="200" height="50" class="" />
          </v-container>
        </div>
        <div class="d-flex flex-column mt-8">
          <v-skeleton-loader type="text" width="100" class="mb-2" />
          <v-skeleton-loader type="text" width="300" />
        </div>
      </v-sheet>
      <v-skeleton-loader type="heading" class="mt-10" />
      <v-sheet height="50" class="mt-7">
        <v-skeleton-loader type="image" height="50" />
      </v-sheet>
      <v-row class="justify-space-between ma-0">
        <v-col class="pa-0">
          <v-sheet height="500" class="mt-5 mb-5 pa-10">
            <div v-for="i in 3" :key="i" class="d-flex flex-column align-center ">
              <v-skeleton-loader type="image" height="100" width="200" class="mb-5" />
            </div>
          </v-sheet>
        </v-col>
        <v-col class="pa-0" cols="7">
          <v-sheet height="500" class="d-flex flex-column mt-5 mb-5 overflow-hidden">
            <div v-for="i in 3" :key="i">
              <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
            </div>
          </v-sheet>
        </v-col>
      </v-row>
    </div>

    <v-container v-else-if="!isLoadingSc && !isLoadingDetailSc" fluid class="pa-0">
      <v-container
        v-if="dataDetailSc"
        fluid
        class="pa-5 rounded white"
      >
        <v-container fluid class="pa-0 mb-5 d-flex flex-column flex-lg-row justify-space-between">
          <div class="d-flex align-center">
            <v-img
              class="mr-5"
              :src="dataDetailSc?.logo_url"
              :min-width="$vuetify.breakpoint.lgAndUp ? 120 : 60"
              :max-width="$vuetify.breakpoint.lgAndUp ? 120 : 60"
              aspect-ratio="1"
              contain
            >
              <template #placeholder>
                <v-img
                  :min-width="$vuetify.breakpoint.lgAndUp ? 120 : 60"
                  :max-width="$vuetify.breakpoint.lgAndUp ? 120 : 60"
                  aspect-ratio="1"
                  contain
                  :src="require(`~/assets/images/placeholder-company-logo.svg`)"
                />
              </template>
            </v-img>
            <div>
              <h3 v-if="$vuetify.breakpoint.lgAndUp" class="mb-2">
                {{ dataDetailSc?.name }}
              </h3>
              <h4 v-else class="mb-2">
                {{ dataDetailSc?.name }}
              </h4>
              <p class="ma-0 body-1">
                <span>
                  <v-icon color="black">mdi-package-variant-closed</v-icon>
                </span>
                {{ dataDetailSc?.products_count }} {{ $t('lspCustomer.product') }}
              </p>
            </div>
          </div>

          <div class="d-flex mt-2 ma-lg-0">
            <block-dialog
              type-label="Product Owner"
              :name="dataDetailSc?.name"
              :status="dataDetailSc?.status"
              :is-loading-form="{
                accept: isLoadingFormSc,
                reject: isLoadingFormSc
              }"
              :dialog="dialogBlockUnblock"
              @on-click-yes="blockUnblockSc(dataDetailSc?.id)"
              @on-close-dialog="dialogBlockUnblock = false"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  fab
                  elevation="0"
                  color="white"
                  class="mr-2 custom-btn"
                  style="height: 45px; width: 45px"
                  v-bind="attrs"
                  v-on="on"
                  @click="dialogBlockUnblock = true"
                >
                  <v-icon v-if="dataDetailSc?.status === 'COLLABORATE'">
                    mdi-block-helper
                  </v-icon>
                  <v-icon v-else>
                    mdi-lock-open
                  </v-icon>
                </v-btn>
              </template>
            </block-dialog>

            <v-chip
              v-if="dataDetailSc?.status === 'COLLABORATE'"
              style="height: 45px; width: 140px"
              label
              class="chip-success d-flex justify-center"
            >
              <p class="subtitle-2 ma-0 text-success">
                Collaborate
              </p>
            </v-chip>

            <v-chip
              v-if="dataDetailSc?.status === 'BLOCK'"
              style="height: 45px; width: 140px"
              label
              class="chip-danger d-flex justify-center"
            >
              <p class="subtitle-2 ma-0 text-primary">
                Block
              </p>
            </v-chip>
          </div>
        </v-container>

        <div>
          <p class="caption mb-2">
            {{ $t('lspCustomer.location') }}
          </p>
          <p class="body-1 ma-0">
            {{ dataDetailSc?.address }}
          </p>
        </div>
      </v-container>

      <v-btn
        v-if="!$vuetify.breakpoint.mdAndUp"
        block
        x-large
        color="white"
        elevation="0"
        class="mt-5 text-capitalize justify-space-between custom-btn-hover"
        @click.stop="customerNavigationDrawer = !customerNavigationDrawer"
      >
        <p class="body-1 ma-0">
          {{ dataDetailSc?.name ? dataDetailSc.name : 'Select Product Owner' }}
        </p>

        <v-icon>
          mdi-chevron-right
        </v-icon>
      </v-btn>

      <v-container v-if="dataDetailSc">
        <div class="d-inline-flex mb-5">
          <v-toolbar
            color="transparent"
            elevation="0"
            class="float-left"
            height="40"
          >
            <template #extension>
              <v-tabs
                v-model="tabs"
                right
              >
                <v-tab
                  class="font-weight-bold"
                  color="primary"
                >
                  {{ $t('lspCustomer.location') }}
                </v-tab>
                <v-tab
                  class="font-weight-bold"
                  color="primary"
                >
                  {{ $t('lspCustomer.product') }}
                </v-tab>
              </v-tabs>
            </template>
          </v-toolbar>
        </div>

        <v-container
          v-if="tabs === 0"
          fluid
          class="pa-0"
        >
          <detail-sc-location
            :id-sc="dataDetailSc?.id"
          />
        </v-container>

        <v-container
          v-if="tabs === 1"
          fluid
          class="pa-0"
        >
          <product-management :id="dataDetailSc?.id" class-grid="col-xs-4 col-sm-12 col-lg-6 col-12" />
        </v-container>
      </v-container>
    </v-container>

    <v-container v-if="!dataDetailSc">
      <div class="justify-center align-center fill-height">
        <empty-placeholder
          hero="empty-shipment.svg"
          :message-title="'No Product selected'"
          :message-description="'Please select one of the listed Product Owners to view details.'"
        />
      </div>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomerSideBar from '~/components/logistic-service-provider/CustomerSideBar.vue'
import DetailScLocation from '~/components/logistic-service-provider/DetailScLocation.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import BlockDialog from '~/components/BlockDialog.vue'
import { toastSuccess } from '~/utils/toasts'
import { ShippingCompany } from '~/types/user'

export default Vue.extend({
  name: 'LogisticServiceProviderCustomerPage',

  components: { CustomerSideBar, DetailScLocation, BlockDialog, EmptyPlaceholder },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    tabs: 0,
    pageMounted: false,
    customerNavigationDrawer: true,
    dataDetailSc: null as ShippingCompany | null,
    dialogBlockUnblock: false
  }),

  computed: {
    isLoadingSc () {
      return this.$store.getters['shipping-company/isLoading']
    },
    isLoadingFormSc () {
      return this.$store.getters['shipping-company/isLoadingForm']
    },
    isLoadingDetailSc () {
      return this.$store.getters['shipping-company/isLoadingDetail']
    },
    dataLocation () {
      return this.$store.getters['pick-up-drop-off-location-point/data'].items
    },
    isLoadingLocation () {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },
    isLoadingRegisterLink () {
      return this.$store.getters['logistic-service-provider/isLoadingRegisterLink']
    }
  },

  watch: {
    tabs (tab: string) {
      const currentQuery = this.$route.query

      if (this.$route.query.tab !== tab) {
        this.$router.push({
          path: this.$route.path,
          query: { ...currentQuery, tab }
        })
      }
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', `${this.$t('lspCustomer.customer')}`)
    this.$store.dispatch('logistic-service-provider/getRegisterLink')

    this.pageMounted = true

    if (this.$route.query.tab) {
      this.tabs = parseInt(this.$route.query.tab as string)
    }
  },

  methods: {
    async blockUnblockSc (id: any) {
      await this.$store.dispatch('shipping-company/blockUnblock', {
        idLsp: this.$auth.$state.user.data.logistics_service_provider_id,
        idSc: id
      })
      this.dialogBlockUnblock = false
    },
    getRegisterLink () {
      const baseUrl = this.$store.getters['logistic-service-provider/registerLink']

      navigator.clipboard.writeText(baseUrl)
        .then(() => {
          toastSuccess('Copied to clipboard', this)
        })
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}
.custom-btn-hover {
  transition: .28s !important;
}
.custom-btn-hover:hover {
  background-color: #EF3434 !important;
  color: white !important;
 }
.chip-success {
  background-color: #EAF6EC !important;
}
.chip-danger {
  background: #FDE0E0 !important;
}
</style>

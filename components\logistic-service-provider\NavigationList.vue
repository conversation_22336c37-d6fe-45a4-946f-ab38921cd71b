<template>
  <div class="mx-5 d-flex flex-column">
    <p class="mb-5 text-secondary">
      Menu
    </p>
    <v-list nav class="pa-0">
      <v-list-item-group color="primary">
        <v-list-item link :to="localePath('/logistic-service-provider/dashboard')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('lspSidebar.dashboard') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item link :to="localePath('/logistic-service-provider/performance-vendor')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Performance Transporter
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-group :value="true">
          <template #activator>
            <v-list-item-content class="pl-3 py-0">
              <v-list-item-title class="font-weight-medium">
                <p class="ma-0">
                  Order Shipment
                </p>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/order-shipment/create-shipment')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none overflow-visible"
            >
              <v-badge dot bordered :value="isShowBadge?.createShipment || false" class="ma-0">
                <p class="ma-0">
                  {{ $t('lspSidebar.create_shipment') }}
                </p>
              </v-badge>
            </v-list-item-title>
          </v-list-item>

          <!-- <v-list-item
            link
            class="mb-2 pa-0"
            @click="$store.dispatch('tab/changeTab', 0).then(() => {
              $router.push(localePath('/logistic-service-provider/order-shipment/invoice-order'))
            })"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none overflow-visible"
            >
              <v-badge dot bordered :value="isShowBadge?.invoiceShipment || false" class="ma-0">
                <p class="ma-0">
                  {{ $t('lspSidebar.invoice_shipment') }}
                </p>
              </v-badge>
            </v-list-item-title>
          </v-list-item> -->

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/order-shipment/history-order')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('lspSidebar.history_shipment') }}
              </p>
            </v-list-item-title>
          </v-list-item>
        </v-list-group>

        <v-list-item link :to="localePath('/logistic-service-provider/customer')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('lspCustomer.product') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-group :value="true">
          <template #activator>
            <v-list-item-content class="pl-3 py-0">
              <v-list-item-title class="font-weight-medium">
                <p class="ma-0">
                  Vendor
                </p>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/list-vendor')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('lspSidebar.list_vendor') }}
              </p>
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/list-vehicle')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('lspSidebar.list_vehicle') }}
              </p>
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/list-driver')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('lspSidebar.list_driver') }}
              </p>
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            link
            :to="localePath('/logistic-service-provider/request-vendors')"
            class="mb-2 pa-0"
          >
            <v-list-item-title
              class="pl-10 font-weight-medium text-decoration-none"
            >
              <p class="ma-0">
                {{ $t('lspSidebar.request_vendor') }}
              </p>
            </v-list-item-title>
          </v-list-item>
        </v-list-group>

        <v-list-item link :to="localePath('/logistic-service-provider/export-files')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Export Files
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item link :to="localePath('/logistic-service-provider/users')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('lspSidebar.users') }}
            </p>
          </v-list-item-title>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'NavigationList',

  computed: {
    isShowBadge () {
      return this.$store.getters['firebase/dataLsp']
    }
  },

  mounted () {
    this.$store.dispatch('vehicle/clearVehicles')
  },

  methods: {
    logout () {
      this.$auth.logout()
    }
  }
})
</script>

<style lang="scss" scoped>
p {
  font-weight: 600;
}
</style>

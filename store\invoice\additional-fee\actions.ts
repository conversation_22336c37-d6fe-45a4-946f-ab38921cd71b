import { ActionTree } from 'vuex'
import { InvoiceAdditionalFeeState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<InvoiceAdditionalFeeState, InvoiceAdditionalFeeState> = {
  async getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    await this.$axios.get('/v1/invoice-fees', {
      params: {
        filter_columns: payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page == null ? '' : payload.page,
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns: payload.searchColumns == null ? '' : payload.searchColumns
      }
    }).then((response: any) => {
      commit('SET_RESULT', response.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('v1/invoice-fees', {
      description: payload.description,
      cost: payload.cost,
      invoice_id: payload.invoice_id,
      invoice_detail_id: payload.invoice_detail_id
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      commit('SET_IS_LOADING_FORM', false)

      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      commit('SET_IS_LOADING_FORM', false)

      return false
    })
  },

  async updateItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .put('/v1/invoice-fees/' + payload.id, {
        description: payload.description,
        cost: payload.cost,
        invoice_id: payload.invoice_id,
        invoice_detail_id: payload.invoice_detail_id
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        commit('SET_IS_LOADING_FORM', false)

        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_IS_LOADING_FORM', false)

        return false
      })
  },

  async deleteItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .delete('/v1/invoice-fees/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        commit('SET_IS_LOADING_FORM', false)

        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_IS_LOADING_FORM', false)

        return false
      })
  }
}

export default actions

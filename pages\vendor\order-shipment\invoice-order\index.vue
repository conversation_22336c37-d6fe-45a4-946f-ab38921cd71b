<template>
  <v-container fluid class="pa-0 px-10 mb-10 d-flex flex-column align-end">
    <header-datatable
      default-sort-column="created_at"
      default-sort-type="desc"
      :sort-column-items="sortColumn"
      :sort-type-items="sortType"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-filter-change="getData({ page: $route.query?.page, filter: $event })"
      @on-search-icon-click="getData({ searchKey: $event })"
    >
      <template #toggle-button>
        <toggle-button />
      </template>
    </header-datatable>

    <display-mode class="mb-10">
      <template #card-mode>
        <v-container fluid class="pa-0">
          <invoice-loading v-if="isLoadingInvoice" />

          <v-container v-else fluid class="pa-0">
            <v-row v-if="invoiceShipment.items" class="ma-n5">
              <v-col
                v-for="(item, i) in invoiceShipment.items"
                :key="i"
                class="pa-5 col-lg-4 col-sm-6 col-12"
              >
                <invoice-card-item
                  :id="item.id"
                  :identity="item.shipment?.identity"
                  :lsa-name="item.shipment?.logistics_service_provider.name"
                  :read-at="item.read_at"
                  :lsa-image="item.shipment?.logistics_service_provider.logo_url"
                  :date="$moment(item.created_at).format('DD-MM-YYYY')"
                  :detail-route="'/vendor/order-shipment/invoice-order'"
                />
              </v-col>
            </v-row>

            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-placeholder.svg"
                  :message-title="$t('vendorInvoiceOrder.empty_message_title')"
                  :message-description="
                    $t('vendorInvoiceOrder.empty_message_description')
                  "
                />
              </v-col>
            </v-row>
          </v-container>
        </v-container>
      </template>

      <template #data-table-mode>
        <v-container
          fluid
          class="pa-0 mt-3"
          style="background-color: #f0f0f0"
        >
          <div v-if="invoiceShipment.items">
            <v-data-table
              :loading="isLoadingInvoice"
              loading-text="Loading... Please wait"
              :headers="tableHeaders"
              :items="invoiceShipment.items"
              :page.sync="page"
              :single-expand="singleExpand"
              :expanded.sync="expanded"
              :items-per-page="-1"
              hide-default-footer
              class="pa-md-10 pa-5"
              style=""
              @page-count="pageCount = $event"
            >
              <template #item.created_at="{ item }">
                {{ $moment(item.created_at).format('DD/MM/yyyy HH:mm') }}
              </template>
              <template #item.status="{ item }">
                <v-chip
                  v-if="item.status === 'PROPOSED'"
                  label
                  class="chip-success font-weight-medium"
                >
                  <p class="ma-0 subtitle-1 text-success">
                    {{ item.status }}
                  </p>
                </v-chip>
                <v-chip
                  v-else-if="item.status === 'REJECT'"
                  label
                  class="chip-danger font-weight-medium"
                >
                  <p class="ma-0 subtitle-1 text-primary">
                    {{ item.status }}
                  </p>
                </v-chip>
                <v-chip v-else label class="chip-success font-weight-medium">
                  <p class="ma-0 subtitle-1 text-info">
                    {{ item.status }}
                  </p>
                </v-chip>
              </template>
              <template #item.detail="{ item }">
                <v-btn
                  class="font-weight-medium pa-0 text-capitalize"
                  text
                  plain
                  @click="
                    $router.push(
                      '/vendor/order-shipment/invoice-order/' + `${item.id}`
                    )
                  "
                >
                  Detail
                  <v-icon> mdi-chevron-right </v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </div>
          <div v-else>
            <div class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-placeholder.svg"
                :message-title="$t('vendorInvoiceOrder.empty_message_title')"
                :message-description="
                  $t('vendorInvoiceOrder.empty_message_description')
                "
              />
            </div>
          </div>
        </v-container>
      </template>
    </display-mode>

    <pagination-component
      :page="invoiceShipment.page"
      :total-page="invoiceShipment.totalPage"
      page-id="page"
      @on-change-page="getData({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import InvoiceCardItem from '~/components/vendor/InvoiceCardItem.vue'
import InvoiceLoading from '~/components/loading/InvoiceLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import ToggleButton from '~/components/ToggleButton.vue'
import DisplayMode from '~/components/DisplayMode.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'InvoiceOrderPage',

  components: {
    HeaderDatatable,
    InvoiceCardItem,
    InvoiceLoading,
    EmptyPlaceholder,
    ToggleButton,
    DisplayMode,
    PaginationComponent
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    windowWidth: 0,
    expanded: [],
    singleExpand: true,
    button: true,
    page: 1,
    pageCount: 0,
    sortColumn: {
      date: {
        label: 'Date',
        value: 'created_at'
      }
    },
    sortType: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    tableHeaders: [
      { text: 'Invoice Number', value: 'shipment.identity' },
      { text: 'Lsa Name', value: 'shipment.logistics_service_provider.name' },
      { text: 'Date', value: 'created_at' },
      { text: 'Status', value: 'status' },
      { text: '', value: 'detail' }
    ]
  }),

  computed: {
    isLoadingInvoice () {
      return this.$store.getters['invoice/isLoading']
    },
    invoiceShipment () {
      return this.$store.getters['invoice/data']
    }
  },

  mounted () {
    this.getData({
      page: this.$route?.query?.page as string
    })
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Invoice Order Shipment')
  },

  methods: {
    getData ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.$store.dispatch('invoice/getItems', {
        searchColumns:
          'shipment.identity,shipment.logistics_service_provider.name',
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'status',
        filterKeys: 'DRAFT',
        page
      })
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: 0.28s !important;
}
.custom-icon {
  transition: 0s !important;
}
.custom-btn:hover {
  background-color: #ef3434 !important;
  color: white !important;
}
</style>

import { ActionTree, GetterTree, MutationTree } from 'vuex'
import { RootState } from '../index'

export interface PerformanceVendorState {
  dateRange: Date[],
  selectedMill: string | null,
  selectedRefinery: string | null,
  selectedVendor: string | null,
  selectedDriver: string | null,
  selectedPlateNumber: any[],
  selectedProductName: string | null,
  isSelectedDispatch: string | null,
  isSelectedOnProgress: string | null,
  isSelectedDelivered: string | null
}
export const state = (): PerformanceVendorState => ({
  dateRange: [],
  selectedMill: null,
  selectedRefinery: null,
  selectedVendor: null,
  selectedDriver: null,
  selectedPlateNumber: [],
  selectedProductName: null,
  isSelectedDispatch: null,
  isSelectedOnProgress: null,
  isSelectedDelivered: null
})

export const getters: GetterTree<PerformanceVendorState, RootState > = {
  dateRange (state) {
    return state.dateRange
  },
  selectedMill (state) {
    return state.selectedMill
  },
  selectedRefinery (state) {
    return state.selectedRefinery
  },
  selectedVendor (state) {
    return state.selectedVendor
  },
  selectedDriver (state) {
    return state.selectedDriver
  },
  selectedPlate<PERSON><PERSON>ber (state) {
    return state.selectedPlateNumber
  },
  selectedProductName (state) {
    return state.selectedProductName
  },
  isSelectedOnProgress (state) {
    return state.isSelectedOnProgress
  },
  isSelectedDelivered (state) {
    return state.isSelectedDelivered
  },
  isSelectedDispatch (state) {
    return state.isSelectedDispatch
  }
}

export const mutations: MutationTree<PerformanceVendorState> = {
  SET_DATE_RANGE (state, dateRange: Date[]) {
    state.dateRange = dateRange
  },
  SET_SELECTED_MILL (state, selectedMill: string) {
    state.selectedMill = selectedMill
  },
  SET_SELECTED_REFINERY (state, selectedRefinery: string) {
    state.selectedRefinery = selectedRefinery
  },
  SET_SELECTED_VENDOR (state, selectedVendor: string) {
    state.selectedVendor = selectedVendor
  },
  SET_SELECTED_DRIVER (state, selectedDriver: string) {
    state.selectedDriver = selectedDriver
  },
  SET_SELECTED_PLATE_NUMBER (state, selectedPlateNumber: any[]) {
    state.selectedPlateNumber = selectedPlateNumber
  },
  SET_SELECTED_PRODUCT_NAME (state, selectedProductName: string) {
    state.selectedProductName = selectedProductName
  },
  SET_IS_SELECTED_ON_PROGRESS (state, isSelectedOnProgress: string) {
    state.isSelectedOnProgress = isSelectedOnProgress
  },
  SET_IS_SELECTED_DELIVERED (state, isSelectedDelivered: string) {
    state.isSelectedDelivered = isSelectedDelivered
  },
  SET_IS_SELECTED_DISPATCH (state, isSelectedDispatch: string) {
    state.isSelectedDispatch = isSelectedDispatch
  },
  RESET_STATE (state) {
    state.selectedProductName = null
    state.selectedPlateNumber = []
    state.selectedRefinery = null
    state.selectedMill = null
    state.selectedDriver = null
    state.selectedVendor = null
    state.isSelectedDispatch = null
    state.isSelectedOnProgress = null
    state.isSelectedDelivered = null
  }
}

export const actions: ActionTree<PerformanceVendorState, RootState> = {
  resetFilter ({ commit }) {
    commit('SET_SELECTED_MILL', null)
    commit('SET_SELECTED_REFINERY', null)
    commit('SET_SELECTED_VENDOR', null)
    commit('SET_SELECTED_DRIVER', null)
    commit('SET_SELECTED_PLATE_NUMBER', [])
    commit('SET_SELECTED_PRODUCT_NAME', [])
    commit('SET_IS_SELECTED_DISPATCH', [])
    commit('SET_IS_SELECTED_ON_PROGRESS', [])
    commit('SET_IS_SELECTED_DELIVERED', [])
  }
}

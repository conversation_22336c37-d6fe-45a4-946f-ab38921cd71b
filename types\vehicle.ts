import { Driver } from '~/types/driver'
import { Vendor } from '~/types/user'

export interface VehicleFromFms {
  driver: any
  id: number,
  device_model: string,
  plate_number: string
}

export interface VehicleType {
  id: string
  name: string

  is_owned?: boolean
  deleted_at?: any
  created_at: Date
  updated_at: Date
}

export interface Pivot {
  vehicle_id: string
  vehicle_feature_id: string
}

export interface VehicleFeature {
  id: string
  name: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  pivot: Pivot
}

export interface VehicleDetail {
  tracks: any
  vendor_id: string
  id: string
  name: string | null
  features: VehicleFeature[] | null
  plate_number: string
  fms_identity?: any
  driver_id: string
  vehicle_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  driver?: Driver
  is_active: boolean | null
  // eslint-disable-next-line no-use-before-define
  vehicle?: Vehicle
}

export interface Vehicle {
  id: string
  name: string
  photo?: any
  length: string
  width: string
  height: string
  max_volume: string
  max_weight: string
  vehicle_type_id: string
  vendor_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  vehicle_details_count: number
  photo_url?: any
  vehicle_type?: VehicleType
  vehicle_features: VehicleFeature[]
  vehicle_details?: VehicleDetail[]
  pivot: any
  vendor: Vendor
  quantity?: number
  isSelected: boolean
  weight: number
  selected_vehicle_detail_id?: string[];
  selected_driver_id?: string[];
  showNote: any
}

export interface VehicleKey {
  key: string
  name: string
  is_required: boolean
  type: string
}

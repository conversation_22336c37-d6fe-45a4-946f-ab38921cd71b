<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column align-end">
    <header-datatable
      default-sort-column="name"
      default-sort-type="asc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-filter-change="getUsers({filter: $event, page: $route.query?.page})"
      @on-search-icon-click="getUsers({searchKey: $event})"
    >
      <template #toggle-button>
        <toggle-button />
      </template>
      <template #button>
        <user-form-item
          :is-loading-form="isLoadingUserForm"
          :dialog="dialogCreateUser"
          :clear-form="clearForm"
          @on-click-save="createUser"
          @on-close-dialog="dialogCreateUser = false; clearForm = true"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              v-if="$vuetify.breakpoint.xs"
              depressed
              color="primary"
              height="52"
              v-bind="attrs"
              block
              class="mb-4 mt-5"
              v-on="on"
              @click="dialogCreateUser = true; clearForm = false"
            >
              {{ $t('lspUsers.add_user') }}
            </v-btn>
            <v-btn
              v-else
              depressed
              color="primary"
              height="52"
              v-bind="attrs"
              v-on="on"
              @click="dialogCreateUser = true; clearForm = false"
            >
              {{ $t('lspUsers.add_user') }}
            </v-btn>
          </template>
        </user-form-item>
      </template>
    </header-datatable>

    <display-mode class="mb-10">
      <template #card-mode>
        <v-container fluid class="pa-0">
          <users-loading v-if="isLoading" />
          <v-row
            v-else
            class="ma-n5 pa-3"
          >
            <v-row v-if="data.items.length !== 0">
              <v-col
                v-for="(user, index) in data.items"
                :key="index"
                class="pa-5 col-lg-4 col-sm-6 col-12"
              >
                <user-card-item
                  :user="user"
                  :is-loading-form="isLoadingUserForm"
                  :is-loading-form-clear-image="isLoadingUserFormClearImage"
                  :index="index"
                  :is-has-new="true"
                  :is-has-edit="$auth?.user?.data?.name === 'Admin Apical'"
                  :is-has-dialog-delete-user="$auth?.user?.data?.name === 'Admin Apical'"
                  :is-has-dialog-permission-user="$auth?.user?.data?.name === 'Admin Apical'"
                  :dialog-update-user="dialogUpdateUser[index]"
                  :dialog-delete-user="dialogDeleteUser[index]"
                  :permission-user-id="permissionUsers"
                  @on-click-delete="deleteUser"
                  @on-click-edit="editUser"
                  @on-click-permission="permissionUser"
                  @on-clear-image="clearImage($event, index)"
                  @on-open-update-dialog="$set(dialogUpdateUser, index, true)"
                  @on-close-update-dialog="$set(dialogUpdateUser, index, false)"
                  @on-open-delete-dialog="$set(dialogDeleteUser, index, true)"
                  @on-close-delete-dialog="$set(dialogDeleteUser, index, false)"
                  @on-open-permission-dialog="$set(dialogPermissionUser, index, true)"
                  @on-close-permission-dialog="$set(dialogPermissionUser, index, false)"
                />
              </v-col>
            </v-row>

            <v-row v-else>
              <v-col class="justify-center align-center fill-height">
                <empty-placeholder
                  hero="empty-shipment.svg"
                  :message-title="`${ $t('lspUsers.empty_title') }`"
                  :message-description="`${ $t('lspUsers.empty_desc') }`"
                />
              </v-col>
            </v-row>
          </v-row>
        </v-container>
      </template>

      <template #data-table-mode>
        <v-container
          fluid
          class="pa-0 mt-3"
          style="background-color: #f0f0f0"
        >
          <div v-if="data.items">
            <v-data-table
              :loading="isLoading"
              loading-text="Loading... Please wait"
              :headers="tableHeaders"
              :items="data.items"
              :page.sync="page"
              :single-expand="singleExpand"
              :expanded.sync="expanded"
              :items-per-page="-1"
              hide-default-footer
              class="pa-md-10 pa-5"
              style=""
              @page-count="pageCount = $event"
            >
              <template #item.image="{ item, index }">
                <image-component v-if="item.avatar_url" :image="item.avatar_url" max-width="40" max-height="40" class="mr-5" />
              </template>

              <template v-if="$auth?.user?.data?.name === 'Admin Apical'" #item.detail="{ item, index }">
                <v-menu
                  bottom
                  transition="slide-y-transition"
                >
                  <template #activator="{on, attrs}">
                    <v-btn
                      icon
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-icon color="black">
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>

                  <v-list>
                    <user-form-item
                      :user="item"
                      :is-loading-form="isLoadingUserForm"
                      :is-loading-form-clear-image="isLoadingUserFormClearImage"
                      :dialog="dialogUpdateUser[index]"
                      @on-click-save="editUser($event, index)"
                      @on-clear-image="clearImage($event, index)"
                      @on-close-dialog="$set(dialogUpdateUser, index, false)"
                    >
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="edit"
                          v-bind="attrs"
                          v-on="on"
                          @click="$set(dialogUpdateUser, index, true)"
                        >
                          <v-list-item-title>
                            {{ $t('userCardItem.edit') }}
                          </v-list-item-title>
                        </v-list-item>
                      </template>
                    </user-form-item>

                    <v-dialog
                      v-model="dialogDeleteUser[index]"
                      persistent
                      max-width="600px"
                    >
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="delete"
                          v-bind="attrs"
                          v-on="on"
                          @click="$set(dialogDeleteUser, index, true)"
                        >
                          <v-list-item-title>
                            {{ $t('userCardItem.delete') }}
                          </v-list-item-title>
                        </v-list-item>
                      </template>
                      <v-card>
                        <v-card-title class="text-h6 lighten-2">
                          {{ $t('userCardItem.confirm_delete_title') }} {{ item.name }}
                        </v-card-title>

                        <v-card-text>
                          {{ $t('userCardItem.confirm_delete_description') }}
                        </v-card-text>

                        <v-divider />

                        <v-card-actions>
                          <v-spacer />
                          <v-btn
                            color="primary"
                            text
                            :loading="isLoadingUserForm"
                            @click="deleteUser(item.id, index)"
                          >
                            {{ $t('userCardItem.button_yes') }}
                          </v-btn>
                          <v-btn
                            color="primary"
                            @click="$set(dialogDeleteUser, index, false)"
                          >
                            {{ $t('userCardItem.button_cancel') }}
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog>

                    <v-dialog
                      v-model="dialogPermission"
                      persistent
                      max-width="600px"
                    >
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="delete"
                          v-bind="attrs"
                          v-on="on"
                          @click="dialogPermission = true"
                        >
                          <v-list-item-title>
                            Permission
                          </v-list-item-title>
                        </v-list-item>
                      </template>
                      <v-card class="pa-5">
                        <v-card-title class="text-h6 lighten-2 d-flex justify-space-between">
                          <h4>User Permission</h4>

                          <v-icon color="black" @click="dialogPermission = false">
                            mdi-close
                          </v-icon>
                        </v-card-title>

                        <v-row class="pa-3 ma-1 d-flex mt-3">
                          <v-autocomplete
                            v-model="permissionUserId"
                            outlined
                            clearable
                            :items="permissionUsers.map(item => {
                              return { text: item.name, value: item.id }
                            })"
                            label="Permission"
                          />
                        </v-row>

                        <v-card-actions>
                          <v-btn
                            color="primary"
                            class="text-capitalize"
                            :loading="isLoadingForm"
                            @click="createPermissionUser(item.id)"
                          >
                            Save
                          </v-btn>
                          <v-btn
                            color="primary"
                            class="ml-5 text-capitalize"
                            outlined
                            text
                            @click="dialogPermission = false"
                          >
                            Cancel
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog>
                  </v-list>
                </v-menu>
              </template>
            </v-data-table>
          </div>
          <div v-else>
            <div class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-placeholder.svg"
                :message-title="$t('vendorUsers.empty_message_title')"
                :message-description="$t('vendorUsers.empty_message_description') "
              />
            </div>
          </div>
        </v-container>
      </template>
    </display-mode>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getUsers({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserCardItem from '~/components/UserCardItem.vue'
import UsersLoading from '~/components/loading/UsersLoading.vue'
import ImageComponent from '~/components/ImageComponent.vue'
import DisplayMode from '~/components/DisplayMode.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'UsersPage',

  components: {
    UserCardItem,
    UsersLoading,
    ImageComponent,
    DisplayMode,
    PaginationComponent
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    searchKey: '',
    page: 1,
    filterOptions: false,
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      },
      email: {
        label: 'Email',
        value: 'email'
      },
      phoneNumber: {
        label: 'Phone Number',
        value: 'phone_number'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateUser: false,
    dialogUpdateUser: [] as Array<Boolean>,
    dialogDeleteUser: [] as Array<Boolean>,
    dialogPermissionUser: [] as Array<Boolean>,
    dialogPermission: false as Boolean,
    clearForm: false,
    expanded: [],
    singleExpand: true,
    button: true,
    pageCount: 0,
    tableHeaders: [
      { text: 'Image', value: 'image' },
      { text: 'User Name', value: 'name' },
      { text: 'Email', value: 'email' },
      { text: 'Phone Number', value: 'phone_number' },
      { text: 'Permission', value: 'permission.name' },
      { text: '', value: 'detail' }
    ],
    menu: false,
    isShowFormPassword: false,
    permissionUserId: false
  }),

  computed: {
    data () {
      return this.$store.getters['users/data']
    },
    isLoading () {
      return this.$store.getters['users/isLoading']
    },
    isLoadingUserForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingUserFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    },
    permissionUsers () {
      return this.$store.getters['users/dataPermission'].itemsPermission
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', `${this.$t('lspUsers.users')}`)
    this.getUsers({
      page: this.$route.query?.page as string
    })
    this.getPermissionUsers()
  },

  methods: {
    clearImage (id: string, index: number) {
      this.$store.dispatch('users/removeAvatar', id)
        .then(() => {
          this.getUsers({})
        })
        .then(() => {
          this.$set(this.dialogUpdateUser, index, true)
        })
    },
    async getUsers ({ page = '', searchKey = '', filter = { sortColumn: 'name', sortType: 'asc' } }) {
      this.searchKey = searchKey

      await this.$store.dispatch('users/getItems', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterKeys: this.$auth.$state.user.data.logistics_service_provider_id,
        role: this.$auth.$state.user.data.role,
        page
      })
    },

    async createUser (value: any) {
      const response = await this.$store.dispatch('users/createItem', {
        value,
        selectedId: this.$auth.$state.user.data.logistics_service_provider_id,
        role: this.$auth.$state.user.data.role
      })
      if (response) {
        this.dialogCreateUser = false
        this.clearForm = true

        await this.getUsers({
          page: this.$route.query?.page as string
        })
      }
    },
    async editUser (value: any, i: any) {
      const response = await this.$store.dispatch('users/editItem', {
        value,
        selectedId: this.$auth.$state.user.data.logistics_service_provider_id,
        role: this.$auth.$state.user.data.role
      })
      if (response) {
        this.$set(this.dialogUpdateUser, i, false)

        await this.getUsers({
          page: this.$route.query?.page as string
        })
      }
    },
    async deleteUser (id: any, i: any) {
      const response = await this.$store.dispatch('users/deleteItem', {
        id,
        selectedId: this.$auth.$state.user.data.logistics_service_provider_id,
        role: this.$auth.$state.user.data.role
      })
      if (response) {
        this.$set(this.dialogDeleteUser, i, false)
      }
    },
    async permissionUser (id: any, i: any, permissionId: any) {
      const response = await this.$store.dispatch('users/updatePermission', {
        id,
        permission_id: permissionId
      })

      if (response) {
        this.$set(this.dialogPermissionUser, i, false)

        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    async createPermissionUser (id: any) {
      const response = await this.$store.dispatch('users/updatePermission', {
        id,
        permission_id: this.permissionUserId
      })

      if (response) {
        this.dialogPermission = false
        this.getUsers({
          page: this.$route.query?.page_user as string
        })
      }
    },
    getPermissionUsers () {
      this.$store.dispatch('users/getPermissionUsers')
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: .28s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}

.custom-btn:hover .custom-icon{
  color: white !important;
}
</style>

import { GetterTree } from 'vuex'
import { LogisticServiceProviderState } from './state'

export const getters: GetterTree<LogisticServiceProviderState, LogisticServiceProviderState> = {
  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingRegisterLink (state) {
    return state.isLoadingRegisterLink
  },

  registerLink (state) {
    return state.registerLink
  },

  selectedShipmentCompany (state) {
    return state.selectedShipmentCompany
  }
}

export default getters

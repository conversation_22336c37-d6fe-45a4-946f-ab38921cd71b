<template>
  <v-card outlined class="pa-5 overflow-visible">
    <v-dialog
      v-model="dialogListDo"
      max-width="540px"
      persistent
    >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex align-center justify-space-between">
        <h4>Vehicle Status</h4>

        <v-icon color="black" @click.stop="dialogListDo = false">
          mdi-close
        </v-icon>
      </v-container>

      <div class="mb-5 text-start">
        Plate Number <br>
       <h3 class="mt-3">{{ dataDetail?.plate_number }}</h3>
       On Shipment 
       <span v-if="dataDetail?.driver !== null"> 
        <v-icon color="grey" class="ml-1 mr-2" size="10">
         mdi-circle
      </v-icon></span><span>{{ dataDetail?.driver?.user?.name }}</span>
      </div>

      <v-divider class="my-3" />

      <div>
        <p class="subtitle-1">Order Running</p>
        <div v-if="isLoading">
          <div>
            <v-skeleton-loader  type="text" />
            <v-skeleton-loader  type="text" />
          </div>
        </div>
        <div v-else>
          <div v-if="detailData?.active_orders?.length > 0">
            <v-col v-for="data in detailData.active_orders" class="pa-0" :key="data.track_id">
              <div class="d-flex justify-space-between">
                <div>
                  <p class="pa-0 ma-0">{{ data?.order_identity }}</p>
                  
                  <div class="d-flex align-center mt-1">
                    <v-chip
                      v-if="data?.status === 'PROPOSED'"
                      label
                      class="chip-secondary font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-secondary">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'REJECT'"
                      label
                      class="chip-danger font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-primary">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'DISPATCH'"
                      label
                      class="chip-orange font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-orange">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'FINISHED'"
                      label
                      class="chip-success font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-success">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'ON_PROGRESS'"
                      label
                      class="chip-success font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-info">
                        ON PROGRESS
                      </span>
                    </v-chip>
                    <v-chip v-else label class="chip-success font-weight-medium mr-2" small>
                      <span class="ma-0 caption text-info">
                        {{ data?.status }}
                      </span>
                    </v-chip>

                    <span><v-icon color="grey" class="ml-1 mr-2" size="10">
                      mdi-circle
                    </v-icon></span>
                    <span class="caption">
                      Reorder {{ data?.ritase_identity === '-' ? '1' : parseInt(data?.ritase_identity) + 1 }}
                    </span>
                  </div>
                </div>
                <div class="d-flex align-center">
                  <p class="mb-0">{{ $moment(data?.order_created).format('DD-MM-yyyy HH:mm') }}</p>
                  <p class="ml-3 mb-0" style="cursor: pointer" 
                    @click="$router.push(
                      localePath(
                        $auth.user?.data.role === 'VENDOR'
                        ? '/vendor/order-shipment/history-order/detail-history-order/' + `${data?.shipment_id}`
                        : '/logistic-service-provider/order-shipment/history-order/detail-history-order/' + `${data?.shipment_id}`
                      )
                    )"
                  >
                    <v-icon color="red">mdi-chevron-right</v-icon>
                  </p>
                </div>
              </div>
            </v-col>
          </div>
          <div class="mb-2" v-else>
            Empty
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
    <v-row class="pa-5 overflow-visible">
      <v-col class="pa-0 mt-sm-10 mt-0 col-md-3 col-12 d-flex justify-center">
        <div class="text-center justify-center">
          <div>
            <v-img
              :src="vehicle?.photo_url"
              aspect-ratio="1"
              min-width="120"
              max-width="140"
              contain
              class="d-inline-flex"
            >
              <template #placeholder>
                <v-img
                  min-width="120"
                  max-width="140"
                  aspect-ratio="1"
                  contain
                  :src="require(`~/assets/images/icon-select-image.png`)"
                />
              </template>
            </v-img>
            <h4>
              {{ vehicle?.name }}
            </h4>
            <p style="color: #6E6666">
              ( {{ vehicle.vehicle_details.length }} {{ $t('vehicleCard.plate_found') }} )
            </p>
            <v-tooltip right color="white">
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-row class="pa-4 align-center justify-center">
                    <v-icon style="color: #0094BC" class="mr-2">
                      mdi-information-outline
                    </v-icon>
                    <p style="color: #0094BC" class="mb-0">
                      {{ $t('vehicleCard.specification') }}
                    </p>
                  </v-row>
                </div>
              </template>
              <v-card elevation="0" class="pa-5">
                <v-card-title class="ma-0">
                  <p class="subtitle-1 ma-0">
                    {{ $t('vehicleCard.specification') }}
                  </p>
                </v-card-title>
                <v-row class="ma-0 mb-5">
                  <v-col class="pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.length') }}
                    </p>
                    <p class="mb-0">
                      {{ vehicle.length }}
                    </p>
                  </v-col>
                  <v-col class="mx-5 pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.width') }}
                    </p>
                    <p class="mb-0">
                      {{ vehicle.width }}
                    </p>
                  </v-col>
                  <v-col class="pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.height') }}
                    </p>
                    <p class="mb-0">
                      {{ vehicle.height }}
                    </p>
                  </v-col>
                </v-row>
                <v-row class="ma-0 mb-5">
                  <v-col class="mr-5 pa-0">
                    <p class="mb-2 text--secondary">
                      Volume
                    </p>
                    <p class="mb-0">
                      {{ vehicle.max_volume }}
                    </p>
                  </v-col>
                  <v-col class="pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.weight') }}
                    </p>
                    <p class="mb-0">
                      {{ vehicle.max_weight }}
                    </p>
                  </v-col>
                </v-row>
                <v-row class="ma-0 mb-5 justify-content-between">
                  <v-col class="mr-5 pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.features') }}
                    </p>

                    <div
                      v-for="feature in vehicle.vehicle_features"
                      :key="feature.id"
                      class="pa-2 ma-0 my-1 d-flex caption"
                      style="
                              border-color: #cfcccc;
                              border-style: solid;
                              border-radius: 4px;
                            "
                    >
                      {{ feature.name?.toUpperCase() }}
                    </div>
                  </v-col>
                  <v-col class="pa-0">
                    <p class="mb-2 text--secondary">
                      {{ $t('vehicleCard.type') }}
                    </p>

                    <p class="mb-0">
                      {{ vehicle.vehicle_type?.name }}
                    </p>
                  </v-col>
                </v-row>
              </v-card>
            </v-tooltip>
          </div>
        </div>
        <v-menu v-if="isHasMenu" bottom transition="slide-y-transition">
          <template #activator="{ on, attrs }">
            <div v-if="$vuetify.breakpoint.xs">
              <v-btn icon v-bind="attrs" class="mr-md-10 mr-0 mt-5" style="position: absolute; right: 30px; top: 0" v-on="on">
                <v-icon style="color: #6E6666">
                  mdi-dots-vertical
                </v-icon>
              </v-btn>
            </div>
            <div v-else>
              <v-btn icon v-bind="attrs" class="mr-md-10 mr-0 mt-5" style="position: relative;" v-on="on">
                <v-icon style="color: #6E6666">
                  mdi-dots-vertical
                </v-icon>
              </v-btn>
            </div>
          </template>

          <v-list>
            <v-list-item>
              <slot name="form-update-vehicle" />
            </v-list-item>
            <!-- <v-dialog v-model="dialogDelete" max-width="600px">
              <template #activator="{ on, attrs }">
                <v-list-item
                  key="delete"
                  v-bind="attrs"
                  :disabled="isVehicleActive"
                  v-on="on"
                  @click="$emit('on-open-delete-dialog')"
                >
                  <v-list-item-title>
                    <v-icon style="color: black">
                      mdi-delete
                    </v-icon>
                    {{ $t('vehicleCard.delete') }}
                  </v-list-item-title>
                </v-list-item>
              </template>
              <v-card>
                <v-card-title class="text-h6 lighten-2">
                  {{ $t('vehicleCard.confirm_delete_title') }}
                </v-card-title>

                <v-card-text>
                  {{ $t('vehicleCard.confirm_delete_text') }}
                </v-card-text>

                <v-divider />

                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    color="primary"
                    text
                    :loading="isLoadingForm"
                    @click="onClickSaveDelete"
                  >
                    {{ $t('vehicleCard.yes') }}
                  </v-btn>
                  <v-btn color="primary" @click="$emit('on-close-delete-dialog')">
                    {{ $t('vehicleCard.cancel') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog> -->
          </v-list>
        </v-menu>
      </v-col>
      <v-divider v-if="$vuetify.breakpoint.mdAndUp" vertical />
      <v-col class="pa-5 col-md-9 col-12">
        <v-row class="pl-5 justify-space-between">
          <v-col class="pa-0 col-sm-auto col-12 mb-2">
            <h4>{{ $t('vehicleCard.plate_number') }}</h4>
            <v-row class="mt-2 pl-2">
              <v-icon style="color: black">
                mdi-record
              </v-icon>
              <p class="mb-0">
                On Shipment
              </p>
              <v-icon class="ml-4" style="color: red">
                mdi-record
              </v-icon>
              <p class="mb-0">
                Idle
              </p>
            </v-row>
          </v-col>

          <div v-if="isHasMenu">
            <v-col v-if="isDeletePlate" class="pa-0 col-auto d-flex align-center">
              <v-checkbox
                v-model="selectAll"
                hide-details
                label="Select All"
                class="ma-0 pa-0"
                @click="selectAllPlateNumbers"
              />

              <v-dialog
                v-model="dialogDeletePlateNumbers"
                width="480"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    text
                    large
                    color="primary"
                    class="mx-4 subtitle-1 text-capitalize"
                    :disabled="selectedPlateNumbers.length < 1"
                    v-on="on"
                  >
                    <v-icon>mdi-delete</v-icon>
                    Delete
                  </v-btn>
                </template>

                <v-card class="py-9 px-10">
                  <v-container fluid class="pa-0 mb-10 d-flex justify-space-between align-center">
                    <h3>Delete Plate Number</h3>

                    <v-icon @click="dialogDeletePlateNumbers = false">
                      mdi-close
                    </v-icon>
                  </v-container>

                  <v-img
                    max-width="120"
                    :src="require(`~/assets/icons/icon-delete.svg`)"
                    class="mx-auto mb-10"
                  />

                  <p class="body-1 ma-0">
                    Are you sure want to delete
                  </p>

                  <p class="subtitle-1 mb-10">
                    <span
                      v-for="(plateNumber, i) in selectedPlateNumbers"
                      :key="plateNumber.id"
                    >
                      {{ plateNumber.plate_number }} {{ i !== selectedPlateNumbers.length - 1 ? ', ' : '' }}
                    </span> ?
                  </p>

                  <v-row class="ma-n3">
                    <v-col class="pa-3">
                      <v-btn
                        block
                        x-large
                        depressed
                        color="primary"
                        class="subtitle-1 text-capitalize"
                        @click="dialogDeletePlateNumbers = false"
                      >
                        Cancel
                      </v-btn>
                    </v-col>

                    <v-col class="pa-3">
                      <v-btn
                        block
                        x-large
                        outlined
                        color="primary"
                        class="subtitle-1 text-capitalize"
                        :loading="isLoadingFormPlateNumber"
                        @click="deletePlateNumbers"
                      >
                        Delete
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-card>
              </v-dialog>

              <v-btn
                depressed
                large
                color="primary"
                class="subtitle-1 text-capitalize"
                @click="selectedPlateNumbers = []; isDeletePlate = false"
              >
                Cancel
              </v-btn>
            </v-col>

            <div v-else class="my-sm-0 my-10">
              <v-row>
                <vehicle-plate-form-dialog
                  :dialog="dialogCreatePlate[index]"
                  :vendor-id="vendorId"
                  :selected-vehicle="vehicle"
                  @on-close-dialog="$set(dialogCreatePlate, index, false)"
                  @on-success-create="$emit('on-success-create-plate')"
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      v-if="isHasPlate"
                      :v-on="on"
                      :v-bind="attrs"
                      depressed
                      color="primary"
                      height="40"
                      class="mr-4"
                      @click="$set(dialogCreatePlate, index, true)"
                    >
                      {{ $t('vehiclePlateFormAddDialog.button_dialog_add') }}
                    </v-btn>
                  </template>
                </vehicle-plate-form-dialog>

                <v-menu v-if="isHasMenu" bottom transition="slide-y-transition">
                  <template #activator="{ on, attrs }">
                    <v-btn icon v-bind="attrs" v-on="on">
                      <v-icon size="40" style="color: #6E6666">
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>

                  <v-list>
                    <v-list-item>
                      <slot name="form-update-plate" />
                    </v-list-item>
                    <v-list-item
                      key="delete"
                      @click="isDeletePlate = true"
                    >
                      <v-list-item-title>
                        <v-icon style="color: black">
                          mdi-delete
                        </v-icon>
                        {{ $t('vehicleCard.delete') }}
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </v-row>
            </div>
          </div>
        </v-row>
        <div style="height: 220px" class="overflow-y-auto overflow-x-hidden mt-5">
          <v-row v-if="vehicle.vehicle_details.length !== 0" class="pa-5">
            <div
              v-for="detail in vehicle.vehicle_details"
              :key="detail.id"
              class="column pa-2 ma-1 my-1 text-center caption"
              @click.stop="detail.is_active ? (dialogListDo = true, clickDetail(detail), getVehicleDetail()) : null"
              style="
                  border: 1px solid #6E6666;
                  border-radius: 4px;
                  cursor: pointer
                "
            >
              <v-checkbox
                v-if="isDeletePlate"
                v-model="selectedPlateNumbers"
                hide-details
                :value="detail"
                class="ma-0 pa-0"
                @click.stop="dialogListDo = false"
              />
              <v-chip v-if="detail.fms_identity" label x-small class="chip-success mb-2 font-weight-medium">
                <p class="ma-0 subtitle-2 text-info">
                  FMS Synced
                </p>
              </v-chip>
              <h5 :style="detail.is_active === true ? 'color: #000000' : 'color: #EF3434'">
                <h5
                  v-html="$options.filters.toHighlight(detail.plate_number?.toUpperCase(), searchKey?.toUpperCase())"
                />
              </h5>
              <p class="mb-0">
                {{ detail.driver?.user?.name }}
              </p>
            </div>
          </v-row>
          <v-col v-else class="d-flex justify-center">
            <div class="text-center mt-10">
              <h4>{{ $t('vehicleCard.plate_empty_title') }}</h4>
              <p>{{ $t('vehicleCard.plate_empty_text') }}</p>
            </div>
          </v-col>
        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import VehiclePlateFormDialog from '~/components/VehiclePlateFormCreateDialog.vue'
import { Vehicle, VehicleDetail } from '~/types/vehicle'

export default Vue.extend({
  name: 'ProductCardItem',

  components: { VehiclePlateFormDialog },

  props: {
    vehicle: {
      type: Object as () => Vehicle,
      required: true
    },
    isHasMenu: {
      type: Boolean,
      default: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    dialogDelete: {
      type: Boolean,
      default: false
    },
    vendorId: {
      type: String,
      default: ''
    },
    isHasPlate: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    selectAll: false,
    hover: false,
    dialogDeletePlateNumbers: false,
    selectedVehicle: '' as string,
    dialogCreatePlate: [] as boolean[],
    selectedPlateNumbers: [] as VehicleDetail[],
    isDeletePlate: false,
    dialogListDo: false,
    dataDetail: null as any
  }),

  computed: {
    isVehicleActive () {
      const vehicleDetails = this.vehicle.vehicle_details as VehicleDetail[]

      for (let i = 0; i < vehicleDetails.length; i++) {
        if (vehicleDetails[i].is_active) {
          return true
        }
      }

      return false
    },

    isLoadingFormPlateNumber (): Boolean {
      return this.$store.getters['vehicle/details/isLoadingForm']
    },

    reformattedFormValues (): string[] {
      const selectedItems = this.selectedPlateNumbers as VehicleDetail[]

      return selectedItems.map((item) => {
        return item.id
      })
    },

    entriesMode (): any {
      return this.$store.getters['layout/entriesMode']
    },

    detailData (): VehicleDetail {
      return this.$store.getters['vehicle/details/detailData']
    },

    isLoading (): boolean {
      return this.$store.getters['vehicle/details/isLoading']
    },

    searchKey () {
      return this.$store.getters['vehicle/searchKey']
    }
  },

  mounted () { 
    this.$nextTick(() => {
      this.scrollToFirstHighlight()
    })
  },

  methods: {
    clickDetail (detail: any) {
      this.dataDetail = detail
    },
    selectAllPlateNumbers () {
      const nonActiveVehicleDetails = this.vehicle.vehicle_details?.filter((vehicleDetail) => {
        return vehicleDetail.is_active === false
      }) as VehicleDetail[]

      if (this.selectAll) {
        this.selectedPlateNumbers = nonActiveVehicleDetails
      } else {
        this.selectedPlateNumbers = []
      }
    },

    async deletePlateNumbers () {
      const response = await this.$store.dispatch('vehicle/details/deleteItems', {
        vehicleDetailIds: this.reformattedFormValues,
        vendorId: this.vendorId,
        entries: this.entriesMode
      })

      if (response) {
        this.$emit('on-success-delete-plate')
      }
    },

    onClickSaveDelete () {
      this.$emit('on-click-save-delete', this.vehicle.id, this.index)
    },

    scrollToFirstHighlight () {
      const element = document.getElementsByClassName('text-highlight-plate')
      if (element && element.length > 0) {
        console.log(element)
        element[0].scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        })
      }
    },

    async getVehicleDetail () {
      await this.$store.dispatch('vehicle/details/getVehicleDetail', {
        id: this.dataDetail?.id + '/active-order'
      })
    },

  }
})
</script>

<style lang="scss" scoped>
.v-expansion-panels.custom-panel {
  overflow: hidden;
}

.v-expansion-panels.custom-panel .v-expansion-panel-header {
  border-radius: 4px;
  padding: 0 20px;
  min-height: 52px;
}

.v-expansion-panels.custom-panel .v-expansion-panel-content::v-deep .v-expansion-panel-content__wrap {
  padding: 0 !important;
  height: auto !important;
}

.chip-success {
  background: #eaf6ec !important;
}

.chip-orange {
  background: #ffdfaa !important;
}

.chip-danger {
  background: #fde0e0 !important;
}

.chip-info {
  background: #e6f4f8 !important;
}
</style>

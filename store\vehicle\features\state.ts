import { VehicleFeature } from '~/types/vehicle'

export interface VehicleFeatureState {
  isLoading: boolean
  isLoadingForm: boolean
  items: VehicleFeature[]
  selectedFeatures: VehicleFeature[]
  totalPage: number
  page: number
}

export const state = (): VehicleFeatureState => ({
  isLoading: false,
  isLoadingForm: false,
  items: [] as Array<VehicleFeature>,
  selectedFeatures: [],
  totalPage: 1,
  page: 1
})

export default state

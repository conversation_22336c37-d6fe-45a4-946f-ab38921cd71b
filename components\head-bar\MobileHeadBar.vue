<template>
  <v-container fluid class="pa-0 bg-color">
    <v-app-bar class="py-4 px-1 custom-head-bar" elevation="0" height="auto">
      <v-container class="pa-0 d-flex justify-space-between">
        <v-img :src="logo" aspect-ratio="1" max-width="50" contain />

        <div class="d-flex align-center">
          <v-badge
            v-if="notificationIsExist"
            dot
            bordered
            overlap
            color="primary"
          >
            <v-icon color="black" @click="notificationDrawer = true">
              mdi-bell
            </v-icon>
          </v-badge>

          <v-icon v-else color="black" @click="notificationDrawer = true">
            mdi-bell
          </v-icon>

          <nuxt-link :to="localePath('/profile/account-setting')">
            <v-avatar size="36" rounded class="mx-5" color="primary">
              <p class="white--text ma-0 body-1">
                {{ initialName }}
              </p>
            </v-avatar>
          </nuxt-link>

          <v-avatar
            size="36"
            rounded
            color="white"
            class="custom-drawer-btn"
            @click="navigationDrawer = true"
          >
            <v-icon size="24" class="custom-icon" color="black">
              mdi-menu
            </v-icon>
          </v-avatar>
        </div>
      </v-container>
    </v-app-bar>

    <h3 class="mx-5 mt-0 mb-10">
      {{ title }}
    </h3>

    <v-navigation-drawer
      v-model="notificationDrawer"
      app
      right
      temporary
      width="100%"
    >
      <div class="d-flex justify-space-between ma-5">
        <h3>Notification</h3>

        <div>
          <v-icon @click="notificationDrawer = false">
            mdi-close
          </v-icon>
        </div>
      </div>
      <notification-list />
    </v-navigation-drawer>

    <v-navigation-drawer v-model="navigationDrawer" app temporary width="100%">
      <div class="mx-5 my-5 d-flex align-end flex-column">
        <v-icon @click="navigationDrawer = false">
          mdi-close
        </v-icon>
      </div>

      <slot name="navigation-list" />
    </v-navigation-drawer>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import NotificationList from '~/components/NotificationList.vue'

export default Vue.extend({
  name: 'MobileHeadBar',

  components: { NotificationList },

  props: {
    logo: {
      type: String,
      required: true
    }
  },

  data: () => ({
    notificationIsExist: false,
    notificationDrawer: false,
    navigationDrawer: false
  }),

  computed: {
    title ():string {
      return this.$store.getters['layout/title']
    },
    initialName () {
      const user = this.$auth.user as any
      const split = user.data.name.split(' ')
      if (split.length > 1) {
        return split[0].charAt(0) + split[1].charAt(0)
      } else {
        return split[0].charAt(0)
      }
    }
  },

  watch: {
    notificationDrawer: {
      handler (val) {
        if (val) {
          this.$store.dispatch('notification/getItems', {})
        }
      }
    }
  }

})
</script>

<style lang="scss" scoped>
.custom-head-bar {
  background-color: #f0f0f0 !important;
}

.custom-btn {
  transition: 0.28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #ef3434 !important;
  color: white !important;
}

.custom-drawer-btn {
  transition: 0.28s !important;
  cursor: pointer !important;
}

.custom-drawer-btn:hover {
  background-color: #ef3434 !important;
}

.custom-drawer-btn:hover :first-child {
  color: white !important;
}
</style>

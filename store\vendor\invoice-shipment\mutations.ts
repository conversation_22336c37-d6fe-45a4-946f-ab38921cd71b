import { MutationTree } from 'vuex'
import { InvoiceShipmentState } from '~/store/vendor/invoice-shipment/state'
import { InvoiceDetail } from '~/types/invoice'

export const mutations: MutationTree<InvoiceShipmentState> = {
  SET_ITEMS (state, items: InvoiceDetail[]) {
    state.items = items
  },

  SET_VENDOR_ITEMS (state, vendorItems : InvoiceDetail[]) {
    state.vendorItems = vendorItems
  },

  SET_ITEM (state, item: InvoiceDetail) {
    state.item = item
  },

  SET_TOTAL_PAGE (state, totalPage: number) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: number) {
    state.page = page
  },

  SET_TOTAL_PAGE_VENDOR (state, totalPageVendor: number) {
    state.totalPageVendor = totalPageVendor
  },

  SET_PAGE_VENDOR (state, pageVendor: number) {
    state.pageVendor = pageVendor
  },

  SET_IS_LOADING (state, isLoading: boolean) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_VENDOR (state, isLoadingVendor: boolean) {
    state.isLoadingVendor = isLoadingVendor
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail: boolean) {
    state.isLoadingDetail = isLoadingDetail
  },

  SET_IS_LOADING_PUBLISH (state, isLoadingPublish: boolean) {
    state.isLoadingPublish = isLoadingPublish
  }
}

export default mutations

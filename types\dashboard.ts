import { Track } from '~/types/shipment'

export interface UserGrowthSC{
  count: number
  month: Date
}

export interface UserGrowthVendor{
  count: number
  month: Date
  first_registered_account?: any
}

export interface UserGrowthLsp{
  count: number
  month: Date
  register_banner_url?: any
}

export interface DashboardAdmin{
  shipment_company: number
  last_added_sc: string
  logistic_service_integrator: number
  last_added_lsp: string
  vendor: number
  last_added_vendor: string
  user_growth_sc: UserGrowthSC[]
  user_growth_lsp: UserGrowthLsp[]
  user_growth_vendor: UserGrowthVendor[]
}
export interface DashboardLsa{
  request_order: any
  order_canceled: any
  shipment_reject: any
}

export interface DashboardShipment {
  id: string
  identity: string
  tracks: Track[]
}

export interface DashboardVendor {
  request_shipment: number
  shipment_canceled: number
  active_order: ActiveOrder[]
}

export interface ActiveOrder {
  id: string
  identity: string
  tracks: Track[]
}

export interface Geofences{
  name: string
  coordinates: string
  polygon_color: string
  center: {lat: number, lng: number} | undefined
  radius: number | undefined
  type: string
}

export interface VendorOrderShipment {
  vendor: string
  mill_weight: number
  refinery_weight: number
}

export interface PerformanceVendor {
  itemVendorVehicles: PerformanceVendor[]
  vendor: string
  loss_percentage: number
  total_loss_weight: number
  mill_weight: number
  refinery_weight: number
  average_ffa_quality_mill: number
  average_ffa_quality_refinery: number
  diff_average_ffa_quality: number
  average_mmi_quality_mill: number
  average_mmi_quality_refinery: number
  diff_average_mmi_quality: number
}

// export interface Driver {
//   id: string
//   name: string
//   avatar?: any
//   avatar_url?: any
// }

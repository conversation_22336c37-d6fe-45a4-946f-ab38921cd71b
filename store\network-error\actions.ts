import { ActionTree } from 'vuex'
import { NetworkErrorState } from '~/store/network-error/state'

export const actions: ActionTree<NetworkErrorState, NetworkErrorState> = {
  async setIsNetworkError ({ commit }, payload: any) {
    await commit('SET_IS_NETWORK_ERROR', payload)
  },

  retryAction ({ state, dispatch }) {
    setTimeout(() => {
      try {
        dispatch(state.lastAction, state.lastPayload, { root: true })
      } catch (e) { }
    }, 5000)
  }
}

export default actions

<template>
  <v-app>
    <network-error-overlay v-if="isNetworkError" />

    <v-main class="bg-color">
      <nuxt />
    </v-main>
  </v-app>
</template>

<script lang="ts">
import Vue from 'vue'
import { Personalize } from '~/types/user'
import NetworkErrorOverlay from '~/components/NetworkErrorOverlay.vue'
import { toastNetworkSuccess } from '~/utils/functions'

export default Vue.extend({
  name: 'BodyAccountSetting',

  components: { NetworkErrorOverlay },

  computed: {
    isNetworkError (): Boolean {
      return this.$store.getters['network-error/isNetworkError']
    }
  },

  watch: {
    isNetworkError () {
      if (!this.isNetworkError) {
        toastNetworkSuccess(this)
      }
    }
  },

  mounted () {
    this.getPersonalize()
  },

  methods: {
    getPersonalize () {
      const ctx = this
      setTimeout(function () {
        const personalize = ctx.$store.getters['logistic-service-provider/personalize/data'] as Personalize
        ctx.$vuetify.theme.themes.light.primary = personalize.primary_color ?? ctx.$vuetify.theme.themes.light.primary
      }, 100)
    }
  }
})
</script>

<style scoped>

</style>

<template>
  <div class="px-md-10 px-5 mb-10">
    <product-management :id="$auth.$state.user.data.shipment_company_id" class-grid="col-xs-12 col-sm-6 col-lg-4 col-12" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductManagement from '~/components/ProductManagement.vue'

export default Vue.extend({
  name: 'ProductPage',

  components: { ProductManagement },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company']

})
</script>

<style lang="scss" scoped> </style>

<template>
  <v-btn
    plain
    class="pa-2 subtitle-1 text-capitalize"
    @click="onClick"
  >
    <v-icon
      size="24"
      class="mr-2"
    >
      mdi-chevron-left
    </v-icon>
    {{ title }}
  </v-btn>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ButtonBack',

  props: {
    title: {
      type: String,
      default: ''
    },
    path: {
      type: String || null,
      default: null
    }
  },

  methods: {
    onClick () {
      if (this.path) {
        this.$router.push(this.localePath(this.path))
      } else {
        this.$router.back()
      }
    }
  }
})
</script>

<style scoped> </style>

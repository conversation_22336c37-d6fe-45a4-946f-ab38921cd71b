import { MutationTree } from 'vuex'
import { ShipmentHistoryState } from './state'

export const mutations: MutationTree<ShipmentHistoryState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  }
}

export default mutations

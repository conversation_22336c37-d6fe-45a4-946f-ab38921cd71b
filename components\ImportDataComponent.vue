<template>
  <v-container fluid class="pa-5 pa-md-10 white rounded">
    <div class="mb-6 d-flex align-start justify-space-between">
      <div>
        <h3 class="mb-1">
          Upload File
        </h3>
        <p class="ma-0 body-1 text-secondary">
          Upload files to import into the system
        </p>
      </div>

      <slot name="button-download-template" />
    </div>

    <div class="col-12 col-md-6 pa-0 mb-10">
      <v-file-input
        outlined
        persistent-hint
        label="File"
        hint="Format: .csv, .xlsx"
        prepend-icon="mdi-paperclip"
        accept=".xlsx,text/csv,application/vnd.ms-excel"
        @change="onChangeFile($event)"
      />
    </div>

    <div class="mb-6">
      <h3 class="mb-1">
        Adjust Data File
      </h3>
      <v-container fluid class="pa-0 d-flex flex-column flex-md-row justify-md-space-between">
        <p class="ma-0 mb-5 body-1 text-secondary">
          Adjust the imported data with the data in our data
        </p>

        <p v-if="type === 'VEHICLE'" class="ma-0 body-1 text-secondary">
          Step {{ tab === 'tabBulkImport' ? '1' : '2' }} of 2
        </p>
      </v-container>
    </div>

    <v-container v-if="type === 'VEHICLE'" fluid class="pa-0">
      <v-tabs v-model="tab" class="d-none">
        <v-tab href="#tabBulkImport">
          Bulk Import
        </v-tab>
        <v-tab href="#tabSelectPlate">
          Select Plate
        </v-tab>
      </v-tabs>

      <v-tabs-items v-model="tab">
        <v-tab-item value="tabBulkImport">
          <table-adjust-data
            v-if="dataJson.length > 0"
            :page="page"
            :type="type"
            :import-keys="importKeys"
            :data-json="dataJson"
            :data-table-headers="dataTableHeaders"
            :data-table-items="dataTableItems"
            class="mb-5 mb-md-10"
            @on-page-count="pageCount = $event"
            @on-change-form-values="
              $emit('on-change-form-values', $event.formValuesVehicle)
              formValuesPlateNumber = $event.formValuesPlateNumber
            "
          />

          <div v-else class="mb-10">
            <h4 class="mb-2 text-center">
              No data imports
            </h4>
            <p class="ma-0 body-1 text-secondary text-center">
              Upload your file to display the data that will be import into the system.
            </p>
          </div>

          <v-container fluid class="pa-0 d-flex flex-column-reverse flex-md-row align-md-center justify-md-space-between">
            <slot name="footer-bulk-import" />

            <v-pagination
              v-if="dataJson.length > 0"
              v-model="page"
              total-visible="5"
              :length="pageCount"
              class="mb-10 mb-md-0"
            />
          </v-container>
        </v-tab-item>

        <v-tab-item value="tabSelectPlate">
          <form-categorize-vehicle
            type="IMPORT"
            :vehicles="vehicles"
            :imported-plate-numbers="formValuesPlateNumber"
            button-label="Select Plate"
            @on-success-add-vehicle-detail="reloadPlateNumbers($event)"
          />
        </v-tab-item>
      </v-tabs-items>
    </v-container>

    <v-container v-else fluid class="pa-0">
      <v-container v-if="dataJson.length > 0" fluid class="pa-0">
        <table-adjust-data
          :page="page"
          :import-keys="importKeys"
          :data-json="dataJson"
          :data-table-headers="dataTableHeaders"
          :data-table-items="dataTableItems"
          class="mb-10"
          @on-page-count="pageCount = $event"
          @on-change-form-values="$emit('on-change-form-values', $event)"
        />

        <v-container fluid class="pa-0 d-flex flex-column-reverse flex-md-row align-md-center justify-md-space-between">
          <slot name="footer-bulk-import" />

          <v-pagination
            v-model="page"
            total-visible="5"
            :length="pageCount"
            class="mb-10 mb-md-0"
          />
        </v-container>
      </v-container>

      <div v-else>
        <h4 class="mb-2 text-center">
          No data imports
        </h4>
        <p class="ma-0 body-1 text-secondary text-center">
          Upload your file to display the data that will be import into the system.
        </p>
      </div>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import TableAdjustData from '~/components/TableAdjustData.vue'
import FormCategorizeVehicle from '~/components/FormCategorizeVehicle.vue'
import { Vehicle, VehicleDetail } from '~/types/vehicle'
import { Product } from '~/types/product'
import { ImportKey } from '~/types/import-key'
import { sheetToJson } from '~/utils/functions'

interface Header {
  text: string
  value: string
  disabled: boolean
  is_required: boolean
}

export default Vue.extend({
  name: 'ImportDataComponent',

  components: { TableAdjustData, FormCategorizeVehicle },

  props: {
    tab: {
      type: String,
      default: 'tabBulkImport'
    },
    type: {
      type: String,
      default: ''
    },
    buttonName: {
      type: String,
      default: ''
    },
    importKeys: {
      type: Array as () => ImportKey[],
      default: () => []
    },
    vehicles: {
      type: Array as () => Vehicle[],
      default: () => []
    },
    vehicleDetails: {
      type: Array as () => VehicleDetail[],
      default: () => []
    },
    products: {
      type: Array as () => Product[],
      default: () => []
    }
  },

  data: () => ({
    dataJson: [] as any[],
    dataTableHeaders: [] as Header[],
    dataTableItems: [] as any[],
    page: 1 as number,
    pageCount: 0 as number,
    formValuesPlateNumber: [] as string[]
  }),

  watch: {
    dataJson (json: object[]) {
      this.dataTableHeaders = []
      this.dataTableItems = []

      if (json.length > 0) {
        this.generateDataTableHeaders()
        this.generateDataTableItems()
      }
    },

    formValuesPlateNumber (values: string[]) {
      this.$emit('on-select-header-plate-number', values.length > 0)
    }
  },

  methods: {
    reloadPlateNumbers (selectedItems: { value: string, text: string }[]) {
      selectedItems.forEach((item) => {
        this.formValuesPlateNumber = this.formValuesPlateNumber.filter(itm => itm !== item.text)
      })
    },

    async onChangeFile (file: Blob & File | null) {
      if (!file) {
        this.dataJson = []
        return null
      }

      const fileExtension = file.name.split('.').pop()

      if (fileExtension !== 'csv' && fileExtension !== 'xlsx') {
        this.dataJson = []
        return null
      }

      this.dataJson = await sheetToJson(file)
    },

    generateDataTableHeaders () {
      const keys: string[] = Object.keys(this.dataJson[0])

      this.dataTableHeaders = keys.map(key => ({
        text: key,
        value: key.toLocaleLowerCase().replace(/\s+/g, '_'),
        disabled: false,
        is_required: false
      }))
    },

    generateDataTableItems () {
      this.dataTableItems = this.dataJson.map((item) => {
        const newItem: any = {}

        for (const key in item) {
          const newKey = key.toLocaleLowerCase().replace(/\s+/g, '_')
          newItem[newKey] = item[key]
        }

        return newItem
      })
    }
  }
})
</script>

<style scoped> </style>

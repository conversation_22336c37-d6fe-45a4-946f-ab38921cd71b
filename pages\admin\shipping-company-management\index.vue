<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column align-end">
    <v-container class="pa-0" fluid>
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-filter-change="getData({filter: $event, page: $route.query?.page})"
        @on-search-icon-click="searchData"
      >
        <template #button>
          <user-role-form-item
            label="Product Owner"
            :is-has-domain-field="false"
            :is-loading-form="isLoadingForm"
            :logistics-service-provider-domains="
              logisticsServiceProviderDomains
            "
            :dialog="dialogSc"
            :clear-form="clearForm"
            @on-click-save="createItem"
            @on-close-dialog="
              dialogSc = false
              clearForm = true
            "
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-if="$vuetify.breakpoint.xs"
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                block
                class="mb-4 mt-5"
                v-on="on"
                @click="
                  dialogSc = true
                  clearForm = false
                "
              >
                {{ $t('adminSc.add_sc') }}
              </v-btn>
              <v-btn
                v-else
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                v-on="on"
                @click="
                  dialogSc = true
                  clearForm = false
                "
              >
                {{ $t('adminSc.add_sc') }}
              </v-btn>
            </template>
          </user-role-form-item>
        </template>
      </header-datatable>
    </v-container>

    <company-loading v-if="isLoading" />

    <v-container v-else fluid class="pa-0 mb-10">
      <v-row v-if="(data.items.length !== 0)" class="ma-n5">
        <v-col
          v-for="(item, i) in data.items"
          :key="item.id"
          md="4"
          sm="6"
          class="pa-5"
        >
          <user-role-card-item
            :item="item"
            :is-location-visible="true"
            :is-loading-form="isLoadingForm"
            detail-route="/admin/shipping-company-management"
            :logistics-service-provider-domains="logisticsServiceProviderDomains"
            :logistics-service-provider-domain="
              item.logistics_service_provider?.domain
            "
            :index="i"
            :dialog-update="dialogUpdateSc[i]"
            :dialog-delete="dialogDeleteSc[i]"
            @on-click-save-edit="editItem"
            @on-click-save-delete="deleteItem"
            @on-open-update-dialog="$set(dialogUpdateSc, i, true)"
            @on-close-update-dialog="$set(dialogUpdateSc, i, false)"
            @on-open-delete-dialog="$set(dialogDeleteSc, i, true)"
            @on-close-delete-dialog="$set(dialogDeleteSc, i, false)"
          />
        </v-col>
      </v-row>

      <v-row v-else>
        <v-col class="justify-center align-center fill-height">
          <empty-placeholder
            hero="empty-shipment.svg"
            :message-title="$t('adminSc.empty_message_title')"
            :message-description="$t('adminSc.empty_message_description')"
          />
        </v-col>
      </v-row>
    </v-container>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getData({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import CompanyLoading from '~/components/loading/CompanyLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'ScManagementPage',
  components: {
    HeaderDatatable,
    CompanyLoading,
    EmptyPlaceholder,
    PaginationComponent
  },
  layout: 'admin/body',
  middleware: ['auth', 'is-admin'],
  data: () => ({
    searchKey: '',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogSc: false,
    dialogUpdateSc: [] as Array<Boolean>,
    dialogDeleteSc: [] as Array<Boolean>,
    clearForm: false
  }),
  computed: {
    data () {
      return this.$store.getters['admin/shipping-company/data']
    },
    isLoading () {
      return this.$store.getters['admin/shipping-company/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['admin/shipping-company/isLoadingForm']
    },
    logisticsServiceProviderDomains () {
      return this.$store.getters[
        'admin/logistic-service-provider/data'
      ].items.map((value: any) => value.domain)
    }
  },
  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Product Owner Management')

    this.getData({
      page: this.$route.query?.page as string
    })

    this.$store.dispatch('admin/logistic-service-provider/getItems', {
      name: '',
      page: '-1'
    })
  },
  methods: {
    getData ({ page = '', searchKey = '', filter = { sortColumn: 'name', sortType: 'asc' } }) {
      this.$store.dispatch('admin/shipping-company/getItems', {
        name: searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page
      })
    },
    searchData (value: any) {
      this.searchKey = value

      this.$store.dispatch('admin/shipping-company/getItems', {
        name: this.searchKey,
        page: 1
      })
    },
    async createItem (value: any) {
      const response = await this.$store.dispatch(
        'admin/shipping-company/createItem',
        value
      )
      if (response) {
        this.dialogSc = false
        this.clearForm = true
      }
    },
    async editItem (value: any, i: any) {
      const response = await this.$store.dispatch(
        'admin/shipping-company/editItem',
        value
      )
      if (response) {
        this.$set(this.dialogUpdateSc, i, false)
      }
    },
    async deleteItem (id: any, i: any) {
      await this.$store.dispatch('admin/shipping-company/deleteItem', {
        id
      })
      this.$set(this.dialogDeleteSc, i, false)
    }
  }
})
</script>

<style scoped>
.v-avatar:hover {
  cursor: pointer;
}
</style>

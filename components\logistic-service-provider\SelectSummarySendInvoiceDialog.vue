<template>
  <v-dialog v-model="dialog" persistent max-width="600px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-10">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>{{ $t('lspInvoiceShipment.send_invoice') }}</h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>
        <v-card class="inner-card pa-1">
          <div class="container">
            <v-card-title style="font-weight: bold;">
              {{ $t('lspInvoiceShipment.preview_invoice') }}
            </v-card-title>
            <v-row>
              <v-col>
                <v-card-title>Customer</v-card-title>
                <v-divider />
                <v-col
                  v-for="(name, index) in shipmentCompanyNames"
                  :key="index"
                >
                  <p class="subtitle-1 text-bold mb-1 pa-1">
                    {{ name }}
                  </p>
                </v-col>

                <v-divider />
                <v-col>
                  <p class="subtitle-1 text-bold mb-1 pa-1" style="color: red">
                    Total Invoice
                  </p>
                </v-col>
              </v-col>
              <v-col>
                <v-card-title>Invoice</v-card-title>
                <v-divider />
                <v-col v-for="(amount, index) in totalAmounts" :key="index">
                  <p class="subtitle-1 mb-1 pa-1">
                    {{ amount | toCurrency }}
                  </p>
                </v-col>
                <v-divider />
                <v-col>
                  <p class="subtitle-1 text-bold mb-1 pa-1" style="color: red">
                    {{ sumTotalAmounts() | toCurrency }}
                  </p>
                </v-col>
              </v-col>
            </v-row>
          </div>
        </v-card>

        <p class="mb-4 subtitle-1 mt-5">
          {{ $t('lspInvoiceShipment.dialog_send_invoice') }} <span style="font-weight: bold;">#{{ dataInvoiceShipment?.invoice?.shipment?.identity }}</span> {{ $t('lspInvoiceShipment.dialog_send_invoice_correctly') }}
        </p>
        <div class="d-flex mt-2">
          <v-btn
            depressed
            class="mr-2"
            color="primary"
            @click="
              selected = null;
              $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', null);
              $emit('on-close-dialog')"
          >
            {{ $t('lspInvoiceShipment.check_again') }}
          </v-btn>
          <v-btn
            outlined
            depressed
            color="primary"
            :disabled="dataInvoiceShipment?.invoice?.status === 'PUBLISHED' "
            :loading="isLoadingPublish"
            @click="createInvoice"
          >
            {{ $t('lspInvoiceShipment.send_invoice') }}
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Fee, InvoiceOrder } from '~/types/invoice'
import { SubOrder } from '~/types/product'

export default Vue.extend({
  name: 'SelectSummarySendInvoiceDialog',

  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    totalAmounts: [] as Number[],
    shipmentCompanyNames: [] as String[]
  }),

  computed: {
    dataInvoiceShipment (): InvoiceOrder | null {
      return this.$store.getters['logistic-service-provider/invoice-shipment/detailData'].item
    },
    isLoadingPublish (): Boolean {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingPublish']
    }
  },

  watch: {
    dialog () {
      if (this.dialog) { this.calculatesDataInvoiceShipmentOrders() }
    }

  },

  methods: {
    calculatesDataInvoiceShipmentOrders () {
      this.totalAmounts = []
      this.shipmentCompanyNames = []

      for (const invoice of this.dataInvoiceShipment?.invoice_details || []) {
        if (invoice.order.shipment_company) {
          this.shipmentCompanyNames.push(invoice.order.shipment_company.name)
        }

        const subOrders = invoice.order?.suborders as SubOrder[]

        const pickupSubOrder: SubOrder[] = []
        const dropOffSubOrder: SubOrder[] = []

        subOrders.forEach((item: SubOrder) => {
          if (item.type === 'PICKUP') {
            pickupSubOrder?.push(item)
          } else {
            dropOffSubOrder?.push(item)
          }
        })

        const fees = invoice?.fees as Fee[]

        const additionalCosts: any[] = fees.map((item:any) => ({
          id: item.id,
          description: item.description,
          cost: item.cost
        }))

        const costs = Number(invoice.cost)

        let amountCost = 0

        amountCost += Number(costs)

        for (const items of additionalCosts) {
          if (items.cost === null) {
            amountCost += 0
            continue
          }
          amountCost += Number(`${items.cost}`)
        }

        this.totalAmounts.push(amountCost)
      }
    },
    sumTotalAmounts () {
      return this.totalAmounts.reduce((acc, curr) => +acc + +curr, 0)
    },
    async createInvoice () {
      const response = await this.$store.dispatch('logistic-service-provider/invoice-shipment/createItem', {
        id: this.$route.params.id
      })
      if (response) {
        this.$emit('on-send')
      }
    }
  }
})

</script>
<style scoped>

.shipment-item {
  margin-bottom: 10px;
}

.inner-card {
  background-color: rgb(233, 227, 227);
}

</style>

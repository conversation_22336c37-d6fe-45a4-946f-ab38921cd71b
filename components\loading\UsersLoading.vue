<template>
  <v-row class="ma-n5">
    <v-col
      v-for="i in 3"
      :key="i"
      class="pa-5 col-lg-4 col-sm-6 col-12"
    >
      <v-sheet height="165" class="pa-5 overflow-hidden">
        <div class="d-flex">
          <div class="d-flex flex-row align-center" style="width: 100%">
            <v-skeleton-loader type="image" height="50" width="50" />
            <div class="d-flex flex-column" style="width: 100%">
              <v-skeleton-loader type="list-item-two-line" width="100%"/>
            </div>
          </div>
        </div>
        <div class="d-flex flex-column pt-4 ">
          <v-skeleton-loader type="text" width="250" class="pb-2 flex" />
          <v-skeleton-loader type="text" width="150" />
        </div>
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'UsersLoading'
})
</script>

<template>
  <v-dialog v-if="isHasDetailView" max-width="540">
    <template #activator="{ attrs, on }">
      <v-img
        :src="image"
        :max-width="maxWidth"
        :min-width="minWidth"
        contain
        aspect-ratio="1"
        v-bind="attrs"
        v-on="on"
      >
        <template #placeholder>
          <v-img
            :max-width="maxWidth"
            :min-width="minWidth"
            aspect-ratio="1"
            contain
            :src="require(`~/assets/images/placeholder-company-logo.svg`)"
          />
        </template>
      </v-img>
    </template>

    <v-card class="pa-5">
      <v-img
        :src="image"
        contain
        aspect-ratio="1"
      >
        <template #placeholder>
          <v-img
            aspect-ratio="1"
            contain
            :src="require(`~/assets/images/placeholder-company-logo.svg`)"
          />
        </template>
      </v-img>
    </v-card>
  </v-dialog>

  <v-img
    v-else
    :src="image"
    :max-width="maxWidth"
    :min-width="minWidth"
    contain
    aspect-ratio="1"
  >
    <template #placeholder>
      <v-img
        :max-width="maxWidth"
        :min-width="minWidth"
        aspect-ratio="1"
        contain
        :src="require(`~/assets/images/placeholder-company-logo.svg`)"
      />
    </template>
  </v-img>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ImageComponent',

  props: {
    maxWidth: {
      type: String,
      default: '80'
    },
    minWidth: {
      type: String,
      default: '80'
    },
    image: {
      type: String,
      default: '~/assets/images/placeholder-company-logo.svg'
    },
    isHasDetailView: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped lang="scss"></style>

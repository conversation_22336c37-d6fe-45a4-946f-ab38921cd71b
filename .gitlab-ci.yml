stages:
  - build
  - test
  - publish
  - release

default:
  tags:
    - build-gcp

variables:
  CONTAINER_REGISTRY: ${CI_REGISTRY_IMAGE}/application

build-check:
  stage: build
  script:
    - cp .env.example .env
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_SHA} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  artifacts:
    paths:
      - .env
    expire_in: 2 hours
    when: always

build:
  stage: build
  script:
    - cp .env.example .env
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_TAG} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
  except:
    - branches
  artifacts:
    paths:
      - .env
    expire_in: 1 days
    when: always

automation-test:
  stage: test
  dependencies:
    - build
  script:
    - echo run tests in this section
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

vulnerabilities-test:
  stage: test
  dependencies:
    - build
  script:
    - echo run tests in this section
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

sonarqube-check:
  stage: test
  tags:
    - tools-gcp
  image: 
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - sonar-scanner
  only:
    - merge_requests
  #allow_failure: true
  except:
    - branches

report-sonar-to-merge-id:
  stage: test
  dependencies:
    - sonarqube-check
  tags:
    - tools-gcp
  script:
    - PROJECT_ID=$CI_PROJECT_ID
    - MERGE_IID=$CI_MERGE_REQUEST_IID
    - KEY=$KEY SONAR_TOKEN=$sonar_access_token repopo_token=$repopo_token PROJECT_ID=$PROJECT_ID MERGE_IID=$MERGE_IID bash -c 'sed -i "1s/^/project_id=$PROJECT_ID merge_iid=$MERGE_IID key=$KEY sonar_token=$sonar_access_token repopo_token=$repopo_token /" report.sh'
    - apt-get update -qy  # Install curl
    - apt-get install -y curl
    - bash report.sh
    # - cat report.sh
  except:
    - branches
  only:
    - merge_requests
  needs:
    - job: sonarqube-check
  # when: manual

publish:
  stage: publish
  dependencies:
    - automation-test
    - vulnerabilities-test
    - sonarqube-check
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CONTAINER_REGISTRY:${CI_COMMIT_TAG}
    - docker push $CONTAINER_REGISTRY:latest
  except:
    - branches
  artifacts:
    paths:
      - .env
    expire_in: 1 days
    when: always

staging:
  stage: release
  dependencies:
    - publish
  variables:
    GIT_STRATEGY: none
    ENVI: staging
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_STAGING_APICAL" > "$(pwd)/.env"
    - echo "$DOCKER_COMPOSE_APICAL" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker compose pull application && docker compose up -d --force-recreate
    - docker image prune -f
    - bash notifier.sh
  tags:
    - apical-staging-gcp
  only:
    - tags
    - /^v\d+\.\d+\.\d+-apical$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always

pre-production:
  stage: release
  dependencies:
    - publish
  variables:
    GIT_STRATEGY: none
    ENVI: pre-production
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_APICAL_PRE_PROD" > "$(pwd)/.env"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker compose pull application && docker compose up -d --force-recreate
    - docker image prune -f
    - bash notifier.sh
  tags:
    - apical-pre-prod-gcp
  only:
    - tags
    - /^v\d+\.\d+\.\d+-apical$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always
  when : manual

production:
  stage: release
  dependencies:
    - publish
  variables:
    GIT_STRATEGY: none
    ENVI: production
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_APICAL" > "$(pwd)/.env"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker compose pull application && docker compose up -d --force-recreate
    - docker image prune -f
    - bash notifier.sh
  tags:
    - apical
  only:
    - tags
    - /^v\d+\.\d+\.\d+-apical$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always
  when : manual

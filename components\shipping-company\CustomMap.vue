<template>
  <v-container id="map-wrap" fluid style="z-index: 0" class="pa-0 fill-height">
    <client-only>
      <l-map
        ref="map"
        :zoom="zoom"
        :bounds="bounds"
        :center="[latitude, longitude]"
        :fade-animation="true"
        :no-blocking-animations="true"
        :options="{ zoomControl: true, scrollWheelZoom: true }"
        :use-global-leaflet="false"
        @click="onClickMap"
      >
               <!-- <l-tile-layer url="https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png" /> -->
        <l-tile-layer url="https://api.mapbox.com/styles/v1/mapbox/streets-v12/tiles/{z}/{x}/{y}?access_token=**********************************************************************************************" />

        <slot name="marker" />

        <l-marker
          v-if="isClickable && isMapClicked"
          :lat-lng="[currentPosition.lat, currentPosition.lng]"
        />

        <slot name="polyline" />

        <l-polyline
          v-for="(line, i) in polyline?.mapBox"
          :key="line.id + 'MAPBOX'"
          :lat-lngs="line.latLng"
          :options="polylineOptions?.mapBox[i]"
        />

        <l-polyline
          v-for="(line,i) in polyline?.fms"
          :key="line.id + 'FMS'"
          :lat-lngs="line.latLng"
          :options="polylineOptions?.fms[i]"
        />

        <div v-for="(geofence, i) in geofences" :key="i">
          <l-circle
              v-if="geofence.type === 'circle'"
              ref="circle"
              :color="geofence.polygon_color"
              :lat-lng="[geofence.center?.lat, geofence.center?.lng]"
              :radius="geofence.radius ?? 0"
          >
            <l-tooltip class="text-labels" direction="center" permanent>
              {{ geofence.name }}
            </l-tooltip>
          </l-circle>
          <l-polygon
              v-else
              :lat-lngs="JSON.parse(geofence.coordinates)"
          />
        </div>
      </l-map>
    </client-only>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { defaultLat, defaultLng } from '~/utils/functions'
import { Geofences } from '~/types/dashboard'

interface Polyline {
  mapBox: any[]
  fms: any[]
}

export default Vue.extend({
  name: 'CustomMap',

  props: {
    isClickable: {
      type: Boolean,
      default: false
    },
    latitude: {
      type: Number,
      default: defaultLat
    },
    longitude: {
      type: Number,
      default: defaultLng
    },
    bounds: {
      type: Array as () => any | null,
      default: null
    },
    zoom: {
      type: Number,
      default: 4
    },
    polyline: {
      type: Object as () => Polyline,
      default: () => ({
        mapBox: [],
        fms: []
      })
    },
    geofences: {
      type: Array as () => Geofences[],
      default: () => []
    },
    selectedGeofence: {
      type: Object as () => Geofences | undefined,
      default: undefined
    }
  },

  data: () => ({
    currentPosition: {
      lat: 0,
      lng: 0
    },
    // bounds: undefined as any,
    colors: {
      mapBox: [
        '#751818',
        '#B100CE',
        '#5474E7',
        '#2599A0',
        '#8CBE22',
        '#F58604',
        '#23DB9D',
        '#7E7B52',
        '#063971',
        '#415458',
        '#8673A1',
        '#DE4C8A'] as string[],
      fms: [
        '#26CFDA',
        '#17CE29',
        '#20603D',
        '#D95030',
        '#89AC76',
        '#955F20',
        '#BFB31A',
        '#49678D',
        '#83235C',
        '#063971'] as string[]
    } as any,
    hexColor: [] as string[],
    isMapClicked: false
  }),

  computed: {
    polylineOptions () {
      const options = {
        mapBox: [] as object[],
        fms: [] as object[]
      }

      for (let i = 0; i < this.polyline?.mapBox?.length; i++) {
        options.mapBox.push({
          color: this.colors.mapBox[i],
          weight: 3,
          opacity: 0.5
        })
      }

      for (let i = 0; i < this.polyline?.fms?.length; i++) {
        options.fms.push({
          color: this.colors.fms[i],
          weight: 3,
          opacity: 0.5
        })
      }
      return options
    }
  },

  watch: {
    isClickable () {
      if (this.isClickable) {
        this.isMapClicked = false
      }
    },
    selectedGeofence (value) {
      this.fitPolyline(value)
    }
  },

  mounted () {
    const map = this.$refs.map as any
    setTimeout(function () {
      window.dispatchEvent(new Event('resize'))
      map?.fitWorld()
    }, 250)

    this.setTrackColors()
  },

  methods: {
    fitPolyline (value: Geofences) {
      if (value) {
        const parsed = JSON.parse(value.coordinates)

        const map = this.$refs.map as any
        map?.mapObject?.flyToBounds(parsed)
      }
    },

    setTrackColors () {
      const trackColors = [] as string[]

      for (let i = 0; i < this.polyline?.fms.length; i++) {
        trackColors.push(this.colors.fms[i])
      }

      this.$emit('on-set-track-colors', trackColors)
    },

    onClickMap (value: any) {
      this.isMapClicked = true
      this.currentPosition = {
        lat: value.latlng.lat,
        lng: value.latlng.lng
      }
      this.$emit('on-click-map', this.currentPosition)
    }
  }
})
</script>

<style>
.text-labels {
  font-size: 16px;
  color: #000;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.7);
  padding: 2px 5px;
  border-radius: 3px;
}
</style>

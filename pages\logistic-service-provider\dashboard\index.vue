
<template>
  <v-container fluid>
    <v-row class="ma-0 mb-4 px-md-2 px-5 d-flex flex-lg-row flex-column-reverse">
      <v-container class="pa-md-2 pa-5" fluid>
        <v-container class="white rounded pa-md-2 pa-5 mb-5" fluid>
          <v-row>
            <v-col class="col-lg-6 col-12">
              <v-container class="white rounded pa-md-10 pa-5" fluid>
                <div class="pa-2 mb-4">
                  <h2>Order Shipment</h2>
                </div>
                <v-tabs v-model="tabs">
                  <v-tab :class="{ 'subtitle-1': true, 'text-capitalize': true }">
                    {{ $t('lspDashboard.active_order') }}
                  </v-tab>
                  <v-tab :class="{ 'subtitle-1': true, 'text-capitalize': true }">
                    Active Geofences
                </v-tab>
              </v-tabs>
                <v-tabs-items v-model="tabs">
                  <v-tab-item>
                    <v-text-field
                      v-model="searchKey"
                      :label="$t('general.search')"
                      append-icon="mdi-magnify"
                      class="mb-5 mt-5"
                      hide-details
                      outlined
                      :loading="isLoadingShipmentLoadmore"
                      @keydown.enter="searchShipments"
                      @keyup.delete="searchShipments"
                    />
                    <div v-if="isLoadingShipment">
                      <div v-for="i in 3" :key="i">
                        <v-skeleton-loader style="border-radius: 0 !important;" type="image" />
                      </div>
                    </div>
                    <div v-else>
                      <div
                        v-if="shipments?.length !== 0"
                        ref="scrollContainer"
                        style="max-height: 45em; overflow-y: auto"
                        @scroll="handleScroll"
                      >
                        <v-list class="pa-0">
                          <v-card class="pa-5 mt-5 rounded v-sheet--outlined">
                            <v-expansion-panels class="pa-0 custom-panel" flat>
                              <v-expansion-panel
                                v-for="(item, i) in shipments"
                                :key="`${i}-${item?.id}`"
                                class="pa-0 ma-5"
                              >
                                <v-expansion-panel-header
                                  class="pa-0"
                                  @click="trackColors = []; selectedOrder = item; shipmentDetailData();"
                                >
                                  <div class="d-flex mx-4">
                                    <v-icon
                                      class="mr-5"
                                      color="black"
                                      size="40"
                                    >
                                      mdi-text-box-multiple
                                    </v-icon>
                                    <div class="d-block">
                                      <p class="grey--text text-capitalize ma-0">
                                        order shipment
                                      </p>
                                      <p class="subtitle-1 ma-0">
                                        {{ item?.order_identity }}
                                      </p>
                                    </div>
                                  </div>
                                </v-expansion-panel-header>
                                <v-expansion-panel-content class="body-1 pa-0">
                                  <v-divider class="mt-4" />
                                  <div
                                    v-for="(subitem, j) in item?.tracks"
                                    :key="`${i}-${j}`"
                                    class="d-flex mr-4 mt-5 align-center"
                                  >
                                    <v-container
                                      v-if="subitem?.driver !== ''"
                                      :class="{ 'clicked': subitem.id === clickedItemId }"
                                      class="pa-2 d-flex align-center"
                                      style="cursor: pointer"
                                      @click="subitem?.vehicle_synced ? clickDriver(subitem, item, j) : null"
                                    >
                                      <v-img
                                        :src="require(`~/assets/images/photo.png`)"
                                        class="mr-5"
                                        max-height="35"
                                        max-width="35"
                                        position="center center"
                                      />
                                      <div class="d-block">
                                        <p class="subtitle-1 text-capitalize ma-0">
                                          {{ subitem?.driver }}
                                        </p>
                                        <p class="ma-0">
                                          <v-badge
                                            :color="subitem?.live_tracking_status === 'ONGOING_MILL' ? 'blue' : 'green'"
                                          />
                                          <span class="ml-4">
                                            {{ subitem?.live_tracking_status }}
                                          </span>
                                        </p>
                                        <p class="grey--text ma-0">
                                          {{ subitem?.plate_number }}
                                        </p>
                                        <p v-if="!subitem?.vehicle_synced" class="red--text caption ma-0">
                                          * This vehicle has no gps
                                        </p>
                                      </div>
                                    </v-container>
                                  </div>
                                </v-expansion-panel-content>
                              </v-expansion-panel>
                            </v-expansion-panels>
                          </v-card>
                        </v-list>
                        <div v-if="shipments?.length === 0" class="d-flex flex-column align-center">
                          <h4 class="mb-5">
                            {{ $t('lspDashboard.no_active_order_title') }}
                          </h4>
                          <p class="ma-0 text-secondary body-1 text-center">
                            {{ $t('lspDashboard.no_active_order_text') }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </v-tab-item>

                  <v-tab-item>
                    <v-text-field
                      :label="$t('general.search')"
                      append-icon="mdi-magnify"
                      class="mb-5 mt-5"
                      hide-details
                      outlined
                      @keydown.enter="searchGeofences"
                      @keyup.delete="searchGeofences"
                    />

                    <v-list v-if="dataGeofences.length > 0" class="pa-0">
                      <v-list-item
                        v-for="(item, i) in dataGeofences"
                        :key="i"
                        @click="setCameraToGeofence(item)"
                      >
                        <v-row>
                          <v-col cols="1">
                            <v-badge :color="item.polygon_color" />
                          </v-col>
                          <v-col cols="11" style="display: flex; justify-items: start">
                            <v-list-item-title v-text="item.name" />
                          </v-col>
                        </v-row>
                      </v-list-item>
                    </v-list>
                  </v-tab-item>
                </v-tabs-items>
              </v-container>
            </v-col>

            <v-col class="pl-lg-4 col-lg-6 col-12">
              <v-row class="pa-2 ma-2 d-flex justify-space-between">
                <v-sheet v-if="isLoadingTruckStatus" class="mb-5 px-4">
                  <v-row>
                    <v-col class="">
                      <div class="d-flex flex-row align-center" style="width: 100%">
                        <v-skeleton-loader height="50" type="image" width="50" />
                        <div class="d-flex flex-column" style="width: 100%">
                          <v-skeleton-loader type="list-item-two-line" width="100%" />
                        </div>
                      </div>
                    </v-col>
                    <v-col>
                      <div class="d-flex flex-row align-center" style="width: 100%">
                        <v-skeleton-loader height="50" type="image" width="50" />
                        <div class="d-flex flex-column" style="width: 100%">
                          <v-skeleton-loader type="list-item-two-line" width="100%" />
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-sheet>
                <v-col v-else>
                  <div class="d-flex">
                    <v-icon color="black" size="40">
                      mdi-truck-fast
                    </v-icon>
                    <p class="pa-2 subtitle-1 align-end ml-1 mt-2">
                      Truck Status
                    </p>
                  </div>
                  <div class="d-flex align-center ml-2">
                    <v-badge :color="'red'" class="mr-3 mt-2" dot />
                    <span> <span class="subtitle-1">{{ truckStatus?.truck_on_shipment }}</span> Truck On Shipment</span>
                  </div>
                  <div class="d-flex align-center ml-2">
                    <v-badge :color="'black'" class="mr-3 mt-2" dot />
                    <span> <span class="subtitle-1">{{ truckStatus?.truck_iddle }}</span> Truck On Idle</span>
                  </div>
                </v-col>
                <v-sheet v-if="isLoading" class="mb-5 px-4">
                  <v-row>
                    <v-col class="">
                      <div class="d-flex flex-row align-center" style="width: 100%">
                        <v-skeleton-loader height="50" type="image" width="50" />
                        <div class="d-flex flex-column" style="width: 100%">
                          <v-skeleton-loader type="list-item-two-line" width="100%" />
                        </div>
                      </div>
                    </v-col>
                    <v-col>
                      <div class="d-flex flex-row align-center" style="width: 100%">
                        <v-skeleton-loader height="50" type="image" width="50" />
                        <div class="d-flex flex-column" style="width: 100%">
                          <v-skeleton-loader type="list-item-two-line" width="100%" />
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-sheet>
                <v-col v-else>
                  <div class="d-flex">
                    <v-icon color="black" size="35">
                      mdi-text-box-multiple
                    </v-icon>
                    <p class="pa-2 subtitle-1 align-end ml-1 mt-2">
                      Order Status
                    </p>
                  </div>
                  <div class="d-flex align-center ml-2">
                    <v-badge :color="'black'" class="mr-3 mt-2" dot />
                    <span> <span class="subtitle-1"> {{ data.request_order || 0 }}</span><span class="ml-1">Order Requested</span></span><span>
                      <v-icon
                        right
                        @click="$store.dispatch('tab/changeTab', 0).then(() => {
                          $router.push(localePath('/logistic-service-provider/order-shipment/create-shipment'))
                        })"
                      >
                        mdi-chevron-right
                      </v-icon></span>
                  </div>
                  <!-- <div class="d-flex align-center ml-2">
                    <v-badge :color="'red'" class="mr-3 mt-2" dot />
                    <span> <span class="subtitle-1"> {{ data?.shipment_reject || 0 }}</span> <span class="ml-1">Order Canceled</span></span><span>
                      <v-icon
                        right
                        @click="$store.dispatch('tab/changeTab', 1).then(() => {
                          $router.push(localePath('/logistic-service-provider/order-shipment/create-shipment'))
                        })"
                      >
                        mdi-chevron-right
                      </v-icon></span>
                  </div> -->
                </v-col>
              </v-row>

              <v-responsive :aspect-ratio="getAspectRatio" style="z-index: 0">
                <div v-if="!isClicked" style="position: relative;">
                  <v-col>
                    <v-list-item-avatar
                      tile
                      size="900"
                      color="grey"
                    />
                    <div
                      class="pa-4 white rounded px-10"
                      style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;"
                    >
                      <div class="pa-6">
                        <div class="d-flex justify-center">
                          <v-img
                            v-show="!$vuetify.breakpoint.xs"
                            :src="require(`~/assets/images/package.svg`)"
                            small
                            class="d-flex"
                            style="max-width: 100px; display: block;"
                          />
                        </div>
                        <p class="pa-1">
                          See your order's real-time location and <br>
                          estimated arrival on the map
                        </p>
                        <v-btn
                          elevation="0"
                          color="primary"
                          class="text-capitalize"
                          x-large
                          @click="handleClick"
                        >
                          Show Map
                        </v-btn>
                      </div>
                    </div>
                  </v-col>
                </div>

                <div v-else-if="isLoadingShipmentDetail || isLoadingShipment">
                  <div v-for="i in 4" :key="i">
                    <v-skeleton-loader style="border-radius: 0 !important;" type="image" />
                  </div>
                </div>
                <custom-map
                  ref="customMap"
                  v-else
                  :geofences="dataGeofences"
                  :latitude="centerLatLng?.lat"
                  :longitude="centerLatLng?.lng"
                  :polyline="polyline"
                  :selected-geofence="selectedGeofence"
                  :zoom="zoomMap"
                  class="custom-map"
                  @on-set-track-colors="trackColors = $event"
                >
                  <template #marker>
                    <div v-if="liveTracking && tabs === 0">
                      <v-rotated-marker
                        v-for="(marker, i) in liveTracking?.vehicle_device.filter(device => device.status !== 'FINISHED')"
                        :key="i"
                        :lat-lng="[marker?.lat, marker?.lng]"
                        :rotation-angle="marker?.course"
                      >
                        <l-icon
                          :icon-anchor="[32, 32]"
                          :icon-size="[64, 64]"
                          :icon-url="require('~/assets/icons/driver.svg')"
                        />

                        <custom-map-popup
                          :driver-avatar="marker.icon_url"
                          :driver-name="marker.driver_name"
                          :estimate-time="marker?.eta"
                          :identity-track="marker?.order_identity"
                          :plate-number="marker?.plate_number"
                          :status="marker.status"
                        />
                      </v-rotated-marker>

                      <l-marker
                        v-for="marker in liveTracking?.driver_device"
                        :key="marker.id"
                        :lat-lng="[marker.latitude, marker.longitude]"
                      >
                        <l-icon
                          :icon-anchor="[16, 16]"
                          :icon-size="[32, 32]"
                        >
                          <v-sheet
                            class="d-flex align-center justify-center pa-1"
                            color="white"
                            height="100%"
                            style="border-radius: 100%; box-shadow: 0 0 5px rgba(0, 0, 0, 0.25)"
                            width="100%"
                          >
                            <v-img
                              v-if="marker.avatar_url"
                              :src="marker.avatar_url"
                              height="100%"
                              style="border-radius: 100%"
                              width="100%"
                            />

                            <v-icon
                              v-else
                              color="primary"
                              size="28"
                            >
                              mdi-account-circle
                            </v-icon>
                          </v-sheet>
                        </l-icon>

                        <l-popup>
                          <div class="d-flex align-center">
                            <v-img
                              :src="marker.avatar_url"
                              class="mr-2"
                              contain
                              width="24px"
                            />
                            <p>{{ marker.driver_name }}</p>
                          </div>
                        </l-popup>
                      </l-marker>
                    </div>

                    <l-marker
                      v-for="(marker, i) in markers"
                      :key="i"
                      :lat-lng="[parseFloat(marker.lat), parseFloat(marker.lng)]"
                    >
                      <l-icon v-if="marker.type === 'PICKUP'" :icon-anchor="[20, 40]">
                        <v-icon
                          color="info"
                          size="40"
                          style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                        >
                          mdi-map-marker
                        </v-icon>
                      </l-icon>

                      <l-icon v-else :icon-anchor="[20, 40]">
                        <v-icon
                          color="success"
                          size="40"
                          style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                        >
                          mdi-map-marker
                        </v-icon>
                      </l-icon>

                      <l-popup>
                        <div>
                          <p
                            :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                            class="subtitle-3 mb-1"
                          >
                            {{ marker.type }}
                          </p>
                          <p class="caption ma-0 text--primary">
                            {{ marker.name }}
                          </p>
                        </div>
                      </l-popup>
                    </l-marker>

                    <l-marker
                      v-for="marker in driverMarker"
                      :key="marker.id"
                      :lat-lng="marker.latLng"
                    >
                      <l-icon :icon-anchor="[20, 40]">
                        <v-icon
                          color="primary"
                          size="40"
                          style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                        >
                          mdi-map-marker-account
                        </v-icon>
                      </l-icon>
                    </l-marker>
                  </template>
                </custom-map>
              </v-responsive>
            </v-col>
          </v-row>
        </v-container>
        <v-row class="mt-3">
          <v-col>
            <v-container class="white rounded pa-md-10 pa-5" fluid>
              <v-menu
                ref="menu"
                :close-on-content-click="false"
                max-width="350"
                offset-y
                transition="slide-y-transition"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    class="ma-2 ml-3 text-capitalize custom-btn"
                    elevation="0"
                    large
                    outlined
                    v-bind="attrs"
                    x-large
                    v-on="on"
                  >
                    <v-icon
                      class="mr-3 custom-icon"
                    >
                      mdi-file-download
                    </v-icon>
                    Export
                  </v-btn>
                </template>

                <v-card
                  class="d-flex justify-start flex-column"
                  elevation="0"
                >
                  <v-container class="pa-0">
                    <div class="mt-2">
                      <h5 class="pa-4">
                        Export
                      </h5>
                      <div class="d-flex flex-column">
                        <!-- <v-btn class="d-flex align-center justify-start" color="transparent" elevation="0">
                          <v-img
                            :src="require('~/assets/icons/pdf.svg')"
                            class="mr-2"
                            max-width="30"
                          />
                          <span>PDF</span>
                        </v-btn> -->
                        <v-btn
                          :loading="isLoadingDownloadExcel"
                          class="d-flex align-center justify-start"
                          color="transparent"
                          elevation="0"
                          @click="downloadExcelReport()"
                        >
                          <v-img
                            :src="require('~/assets/icons/xls.svg')"
                            class="mr-2"
                            max-width="30"
                          />
                          <span>Excell</span>
                        </v-btn>
                      </div>
                    </div>
                  </v-container>
                </v-card>
              </v-menu>
              <div class="py-5">
                <div class="pa-5 ma-2" style="border: 1px solid #CFCCCC">
                  <v-row class="mt-2 ml-3 d-flex justify-space-between">
                    <!-- <v-row class="ml-2 mr-2 mt-2 d-flex justify-space-between" style="width: 100%">
                      <v-col class="d-flex justify-center">
                        <v-img
                          :src="require(`~/assets/icons/truck-incoming-refinery.svg`)"
                          max-width="75"
                          style="border-radius: var(--Radius-Radius-80px, 60px); background: var(--Semantic-Color-Success-Color-Success-01, #EAF6EC);"
                        />
                        <div class="pa-2 d-flex">
                          <h3>{{ truckStatus?.incoming_mill }}</h3><span class="pa-1"><p>Truck Dispatch</p></span>
                        </div>
                      </v-col>
                      <v-divider vertical />
                      <v-col class="d-flex justify-center">
                        <v-img
                          :src="require(`~/assets/icons/truck-in-refinery.svg`)"
                          max-width="75"
                          style="border-radius: var(--Radius-Radius-40px, 40px); background: var(--Semantic-Color-Information-Color-Information-01, #E6F4F8);"
                        />
                        <div class="pa-2 d-flex">
                          <h3>{{ truckStatus?.incoming_refinery }}</h3><span class="pa-1"><p>Truck On Progress</p></span>
                        </div>
                      </v-col>
                      <v-divider vertical />
                      <v-col class="d-flex justify-center">
                        <v-img
                          :src="require(`~/assets/icons/truck-exit.svg`)"
                          max-width="75"
                          style="border-radius: var(--Radius-Radius-40px, 40px); background: var(--Semantic-Color-Danger-Color-Danger-01, #FDEBEB);"
                        />
                        <div class="pa-2 d-flex">
                          <h3>{{ truckStatus?.exit_refinery_today }}</h3><span class="pa-1"><p>Truck Finished</p></span>
                        </div>
                      </v-col>
                    </v-row> -->
                    <!-- <v-col cols="12">
                      <v-divider />
                    </v-col> -->

                    <v-row>
                      <v-row class="pa-3 ma-3 d-flex">
                        <div class="mt-2">
                          <h3>Incoming Truck</h3>
                        </div>
                        <v-spacer />
                        <v-col cols="4">
                          <!--                        <v-select-->
                          <!--                          v-model="filterDays"-->
                          <!--                          small-->
                          <!--                          outlined-->
                          <!--                          :items="[-->
                          <!--                            { text: 'Today', value: 0 },-->
                          <!--                            { text: 'Tomorrow', value: 1 },-->
                          <!--                            { text: 'Next 3 Days', value: 2 },-->
                          <!--                            { text: 'Next 4 Days', value: 3 },-->
                          <!--                            { text: 'Next 5 Days', value: 4 }-->
                          <!--                          ]"-->
                          <!--                          item-text="text"-->
                          <!--                          item-value="value"-->
                          <!--                        />-->
                        </v-col>

                        <div class="pa-2 d-flex">
                          <v-btn-toggle
                            v-model="btnMillRefinery"
                            color="primary"
                            mandatory
                          >
                            <v-btn class="pa-2 mr-4 elevation-0 rounded-xl" outlined @click="options.page = 1; debouncedGetIncomingTruckMill()">
                              Mill
                            </v-btn>
                            <v-btn class="pa-2 elevation-0 rounded-xl" outlined @click="options.page = 1; debouncedGetIncomingTruckRefinery()">
                              Refinery
                            </v-btn>
                          </v-btn-toggle>
                        </div>
                      </v-row>
                    </v-row>
                  </v-row>
                  <v-row class="ma-2 d-flex justify-space-between">
                    <div class="pa-3 ma-1 d-flex">
                      <div class="col-4">
                        <v-menu
                          ref="menu"
                          v-model="menuDateRangeStart"
                          :close-on-content-click="false"
                          max-width="350"
                          offset-y
                          transition="slide-y-transition"
                        >
                          <template #activator="{ on, attrs }">
                            <v-text-field
                              v-model="dateRangeStart"
                              append-icon="mdi-calendar-range"
                              clearable
                              readonly
                              label="Select Date"
                              outlined
                              v-bind="attrs"
                              v-on="on"
                            >
                              Select Date Range
                            </v-text-field>
                          </template>
                          <v-date-picker
                            v-model="dateRangeStart"
                            :min="new Date().toISOString()"
                            color="primary"
                            no-title
                          />
                        </v-menu>
                      </div>
                      <div class="col-4">
                        <v-menu
                          ref="menu"
                          v-model="menuDateRangeEnd"
                          :close-on-content-click="false"
                          max-width="350"
                          offset-y
                          transition="slide-y-transition"
                        >
                          <template #activator="{ on, attrs }">
                            <v-text-field
                              v-model="dateRangeEnd"
                              append-icon="mdi-calendar-range"
                              clearable
                              readonly
                              label="Select Date"
                              outlined
                              v-bind="attrs"
                              v-on="on"
                            >
                              Select Date Range
                            </v-text-field>
                          </template>
                          <v-date-picker
                            v-model="dateRangeEnd"
                            :min="new Date().toISOString()"
                            color="primary"
                            no-title
                          />
                        </v-menu>
                      </div>
                      <div class="ma-3">
                        <v-btn
                          block
                          elevation="0"
                          color="primary"
                          class="text-capitalize"
                          height="55"
                          :loading="isLoadingIncomingTruck"
                          :disabled="dateRangeStart === null && dateRangeEnd === null"
                          @click="applyFilterIncomingTruck"
                        >
                          Apply
                        </v-btn>
                      </div>
                    </div>

                    <v-col class="d-flex justify-end">
                      <v-menu
                        ref="menu"
                        :close-on-content-click="false"
                        max-width="550"
                        offset-y
                        transition="slide-y-transition"
                      >
                        <template #activator="{ on, attrs }">
                          <v-btn
                            class="ma-2 ml-5 text-capitalize custom-btn"
                            elevation="0"
                            large
                            outlined
                            v-bind="attrs"
                            x-large
                            @click="handleFilter"
                            v-on="on"
                          >
                            <v-icon
                              class="mr-3 custom-icon"
                            >
                              mdi-filter
                            </v-icon>
                            Filter
                          </v-btn>
                        </template>

                        <v-card
                          class="pa-5 d-flex justify-start flex-column"
                          elevation="0"
                        >
                          <v-container class="pa-0">
                            <p class="subtitle-1">
                              Filter
                            </p>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-model="selectedPo"
                                :items="shipmentCompanyData.items.map((e) => {
                                  return {
                                    text: e.name,
                                    value: e.id
                                  }
                                })"
                                :loading="isLoadingCustomers"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                label="Select PO"
                                outlined
                                small-chips
                              />
                            </v-row>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-if="btnMillRefinery === 0"
                                v-model="selectedMill"
                                :items="dataLocation.filter(e => e?.category === 'MILL' || e?.category === null )"
                                :loading="isLoadingLocation"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                item-text="identity"
                                item-value="id"
                                label="Select Mill"
                                return-object
                                multiple
                                outlined
                                small-chips
                                @input="handleMillChange"
                                :menu-props="{
                                      selection: 'multiple',
                                      'closeOnClick': false,
                                      'closeOnContentClick': false,
                                      'offsetY': true
                                    }"
                                  >
                                  <template #item="{ item, attrs, on }">
                                    <v-list-item v-bind="attrs" v-on="on">
                                      <template #default>
                                        <v-list-item-action>
                                          <v-checkbox :value="attrs['aria-selected'] === 'true'" />
                                        </v-list-item-action>
                                        <v-list-item-content>
                                          <v-list-item-title>{{ item?.name }}</v-list-item-title>
                                          <v-list-item-subtitle class="text-secondary">{{ item?.shipment_company?.name }}</v-list-item-subtitle>
                                        </v-list-item-content>
                                      </template>
                                    </v-list-item>
                                  </template>
                                </v-autocomplete>
                            </v-row>
                            <div
                              v-if="btnMillRefinery === 1"
                            >
                              <v-row class="ma-0 d-flex mt-3">
                                <v-autocomplete
                                  v-model="selectedMill"
                                  :items="dataLocation.filter(e => e?.category === 'MILL' || e?.category === null)"
                                  :loading="isLoadingLocation"
                                  class="pa-1"
                                  deletable-chips
                                  hide-details
                                  item-text="identity"
                                  item-value="id"
                                  return-object
                                  label="Select Mill"
                                  multiple
                                  outlined
                                  small-chips
                                  @input="handleMillChange"
                                  :menu-props="{
                                      selection: 'multiple',
                                      'closeOnClick': false,
                                      'closeOnContentClick': false,
                                      'offsetY': true
                                    }"
                                  >
                                  <template #item="{ item, attrs, on }">
                                    <v-list-item v-bind="attrs" v-on="on">
                                      <template #default>
                                        <v-list-item-action>
                                          <v-checkbox :value="attrs['aria-selected'] === 'true'" />
                                        </v-list-item-action>
                                        <v-list-item-content>
                                          <v-list-item-title>{{ item?.name }}</v-list-item-title>
                                          <v-list-item-subtitle class="text-secondary">{{ item?.shipment_company?.name }}</v-list-item-subtitle>
                                        </v-list-item-content>
                                      </template>
                                    </v-list-item>
                                  </template>
                                </v-autocomplete>
                                <v-autocomplete
                                  v-model="selectedRefinery"
                                  :items="dataLocation.filter(e => e?.category === 'REFINERY')"
                                  :loading="isLoadingLocation"
                                  class="pa-1 mt-2"
                                  deletable-chips
                                  hide-details
                                  item-text="identity"
                                  item-value="id"
                                  return-object
                                  label="Select Refinery"
                                  multiple
                                  outlined
                                  small-chips
                                  @input="handleMillChange"
                                  :menu-props="{
                                      selection: 'multiple',
                                      'closeOnClick': false,
                                      'closeOnContentClick': false,
                                      'offsetY': true
                                    }"
                                  >
                                  <template #item="{ item, attrs, on }">
                                    <v-list-item v-bind="attrs" v-on="on">
                                      <template #default>
                                        <v-list-item-action>
                                          <v-checkbox :value="attrs['aria-selected'] === 'true'" />
                                        </v-list-item-action>
                                        <v-list-item-content>
                                          <v-list-item-title>{{ item?.name }}</v-list-item-title>
                                          <v-list-item-subtitle class="text-secondary">{{ item?.shipment_company?.name }}</v-list-item-subtitle>
                                        </v-list-item-content>
                                      </template>
                                    </v-list-item>
                                  </template>
                                </v-autocomplete>
                              </v-row>
                            </div>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-model="selectedVendor"
                                :items="dataVendor.items"
                                :loading="isLoadingVendor"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                item-text="name"
                                item-value="id"
                                label="Select Transporter"
                                multiple
                                outlined
                                small-chips
                              />
                            </v-row>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-model="selectedDriver"
                                :items="drivers"
                                :loading="isLoadingDrivers"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                item-text="user.name"
                                item-value="id"
                                label="Select Driver"
                                multiple
                                outlined
                                small-chips
                              />
                            </v-row>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-model="selectedPlateNumber"
                                :items="vehicleDetail.map(d => {
                                  return { text: d.plate_number, value: d.id }
                                })"
                                :loading="isLoadingVehicleDetail"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                item-text="text"
                                item-value="value"
                                label="Select Plate Number"
                                multiple
                                outlined
                                small-chips
                              />
                            </v-row>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-autocomplete
                                v-model="selectedProductName"
                                :items="dataProduct?.items.map(value => {
                                  return {
                                    text: value.name,
                                    value: value.id
                                  }
                                })"
                                :loading="isLoadingProduct"
                                class="pa-1"
                                deletable-chips
                                hide-details
                                item-text="text"
                                item-value="value"
                                label="Select Comodity"
                                multiple
                                outlined
                                small-chips
                              />
                            </v-row>
                            <v-row class="ma-0 d-flex mt-3">
                              <v-btn
                                block
                                class="text-capitalize"
                                color="primary"
                                elevation="0"
                                x-large
                                @click="applyFilterIncomingTruck"
                              >
                                Apply Filter
                              </v-btn>
                            </v-row>
                            <div class="pa-1">
                              <v-btn
                                class="mt-2 pa-0 text-capitalize text-secondary"
                                color="transparent"
                                elevation="0"
                                @click="resetFilter"
                              >
                                <p class="text-secondary">
                                  Reset Filter
                                </p>
                              </v-btn>
                            </div>
                          </v-container>
                        </v-card>
                      </v-menu>
                    </v-col>
                    <v-col class="mt-2" cols="3">
                      <div>
                        <v-text-field
                          v-model="searchKeyMillRefinery"
                          :label="$t('general.search')"
                          append-icon="mdi-magnify"
                          background-color="white"
                          flat
                          height="52"
                          hide-details
                          outlined
                          single-line
                          solo
                          width="100"
                        />
                      </div>
                    </v-col>
                  </v-row>

                  <div class="mt-5">
                    <v-data-table
                      v-if="btnMillRefinery === 0"
                      :headers="tableIncomingTruck"
                      :items="incomingTruck?.itemIncomingTruck ?? []"
                      :loading="isLoadingIncomingTruck"
                      :options.sync="options"
                      :server-items-length="incomingTruck.totalData"
                      :items-per-page="options.itemsPerPage"
                      hide-default-footer
                    >
                      <template #item.nearBy="{ index }">
                        <p>
                          {{ (incomingTruck?.page - 1) * isRowData + index + 1 }}
                        </p>
                      </template>
                      <template #item.order="{ item }">
                        <p>
                          {{ item.order }}
                        </p>
                      </template>
                      <template #item.vendor="{ item }">
                        <p>
                          {{ item.vendor }}
                        </p>
                      </template>
                      <template #item.driver="{ item }">
                        <p>
                          {{ item.driver }}
                        </p>
                      </template>
                      <template #item.plate_number="{ item }">
                        <p>
                          {{ item.plate_number }}
                        </p>
                      </template>
                      <template #item.product_name="{ item }">
                        <p>
                          {{ item.product_name }}
                        </p>
                      </template>
                      <template #item.estimated_time_arrival="{ item }">
                        <p>
                          {{ item.estimated_time_arrival }}
                        </p>
                      </template>
                      <template #item.mill="{ item }">
                        {{ item.mill }}
                      </template>
                    </v-data-table>
                    <v-data-table
                      v-if="btnMillRefinery === 1"
                      :headers="tableIncomingTruckRefinery"
                      :items="incomingTruck?.itemIncomingTruck ?? []"
                      :items-per-page="options.itemsPerPage"
                      hide-default-footer
                      :loading="isLoadingIncomingTruck"
                      :options.sync="options"
                      :server-items-length="incomingTruck.totalData"
                    >
                      <template #item.nearBy="{ index }">
                        <p>
                          {{ (incomingTruck?.page - 1) * isRowData + index + 1 }}
                        </p>
                      </template>
                      <template #item.order="{ item }">
                        <p>
                          {{ item.order }}
                        </p>
                      </template>
                      <template #item.vendor="{ item }">
                        <p>
                          {{ item.vendor }}
                        </p>
                      </template>
                      <template #item.driver="{ item }">
                        <p>
                          {{ item.driver }}
                        </p>
                      </template>
                      <template #item.plate_number="{ item }">
                        <p>
                          {{ item.plate_number }}
                        </p>
                      </template>
                      <template #item.product_name="{ item }">
                        <p>
                          {{ item.product_name }}
                        </p>
                      </template>
                      <template #item.estimated_time_arrival="{ item }">
                        <p>
                          {{ item.estimated_time_arrival }}
                        </p>
                      </template>
                      <template #item.mill="{ item }">
                        {{ item.mill }}
                      </template>
                      <template #item.refinery="{ item }">
                        {{ item.refinery }}
                      </template>
                      <template #item.quantity_in_mill="{ item }">
                        {{ formatNumber(parseFloat(item?.quantity_in_mill)) }} KG
                      </template>
                      <template #item.ffa_quality_in_mill="{ item }">
                        {{ item.ffa_quality_in_mill }}
                      </template>
                      <template #item.mmi_quality_in_mill="{ item }">
                        {{ item.mmi_quality_in_mill }}
                      </template>
                    </v-data-table>
                  </div>
                </div>

                <div class="ma-2 d-flex justify-space-between">
                  <div class="pa-0 d-flex align-center col-6">
                    <v-col
                      class="pa-0"
                      cols="2"
                    >
                      <v-select
                        v-model="isRowData"
                        solo
                        class="pa-0"
                        :items="[10, 25, 50, 100]"
                        label="10"
                        @change="applyFilterIncomingTruck($event)"
                      />
                    </v-col>
                    <p class="ml-4 text-secondary">
                      {{ incomingTruck.itemIncomingTruck?.length }} From {{ incomingTruck?.totalData }} records
                    </p>
                  </div>
                  <pagination-component
                    :page="incomingTruck?.page"
                    :total-page="incomingTruck?.totalPage"
                    page-id="page"
                    class="float-end"
                    @on-change-page="applyFilterIncomingTruck({
                      page: $event
                    }); options.page = $event"
                  />
                </div>
              </div>
            </v-container>
          </v-col>
        </v-row>
      </v-container>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '@/components/shipping-company/CustomMap.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'
import CustomMapPopup from '~/components/CustomMapPopup.vue'
import { DashboardLsa, Geofences } from '~/types/dashboard'
import { Destination, LiveTracking, Route, Shipment, ShipmentActiveOrder, Track, TrackActiveOrder } from '~/types/shipment'
import {
  defaultLat,
  defaultLng,
  generateCenterLatLng,
  generateDriverMarker,
  generatePolyline,
  generatePolylineActive,
  zoom,
  formatNumber
} from '~/utils/functions'
import { ShippingCompany, Vendor } from '~/types/user'
import { PickupDropOffLocationPoint } from '~/types/product'
import { Driver } from '~/types/driver'
import { VehicleDetail } from '~/types/vehicle'
import _ from 'lodash'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'DashboardLogisticServiceProviderPage',

  components: {
    CustomMap,
    CustomMapPopup,
    PaginationComponent
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  beforeRouteLeave (to, from, next) {
    if (this.activeInterval) {
      clearInterval(this.activeInterval)
    }
    this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
    next()
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    isRowData: 10,
    isClicked: false,
    poType: null,
    tabMapStates: [false, false],
    menuDateRangeStart: false as boolean,
    menuDateRangeEnd: false as boolean,
    dateRange: new Date().toISOString().substr(0, 7),
    dateRangeStart: null,
    dateRangeEnd: null,
    dateRangeReport: [],
    searchKeyMillRefinery: '',
    btnMillRefinery: null,
    filterDays: 0,
    searchKey: '',
    selectedPo: null as any,
    selectedShipmentId: null as any,
    selectedMill: null as any,
    selectedRefinery: null as any,
    selectedVendor: null,
    selectedDriver: null,
    selectedPlateNumber: [],
    selectedProductName: null,
    isShow: false,
    selectedTrackId: '',
    selectedRoutes: [] as Destination[],
    polyline: null as Polyline | null,
    markers: [] as { lat: number, lng: number, type: string, name: string }[],
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4,
    panel: 0,
    trackColors: [] as string[],
    activeInterval: null as any,
    tabs: 0,
    clickedItemId: null,
    selectedOrder: null as ShipmentActiveOrder | null,
    selectedGeofence: undefined as Geofences | undefined,
    options: {
      sortBy: [],
      sortDesc: [],
      page: 1,
      itemsPerPage: 10
    },
    tableIncomingTruck: [
      { text: 'Nearby', value: 'nearBy', sortable: false },
      { text: 'Order Number', value: 'order' },
      { text: 'Transporter', value: 'vendor' },
      { text: 'Driver', value: 'driver' },
      { text: 'Plate Number', value: 'plate_number' },
      { text: 'Product Name', value: 'product_name' },
      { text: 'Estimated Time Arrival', value: 'estimated_time_arrival' },
      { text: 'Mill', value: 'mill' }
    ],
    tableIncomingTruckRefinery: [
      { text: 'Nearby', value: 'nearBy', sortable: false },
      { text: 'Order Number', value: 'order' },
      { text: 'Transporter', value: 'vendor' },
      { text: 'Driver', value: 'driver' },
      { text: 'Plate Number', value: 'plate_number' },
      { text: 'Product Name', value: 'product_name' },
      { text: 'Estimated Time Arrival', value: 'estimated_time_arrival' },
      { text: 'Mill', value: 'mill' },
      { text: 'Refinery', value: 'refinery' },
      { text: 'Quantity in Mill', value: 'quantity_in_mill', width: 150 },
      { text: 'Ffa Quantity in Mill', value: 'ffa_quality_in_mill' },
      { text: 'M&I Quantity in Mill', value: 'mmi_quality_in_mill' }
    ],
    pageActiveOrder: 1,
    debouncedGetIncomingTruckMill: null as any,
    debouncedGetIncomingTruckRefinery: null as any
  }),

  computed: {
    getAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.xs) {
        aspectRatio = 16 / 9
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    data () {
      return this.$store.getters['dashboard/dataLsa'] as DashboardLsa || null
    },
    isLoading () {
      return this.$store.getters['dashboard/isLoadingLogisticServiceProvider']
    },
    isLoadingShipment () {
      return this.$store.getters['shipment/isLoading']
    },

    shipments () {
      return this.$store.getters['shipment/dataShipmentActiveOrders'].items
    },

    shipmentDetail (): Shipment {
      return this.$store.getters['shipment/detailShipment'] as Shipment
    },

    isLoadingShipmentDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    },

    liveTracking (): LiveTracking {
      return this.$store.getters['dashboard/liveTracking'] as LiveTracking
    },
    driverMarker (): object[] {
      return generateDriverMarker(this.polyline?.fms!)
    },
    tabIndex (): number {
      return this.$store.getters['tab/index']
    },
    isLoadingTruckStatus () {
      return this.$store.getters['dashboard/isLoadingTruckStatus']
    },
    truckStatus () {
      return this.$store.getters['dashboard/truckStatus']
    },
    isLoadingIncomingTruck () {
      return this.$store.getters['dashboard/isLoadingIncomingTruck']
    },
    incomingTruck () {
      return this.$store.getters['dashboard/dataIncomingTruckStatus']
    },
    dataGeofences (): Geofences[] {
      return this.$store.getters['dashboard/geofences']
    },
    dataVendor (): { items: Vendor[], page: number, totalPage: number } {
      return this.$store.getters['vendor/data']
    },
    isLoadingVendor (): boolean {
      return this.$store.getters['vendor/isLoading']
    },
    drivers () {
      return this.$store.getters['vehicle/drivers/dataFilter'].items as Driver[]
    },
    isLoadingDrivers (): boolean {
      return this.$store.getters['vehicle/drivers/isLoading']
    },
    dataLocation (): any[] {
      return this.$store.getters['pick-up-drop-off-location-point/data'].items
    },

    isLoadingLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },
    listShipmentCompany (): {items: ShippingCompany[]} {
      return this.$store.getters['shipping-company/data']
    },
    dataProduct () {
      return this.$store.getters['shipping-company/product/data']
    },
    isLoadingProduct () {
      return this.$store.getters['shipping-company/product/isLoading']
    },
    vehicleDetail () {
      return this.$store.getters['vehicle/details/data'].items as VehicleDetail[]
    },
    isLoadingVehicleDetail () {
      return this.$store.getters['vehicle/details/isLoading'].items as VehicleDetail[]
    },
    isLoadingDownloadExcel () {
      return this.$store.getters['dashboard/isLoadingExportExcel']
    },

    isLoadingShipmentLoadmore () {
      return this.$store.getters['shipment/isLoadingShipmentLoadmore']
    },

    shipmentCompanyData (): {items: ShippingCompany[], totalPage: number, page: number} {
      return this.$store.getters['shipping-company/data']
    },

    isLoadingCustomers (): Boolean {
      return this.$store.getters['shipping-company/isLoading']
    },
  },

  watch: {
    dateRangeReport: {
      handler () {
        try {
          if (this.dateRangeReport?.length === 2) {
            const start = new Date(this.dateRangeReport?.[0])
            const end = new Date(this.dateRangeReport?.[1])
            const timeDifference = end?.getTime() - start?.getTime()
            this.filterDays = timeDifference / (1000 * 3600 * 24)
          }
        } catch (e) {
          console.error(e)
        }
      }
    },
    selectedPo () {
      this.getPickupDropoffLocation()
    },
    // shipmentDetail () {
    //   if (this.shipmentDetail && this.shipmentDetail.tracks.length > 0) {
    //     this.selectedTrackId = this.shipmentDetail.tracks[0].id
    //     this.selectedRoutes = this.shipmentDetail.tracks[0].routes

    //     this.setupDirectionSingle(0)

    //     const polylineTrack = []

    //     polylineTrack.push(this.shipmentDetail.tracks[0])

    //     this.polyline = generatePolyline(polylineTrack)

    //     // this.polyline = generatePolyline(this.shipmentDetail.tracks)
    //     this.centerLatLng = generateCenterLatLng(this.markers)
    //     this.zoomMap = zoom(this.markers)

    //     this.$store.dispatch('dashboard/getLiveTracking', {
    //       shipmentId: this.shipmentDetail.id
    //     })

    //     this.recurringTracking()
    //   }
    // },
    // shipments () {
    //   if (this.shipments?.length > 0) {
    //     this.getDetailShipment(this.shipments[0].id)
    //   }
    // },
    // tabIndex: {
    //   handler() {
    //   this.tabs = this.tabIndex
    //     this.isClicked = this.tabMapStates[this.tabIndex] || false
    //     if (this.tabs === 1 && !this.isLoading) {
    //       this.getData()
    //     }
    //   },
    //   immediate: true
    // },
    btnMillRefinery: {
      handler (val) {
        if (val === 'mill') {
          this.debouncedGetIncomingTruckMill()
        } else if (val === 'refinery') {
          this.debouncedGetIncomingTruckRefinery()
        }
      },
      immediate: true
    },
    filterDays: {
      handler () {
        if (this.btnMillRefinery === 0) {
          this.debouncedGetIncomingTruckMill()
        } else if (this.btnMillRefinery === 1) {
          this.debouncedGetIncomingTruckRefinery()
        }
      },
      immediate: true
    },
    searchKeyMillRefinery: {
      handler () {
        if (this.btnMillRefinery === 0) {
          this.debouncedGetIncomingTruckMill()
        } else if (this.btnMillRefinery === 1) {
          this.debouncedGetIncomingTruckRefinery()
        }
      },
      immediate: true
    },

    selectedShipmentId (newType) {
      if (newType) {
        this.getCustomers()
      }
    },

    options: {
      handler (newOptions, oldOptions) {
        // Check if sorting options (sortBy or sortDesc) have changed
        const sortByChanged = JSON.stringify(newOptions.sortBy) !== JSON.stringify(oldOptions?.sortBy);
        const sortDescChanged = JSON.stringify(newOptions.sortOrder) !== JSON.stringify(oldOptions?.sortOrder);
        // console.log(sortByChanged, sortDescChanged, newOptions.page, oldOptions?.page)

        // If sorting changed, reset the page to 1
        if ((sortByChanged || sortDescChanged) && newOptions.page !== oldOptions?.page ||  newOptions.page === 1) {
          this.options.page = oldOptions?.page;
        }

        if (this.btnMillRefinery === 0) {
            this.debouncedGetIncomingTruckMill()
        } else if (this.btnMillRefinery === 1) {
            this.debouncedGetIncomingTruckRefinery()
        }

       
      },
      deep: true
    }
  },

  created () {
    this.debouncedGetIncomingTruckMill = _.debounce(this.getIncomingTruckMill, 500)
    this.debouncedGetIncomingTruckRefinery = _.debounce(this.getIncomingTruckRefinery, 500)
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', `${this.$t('lspDashboard.page_title')}`)
    this.getData()
    this.getShipments({
      filterColumns: 'status',
      filterKeys: 'DISPATCH|ON_PROGRESS'
    })
    this.getTruckStatus()
    this.debouncedGetIncomingTruckMill!()
    this.getVendors()
    // this.getDriverData({})
    this.getProductName()
    // this.getVehicleDetailData()
    this.getPickupDropoffLocation()
    this.$store.dispatch('dashboard/getGeofences')
    this.getCustomers()
  },

  beforeDestroy () {
    clearInterval(this.activeInterval)

    this.resetIncomingTruckData()
  },

  methods: {
    formatNumber,
    handleClick() {
      this.isClicked = !this.isClicked
      this.tabMapStates[this.tabIndex] = this.isClicked
    },
    handleFilter() {
      this.getVehicleDetailData()
      this.getDriverData({})
    },
    handleMillChange (selectedIds: any) {
      const selectedData = selectedIds.map((id: any) => {
        const location = this.dataLocation.find(location => location.id === id)

        if (location) {
          const locationShipmentCompanyId = location.shipment_company_id
          const pickupDropOffid = location.id
          const sameLocationShipmentCompanyId = location.same_location.length > 0
            ? location.same_location[0].shipment_company_id
            : null
          const samePickupDropoffId = location.same_location.length > 0
            ? location.same_location[0].id
            : null

          return {
            shipment_company_ids: {
              location: locationShipmentCompanyId,
              same_location: sameLocationShipmentCompanyId,
              id: pickupDropOffid,
              same_id: samePickupDropoffId
            }
          }
        }

        return null
      })

      this.selectedShipmentId = selectedData.filter((data: null) => data !== null)
    },

    defaultLat () {
      return defaultLat
    },
    defaultLng () {
      return defaultLng
    },

    handleScroll () {
      const container = this.$refs.scrollContainer
      if ((container as any).scrollTop + (container as any).clientHeight >= (container as any).scrollHeight) {
        this.onScrollToBottom()
      }
    },

    onScrollToBottom () {
      this.pageActiveOrder += 1
      this.$store.dispatch('shipment/loadMoreActiveOrders', {
        searchColumns: '',
        searchKey: this.searchKey,
        filterColumns: 'status',
        filterKeys: 'DISPATCH|ON_PROGRESS',
        page: this.pageActiveOrder,
        perPage: 10
      })
    },

    searchGeofences (e: any) {
      this.$store.commit('dashboard/SET_FILTER_GEOFENCE', e.target.value)
    },

    setCameraToGeofence (item: Geofences) {
      this.selectedGeofence = item
    },

    getData () {
      this.$store.dispatch('dashboard/getItemLsa')
    },

    async getShipments ({
      searchColumns = '',
      searchKey = '',
      filterColumns = '',
      filterKeys = '',
      page = 1,
      perPage = 10
    }) {
      try {
        await this.$store.dispatch('shipment/getItemActiveOrders', {
          searchColumns,
          searchKey,
          filterColumns,
          filterKeys,
          page,
          perPage
        })
      } catch (error) {
        console.error(error)
      }
    },

    setupDirections () {
      const markers = [] as { lat: number, lng: number, type: string, name: string }[]

      this.selectedOrder?.tracks.forEach((track) => {
        track.destinations?.forEach((route) => {
          const lat = parseFloat(route.lat)
          const lng = parseFloat(route.lng)
          const type = route.type
          const name = route.loc_name

          markers.push({
            lat,
            lng,
            type,
            name
          })
        })

        this.markers = markers
      })
    },

    setupDirectionSingle (index: number) {
      const markers = [] as { lat: number, lng: number, type: string, name: string }[]

      this.selectedOrder?.tracks[index].destinations?.forEach((route) => {
        const lat = parseFloat(route.lat)
        const lng = parseFloat(route.lng)
        const type = route.type
        const name = route.loc_name

        markers.push({
          lat,
          lng,
          type,
          name
        })
      })

      this.markers = markers
    },

    getDetailShipment (id: string) {
      this.$store.dispatch('shipment/getItemDetailDashboard', {
        id
      }).then(() => {
        this.shipmentDetailData()
      })
    },

    searchShipments () {
      this.getShipments({ searchColumns: 'orders.identity' as any, searchKey: this.searchKey })
    },

    recurringTracking () {
      if (this.activeInterval) {
        clearInterval(this.activeInterval)
      }
      this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
      this.activeInterval = setInterval(() => {
        this.$store.dispatch('dashboard/getLiveTracking', {
          shipmentId: this.selectedOrder?.id ?? ''
        })
      }, 10000)
    },

    getTruckStatus () {
      this.$store.dispatch('dashboard/getTruckStatus')
    },

    getIncomingTruckMill () {
      const startDate = this.dateRangeStart
      const endDate = this.dateRangeEnd

      const refineryIds = this.selectedRefinery?.flatMap((refinery: { id: any; same_location: any[] }) => {
        const ids = [refinery.id]
        if (refinery.same_location && refinery.same_location.length > 0) {
          ids.push(...refinery.same_location.map(location => location.id))
        }
        return ids
      })

      let millIds = []
      if (this.selectedMill) {
        millIds = this.selectedMill?.flatMap((mill: { id: any; same_location: any[] }) => {
          const ids = [mill.id]
          if (mill.same_location && mill.same_location.length > 0) {
            ids.push(...mill.same_location.map((location: { id: any }) => location.id))
          }
          return ids
        })
      }

      const productOwner = typeof this.selectedPo === 'string' ? [this.selectedPo] : this.selectedPo

      const { sortBy, sortDesc, page, itemsPerPage } = this.options
      const sort = sortBy[0] || ''
      const desc = sortDesc[0] ? 'desc' : 'asc'

      if (startDate) {
        this.$store.dispatch('dashboard/getIncomingTruck', {
          incoming_type: 'MILL',
          start_date: startDate,
          end_date: endDate,
          search: this.searchKeyMillRefinery,
          product: this.selectedProductName,
          plateNumber: this.selectedPlateNumber,
          refinery: refineryIds,
          mill: millIds,
          driverId: this.selectedDriver,
          vendorId: this.selectedVendor,
          productOwner,
          page,
          entries: this.isRowData,
          sortBy: sort,
          sortOrder: desc
        })
      }

      const query = { ...this.$route.query }
      delete query.page
      this.$router.push({
        path: this.$route?.path,
        query
      })?.catch(() => {})
    },

    getIncomingTruckRefinery () {
      const startDate = this.dateRangeStart
      const endDate = this.dateRangeEnd

      const refineryIds = this.selectedRefinery?.flatMap((refinery: { id: any; same_location: any[] }) => {
        const ids = [refinery.id]
        if (refinery.same_location && refinery.same_location.length > 0) {
          ids.push(...refinery.same_location.map(location => location.id))
        }
        return ids
      })

      let millIds = []
      if (this.selectedMill) {
        millIds = this.selectedMill?.flatMap((mill: { id: any; same_location: any[] }) => {
          const ids = [mill.id]
          if (mill.same_location && mill.same_location.length > 0) {
            ids.push(...mill.same_location.map((location: { id: any }) => location.id))
          }
          return ids
        })
      }

      const productOwner = typeof this.selectedPo === 'string' ? [this.selectedPo] : this.selectedPo

      const { sortBy, sortDesc, page, itemsPerPage } = this.options
      const sort = sortBy[0] || ''
      const desc = sortDesc[0] ? 'desc' : 'asc'

      if (startDate) {
        this.$store.dispatch('dashboard/getIncomingTruck', {
          incoming_type: 'REFINERY',
          start_date: startDate,
          end_date: endDate,
          search: this.searchKeyMillRefinery,
          product: this.selectedProductName,
          plateNumber: this.selectedPlateNumber,
          refinery: refineryIds,
          mill: millIds,
          driverId: this.selectedDriver,
          vendorId: this.selectedVendor,
          productOwner,
          page,
          entries: this.isRowData,
          sortBy: sort,
          sortOrder: desc
        })
      }

      const query = { ...this.$route.query }
      delete query.page
      this.$router.push({
        path: this.$route?.path,
        query
      })?.catch(() => {})
    },

    applyFilterIncomingTruck (page: any) {
      this.resetIncomingTruckData()
      
      let incomingType = ''
      if (this.btnMillRefinery === 0) {
        incomingType = 'MILL'
      } else if (this.btnMillRefinery === 1) {
        incomingType = 'REFINERY'
      }
      const startDate = this.dateRangeStart
      const endDate = this.dateRangeEnd

      const refineryIds = this.selectedRefinery?.flatMap((refinery: { id: any; same_location: any[] }) => {
        const ids = [refinery.id]
        if (refinery.same_location && refinery.same_location.length > 0) {
          ids.push(...refinery.same_location.map(location => location.id))
        }
        return ids
      })

      let millIds = []
      if (this.selectedMill) {
        millIds = this.selectedMill?.flatMap((mill: { id: any; same_location: any[] }) => {
          const ids = [mill.id]
          if (mill.same_location && mill.same_location.length > 0) {
            ids.push(...mill.same_location.map((location: { id: any }) => location.id))
          }
          return ids
        })
      }

      const productOwner = typeof this.selectedPo === 'string' ? [this.selectedPo] : this.selectedPo

      const pageNumber = !isNaN(parseInt(page.page)) ? parseInt(page.page) : 1

      const { sortBy, sortDesc } = this.options
      const sort = sortBy[0] || ''
      const desc = sortDesc[0] ? 'desc' : 'asc'

      this.$store.dispatch('dashboard/getIncomingTruck', {
        incoming_type: incomingType,
        start_date: startDate,
        end_date: endDate,
        search: this.searchKeyMillRefinery,
        product: this.selectedProductName,
        plateNumber: this.selectedPlateNumber,
        refinery: refineryIds,
        mill: millIds,
        driverId: this.selectedDriver,
        vendorId: this.selectedVendor,
        productOwner,
        entries: this.isRowData,
        page: pageNumber,
        sortBy: sort,
        sortOrder: desc
      })

      const query = { ...this.$route.query }
      delete query.page
      this.$router.push({
        path: this.$route?.path,
        query
      })?.catch(() => {})
    },

    downloadExcelReport () {
      let incomingType = ''
      if (this.btnMillRefinery === 0) {
        incomingType = 'MILL'
      } else if (this.btnMillRefinery === 1) {
        incomingType = 'REFINERY'
      }

      const startDate = this.dateRangeStart
      const endDate = this.dateRangeEnd

      this.$store.dispatch('dashboard/downloadExportExcel', {
        start_date: startDate,
        end_date: endDate,
        incoming_type: incomingType,
        search: this.searchKeyMillRefinery,
        product: this.selectedProductName,
        plateNumber: this.selectedPlateNumber,
        refinery: this.selectedRefinery,
        mill: this.selectedMill?.map((e: any) => e.id),
        driverId: this.selectedDriver,
        vendorId: this.selectedVendor
      })
    },

    resetFilter () {
      this.dateRangeStart = null as any
      this.dateRangeEnd = null as any
      this.selectedProductName = null
      this.selectedPlateNumber = null as any
      this.selectedRefinery = null
      this.selectedMill = null
      this.selectedDriver = null
      this.selectedVendor = null
      this.selectedPo = null
    },

    shipmentDetailData () {
      if (this.selectedOrder && this.selectedOrder.tracks.length > 0) {
        this.selectedTrackId = this.selectedOrder.tracks[0].id
        this.selectedRoutes = this.selectedOrder.tracks[0].destinations

        this.setupDirectionSingle(0)

        const polylineTrack = []

        polylineTrack.push(this.selectedOrder.tracks[0])

        this.polyline = generatePolylineActive(polylineTrack)

        // this.polyline = generatePolyline(this.shipmentDetail.tracks)
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)

        // this.$store.dispatch('dashboard/getLiveTracking', {
        //   shipmentId: this.shipmentDetail.id
        // })

        this.recurringTracking()
      }
    },

    clickDriver (track: TrackActiveOrder, item: ShipmentActiveOrder, index: number) {
      const previousTrackId = this.clickedItemId;
      
      this.selectedOrder = item
      this.setupDirectionSingle(index)
      const polylineTrack = [] as TrackActiveOrder[]

      polylineTrack.push(track)

      this.polyline = generatePolylineActive(polylineTrack)

      if (this.liveTracking && this.liveTracking.vehicle_device !== null) {
        if (this.liveTracking.vehicle_device.filter((v: any) => v.vehicle_detail_id === track.vehicle_detail_id && v.status !== 'FINISHED').length > 0) {
          const liveLat = this.liveTracking.vehicle_device.find(val => val.vehicle_detail_id === track.vehicle_detail_id)?.lat as number
          const liveLng = this.liveTracking.vehicle_device.find(val => val.vehicle_detail_id === track.vehicle_detail_id)?.lng as number

          this.centerLatLng = generateCenterLatLng([
            {
              lat: liveLat,
              lng: liveLng
            }
          ])
          this.zoomMap = zoom([
            {
              lat: liveLat,
              lng: liveLng
            }
          ])
        }
      }
      
      if (this.clickedItemId === track.id) {
        this.clickedItemId = null
      } else {
        this.clickedItemId = track.id as any
      }
      
      if (previousTrackId !== null && previousTrackId !== track.id && this.$refs.customMap) {
        const mapComponent = this.$refs.customMap as any;
        if (mapComponent.mapObject) {
          mapComponent.mapObject.closePopup();
        }
      }
    },

    getVendors () {
      this.$store.dispatch('vendor/getVendors', {
        entries: -1,
        filterColumns: 'logistics_service_providers.status',
        filterKeys: 'COLLABORATE'
      })
    },

    getDriverData ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      this.$store.dispatch('vehicle/drivers/getItemFilters', {
        mode: 'simplified',
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        entries: -1
      })
    },

    getPickupDropoffLocation () {
      this.$store.dispatch('pick-up-drop-off-location-point/getItemsLocations', {
        filter_columns: 'shipment_company_id',
        filter_keys: this.selectedPo,
        entries: -1
      })
    },

    getProductName () {
      this.$store.dispatch('shipping-company/product/getItems', {
        entries: -1
      })
    },

    getVehicleDetailData () {
      this.$store.dispatch('vehicle/details/getItems', {
        mode: 'simplified',
        entries: -1
      })
    },

    getCustomers () {
      // const filterKeys = this.selectedShipmentId as {
      //   [key: string]: {
      //     shipment_company_ids: {
      //       location: string;
      //       same_location: string;
      //     };
      //   };
      // }

      // const uniqueLocations = new Set<string>()
      // const uniqueSameLocations = new Set<string>()

      // Object.values(filterKeys).forEach((keyObject) => {
      //   // eslint-disable-next-line camelcase
      //   const { location, same_location } = keyObject.shipment_company_ids
      //   if (location) { uniqueLocations.add(location) }
      //   // eslint-disable-next-line camelcase
      //   if (same_location) { uniqueSameLocations.add(same_location) }
      // })

      // const locationString = Array.from(uniqueLocations).join('|')
      // const sameLocationString = Array.from(uniqueSameLocations).join('|')

      // const filterKeysString = [locationString, sameLocationString].filter(Boolean).join('|')

      this.$store.dispatch('shipping-company/getItems', {
        // filterColumns: 'id',
        // filterKeys: filterKeysString,
        entries: '-1',
        fetchDetail: false
      })
    },
    resetIncomingTruckData() {
      this.$store.commit('dashboard/RESET_INCOMING_TRUCK')
    },
  }
})
</script>

<style scoped>
.custom-map >>> .leaflet-popup { width: auto !important; }
.custom-map >>> .leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 8px 64px 0 rgba(10, 10, 10, 0.24);
}
.custom-map >>> .leaflet-popup-content { margin: 0; padding: 8px 16px; }

.clicked {
  background: rgba(253, 235, 235, 1);
}
</style>

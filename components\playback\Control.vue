<template>
  <v-container fluid class="mt-2">
    <v-row justify="space-between">
      <div class="d-flex justify-center">
        <v-btn
          fab
          outlined
          small
          :disabled="isPlaying"
          :class="`ma-1 ${positions < 1 ? 'opacity-50' : ''}`"
          @click="rewind"
        >
          <v-icon>mdi-rewind</v-icon>
        </v-btn>

        <v-btn
          fab
          outlined
          small
          :class="`ma-1 ${positions < 1 ? 'opacity-50' : ''}`"
          @click="stop"
        >
          <v-icon>mdi-stop</v-icon>
        </v-btn>

        <v-btn
          fab
          outlined
          small
          :disabled="isPlaying"
          :class="`ma-1 ${positions < 1 ? 'opacity-50' : ''}`"
          @click="play"
        >
          <v-icon>mdi-play</v-icon>
        </v-btn>

        <v-btn
          fab
          outlined
          small
          :disabled="!isPlaying"
          :class="`ma-1 ${positions < 1 ? 'opacity-50' : ''}`"
          @click="pause"
        >
          <v-icon>mdi-pause</v-icon>
        </v-btn>

        <v-btn
          fab
          outlined
          small
          :disabled="isPlaying"
          :class="`ma-1 ${positions < 1 ? 'opacity-50' : ''}`"
          @click="forward"
        >
          <v-icon>mdi-fast-forward</v-icon>
        </v-btn>
      </div>
      <v-btn-toggle v-model="playbackSpeedValue">
        <v-btn
          v-for="item in playbackSpeedItems"
          :key="item.value"
          :value="item.value"
        >
          {{ item.text }}
        </v-btn>
      </v-btn-toggle>
      <!-- <v-radio-group v-model="playbackSpeedValue" row>
        <v-radio
          v-for="item in playbackSpeedItems"
          :key="item.value"
          :label="item.text"
          :value="item.value"
        />
      </v-radio-group> -->
    </v-row>
    <v-row>
      <v-col>
        <v-sheet height="200px">
          <play-back-chart
            v-model="playback.step"
            :labels="chartData?.labels"
            :datasets="chartData?.datasets"
            @on-change-step="playback.step = $event"
          />
        </v-sheet>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import PlayBackChart from '~/components/playback/Chart.vue'

export default Vue.extend({
  name: 'PlaybackControl',

  components: { PlayBackChart },

  props: {
    playbackData: {
      type: Array,
      default: () => []
    },
    chartData: {
      type: Object,
      default: null
    },
    positions: {
      type: Number,
      default: 0
    }
  },

  data: () => ({
    playbackSliderValue: '0',
    playbackSpeedValue: '1',
    playbackSpeedItems: [
      { text: '1x', value: '1' },
      { text: '2x', value: '2' },
      { text: '5x', value: '5' },
      { text: '10x', value: '10' },
      { text: '20x', value: '20' }
    ],
    playback: {
      id: undefined as any,
      step: 0,
      speed: 1,
      data: [] as { id: number, lat: number, lng: number, course: number, speed: number, timestamp: Date }[]
    }
  }),

  computed: {
    isPlaying () {
      return Boolean(this.playback.id)
    }
  },

  watch: {
    playbackSliderValue (value) {
      this.playback.step = Number(value)
    },
    playbackSpeedValue (value) {
      this.playback.speed = Number(value)
      if (!this.playback.id) {
        return
      }
      this.pause()
      this.play()
    },
    'playback.step' (step) {
      this.$emit('on-change-step', step)
    },
    playbackData: {
      handler (data) {
        this.playback.step = 0
        this.playback.data = [...data]
      },
      immediate: true,
      deep: true
    }
  },

  mounted () {
    //
  },

  methods: {
    play () {
      if (this.playback.id) { return }
      if (this.playback.step === this.playback.data.length - 1) {
        this.playback.step = 0
      }

      this.playback.id = setInterval(() => {
        if (this.playback.step >= (this.playback.data.length - 1)) {
          this.pause()
          this.$emit('on-finish')
          return
        }
        this.playback.step += 1
      }, 1000 / this.playback.speed)
    },
    pause () {
      if (!this.playback.id) { return }
      clearInterval(this.playback.id)
      this.playback.id = undefined
    },
    stop () {
      this.playback.step = 0
      if (!this.playback.id) { return }
      clearInterval(this.playback.id)
      this.playback.id = undefined
    },
    forward () {
      if (this.playback.id) { return }
      this.playback.step += this.playback.speed
    },
    rewind () {
      if (this.playback.id) { return }
      this.playback.step -= this.playback.speed
    }
  }
})
</script>

<style scoped>
.custom-dropdown .v-list {
  padding-bottom: 0
}
</style>

<template>
  <v-container fluid class="pa-0 mb-10">
    <v-row class="ma-n5">
      <v-col
        v-for="i in 3"
        :key="i"
        class="pa-5 col-lg-4 col-sm-6 col-12"
      >
        <v-sheet height="230" class="pa-5 overflow-hidden">
          <div class="d-flex">
            <div class="d-flex flex-row align-center" style="width: 100%">
              <v-skeleton-loader type="image" height="50" width="50" />
              <div class="d-flex flex-column" style="width: 100%">
                <v-skeleton-loader type="list-item-two-line" width="100%" />
              </div>
            </div>
          </div>
          <v-skeleton-loader type="text" class="mt-5 " />
          <div class="d-flex flex-column pt-7 ">
            <v-skeleton-loader type="text" width="50%" class="pb-2 flex" />
            <v-skeleton-loader type="text" />
          </div>
        </v-sheet>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'CompanyLoading'
})
</script>

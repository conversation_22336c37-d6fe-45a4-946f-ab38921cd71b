import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { ShipmentCompanyState } from './state'

export const getters: GetterTree<ShipmentCompanyState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  dataItem (state) {
    return {
      item: state.item
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  }
}

export default getters

import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleDetailsState } from './state'
import { VehicleKey } from '~/types/vehicle'

export const getters: GetterTree<VehicleDetailsState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  detailData (state) {
    return state.itemDetail
  },

  isHasNullVehicleId (state) {
    return state.isHasNullVehicleId
  },

  vehicleKeys (state): VehicleKey[] {
    return state.vehicleKeys
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export default getters

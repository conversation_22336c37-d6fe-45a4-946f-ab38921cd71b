<template>
  <v-container fluid class="pa-0">
    <v-app-bar class="py-9 px-6 custom-head-bar" elevation="0" height="auto">
      <v-container fluid class="pa-0 d-flex justify-space-between">
        <h3>{{ title }}</h3>

        <div class="d-flex align-center">
          <v-badge
            dot
            bordered
            overlap
            :value="isShowBadge?.notification"
            color="primary"
          >
            <v-icon
              color="black"
              @click="onOpenNotification"
            >
              mdi-bell
            </v-icon>
          </v-badge>

          <v-skeleton-loader v-if="isLoading" type="text" width="100" class="ma-0 ml-5 mr-5 mt-2" />
          <p v-else class="ma-0 ml-5 mr-2 body-1">
            Hi! {{ personName }}
          </p>

          <nuxt-link
            :to="localePath('/profile/account-setting')"
            class="text-decoration-none"
          >
            <v-avatar size="40" rounded="" color="primary">
              <p class="white--text ma-0 body-1">
                {{ initialName }}
              </p>
            </v-avatar>
          </nuxt-link>
        </div>
      </v-container>
    </v-app-bar>

    <v-navigation-drawer
      v-model="notificationDrawer"
      app
      temporary
      right
      width="400"
      style="z-index: 100"
    >
      <div class="d-flex justify-space-between ma-5">
        <h3>Notification</h3>

        <div>
          <v-icon @click="notificationDrawer = false">
            mdi-close
          </v-icon>
        </div>
      </div>

      <notification-list />
    </v-navigation-drawer>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import NotificationList from '~/components/NotificationList.vue'
import { User } from '~/types/user'

export default Vue.extend({
  name: 'HeadBar',

  components: { NotificationList },

  data: () => ({
    notificationDrawer: false
  }),

  computed: {
    isLoading (): any {
      return this.$store.getters['profile/account-setting/isLoading']
    },
    title ():string {
      return this.$store.getters['layout/title']
    },
    data (): User | null {
      return this.$store.getters['profile/account-setting/data']
    },
    personName () {
      const data = this.data as User | null
      return data?.name
    },
    initialName () {
      const user = this.$auth.user as any
      const split = user.data.name.split(' ')
      if (split.length > 1) {
        return split[0].charAt(0) + split[1].charAt(0)
      } else {
        return split[0].charAt(0)
      }
    },
    isShowBadge () {
      return this.$store.getters['firebase/dataNotification']
    }
  },

  watch: {
    notificationDrawer: {
      handler (val) {
        if (val) {
          this.$store.dispatch('notification/getItems', {})
        }
      }
    }
  },

  mounted () {
    this.$store.dispatch('profile/account-setting/getItem')
  },

  methods: {
    onOpenNotification () {
      const childPath = '/badge-notification'

      this.notificationDrawer = true

      this.$store.dispatch('firebase/setDataRealtime', childPath)
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-head-bar {
  background-color: #f0f0f0 !important;
  margin-left: 300px !important;
}
</style>

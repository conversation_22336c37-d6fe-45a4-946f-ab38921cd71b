<template>
  <v-container fluid class="pa-0 px-5 px-sm-10 mb-10">
    <v-container fluid class="pa-0">
      <div>
        <div style="width: 30%;">
        <div class="d-flex">
          <v-menu
            ref="menu"
            v-model="menuDateRange"
            max-width="350"
            :close-on-content-click="false"
            transition="slide-y-transition"
            offset-y
          >
            <template #activator="{ on, attrs }">
              <v-text-field
                v-model="dateRange"
                outlined
                clearable
                label="Select Date"
                v-bind="attrs"
                append-icon="mdi-calendar-range"
                background-color="white"
                v-on="on"
              >
                Select Date Range
              </v-text-field>
            </template>
            <v-date-picker
              v-model="dateRange"
              range
              no-title
              color="primary"
            >
              <v-btn text color="primary" @click="menuDateRange = false">
                Save
              </v-btn>
              <v-btn text color="primary" @click="menuDateRange = false">
                Cancel
              </v-btn>
            </v-date-picker>
          </v-menu>
          <div class="ml-3">
          <v-btn
            block
            elevation="0"
            color="primary"
            class="text-capitalize"
            height="55"
            :loading="isLoadingLogs"
            :disabled="!dateRange"
            @click="getData({});"
          >
            Show Data
          </v-btn>
        </div>
        </div>
        </div>
        <div class="d-flex" style="width: 40%;">
        <v-text-field
            v-model="searchKey"
            flat
            :label="$t('general.search')"
            append-icon="mdi-magnify"
            hide-details
            single-line
            solo
            height="52"
            class="mb-4"
            background-color="white"
            @click:append="handleSearch()"
            @keydown.enter="handleSearch()"
          />
          <v-menu
            ref="menu"
            max-width="550"
            :close-on-content-click="false"
            transition="slide-y-transition"
            offset-y
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                outlined
                elevation="0"
                large
                class="ml-5 text-capitalize custom-btn"
                x-large
                v-on="on"
              >
                <v-icon
                  class="mr-3 custom-icon"
                >
                  mdi-filter
                </v-icon>
                Filter
              </v-btn>
            </template>
  
            <v-card
              elevation="0"
              class="pa-5 d-flex justify-start flex-column"
            >
              <v-container class="pa-0">
                <v-row>
                  <v-col>
                 <v-text-field
                    v-model="searchPickup"
                    flat
                    :label="'Pickup'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                  </v-col>
                  <v-col>
                  <v-text-field
                    v-model="searchDropoff"
                    flat
                    :label="'Dropoff'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                  </v-col>
                </v-row>
                <v-row class="ma-0 d-flex mt-4">
                   <v-text-field
                    v-model="searchTransporter"
                    flat
                    :label="'Transporter'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                </v-row>
                <v-row class="ma-0 d-flex mt-3">
                   <v-text-field
                    v-model="searchDriver"
                    flat
                    :label="'Driver'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                </v-row>
                <v-row class="ma-0 d-flex mt-3">
                  <v-text-field
                    v-model="searchPlateNumber"
                    flat
                    :label="'Plate Number'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                </v-row>
                <v-row class="ma-0 d-flex mt-3">
                 <v-text-field
                    v-model="searchCommodity"
                    flat
                    :label="'Commodity'"
                    append-icon="mdi-magnify"
                    hide-details
                    single-line
                    solo
                    height="52"
                    outlined
                    background-color="white"
                  />
                </v-row>
                <v-row class="ma-0 d-flex mt-3">
                  <v-btn
                    block
                    elevation="0"
                    color="primary"
                    class="text-capitalize"
                    x-large
                    :loading="isLoadingLogs"
                    @click="getData({})"
                  >
                    Apply Filter
                  </v-btn>
                </v-row>
                <div class="pa-1">
                  <v-btn
                    color="transparent"
                    elevation="0"
                    class="mt-2 pa-0 text-capitalize text-secondary"
                    @click="resetFilter"
                  >
                    <p class="text-secondary">
                      Reset Filter
                    </p>
                  </v-btn>
                </div>
              </v-container>
            </v-card>
          </v-menu>
        </div>
      </div>
      <v-data-table
        :loading="isLoadingLogs"
        loading-text="Loading... Please wait"
        :headers="tableHeaders"
        :items="logs.items"
        :page.sync="page"
        :items-per-page="-1"
        disable-sort
        hide-default-footer
        class="pa-md-10 pa-5"
        style="z-index: 999999;"
        @page-count="pageCount = $event"
      >
        <template #item="{ item, index }">
          <tr>
            <td>{{ index + 1 }}</td>
            <td>
              <v-icon :color="item.response_code === 400 ? 'red' : item.response_code === 200 ? 'green' : 'yellow'">
                mdi-circle
              </v-icon>
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on">
                    {{ item.response_code }}
                  </span>
                </template>
                <pre v-if="isJSON(item.response)">{{ JSON.stringify(JSON.parse(item.response), null, 2) }}</pre>
                <span v-else>{{ item.response }}</span>
              </v-tooltip>
            </td>
            <td>
              <v-icon v-if="item.repush_code !== null" :color="item.repush_code === 400 ? 'red' : item.repush_code === 200 ? 'green' : 'yellow'">
                mdi-circle
              </v-icon>
              <div v-else>
                -
              </div>
              <template>
                <span>
                  {{ item.repush_code }}
                </span>
              </template>
            </td>
            <td>{{ logsBody[index]?.['itemrecords']?.[0]?.donumber ?? '' }}</td>
            <td>{{ logsBody[index]?.['itemrecords']?.[0]?.millquantity ?? logsBody[index]?.['itemrecords']?.[0]?.deliveryweight ?? '' }}</td>
            <td>{{ logsBody[index]?.['itemrecords']?.[0]?.totaldeliver ?? '' }}</td>
            <td>{{ logsBody[index]?.mill ?? '' }}</td>
            <td>{{ logsBody[index]?.outtime ?? '' }}</td>
            <td>{{ logsBody[index]?.commodity ?? '' }}</td>
            <td>{{ logsBody[index]?.trucknumber ?? '' }}</td>
            <td>{{ logsBody[index]?.simnumber ?? '' }}</td>
            <td>{{ logsBody[index]?.transporter ?? '' }}</td>
            <td>{{ logsBody[index]?.quality ?? '' }}</td>
            <td>{{ logsBody[index]?.ticketnumber ?? '' }}</td>
            <td>{{ logsBody[index]?.netweight ?? '' }}</td>
            <td>{{ logsBody[index]?.destinationdetails ?? '' }}</td>
            <td>{{ logsBody[index]?.destination ?? '' }}</td>
            <td>{{ logsBody[index]?.driver ?? '' }}</td>
            <td>{{ logsBody[index]?.doquantity ?? '' }}</td>
            <td>{{ $moment(item?.created_at).format('DD/MM/yyyy HH:mm') }}</td>
          </tr>
        </template>

        <template #no-data>
          <div class="d-flex justify-center align-center fill-height">
            <empty-placeholder
              hero="empty-placeholder.svg"
              :message-title="'Empty Data, Please select date First'"
              :message-description="'Please select date range to show data.'"
            />
          </div>
        </template>

      </v-data-table>
      <v-row class="mt-4 mb-4" justify="end">
        <pagination-component
          v-if="logs.totalPage > 0"
          :page="logs.page"
          :total-page="logs.totalPage"
          class="mr-3"
          page-id="page"
          @on-change-page="handlePaginationChange"
        />
      </v-row>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'WBLog',

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  data: () => ({
    searchKey: '',
    searchCommodityType: '',
    searchPickup: '',
    searchDropoff: '',
    searchTransporter: '',
    searchDriver: '',
    searchPlateNumber: '',
    searchCommodity: '',
    menuDateRange: false as boolean,
    dateRange: [] as Date[],
    page: 0 as any,
    pageCount: 0,
    tableHeaders: [
      { text: 'No', value: '' },
      { text: 'Status', value: '' },
      { text: 'Status Repush', value: '' },
      { text: 'DO Number', value: '' },
      { text: 'Mill Quantity', value: '' },
      { text: 'WB Refinery', value: '' },
      { text: 'Mill', value: '' },
      { text: 'Out Time', value: '' },
      { text: 'Commodity', value: '' },
      { text: 'Truck Number', value: '' },
      { text: 'Sim Number', value: '' },
      { text: 'Transporter', value: '' },
      { text: 'Quality', value: '' },
      { text: 'Ticket Number', value: '' },
      { text: 'Net Weight', value: '' },
      { text: 'Destination Details', value: '' },
      { text: 'Destination', value: '' },
      { text: 'Driver', value: '' },
      { text: 'DO Quantity', value: '' },
      { text: 'Log Create Time', value: '' }
    ],
    selectedUsersId: null as string[] | null,
    activeInterval: null as any
  }),

  computed: {
    logs (): { items: any[], page: number, totalPage: number } {
      return this.$store.getters['logs/data']
    },
    logsBody (): any {
      return this.logs.items.map((e: any) => JSON.parse(e.body))
    },
    isLoadingLogs (): boolean {
      return this.$store.getters['logs/isLoading']
    }
  },

  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'WB Activity Log'
    )
    // this.getData({ page: this.$route.query?.page as string, searchKey: this.searchKey })
  },

  beforeDestroy () {
    clearInterval(this.activeInterval)
    
    this.resetItems()
  },

  methods: {
    async getData ({ page = '', searchKey = '', filter = {} }: { page?: string, searchKey?: string, filter?: any }) {
      this.searchKey = searchKey || this.searchKey
      this.resetItems()
      await this.$store.dispatch('logs/getItems', {
        searchKey: this.searchKey,
        filter_date_column: 'created_at',
        filter_date_start: this.dateRange[0],
        filter_date_end: this.dateRange[1],
        entries: 10,
        page,
        commodity: this.searchCommodity,
        pickup: this.searchPickup,
        dropoff: this.searchDropoff,
        transporter: this.searchTransporter,
        driver: this.searchDriver,
        plate_number: this.searchPlateNumber,
        filter_keys: 'weight_bridge:load_weight||weight_bridge:deliver_weight',
        sortColumn: filter.sortColumn,
        sortType: filter.sortType
      })
    },
    handlePaginationChange (page: any) {
      const filter = {
        sortColumn: this.$route.query?.sort_column,
        sortType: this.$route.query?.sort_type
      }
      this.getData({ page, searchKey: this.searchKey, filter })
    },
    handleSearch () {
      this.getData({ page: this.page, searchKey: this.searchKey })
    },
    isJSON (str: string): boolean {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    },

    resetFilter () {
      this.dateRange = []
      this.searchKey = ''
      this.searchCommodity = ''
      this.searchPickup = ''
      this.searchDropoff = ''
      this.searchTransporter = ''
      this.searchDriver = ''
      this.searchPlateNumber = ''
      this.getData({})
    },

    resetItems() {
      this.$store.commit('logs/RESET_ITEMS')
    },
  }
})
</script>

<style lang="scss" scoped>
</style>

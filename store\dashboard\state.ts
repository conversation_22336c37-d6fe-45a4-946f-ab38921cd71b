import { DashboardAdmin, DashboardLsa, DashboardShipment, DashboardVendor, Geofences, PerformanceVendor } from '~/types/dashboard'
import { LiveTracking } from '~/types/shipment'

export interface DashboardState {
  isLoadingAdmin: boolean,
  isLoadingShipmentCompany: boolean,
  isLoadingLogisticServiceProvider: boolean,
  isLoadingLiveTracking: boolean
  isLoadingVendor: boolean,
  isLoadingTruckStatus: boolean,
  isLoadingIncomingTRuck: boolean,
  isLoadingPerformanceVendor: boolean,
  isLoadingPerformanceVendorRate: boolean,
  isLoadingShippingStatusVehicle: boolean,
  isLoadingExportExcel: boolean,
  isLoadingGeofences: boolean,
  isLoadingAllLiveTracking: boolean,
  isLoadingExportAlertGeofencing: boolean,
  itemAdmin: DashboardAdmin | null,
  itemLsa: DashboardLsa | null,
  itemShipment: DashboardShipment[] | null,
  itemVendor: DashboardVendor[] | null,
  liveTracking: LiveTracking | null,
  truckStatus: DashboardLsa | null,
  itemIncomingTruck: [] | null,
  itemPerformanceVendor: PerformanceVendor[],
  itemPerformanceVendorRate: PerformanceVendor[],
  pageVendorRate: number,
  totalPagesVendorRate: number,
  itemExportExcel: DashboardLsa | null
  itemsGeofences: Geofences[],
  itemsAllLiveTracking: LiveTracking[] | null,
  itemVendorVehicles: any,
  filteredItemsGeofences: Geofences[],
  totalPageVehicle: number,
  pageVehicle: number,
  page: number,
  totalPages: number,
  itemsReportTransporter: any,
  isLoadingReportTransporter: boolean,
  isLoadingExportReportExcel: boolean,
  itemsAlertGeofencing: any,
  isLoadingAlertGeofencing: boolean,
  totalData: number,
  totalPageAlertGeofencing: number,
  pageAlertGeofencing: number,
  itemPerformanceDetailOrder: any,
  totalPagePerformanceDetailOrder: number,
  pagePerformanceDetailOrder: number,
  isLoadingPerformanceDetailOrder: boolean
}

export const state = () : DashboardState => ({
  isLoadingAdmin: true,
  isLoadingShipmentCompany: true,
  isLoadingLogisticServiceProvider: true,
  isLoadingVendor: true,
  isLoadingLiveTracking: false,
  isLoadingTruckStatus: false,
  isLoadingIncomingTRuck: false,
  isLoadingPerformanceVendor: false,
  isLoadingPerformanceVendorRate: false,
  isLoadingShippingStatusVehicle: false,
  isLoadingExportExcel: false,
  isLoadingGeofences: false,
  isLoadingAllLiveTracking: false,
  isLoadingExportAlertGeofencing: false,
  itemAdmin: null,
  itemLsa: null,
  itemShipment: null,
  itemVendor: null,
  liveTracking: null,
  truckStatus: null,
  itemIncomingTruck: null,
  itemPerformanceVendor: [],
  itemPerformanceVendorRate: [],
  pageVendorRate: 0,
  totalPagesVendorRate: 0,
  itemExportExcel: null,
  itemsGeofences: [],
  itemsAllLiveTracking: [],
  itemVendorVehicles: [],
  filteredItemsGeofences: [],
  totalPageVehicle: 0,
  pageVehicle: 0,
  page: 0,
  totalPages: 0,
  itemsReportTransporter: [],
  isLoadingReportTransporter: false,
  isLoadingExportReportExcel: false,
  itemsAlertGeofencing: [],
  isLoadingAlertGeofencing: false,
  totalData: 0,
  totalPageAlertGeofencing: 0,
  pageAlertGeofencing: 0,
  itemPerformanceDetailOrder: [],
  totalPagePerformanceDetailOrder: 0,
  pagePerformanceDetailOrder: 0,
  isLoadingPerformanceDetailOrder: false
})

export default state

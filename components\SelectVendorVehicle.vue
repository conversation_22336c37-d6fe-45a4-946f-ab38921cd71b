<template>
  <v-container fluid class="pa-0">
    <v-row class="ma-0 mx-n5 mb-10">
      <v-col class="col-5 pa-0 px-5">
        <v-container fluid class="pa-10 white rounded d-flex flex-column align-start justify-space-between fill-height">
          <div v-if="isLoadingShipmentDetail" />
          <div v-else>
            <v-container fluid class="pa-0 mb-3">
              <h4 class="mb-2">
                Your Delivery Order
              </h4>
              <h4 class="mb-2 red--text">
                {{ orderNumber }}
              </h4>

              <v-divider />
            </v-container>

            <v-container fluid class="pa-0">
              <v-container fluid class="pa-0 mb-6 d-flex justify-space-between align-start">
                <!-- <div>
                <p class="body-1 mb-1 text-secondary">
                  Volume :
                </p>
                <h2>
                  {{ calculationOrder?.totalVolume }} CBM
                </h2>
              </div> -->

                <div>
                  <p class="body-1 mb-1 text-secondary">
                    Remaining Weight :
                  </p>
                  <h2 class="blue--text">
                    {{ formatNumber(parseFloat(orderWeight)) }} KG
                  </h2>
                  <div class="d-flex mt-5">
                    <p class="body-1 mb-1 text-secondary">
                      total weight :
                    </p>
                    <p class="subtitle-1 ml-1">
                      {{ formatNumber(parseFloat(totalWeightOrder)) }} KG
                    </p>
                  </div>
                </div>
              </v-container>

            <!-- <h5 class="mb-4">
              Detail
            </h5>

            <v-container fluid class="pa-0 d-flex justify-space-between">
              <div>
                <p class="caption ma-0 text-secondary">
                  {{ $t('lspCreateShipment.total_length') }}
                </p>
                <p class="subtitle-1 ma-0">
                  {{ calculationOrder?.totalLength }} CM
                </p>
              </div>

              <div>
                <p class="caption ma-0 text-secondary">
                  {{ $t('lspCreateShipment.total_width') }}
                </p>
                <p class="subtitle-1 ma-0">
                  {{ calculationOrder?.totalWidth }} CM
                </p>
              </div>

              <div>
                <p class="caption ma-0 text-secondary">
                  {{ $t('lspCreateShipment.total_height') }}
                </p>
                <p class="subtitle-1 ma-0">
                  {{ calculationOrder?.totalHeight }} CM
                </p>
              </div>
            </v-container> -->
            </v-container>
          </div>
        </v-container>
      </v-col>

      <v-col class="pa-0 px-5">
        <v-container fluid class="pa-10 white rounded fill-height">
          <v-container v-if="selectedVehicle" fluid class="d-flex align-center justify-space-between">
            <div>
              <p class="subtitle-1 text-secondary mb-2">
                {{ selectedVehicle.vendor.name }}
              </p>
              <h3 class="mb-5">
                {{ selectedVehicle.name }}
              </h3>

              <p class="subtitle-1 mb-4">
                Specification
              </p>

              <v-container fluid class="pa-0 mb-4 d-flex">
                <div>
                  <p class="caption ma-0 text-secondary">
                    Length
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.length }} CM
                  </p>
                </div>
                <div class="mx-10">
                  <p class="caption ma-0 text-secondary">
                    Width
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.width }} CM
                  </p>
                </div>
                <div>
                  <p class="caption ma-0 text-secondary">
                    Height
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.height }} CM
                  </p>
                </div>
              </v-container>

              <v-container fluid class="pa-0 mb-4 d-flex">
                <!-- <div class="mr-10">
                  <p class="caption ma-0 text-secondary">
                    Volume
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.max_volume }} CBM
                  </p>
                </div> -->
                <div class="mr-10">
                  <p class="caption ma-0 text-secondary">
                    Max Weight
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.max_weight }} KG
                  </p>
                </div>
                <div class="mr-10">
                  <p class="caption ma-0 text-secondary">
                    Type
                  </p>
                  <p class="body-1 ma-0">
                    {{ selectedVehicle.vehicle_type.name }}
                  </p>
                </div>
              </v-container>

              <!-- <v-container fluid class="pa-0 d-flex">
                <div>
                  <p class="caption ma-0 text-secondary">
                    Features
                  </p>
                  <p class="body-1 ma-0">
                    <v-chip
                      v-for="vehicleFeature in selectedVehicle.vehicle_features"
                      :key="vehicleFeature.id"
                      label
                      small
                      outlined
                      style="border: 1px solid #CFCCCC"
                    >
                      {{ vehicleFeature.name }}
                    </v-chip>
                  </p>
                </div>
              </v-container> -->
            </div>

            <image-component
              min-width="160"
              max-width="160"
              :image="selectedVehicle.photo_url"
            />
          </v-container>

          <h2 v-else class="ma-auto">
            No Vehicle Selected
          </h2>
        </v-container>
      </v-col>
    </v-row>

    <v-container v-if="!isLoadingVendor" fluid class="pa-10 white rounded">
      <h4 class="mb-10">
        Select Transporter
      </h4>
      <div class="d-flex align-center col-4 pa-0 mb-6">
        <v-autocomplete
          v-model="selectedVendor"
          class="my-2"
          :items="dataVendor.items.filter(item => item.value !== filterVendor)"
          label="Select Transporter"
          hide-details
          outlined
          item-value="id"
          item-text="name"
          :multiple="$route.query.shipmentVendorIds ? false : true"
          dense
          clearable
          @blur="getVehicles($event)"
        >
          <template #item="data">
            <div>
              <p class="mb-1 mt-3">
                {{ data.item.name }}
              </p>
              <p class="blue--text">
                {{ data.item.total_vehicle_detail }} <span>Vehicles Owned</span>
              </p>
            </div>
          </template>
        </v-autocomplete>
      </div>

      <v-container v-if="selectedVendor" fluid class="pa-0">
        <v-sheet v-if="isLoadingVehicleList" height="80" class="d-flex mt-5 mb-5 mx-2 overflow-hidden">
          <v-skeleton-loader type="image" height="80" width="250" class="mr-2" />
          <v-skeleton-loader type="image" height="80" width="250" class="ml-2" />
        </v-sheet>

        <div v-else>
          <v-row v-for="(vendor, vendorName) in groupedByVendor" :key="vendorName">
            <v-col class="d-flex">
              <h3 class="pa-2 ma-2">
                {{ vendor.vendor.name }}
              </h3>
              <div class="col-5">
                <FormattedNumberInput
                  :key="vendorName.id"
                  v-model="weightValue[vendor.vendor.id]"
                  :show-kg="true"
                  class="centered-input inputPrice ml-5"
                  style="width: 60%;"
                  append="KG"
                  @input="weightVehicle(vendor, weightValue)"
                />
                <!-- <v-text-field
                  :key="vendorName.id"
                  v-model="weightValue[vendor.vendor.id]"
                  outlined
                  type="number"
                  class="centered-input inputPrice ml-5"
                  style="width: 60%;"
                  persistent-hint
                  hide-spin-buttons
                  @input="weightVehicle(vendor, weightValue)"
                >
                  <template #append>
                    KG
                  </template>
                </v-text-field> -->
              </div>
            </v-col>
            <v-container class="d-flex mb-6">
              <v-row style="overflow-x: auto; flex-wrap: nowrap; -webkit-overflow-scrolling: touch;">
                <v-col v-for="item in vendor.vehicles" :key="item.id" class="col-lg-4 col-sm-6 col-12 mb-3">
                  <v-card
                    outlined
                    elevation="0"
                    class="pa-5"
                    :style="selectedVehicle?.id === item.id ? 'border: 1px solid #EF3434' : ''"
                    @click="selectVehicle(item)"
                  >
                    <v-row class="pa-2 ma-2">
                      <image-component
                        :image="item.photo_url"
                        class="mr-6"
                      />
                      <div class="mb-5">
                        <p class="subtitle-1 mb-1">
                          {{ item.name.length > 10 ? `${item.name.slice(0, 10)}...` : item.name }}
                        </p>
                        <p class="caption text-secondary mb-1">
                          {{
                            item.vehicle_type.name.length > 10 ? `${item.vehicle_type.name.slice(0, 10)}...` : item.vehicle_type.name
                          }}
                        </p>
                        <p class="caption text-secondary mb-1">
                          {{ item.vendor?.name }}
                        </p>
                        <p class="caption text-secondary ma-0">
                          Total Truck :
                          <span class="font-weight-bold black--text">
                            {{ item.vehicle_details_count }} unit
                          </span>
                        </p>
                      </div>
                    </v-row>
                    <div 
                      v-if="exceedMessages[item.id]" 
                      class="exceed-message"
                    >
                    <p class="subtitle-2">exceed total truck</p>
                    </div>
                    <v-divider class="mx-5" />
                    <v-sheet class="pa-2 mt-3 d-flex align-center rounded v-sheet--outlined" height="60">
                      <div class="d-flex align-center justify-space-between col-12">
                        <v-btn
                          small
                          class="ma-0"
                          elevation="0"
                          color="transparent"
                          :disabled="item.vehicle_details_count === 0 || !item.quantity"
                          @click="decreaseQuantity(item)"
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>

                         <div class="pa-2 ma-1">
                           <FormattedNumberInput
                             :value="item.quantity ?? 0"
                             class="quantity-input"
                             hide-details
                             dense
                             @input="updateQuantity(item, $event)"
                           />
                         </div>

                        <v-btn
                          small
                          class="ma-0"
                          elevation="0"
                          color="transparent"
                          :disabled="item.vehicle_details_count === 0 || Number(item.quantity) >= Number(item.vehicle_details_count)"
                          @click="increaseQuantity(item)"
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </div>
                    </v-sheet>
                  </v-card>
                </v-col>
              </v-row>
            </v-container>
          </v-row>
        </div>

        <p v-if="checkedVehicles.length > 0" class="body-1 text-info ml-2" @click="dialogSelectVehicle = true">
          {{ checkedVehicles.length }} vehicle selected
        </p>
        <v-dialog
          v-model="dialogSelectVehicle"
          width="480"
          persistent
        >
          <v-card class="pa-md-10 pa-2">
            <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
              <h4>Detail Vehicle Selected</h4>

              <v-icon color="black" @click="dialogSelectVehicle = false">
                mdi-close
              </v-icon>
            </v-card-title>
            <v-data-table
              :headers="tableHeaders"
              hide-default-footer
              :items="checkedVehicles"
            >
              <template #item.totalWeight="{ item }">
                {{ item.vendor.weight?.toLocaleString('en-US') }}
              </template>
              <template #item.totalUnit="{ item }">
                {{ item.quantity }}
              </template>
            </v-data-table>
            <div class="pa-0 ma-0 d-flex justify-space-between">
              <p class="subtitle-1 mt-3">
                Total Weight
              </p>
              <p class="subtitle-1 mt-3 text-primary mr-3">
                {{ formatNumber(totalWeight()) }} KG
              </p>
            </div>
          </v-card>
        </v-dialog>
        <p v-if="overLoad" class="mt-3">
          {{ overLoad }} <span class="red--text">reduce load by {{ formatNumber(calculationOrder?.totalWeight) }} Kg!
          </span>
        </p>
        <p v-else>
          You have
          <span class="red--text">
            {{
              formatNumber(totalWeightSelected())
            }} KG
          </span>
          of your order load
        </p>
      </v-container>
      <div class="d-sm-flex">
        <v-btn
          class="mt-5 mr-2"
          :block="$vuetify.breakpoint.xs"
          text
          outlined
          color="primary"
          @click="$router.back()"
        >
          <v-icon text>
            mdi-chevron-left
          </v-icon>
          {{ $t('lspCreateShipment.back_to_order') }}
        </v-btn>
        <v-btn
          :block="$vuetify.breakpoint.xs"
          class="mt-5"
          color="primary"
          :disabled="totalWeightSelected () !== 0 || Object.values(exceedMessages).includes(true)"
          @click="onClickReview"
        >
          {{ $t('lspCreateShipment.review_order_shipment') }}
          <v-icon text>
            mdi-chevron-right
          </v-icon>
        </v-btn>
      </div>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { formatNumber } from '~/utils/functions'
import ImageComponent from '~/components/ImageComponent.vue'
import { Vendor } from '~/types/user'
import { Vehicle } from '~/types/vehicle'
import FormattedNumberInput from '~/components/fields/FormattedNumberInput.vue'
import { toastError } from '~/utils/toasts'

interface CalculationOrder {
  totalVolume?: number
  totalWeight?: number
  totalLength?: number
  totalWidth?: number
  totalHeight?: number
}

export default Vue.extend({
  name: 'SelectVendorVehicle',

  components: { ImageComponent, FormattedNumberInput },

  props: {
    calculationOrder: {
      type: Object as () => CalculationOrder | null,
      default: null
    },
    filterVendor: {
      type: String,
      default: ''
    },
    totalWeightOrder: {
      type: Number,
      default: 0
    },
    orderNumber: {
      type: String,
      default: ''
    },
    isLoadingShipmentDetail: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    exceedMessages: {} as Record<string, boolean>,
    selectedVendor: null as string[] | null,
    searchKeyVehicle: null as string | null,
    weightValue: [],
    pageVehicles: 1,
    dialogSelectVehicle: false as boolean,
    tableHeaders: [
      {
        text: 'Vendor',
        value: 'vendor.name'
      },
      {
        text: 'Total Weight',
        value: 'totalWeight'
      },
      {
        text: 'Vehicle',
        value: 'name'
      },
      {
        text: 'Total Unit',
        value: 'totalUnit'
      }
    ],
    overLoad: ''
  }),

  computed: {
    selectedVehicle (): Vehicle | null {
      return this.$store.getters['vehicle/selectedVehicle']
    },

    isLoadingVendor (): boolean {
      return this.$store.getters['vendor/isLoading']
    },

    isLoadingVehicleList (): boolean {
      return this.$store.getters['vehicle/isLoadingList']
    },

    dataVendor (): { items: Vendor[], page: number, totalPage: number } {
      return this.$store.getters['vendor/data']
    },

    dataVehicle (): Vehicle[] {
      return this.$store.getters['vehicle/listVehicles']
    },

    checkedVehicles () {
      return this.$store.getters['vehicle/checkedVehicles'] as Array<Vehicle>
    },

    groupedByVendor (): Record<string, { vendor: any; vehicles: any[] }> {
      const groupedByVendor: Record<string, { vendor: any; vehicles: any[] }> = {}

      this.dataVehicle.forEach((item: any) => {
        const vendorName = item.vendor.name

        if (!groupedByVendor[vendorName]) {
          groupedByVendor[vendorName] = {
            vendor: item.vendor,
            vehicles: []
          }
        }

        groupedByVendor[vendorName].vehicles.push(item)
      })

      return groupedByVendor
    },

    vendorWeight (): any {
      return this.$store.getters['vehicle/vendorWeight'].vendorWeight
    },

    orderWeight (): any {
      if (this.$route.query.shipmentVendorIds) {
        return this.vendorWeight
      } else if (this.$route.query.shipmentRitaseId) {
        return this.calculationOrder
      } else if (this.calculationOrder && 'totalWeight' in this.calculationOrder) {
        return (this.calculationOrder as { totalWeight: any }).totalWeight
      }
      return null
    }

  },

  mounted () {
    const shipmentVendorIds = this.$route.query.shipmentVendorIds
    const shipmentRitaseId = this.$route.query.shipmentRitaseId
    if (this.checkedVehicles.length > 0) {
      const vendorIds = this.checkedVehicles.map((vehicle: any) => vehicle.vendor_id)
      this.selectedVendor = vendorIds
      const obj : any = {}
      this.checkedVehicles.forEach((vehicle: any) => {
        obj[vehicle.vendor.id] = vehicle.vendor.weight
      })
      this.weightValue = obj
    }
    if (typeof shipmentVendorIds === 'string') {
      const vendorIds = shipmentVendorIds
        .replace(/\\/g, '')
        .split(',')
        .map(id => id.replace(/"/g, ''))

      this.selectedVendor = vendorIds
    }
    if (typeof shipmentRitaseId === 'string') {
      const vendorIds = shipmentRitaseId
        .replace(/\\/g, '')
        .split(',')
        .map(id => id.replace(/"/g, ''))

      this.selectedVendor = vendorIds
    }
    // this.$store.dispatch('vehicle/clearVehicles')
    this.getVendors()
  },

  methods: {
    formatNumber,
    decreaseQuantity (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/decreaseVehicleQuantity', vehicle)
      
      const newQuantity = (vehicle.quantity ?? 0)
      const maxValue = vehicle.vehicle_details_count
      
      if (newQuantity <= maxValue) {
        this.$set(this.exceedMessages, vehicle.id, false)
      }
    },

    increaseQuantity (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/increaseVehicleQuantity', vehicle)
    },

    updateQuantity (vehicle: Vehicle, value: number) {
      const newValue = Math.max(0, value)
 
      const maxValue = vehicle.vehicle_details_count
   
      if (newValue > maxValue) {
        this.$set(this.exceedMessages, vehicle.id, true)
        
      } else {
        this.$set(this.exceedMessages, vehicle.id, false)
      }
  
      const validValue = value

      this.$store.commit('vehicle/SET_VEHICLE_QUANTITY', { 
        id: vehicle.id, 
        quantity: validValue 
      })
    },

    weightVehicle (vendor: any, weightValue: any) {
      this.$store.dispatch('vehicle/weightVehicle', {
        weightValue,
        vendor
      })
    },

    selectVehicle (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/selectVehicle', vehicle)
    },

    onClickReview () {
      const vehicles: Vehicle[] = this.dataVehicle
        .filter((vehicle: Vehicle) => (vehicle?.quantity ?? 0) > 0)

      if (vehicles.length === 0) {
        toastError('Please select vehicle', this)
        return
      }

      if (this.$route.query.shipmentVendorIds) {
        const routeOptions = {
          path: `${this.$route.path}/review`,
          query: {
            shipmentVendorIds: this.$route.query.shipmentVendorIds,
            id: this.$route.query.id
          }
        }
        this.$router.push(routeOptions)
      } else {
        const routeOptionsRitase = {
          path: `${this.$route.path}/review`,
          query: {
            shipmentRitaseId: this.$route.query.shipmentRitaseId
          }
        }
        this.$router.push(routeOptionsRitase)
      }
    },

    getVendors () {
      this.$store.dispatch('vendor/getVendors', {
        entries: -1,
        filterColumns: 'logistics_service_providers.status',
        filterKeys: 'COLLABORATE'
      })
    },

    getVehicles ({ page = 1 }) {
      this.$store.dispatch('vehicle/clearVehicles')
      this.weightValue = []
      let filterKeys

      if (!this.$route.query.shipmentVendorIds) {
        if (Array.isArray(this.selectedVendor)) {
          filterKeys = this.selectedVendor?.join('|')
        } else {
          filterKeys = this.selectedVendor
        }
      } else {
        filterKeys = this.selectedVendor
      }

      if (filterKeys !== '') {
        this.$store.dispatch('vehicle/getItems', {
          entries: -1,
          page: '',
          searchColumns: 'name',
          searchKey: this.searchKeyVehicle,
          filterColumns: 'vendor_id',
          filterKeys
        })
      }
    },

    totalWeightSelected () {
      if (this.$route.query.shipmentVendorIds) {
        const orderTotalWeight = this.vendorWeight ? parseFloat(this.vendorWeight) : 0

        const selectedWeights = Object.values(this.weightValue).map((weight) => {
          const parsedWeight = parseFloat(weight)
          return isNaN(parsedWeight) ? 0 : parsedWeight
        })

        const totalSelectedWeight = selectedWeights.reduce((acc, weight) => acc + weight, 0)

        const remainingWeight = orderTotalWeight - totalSelectedWeight

        if (remainingWeight < 0) {
          this.overLoad = 'vendor load exceeds order quantity,'
        } else {
          this.overLoad = ''
        }

        return orderTotalWeight - totalSelectedWeight
      } else if (this.calculationOrder?.totalWeight) {
        const orderTotalWeight = this.calculationOrder?.totalWeight ? parseFloat(this.calculationOrder.totalWeight as any) : 0

        const selectedWeights = Object.values(this.weightValue).map((weight) => {
          const parsedWeight = parseFloat(weight)
          return isNaN(parsedWeight) ? 0 : parsedWeight
        })

        const totalSelectedWeight = selectedWeights.reduce((acc, weight) => acc + weight, 0)

        const remainingWeight = orderTotalWeight - totalSelectedWeight

        if (remainingWeight < 0) {
          this.overLoad = 'vendor load exceeds order quantity,'
        } else {
          this.overLoad = ''
        }

        return orderTotalWeight - totalSelectedWeight
      } else if (this.calculationOrder) {
        const orderTotalWeight = this.calculationOrder ? parseFloat(this.calculationOrder as any) : 0

        const selectedWeights = Object.values(this.weightValue).map((weight) => {
          const parsedWeight = parseFloat(weight)
          return isNaN(parsedWeight) ? 0 : parsedWeight
        })

        const totalSelectedWeight = selectedWeights.reduce((acc, weight) => acc + weight, 0)

        const remainingWeight = orderTotalWeight - totalSelectedWeight

        if (remainingWeight < 0) {
          this.overLoad = 'The total weight has all been sent'
        } else {
          this.overLoad = ''
        }

        return orderTotalWeight - totalSelectedWeight
      }
    },

    totalWeight () {
      const selectedWeights = Object.values(this.weightValue).map((weight) => {
        const parsedWeight = parseFloat(weight)
        return isNaN(parsedWeight) ? 0 : parsedWeight
      })

      const totalSelectedWeight = selectedWeights.reduce((acc, weight) => acc + weight, 0)

      return totalSelectedWeight
    }

  }
})
</script>

<style scoped lang="scss">
.quantity-input >>> .v-input__slot {
  min-height: 32px !important;
  padding: 0 8px !important;
}

.quantity-input >>> input {
  text-align: center;
}

.position-relative {
  position: relative;
}

.exceed-message {
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 100%;
  color: #EF3434;
  font-size: 10px;
  text-align: center;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>

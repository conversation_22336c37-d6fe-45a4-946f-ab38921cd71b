import { MutationTree } from 'vuex'
import { LogisticServiceProviderInvoiceShipmentState } from './state'

export const mutations: MutationTree<LogisticServiceProviderInvoiceShipmentState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_VENDOR_ITEMS (state, vendorItems : any) {
    state.vendorItems = vendorItems
  },

  SET_ITEM (state, item: any) {
    state.item = item
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_TOTAL_PAGE_VENDOR (state, totalPageVendor: any) {
    state.totalPageVendor = totalPageVendor
  },

  SET_PAGE_VENDOR (state, pageVendor: any) {
    state.pageVendor = pageVendor
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_VENDOR (state, isLoadingVendor) {
    state.isLoadingVendor = isLoadingVendor
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  },

  SET_IS_LOADING_PUBLISH (state, isLoadingPublish) {
    state.isLoadingPublish = isLoadingPublish
  }
}

export default mutations

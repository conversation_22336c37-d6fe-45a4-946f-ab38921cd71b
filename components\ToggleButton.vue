<template>
  <div class="pa-0 ml-0 pt-md-0">
    <div class="d-flex justify-start">
      <v-btn-toggle
        v-model="toggleData"
        active-class="v-item--active"
        borderless
        mandatory
        :value="dataTableMode"
        @change="$store.dispatch('layout/setDataTableMode', { dataTableMode: $event})"
      >
        <v-btn
          elevation="0"
          large
          class="text-capitalize"
          x-large
          @click="$emit('grid')"
        >
          <v-icon class="mr-3 custom-icon">
            mdi-view-grid
          </v-icon>
          Grid
        </v-btn>

        <v-btn
          color="white"
          elevation="0"
          large
          class="text-capitalize "
          x-large
          @click="$emit('table')"
        >
          <v-icon class="mr-3 custom-icon">
            mdi-format-list-bulleted
          </v-icon>
          Table
        </v-btn>
      </v-btn-toggle>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ToggleButton',

  data: () => ({
    toggleData: 0
  }),
  computed: {
    dataTableMode (): any {
      return this.$store.getters['layout/dataTableMode']
    }
  },

  mounted () {
    this.$store.dispatch('layout/getDataTableMode')
    this.getValueMode()
  },

  methods: {
    getValueMode () {
      if (this.dataTableMode === '0') {
        this.toggleData = 0
      } else {
        this.toggleData = 1
      }
    }
  }
})
</script>

<style scoped lang="scss">
/*.custom-btn {*/
/*  transition: .28s !important;*/
/*}*/

.custom-icon {
  transition: 0s !important;
}

.v-item--active {
  background-color: var(--v-primary-base) !important;
  color: white !important;
}

.theme--light.v-btn--active::before, .theme--light.v-btn--active:hover::before, .theme--light.v-btn--active  {
  opacity: 1 !important;
  background-color: var(--v-primary-base) !important;
}
.v-item--active .custom-icon {
  color: white !important;
}
</style>

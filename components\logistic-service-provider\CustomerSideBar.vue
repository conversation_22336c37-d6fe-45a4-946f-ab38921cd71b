<template>
  <v-navigation-drawer
    v-model="drawer"
    app
    :temporary="!$vuetify.breakpoint.mdAndUp"
    :permanent="$vuetify.breakpoint.mdAndUp"
    :width="
      $vuetify.breakpoint.xsOnly
        ? '100%'
        : !$vuetify.breakpoint.xsOnly && !$vuetify.breakpoint.mdAndUp
          ? $vuetify.breakpoint.width - 280
          : '360'
    "
    :style="$vuetify.breakpoint.smAndUp ? 'margin-left: 280px' : ''"
    style="z-index: 98"
  >
    <v-container fluid class="pa-0 d-flex flex-column">
      <v-container
        v-if="!$vuetify.breakpoint.mdAndUp"
        fluid
        class="d-flex pa-0 ma-5"
      >
        <div
          class="d-flex"
          style="cursor: pointer"
          @click.stop="$emit('on-close-drawer')"
        >
          <v-icon size="24" color="black" class="mr-2">
            mdi-close
          </v-icon>
          <p class="subtitle-1 ma-0">
            {{ $t('lspCustomer.close') }}
          </p>
        </div>
      </v-container>

      <h3 class="ma-5 ma-md-10">
        {{ $t('lspCustomer.product') }}
      </h3>

      <div class="mx-5 mx-md-10 pa-0 d-flex">
        <v-col class="pa-0">
          <v-dialog
            v-model="scRegisterDialog"
            max-width="540"
            style="z-index: 99"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                class="text-capitalize"
                v-bind="attrs"
                x-large
                block
                color="primary"
                v-on="on"
                @click="setRegisterToken"
              >
                {{ $t('lspCustomer.product') }}
              </v-btn>
            </template>

            <v-container class="pa-5 bg-color">
              <form-user-registration
                @on-register-completed="onRegisterCompleted"
              />
            </v-container>
          </v-dialog>
        </v-col>
        <v-col class="col-3 pa-0 pl-2">
          <v-dialog
            v-model="dialogLinkRegistration"
            max-width="600px"
            style="z-index: 999"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                class="custom-btn pa-0"
                elevation="0"
                x-large
                color="white"
                block
                :loading="isLoadingRegisterLink"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon> mdi-link</v-icon>
              </v-btn>
            </template>
            <v-card class="pt-0 pb-5 px-5">
              <v-card-title
                class="mb-10 pl-0 pr-0 d-flex justify-space-between"
              >
                <h3> {{ $t('lspCustomer.link_registration') }}</h3>
                <v-icon @click="dialogLinkRegistration = false">
                  mdi-close
                </v-icon>
              </v-card-title>
              <v-container
                class="d-flex align-center"
                style="background-color: #f5f5f5"
              >
                <v-row align="center" justify="center">
                  <v-col>
                    <p class="ma-0">
                      {{ registerLink }}
                    </p>
                  </v-col>
                  <v-col class="col-12 col-sm-3">
                    <v-btn
                      class="text-capitalize"
                      color="primary"
                      x-large
                      large
                      @click="getRegisterLink"
                    >
                      <v-icon> mdi-content-copy</v-icon>
                      {{ $t('lspCustomer.copy') }}
                    </v-btn>
                  </v-col>
                </v-row>
              </v-container>
            </v-card>
          </v-dialog>
        </v-col>
      </div>

      <v-divider class="ma-5 mx-md-10" />

      <div class="mx-5 mb-5 mx-md-10 d-flex">
        <v-text-field
          v-model="searchKey"
          class="mr-5"
          outlined
          :label="$t('general.search')"
          hide-details
          append-icon="mdi-magnify"
          @input="getDataSc({ searchKey: searchKey })"
        />

        <filter-menu
          default-sort-column="name"
          default-sort-type="asc"
          :sort-column-items="sortColumnItems"
          :sort-type-items="sortTypeItems"
          default-filter-column="status"
          default-filter-type="COLLABORATE|BLOCK"
          :filter-column-items="filterColumnItems"
          :filter-type-items="filterTypeItems"
          @on-filter-change="getDataSc({ filter: $event })"
        >
          <template #activator="{ on, attrs }">
            <div>
              <v-btn
                fab
                elevation="0"
                color="white"
                class="rounded"
                style="border: 1px solid #cfcccc !important"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon> mdi-filter</v-icon>
              </v-btn>
            </div>
          </template>
        </filter-menu>
      </div>
      <div v-if="isLoading" class="mx-5 mx-md-5">
        <v-skeleton-loader type="text" width="60%" class="mb-2 mx-5 mx-md-5" />
        <div v-for="i in 4" :key="i" class="mx-5 mx-md-5">
          <v-skeleton-loader type="image" class="mb-5" height="48" />
        </div>
      </div>
      <div v-else>
        <div v-if="dataSc" class="mx-5 mx-md-10">
          <p class="caption mb-2">
            Total {{ dataSc?.length }} Product
          </p>

          <v-list nav class="pa-0">
            <v-list-item-group
              v-model="toggleButton"
              class="d-flex flex-column"
              color="primary"
              borderless
              tile
            >
              <v-list-item
                v-for="shippingCompany in dataSc"
                :key="shippingCompany.id"
                style="border-radius: 4px !important"
                :value="shippingCompany"
                min-height="auto"
                class="py-3 px-4 mb-5"
              >
                <div
                  class="d-flex flex-column align-start"
                >
                  <p class="ma-0 mb-2 subtitle-1">
                    {{ shippingCompany.name }} <span class="pl-2 red--text">{{ checkNew(shippingCompany.created_at) }}</span>
                  </p>
                  <div
                    v-if="shippingCompany.status"
                  >
                    <p class="ma-0 caption text-secondary">
                      Status: <span
                        class="text-capitalize"
                        :style="shippingCompany.status === 'COLLABORATE' ? 'color: #2FA841' : 'color: #EF3434'"
                      >
                        {{ shippingCompany.status }}
                      </span>
                    </p>
                  </div>
                </div>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </div>

        <div v-else class="px-10">
          <p class="caption my-4">
            {{ $t('lspCustomer.no_sc') }}
          </p>
          <div class="text-center py-5">
            <h4 class="mb-5">
              {{ $t('lspCustomer.sc_empty') }}
            </h4>
            <p class="grey--text font-weight-medium">
              {{ $t('lspCustomer.sc_empty_text') }}
            </p>
          </div>
        </div>
      </div>
    </v-container>
  </v-navigation-drawer>
</template>

<script lang="ts">
import Vue from 'vue'
import FilterMenu from '~/components/FilterMenu.vue'
import { ShippingCompany } from '~/types/user'
import { styleContainerCollaboration, checkIsNew } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export default Vue.extend({
  name: 'CustomerSideBar',

  components: { FilterMenu },

  props: {
    drawer: {
      type: Boolean,
      default: true
    },
    isPageMounted: {
      type: Boolean,
      default: false
    },
    shipmentCompanies: {
      type: Array as () => Array<ShippingCompany>,
      default: () => []
    },
    isLoadingShipmentCompanies: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    toggleButton: null as number | null,
    searchKey: '',
    dialogLinkRegistration: false,
    registerToken: '',
    scRegisterDialog: false,
    defaultSortColumn: 'name',
    defaultSortType: 'asc',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    filterColumnItems: {
      name: {
        label: 'Status',
        value: 'status'
      }
    },
    filterTypeItems: {
      all: {
        label: 'All',
        value: 'COLLABORATE|BLOCK'
      },
      collaborate: {
        label: 'Collaborate',
        value: 'COLLABORATE'
      },
      block: {
        label: 'Block',
        value: 'BLOCK'
      }
    }
  }),

  computed: {
    dataSc (): ShippingCompany[] | null {
      return this.$store.getters['shipping-company/data'].items
    },
    isLoading () {
      return this.$store.getters['shipping-company/isLoading']
    },
    dataDetailSc (): ShippingCompany {
      return this.$store.getters['shipping-company/dataItem'].item
    },
    isLoadingRegisterLink (): Boolean {
      return this.$store.getters[
        'logistic-service-provider/isLoadingRegisterLink'
      ]
    },
    registerLink () {
      return this.$store.getters['logistic-service-provider/registerLink']
    }
  },

  watch: {
    isPageMounted (isMounted) {
      if (isMounted) {
        this.getDetailScByQuery()
      }
      this.$emit('on-page-mounted', false)
    },
    dataDetailSc () {
      this.$emit('on-get-detail-sc', this.dataDetailSc)
    },
    toggleButton (shippingCompany: ShippingCompany | undefined) {
      if (shippingCompany === undefined) {
        return
      }

      if (this.dataDetailSc?.id !== shippingCompany.id) {
        this.getDetailSc(shippingCompany.id)
      }
    }
  },

  mounted () {
    this.getDataSc({})
    this.getDetailScByQuery()
  },

  methods: {
    getDetailScByQuery () {
      if (this.$route.query.id) {
        const idSc = this.$route.query.id as string
        this.getDetailSc(idSc)
      }
    },

    onFilterChange ({
      sortColumn,
      sortType
    }: any) {
      this.$emit('on-filter-change', {
        sortColumn,
        sortType
      })
    },

    onRegisterCompleted () {
      this.getDataSc({})
      this.scRegisterDialog = false
    },

    setRegisterToken () {
      const registerLink =
        'https://' +
        this.$store.getters['logistic-service-provider/registerLink']

      // Parse the URL and get the search params
      const searchParams = new URLSearchParams(new URL(registerLink).search)

      // Get the token value
      const token = searchParams.get('token')

      this.registerToken = token || ''
    },

    styleContainerCollaboration (status: string): string {
      return styleContainerCollaboration(status)
    },

    async getDataSc ({
      page = 1,
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc',
        filterColumn: 'status',
        filterType: 'COLLABORATE|BLOCK'
      }
    }) {
      const response = (await this.$store.dispatch(
        'shipping-company/getItems',
        {
          sortColumn: filter.sortColumn,
          sortType: filter.sortType,
          filterColumns: `logistics_service_provider_id,${filter.filterColumn}`,
          filterKeys: `${this.$auth.$state.user.data.logistics_service_provider_id},${filter.filterType}`,
          searchKey,
          page
        }
      )) as ShippingCompany[] | null

      if (response) {
        this.toggleButton = null
      }
    },

    getDetailSc (id: string | null) {
      const currentQuery = this.$route.query

      if (this.$route.query.id !== id) {
        this.$router.push({
          path: this.$route.path,
          query: {
            ...currentQuery,
            id
          }
        })
      }

      this.$store.dispatch('shipping-company/viewItem', {
        idSc: id
      })
    },

    getRegisterLink () {
      const baseUrl =
        this.$store.getters['logistic-service-provider/registerLink']

      navigator.clipboard.writeText(baseUrl).then(() => {
        toastSuccess('URL Copied to Clipboard', this)
      })
    },

    checkNew (date: string) {
      return checkIsNew(date)
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  border-radius: 4px !important;
  border: 1px solid #cfcccc !important;
}
</style>

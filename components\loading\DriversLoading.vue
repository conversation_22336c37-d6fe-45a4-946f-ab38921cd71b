<template>
  <v-row class="ma-n10 pa-5">
    <v-col v-for="i in 3" :key="i" class="pa-5 col-lg-4 col-sm-6 col-12">
      <v-sheet height="100" class="overflow-hidden pa-5">
        <div class="d-flex flex-row align-center">
          <v-skeleton-loader type="image" height="50" width="50" />
          <div class="d-flex flex-column" style="width: 100%">
           <v-skeleton-loader type="list-item-two-line" width="100%"/>
          </div>
        </div>
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'DriversLoading'
})
</script>

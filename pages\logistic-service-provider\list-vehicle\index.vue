<template>
  <v-container class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column" fluid>
    <v-dialog
      v-model="dialogListDo"
      max-width="540px"
      persistent
    >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex align-center justify-space-between">
        <h4>Vehicle Status</h4>

        <v-icon color="black" @click.stop="dialogListDo = false">
          mdi-close
        </v-icon>
      </v-container>

      <div class="mb-5 text-start">
        Plate Number <br>
       <h3 class="mt-3">{{ dataDetail?.plat_number }}</h3>
       On Shipment 
       <span v-if="dataDetail?.driver !== null"> 
        <v-icon color="grey" class="ml-1 mr-2" size="10">
         mdi-circle
      </v-icon></span>
      </div>

      <v-divider class="my-3" />

      <div>
        <p class="subtitle-1">Order Running</p>
        <div v-if="isLoading">
          <div>
            <v-skeleton-loader  type="text" />
            <v-skeleton-loader  type="text" />
          </div>
        </div>
        <div v-else>
          <div v-if="detailData?.active_orders?.length > 0">
            <v-col v-for="data in detailData.active_orders" class="pa-0" :key="data.track_id">
              <div class="d-flex justify-space-between">
                <div>
                  <p class="pa-0 ma-0">{{ data?.order_identity }}</p>
                  
                  <div class="d-flex align-center mt-1">
                    <v-chip
                      v-if="data?.status === 'PROPOSED'"
                      label
                      class="chip-secondary font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-secondary">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'REJECT'"
                      label
                      class="chip-danger font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-primary">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'DISPATCH'"
                      label
                      class="chip-orange font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-orange">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'FINISHED'"
                      label
                      class="chip-success font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-success">
                        {{ data?.status }}
                      </span>
                    </v-chip>
                    <v-chip
                      v-else-if="data?.status === 'ON_PROGRESS'"
                      label
                      class="chip-success font-weight-medium mr-2"
                      small
                    >
                      <span class="ma-0 caption text-info">
                        ON PROGRESS
                      </span>
                    </v-chip>
                    <v-chip v-else label class="chip-success font-weight-medium mr-2" small>
                      <span class="ma-0 caption text-info">
                        {{ data?.status }}
                      </span>
                    </v-chip>

                    <span><v-icon color="grey" class="ml-1 mr-2" size="10">
                      mdi-circle
                    </v-icon></span>
                    <span class="caption">
                      Reorder {{ data?.ritase_identity === '-' ? '1' : parseInt(data?.ritase_identity) + 1 }}
                    </span>
                  </div>
                </div>
                <div class="d-flex align-center">
                  <p class="mb-0">{{ $moment(data?.order_created).format('DD-MM-yyyy HH:mm') }}</p>
                  <p class="ml-3 mb-0" style="cursor: pointer" 
                    @click="$router.push(
                      localePath(
                        $auth.user?.data.role === 'VENDOR'
                        ? '/vendor/order-shipment/history-order/detail-history-order/' + `${data?.shipment_id}`
                        : '/logistic-service-provider/order-shipment/history-order/detail-history-order/' + `${data?.shipment_id}`
                      )
                    )"
                  >
                    <v-icon color="red">mdi-chevron-right</v-icon>
                  </p>
                </div>
              </div>
            </v-col>
          </div>
          <div class="mb-2" v-else>
            Empty
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
    <header-datatable
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      default-sort-column="name"
      default-sort-type="asc"
      @on-filter-change="getShippingVehicle({ filter: $event })"
      @on-search-icon-click="getShippingVehicle({ searchKey: $event })"
    />
    <vehicles-loading v-if="isLoadingShippingStatusVehicle"/>
    <v-col v-else class="pa-0">
      <v-col
        v-for="item in dataVendorVehicles.items"
        :key="item.vendor_id"
        class="col-12 px-0 py-4"
      >
        <v-card outlined class="pa-5 overflow-visible">
          <div class="d-flex justify-space-between">
            <h4>
              {{ item?.vendor_name }}
            </h4>

            <div class="d-flex" style="column-gap: 24px">
              <div>
                <h5 class="text-secondary">Total</h5>
                <h5 class="text-center">{{ item?.count_all }}</h5>
              </div>

              <div>
                <h5 class="text-secondary">Idle</h5>
                <h5 class="text-center">{{ item?.count_iddle }}</h5>
              </div>

              <div>
                <h5 class="text-secondary">On Shipment</h5>
                <h5 class="text-center">{{ item?.count_on_shipment }}</h5>
              </div>
            </div>
          </div>
          <div class="overflow-y-auto overflow-x-hidden mt-1" style="height: 220px">
            <div class="pa-2">
              <p class="ml-2 subtitle-1">Plate Number</p>
              <div class="d-flex">
                <div class="d-flex align-center ml-2">
                <v-badge :color="'black'" class="mr-3 mt-1" dot />
                  <span>On Shipment</span>
                  </div>
                  <div class="d-flex align-center ml-4">
                  <v-badge :color="'red'" class="mr-3 mt-1" dot />
                  <span> Idle</span>
                </div>
              </div>
            </div>
            <v-row v-if="item.vehicles.length !== 0" class="pa-5">
              <div
                v-for="(vehicle, index) in item.vehicles"
                :key="index"
                class="column pa-2 ma-1 my-1 text-center caption"
                @click="vehicle.on_shipment && (dialogListDo = true); clickDetail(vehicle); getVehicleDetail()"
                style="
                  border: 1px solid #6E6666;
                  border-radius: 4px;
                "
              >
              <v-chip v-if="vehicle?.fms_identity" label x-small class="chip-success mb-2 font-weight-medium">
                <p class="ma-0 subtitle-2 text-info">
                  FMS Synced
                </p>
              </v-chip>
                <h5 :style="vehicle.on_shipment ? 'color: #000000' : 'color: #EF3434'">
                  {{ vehicle.plat_number?.toUpperCase() }}
                </h5>
              </div>
            </v-row>
            <v-col v-else class="d-flex justify-center">
              <div class="text-center mt-10">
                <h4>{{ $t('vehicleCard.plate_empty_title') }}</h4>
                <p>{{ $t('vehicleCard.plate_empty_text') }}</p>
              </div>
            </v-col>
          </div>
        </v-card>
      </v-col>
    </v-col>
    <v-row class="mt-4 mb-4" justify="end">
      <v-pagination
        v-model="dataVendorVehicles.page"
        :length="dataVendorVehicles.totalPage"
        :total-visible="5"
        @input="getShippingVehicle({ page: $event })"
      />
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import VehiclesLoading from '~/components/loading/VehiclesLoading.vue'
import VehicleCardItem from '~/components/VehicleCardItem.vue'
import { VehicleDetail } from '~/types/vehicle';

export default Vue.extend({
  name: 'VehicleManagementPage',
  components: {
    VehicleCardItem,
    VehiclesLoading
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    dialogListDo: false,
    dataDetail: null as any,
    searchKey: '',
    sortType: 'asc',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
  }),

  computed: {
    dataVendorVehicles () {
      return this.$store.getters['dashboard/dataVendorVehicles']
    },

    isLoadingShippingStatusVehicle () {
      return this.$store.getters['dashboard/isLoadingShippingStatusVehicle']
    },

    detailData (): VehicleDetail {
      return this.$store.getters['vehicle/details/detailData']
    },

    isLoading (): boolean {
      return this.$store.getters['vehicle/details/isLoading']
    },
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Vehicle')

    this.getShippingVehicle({})
  },

  methods: {
    clickDetail (vehicle: any) {
      this.dataDetail = vehicle
    },
    getShippingVehicle ({
      page = 1,
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('dashboard/getShippingStatusVehicleDetail', {
        searchKey,
        page
      })
    },

    async getVehicleDetail () {
      await this.$store.dispatch('vehicle/details/getVehicleDetail', {
        id: this.dataDetail?.vehicle_detail_id + '/active-order'
      })
    },
  }
})
</script>


<style lang="scss" scoped>
.chip-success {
  background: #eaf6ec !important;
}

.chip-orange {
  background: #ffdfaa !important;
}

.chip-danger {
  background: #fde0e0 !important;
}

.chip-info {
  background: #e6f4f8 !important;
}
</style>

<template>
  <v-form ref="form" v-model="isValid" style="width: 100%">
    <div>
      <h1 class="mb-2">
        {{ $t('scRegistration.registration') }}
      </h1>
      <p class="body-1 text-secondary mb-10">
        {{ $t('scRegistration.welcome_text') }}
      </p>
    </div>

    <custom-text-field
      v-model="form.companyName"
      :hint="$t('scRegistration.label_company_name')"
      :rules="[rulesRequired]"
    />

    <custom-text-field
      v-model="form.userName"
      :hint="$t('scRegistration.label_user_name')"
      :rules="[rulesRequired]"
    />

    <v-row class="d-flex ma-0">
      <v-col class="d-flex pa-0 col-sm-4 col-5">
        <v-combobox
          v-model="form.phoneCountryCode"
          :items="phoneCountryCode"
          prefix="+"
          outlined
          class="mr-5"
          :rules="[rulesRequired]"
          @keydown="$event.target.blur()"
          @keypress="$event.target.blur()"
          @keyup="$event.target.blur()"
          @change="form.phoneCountryCode = $event"
        />
      </v-col>
      <v-col class="pa-0">
        <custom-text-field
          v-model="form.phoneNumber"
          type="number"
          hint="8321 XXX XXX XX"
          :rules="[rulesRequired, rulesPhoneMaxDigit, rulesFirstZeroPhoneNumber]"
        />
      </v-col>
    </v-row>

    <custom-text-field
      v-model="form.email"
      prepend-inner-icon="mdi-email"
      hint="Email"
      :rules="[rulesRequired, rulesEmail]"
    />

    <custom-text-field
      v-model="form.password"
      prepend-inner-icon="mdi-lock"
      hint="Password"
      :type="isShowPassword ? 'text' : 'password'"
      :append-icon="isShowPassword ? 'mdi-eye' : 'mdi-eye-off'"
      :rules="[rulesRequired, rulesPasswordMinLength]"
      @click:append="isShowPassword = !isShowPassword"
    />

    <custom-text-field
      v-model="form.passwordConfirmation"
      prepend-inner-icon="mdi-lock"
      :hint="$t('scRegistration.label_confirm_password')"
      :type="isShowPasswordConfirmation ? 'text' : 'password'"
      :append-icon="isShowPasswordConfirmation ? 'mdi-eye' : 'mdi-eye-off'"
      :rules="[rulesRequired, rulesPasswordConfirmation]"
      @click:append="isShowPasswordConfirmation = !isShowPasswordConfirmation"
    />

    <v-btn
      :loading="isLoadingForm"
      block
      x-large
      elevation="0"
      color="primary"
      class="text-capitalize"
      :disabled="!isValid"
      @click="onClickRegister"
    >
      {{ $t('scRegistration.submit_button') }}
    </v-btn>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '~/components/CustomTextField.vue'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'FormUserRegistration',

  components: { CustomTextField },

  props: {
    token: {
      type: String || null,
      default: null
    }
  },

  data: () => ({
    isValid: false,
    isShowPassword: false,
    isShowPasswordConfirmation: false,
    phoneCountryCode: [
      '1', // USA/Canada
      '7', // Russia
      '20', // Egypt
      '27', // South Africa
      '30', // Greece
      '31', // Netherlands
      '32', // Belgium
      '33', // France
      '34', // Spain
      '36', // Hungary
      '39', // Italy
      '40', // Romania
      '41', // Switzerland
      '43', // Austria
      '44', // UK
      '45', // Denmark
      '46', // Sweden
      '47', // Norway
      '48', // Poland
      '49', // Germany
      '51', // Peru
      '52', // Mexico
      '54', // Argentina
      '55', // Brazil
      '56', // Chile
      '57', // Colombia
      '60', // Malaysia
      '61', // Australia
      '62', // Indonesia
      '63', // Philippines
      '64', // New Zealand
      '65', // Singapore
      '66', // Thailand
      '81', // Japan
      '82', // South Korea
      '84', // Vietnam
      '86', // China
      '90', // Turkey
      '91', // India
      '92', // Pakistan
      '93', // Afghanistan
      '94', // Sri Lanka
      '95', // Myanmar
      '98', // Iran
      '212', // Morocco
      '213', // Algeria
      '216', // Tunisia
      '218', // Libya
      '220', // Gambia
      '221', // Senegal
      '234', // Nigeria
      '254', // Kenya
      '261', // Madagascar
      '351', // Portugal
      '352', // Luxembourg
      '353', // Ireland
      '358', // Finland
      '359', // Bulgaria
      '370', // Lithuania
      '371', // Latvia
      '372', // Estonia
      '380', // Ukraine
      '420', // Czech Republic
      '421', // Slovakia
      '852', // Hong Kong
      '853', // Macau
      '855', // Cambodia
      '856', // Laos
      '880', // Bangladesh
      '886', // Taiwan
      '960', // Maldives
      '961', // Lebanon
      '962', // Jordan
      '966', // Saudi Arabia
      '971', // UAE
      '972', // Israel
      '974', // Qatar
      '977', // Nepal
      '998' // Uzbekistan
    ],
    form: {
      companyName: '',
      userName: '',
      phoneCountryCode: '',
      phoneNumber: '',
      email: '',
      password: '',
      passwordConfirmation: ''
    }
  }),

  computed: {
    isLoadingForm (): boolean {
      return this.$store.getters['shipping-company/isLoadingForm'] || this.$store.getters['register/isLoadingForm']
    },

    messagingToken (): string {
      return this.$store.getters['firebase/messagingToken']
    },

    lspDomain (): string {
      return this.$store.getters.domain
    }
  },

  methods: {
    onClickRegister () {
      if (this.token) {
        this.registerScWithToken()
      } else {
        this.registerScWithoutToken()
      }
    },

    async registerScWithToken () {
      const response = await this.$store.dispatch('register/submitRegisterShipmentCompany', {
        lspDomain: this.lspDomain,
        companyName: this.form.companyName,
        userName: this.form.userName,
        phoneCountryCode: this.form.phoneCountryCode,
        phoneNumber: this.form.phoneNumber,
        email: this.form.email,
        password: this.form.password,
        passwordConfirmation: this.form.passwordConfirmation,
        token: this.token
      })

      if (response) {
        this.$refs.form as HTMLFormElement

        await this.$store.dispatch('login/submitLogin', {
          email: this.form.email,
          password: this.form.password,
          domain: this.lspDomain,
          fcmToken: this.messagingToken
        })
      }
    },

    async registerScWithoutToken () {
      const response = await this.$store.dispatch('shipping-company/createItem', {
        lspDomain: this.lspDomain,
        companyName: this.form.companyName,
        userName: this.form.userName,
        phoneCountryCode: this.form.phoneCountryCode,
        phoneNumber: this.form.phoneNumber,
        email: this.form.email,
        password: this.form.password,
        passwordConfirmation: this.form.passwordConfirmation
      })

      if (response) {
        const form = this.$refs.form as HTMLFormElement
        form.reset()

        this.$emit('on-register-completed')
      }
    },
    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    },
    rulesPhoneMaxDigit (value: string) {
      return rules.phoneMaxDigit(value)
    },
    rulesFirstZeroPhoneNumber (value: string) {
      return rules.firstZeroPhoneNumber(value)
    },
    rulesPasswordMinLength (value: string) {
      return rules.passwordMinLength(value)
    },
    rulesPasswordConfirmation (value: string) {
      return rules.passwordConfirm(value, this.form.password)
    }
  }

})
</script>

<style scoped>

</style>

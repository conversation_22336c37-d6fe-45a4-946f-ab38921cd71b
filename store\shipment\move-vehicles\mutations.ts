import { MutationTree } from 'vuex'
import { MoveVehiclesState } from './state'

export const mutations: MutationTree<MoveVehiclesState> = {
  SET_ORDER (state, order) {
    state.order = order
  },

  SET_NEW_ORDER_IDENTITY (state, newOrderIdentity) {
    state.newOrderIdentity = newOrderIdentity
  },

  SET_TRACKS (state, tracks) {
    state.selectedTracks = [...tracks]
  },

  ADD_ADDITIONAL_VEHICLE (state, additionalVehicle) {
    state.selectedAdditionalVehicles = [...state.selectedAdditionalVehicles, additionalVehicle]
  },

  SET_SUBORDERS (state, suborders) {
    state.suborders = suborders
  },

  SET_MOVETYPE (state, moveType) {
    state.moveType = moveType
  },

  SET_SHIPPING_COMPANY_ID (state, selectedShippingCompanyId) {
    state.selectedShippingCompanyId = selectedShippingCompanyId
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  RESET_STATE (state) {
    Object.assign(state, {
      moveType: undefined,
      order: undefined,
      newOrderIdentity: undefined,
      selectedTracks: [],
      selectedAdditionalVehicles: [],
      suborders: [],
      selectedShippingCompanyId: undefined,
      isLoadingForm: false
    })
  }
}

export default mutations

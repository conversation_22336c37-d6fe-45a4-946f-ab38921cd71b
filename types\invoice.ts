import { Shipment } from '~/types/shipment'
import { VehicleDetail } from '~/types/vehicle'
import { Order } from '~/types/product'

export interface Fee {
  id: string
  description: null
  cost: string | null
  invoice_id: string
  invoice_detail_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
}

export interface Invoice {
  id: string
  invoice_from: string
  status: string
  shipment_id: string
  created_by_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  read_date: Date | null
  fees: Fee[]
  shipment?: Shipment
}

export interface InvoiceDetail {
  id: string
  cost: string | null,
  total_odometer: number
  invoice_id: string
  invoice?: Invoice
  order_id: string
  order: Order
  vehicle_detail_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  vehicle_detail?: VehicleDetail
  fees: Fee[]
}

export interface InvoiceOrder {
  invoice: Invoice
  orders?: Order[]
  invoice_details?: InvoiceDetail[]
}

import { GetterTree, ActionTree, MutationTree } from 'vuex'
import { toastSuccess } from '~/utils/toasts'
import { LogisticsServiceProvider } from '~/types/user'
import { exceptionHandler } from '~/utils/functions'

// eslint-disable-next-line no-use-before-define
export type VuexState = ReturnType<typeof state>

export const state = () => ({
  isLoading: true,
  isLoadingForm: false,
  items: [] as Array<LogisticsServiceProvider>,
  totalPage: 1,
  page: 1
})

export const getters: GetterTree<VuexState, VuexState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export const mutations: MutationTree<VuexState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }
}

export const actions: ActionTree<VuexState, VuexState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/logistics-service-providers', {
      params: {
        search_columns: 'name',
        search_key: payload.searchKey,
        sort_column: payload.sortColumn,
        sort_type: payload.sortType,
        filter_columns: payload.filterColumns == null ? 'vendor' : payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page,
        entries: 9
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  createItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.companyName)
    formData.append('address', payload.companyAddress)
    formData.append('domain', payload.companyDomain)
    formData.append('user_name', payload.name)
    formData.append('user_phone_number', payload.phone)
    formData.append('user_phone_country_code', '62')
    formData.append('user_email', payload.email)
    formData.append('user_password', payload.password)
    formData.append('user_password_confirmed', payload.passwordConfirmation)

    if (payload.companyLogo) {
      formData.append('logo', payload.companyLogo)
    }

    this.$axios.post('/v1/logistics-service-providers', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, searchKey: '' })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  editItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    // formData.append('logo', payload.companyLogo)
    formData.append('name', payload.name)
    formData.append('address', payload.address)
    formData.append('domain', payload.domain)
    formData.append('_method', 'PUT')

    this.$axios.post('/v1/logistics-service-providers/' + payload.id, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, searchKey: '' })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  deleteItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios.delete('/v1/logistics-service-providers/' + payload.id).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, searchKey: '' })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  async proposeCollaboration ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('/v1/logistics-service-providers/' + payload.idLSP + '/proposed').then((response: any) => {
      toastSuccess(response.data.message, this)
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }
}

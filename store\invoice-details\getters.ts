import { GetterTree } from 'vuex'
import { InvoiceDetailsState } from './state'

export const getters: GetterTree<InvoiceDetailsState, InvoiceDetailsState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  detailData (state) {
    return state.item
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  },

  isLoadingDownloadInvoice (state) {
    return state.isLoadingDownloadInvoice
  },

  blobInvoice (state) {
    return state.blobInvoice
  }
}

export default getters

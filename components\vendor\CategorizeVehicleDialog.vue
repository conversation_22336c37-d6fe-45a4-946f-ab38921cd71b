<template>
  <v-dialog v-model="dialog" persistent>
    <template #activator="{ on, attrs }">
      <slot name="activator" v-bind="attrs" v-on="on" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-card-title class="pa-0 mb-6 d-flex justify-space-between">
        <h4 v-if="categorizeFMS">
          {{ $t('vendorVehicles.data_vehicle_form_fms') }}
        </h4>
        <h4 v-else>
          {{ $t('vendorVehicles.uncategorized_plate_number') }}
        </h4>

        <v-icon
          color="black"
          @click="$emit('on-close-dialog')"
        >
          mdi-close
        </v-icon>
      </v-card-title>
      <unsync-categorize-vehicle
        v-if="selectedAction === 'unsync'"
        :type="type"
        :vehicles="vehicles"
        button-label="Apply"
        @on-click-cancel="$emit('on-close-dialog')"
        @on-close-dialog="$emit('on-close-dialog')"
    />
      <form-categorize-vehicle
         v-else
        :type="type"
        :vehicles="vehicles"
        button-label="Apply"
        :is-selected-category="true"
        @on-success-add-vehicle-detail="$emit('on-success-add-vehicle-detail')"
        @on-success-delete-vehicle-detail="$emit('on-success-delete-vehicle-detail')"
        @on-click-cancel="$emit('on-close-dialog')"
      />
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Vehicle } from '~/types/vehicle'
import FormCategorizeVehicle from '~/components/FormCategorizeVehicle.vue'
import UnsyncCategorizeVehicle from '~/components/UnsyncCategorizeVehicle.vue'

export default Vue.extend({
  name: 'CategorizeVehicleDialog',

  components: { FormCategorizeVehicle, UnsyncCategorizeVehicle },

  props: {
    type: {
      type: String,
      default: ''
    },
    dialog: {
      type: Boolean,
      default: false
    },
    categorizeFMS: {
      type: Boolean,
      default: false
    },
    selectedAction: {
      type: String,
      default: ''
    }
  },

  data: () => ({
    isValid: false,
    isSelectedAll: false,
    searchKey: '',
    selectedFmsVehicle: [] as number[],
    selectedDetailVehicle: [] as any[],
    selectedVehicle: null,
    appliedVehicle: [] as { vehicle_id: string, vehicle_name: string, plate_numbers: { id: string, value: string }[] }[],
    selectedIndexVehicle: -1
  }),
  computed: {
    vehicles (): Vehicle[] {
      return this.$store.getters['vehicle/data'].items
    }
  }
})
</script>

<style></style>

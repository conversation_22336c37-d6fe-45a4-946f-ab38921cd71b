import { MutationTree } from 'vuex'
import { PlaybackState } from './state'

export const mutations: MutationTree<PlaybackState> = {

  SET_RESULT (state, response: any) {
    state.items = response.data
  },

  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },
  SET_POI_RESULT (state, response: any) {
    state.poiItems = response.data
  },

  SET_POI_ITEMS (state, items: any) {
    state.poiItems = items
  },

  SET_IS_LOADING_POI (state, isLoadingPoi) {
    state.isLoadingPoi = isLoadingPoi
  }
}

export default mutations

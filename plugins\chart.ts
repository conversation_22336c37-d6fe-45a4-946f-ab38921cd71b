import Vue from 'vue'
import { <PERSON>, Pie, Bar, Doughnut } from 'vue-chartjs/legacy'
import ChartDataLabels from 'chartjs-plugin-datalabels'
import zoomPlugin from 'chartjs-plugin-zoom'
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  LineElement,
  LinearScale,
  CategoryScale,
  PointElement,
  Filler,
  BarController,
  LineController
} from 'chart.js'

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  LineElement,
  LinearScale,
  CategoryScale,
  PointElement,
  Filler,
  ChartDataLabels,
  zoomPlugin,
  BarController,
  LineController
)

Vue.component('Line<PERSON>hart', Line)

Vue.component('Pie<PERSON><PERSON>', Pie)

Vue.component('<PERSON><PERSON><PERSON>', Bar)

Vue.component('<PERSON><PERSON><PERSON><PERSON><PERSON>', Doughnut)

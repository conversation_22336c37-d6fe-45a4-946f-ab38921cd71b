<template>
  <v-dialog
    v-model="dialog"
    width="540"
    persistent
    style="z-index: 99999"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <div class="d-flex justify-space-between mb-10">
        <h4 v-if="status === 'COLLABORATE'">
          Block {{ typeLabel }}
        </h4>
        <h4 v-else>
          Unblock {{ typeLabel }}
        </h4>
        <v-icon color="black" @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </div>

      <p class="body-1 text--secondary mb-10 text-center">
        {{ $t('blockDialog.confirm_collaborate_text') }} {{ status === 'COLLABORATE' ? 'block' : 'unblock' }} <span class="subtitle-1 black--text">{{ name }}</span>  Vendor?
      </p>
      <v-row class="d-flex ma-0">
        <v-col class="pa-0 mr-5">
          <v-btn
            color="primary"
            elevation="0"
            x-large
            class="text-capitalize"
            block
            :loading="status === 'COLLABORATE' ? isLoadingForm.reject : isLoadingForm.accept"
            @click="onClickYes"
          >
            <p
              v-if="status === 'COLLABORATE'"
              class="subtitle-1 ma-0"
            >
              {{ $t('blockDialog.approve_block') }}
            </p>
            <p
              v-else
              class="subtitle-1 ma-0"
            >
              {{ $t('blockDialog.approve_unblock') }}
            </p>
          </v-btn>
        </v-col>

        <v-col class="pa-0">
          <v-btn
            color="primary"
            elevation="0"
            outlined
            x-large
            class="text-capitalize"
            block
            @click="$emit('on-close-dialog')"
          >
            {{ $t('blockDialog.reject') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'BlockDialog',

  props: {
    typeLabel: {
      type: String,
      required: true
    },
    name: {
      type: String,
      default: '',
      required: true
    },
    status: {
      type: String,
      default: '',
      required: true
    },
    isLoadingForm: {
      type: Object,
      default: () => ({ accept: false, reject: false })
    },
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({ }),

  methods: {
    onClickYes () {
      this.$emit('on-click-yes')
    }
  }
})
</script>

<style lang="scss" scoped> </style>

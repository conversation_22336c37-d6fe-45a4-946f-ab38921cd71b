<template>
  <v-container
    fluid
    class="pa-5 pa-sm-10 d-flex fill-height"
  >
    <div class="ma-auto d-flex flex-column align-center" style="width: 600px">
      <animation-component
        :animation-data="require('~/assets/animations/success.json')"
        class="mb-10"
      />

      <h4>
        You have successfully Created New Password.
      </h4>
      <p class="body-1 text-secondary mb-10 text-center mt-2">
        You can already login into system with new password. login LSI right now.
      </p>

      <div>
        <v-btn
          height="52"
          color="primary"
          block
          class="text-capitalize"
          @click="$router.push('/login')"
        >
          Login
        </v-btn>
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import AnimationComponent from '~/components/AnimationComponent.vue'

export default Vue.extend({
  name: 'SuccessChangePasswordPage',

  components: { AnimationComponent }

})
</script>

<style scoped>

</style>

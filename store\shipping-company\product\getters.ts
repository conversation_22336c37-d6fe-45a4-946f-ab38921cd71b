import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { ShipmentCompanyProductState } from './state'
import { ImportKey } from '~/types/import-key'

export const getters: GetterTree<ShipmentCompanyProductState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },
  isLoading (state) {
    return state.isLoading
  },
  isLoadingForm (state) {
    return state.isLoadingForm
  },
  isLoadingFormClearImage (state) {
    return state.isLoadingFormClearImage
  },
  dataItemsProductKey (state): ImportKey[] {
    return state.itemsProductKey
  }
}

export default getters

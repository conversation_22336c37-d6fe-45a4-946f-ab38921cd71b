<template>
  <v-container class="white rounded px-10 mb-10 mx-10" fluid>
    <v-container class="pa-2 ma-2">
      <h2>Move</h2>
      <h2 class="red--text">
        {{ moveVehiclesOrder?.identity }}
      </h2>
      <v-col cols="3" class="pa-0 ma-0 mt-5">
        <h3>To New Order</h3>
        <v-text-field v-model="newIdentityOrder" class="mt-2 cols-3" label="Order Number" outlined/>
      </v-col>
    </v-container>

    <v-container fluid>
      <div style="display: flex; align-items: center; column-gap: 8px">
        <h4>Pickup</h4>
        <v-btn
          elevation="0"
          fab
          outlined
          small
          style="border-color: #CFCCCC; border-radius: 4px"
          @click="addProductList('PICKUP')"
        >
          <v-icon>
            mdi-plus
          </v-icon>
        </v-btn>
      </div>
      <v-row class="d-flex justify-space-between mt-2">
        <v-col>
          <v-row
            v-for="i in form.products.pickup.length"
            :key="i"
            class="ma-0"
          >
            <v-col class="mr-5 pa-0">
              <v-combobox
                v-model="form.products.pickup[i - 1].product"
                :items="
                  dataProduct.map(p => {
                    return { text: p.name, value: p.id, pivot: { quantity: 0 }}
                  })
                "
                :label="$t('scCreateOrderDialog.label_product')"
                clearable
                hide-details
                outlined
                @change="onSelectProduct($event, i - 1, 'PICKUP')"
              >
                <template #item="{ item }">
                  <v-list-item-content class="d-flex">
                    <v-list-item-title>{{ item.text }}</v-list-item-title>
                  </v-list-item-content>
                </template>
              </v-combobox>
            </v-col>

            <v-col class="pa-0 col-3">
              <FormattedNumberInput
                :value="form.products.pickup[i - 1]?.pivot.quantity"
                append-icon="mdi-plus"
                class="centered-input inputPrice"
                prepend-inner-icon="mdi-minus"
                @input="setProductQuantityValue(i - 1, $event, 'PICKUP')"
                @click:prepend-inner="setProductQuantity(i - 1, 'SUBTRACT', 'PICKUP')"
                @click:append="setProductQuantity(i - 1, 'ADD', 'PICKUP')"
              />

              <p class="ma-0 pl-3 py-2 caption">
                {{ form.products.pickup[i - 1]?.product?.unit }}
              </p>
            </v-col>

            <v-col
              v-if="i > 1"
              class="ml-5 pa-0 col-auto"
            >
              <v-btn
                elevation="0"
                fab
                outlined
                style="border-color: #CFCCCC; border-radius: 4px"
                @click="removeProductList(i - 1, 'PICKUP')"
              >
                <v-icon>
                  mdi-close
                </v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row class="ma-0 d-flex" style="column-gap: 8px; justify-content: space-between;">
            <v-col class="pa-0">
              <v-combobox
                v-model="form.location.pickup"
                :items="
                  dataLocation.map(l => {
                    return { text: l.name, value: l.id, latitude: l.latitude, longitude: l.longitude }
                  })
                "
                :label="$t('scCreateOrderDialog.label_location')"
                :loading="isLoadingLocation"
                class="mb-1"
                clearable
                hide-details
                outlined
                @change="selectLocation"
              />
            </v-col>
            <v-col class="pa-0">
              <div class="ml-3">
                <v-menu
                  ref="menu"
                  v-model="menuDateRange.pickup"
                  :close-on-content-click="false"
                  max-width="250"
                  offset-y
                  transition="slide-y-transition"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="form.dateTimeEstimation.pickup.date"
                      :label="$t('scCreateOrderDialog.label_date')"
                      append-icon="mdi-calendar-range"
                      clearable
                      outlined
                      v-bind="attrs"
                      v-on="on"
                    >
                      Select Date Range
                    </v-text-field>
                  </template>

                  <v-date-picker
                    v-model="form.dateTimeEstimation.pickup.date"
                    color="primary"
                    no-title
                    @input="menuDateRange.pickup = false"
                  />
                </v-menu>
              </div>
            </v-col>

            <v-col class="pa-0">
              <vue-timepicker
                :placeholder="$t('scCreateOrderDialog.label_time_drop_off')"
                :value="form.dateTimeEstimation.pickup.time"
                auto-scroll
                close-on-complete
                drop-direction="up"
                fixed-dropdown-button
                input-class="vtimepicker"
                input-width="100%"
                manual-input
                style="z-index: 1000;"
                @input="form.dateTimeEstimation.pickup.time = $event;"
              >
                <template #dropdownButton>
                  <v-icon>mdi-clock-time-four-outline</v-icon>
                </template>
              </vue-timepicker>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>

    <v-container fluid>
      <div style="display: flex; align-items: center; column-gap: 8px">
        <h4>Dropoff</h4>
      </div>
      <v-row class="d-flex justify-space-between mt-2">
        <v-col>
          <v-row
            v-for="i in form.products.dropoff.length"
            :key="i"
            class="ma-0"
          >
            <v-col class="mr-5 pa-0">
              <v-combobox
                v-model="form.products.dropoff[i - 1].product"
                :items="
                  form.products.pickup.map(p => {
                    return { text: p.product?.text, value: p.product?.value, pivot: { quantity: p.pivot.quantity }}
                  })
                "
                :label="$t('scCreateOrderDialog.label_product')"
                clearable
                hide-details
                outlined
                @change="onSelectProduct($event, i - 1, 'DROPOFF')"
              >
                <template #item="{ item }">
                  <v-list-item-content class="d-flex">
                    <v-list-item-title>{{ item.text }}</v-list-item-title>
                  </v-list-item-content>
                </template>
              </v-combobox>
            </v-col>

            <v-col class="pa-0 col-3">
              <FormattedNumberInput
                :value="form.products.dropoff[i - 1]?.pivot.quantity"
                append-icon="mdi-plus"
                class="centered-input inputPrice"
                prepend-inner-icon="mdi-minus"
                @input="setProductQuantityValue(i - 1, $event, 'DROPOFF')"
                @click:prepend-inner="setProductQuantity(i - 1, 'SUBTRACT', 'DROPOFF')"
                @click:append="setProductQuantity(i - 1, 'ADD', 'DROPOFF')"
              />

              <p class="ma-0 pl-3 py-2 caption">
                {{ form.products.dropoff[i - 1]?.product?.unit }}
              </p>
            </v-col>

            <v-col
              v-if="i > 1"
              class="ml-5 pa-0 col-auto"
            >
              <v-btn
                elevation="0"
                fab
                outlined
                style="border-color: #CFCCCC; border-radius: 4px"
                @click="removeProductList(i - 1, 'DROPOFF')"
              >
                <v-icon>
                  mdi-close
                </v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row class="ma-0 d-flex" style="column-gap: 8px; justify-content: space-between;">
            <v-col class="pa-0">
              <v-combobox
                v-model="form.location.dropoff"
                :items="
                  dataLocation.map(l => {
                    return { text: l.name, value: l.id, latitude: l.latitude, longitude: l.longitude }
                  })
                "
                :label="$t('scCreateOrderDialog.label_location')"
                :loading="isLoadingLocation"
                class="mb-1"
                clearable
                hide-details
                outlined
                @change="selectLocation"
              />
            </v-col>
            <v-col class="pa-0">
              <div>
                <v-menu
                  ref="menu"
                  v-model="menuDateRange.dropoff"
                  :close-on-content-click="false"
                  max-width="250"
                  offset-y
                  transition="slide-y-transition"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="form.dateTimeEstimation.dropoff.date"
                      :label="$t('scCreateOrderDialog.label_date')"
                      append-icon="mdi-calendar-range"
                      clearable
                      outlined
                      v-bind="attrs"
                      v-on="on"
                    >
                      Select Date
                    </v-text-field>
                  </template>

                  <v-date-picker
                    v-model="form.dateTimeEstimation.dropoff.date"
                    color="primary"
                    no-title
                    @input="menuDateRange.dropoff = false"
                  />
                </v-menu>
              </div>
            </v-col>
            <v-col class="pa-0">
              <vue-timepicker
                :placeholder="$t('scCreateOrderDialog.label_time_drop_off')"
                :value="form.dateTimeEstimation.dropoff.time"
                auto-scroll
                close-on-complete
                drop-direction="up"
                fixed-dropdown-button
                input-class="vtimepicker"
                input-width="100%"
                manual-input
                style="z-index: 1000;"
                @input="form.dateTimeEstimation.dropoff.time = $event;"
              >
                <template #dropdownButton>
                  <v-icon>mdi-clock-time-four-outline</v-icon>
                </template>
              </vue-timepicker>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>

    <v-container class="pa-2 ma-2">
      <h2>Vehicle Selected</h2>
      <v-data-table
        :headers="tableVehicleSelected"
        :items="moveVehiclesTracks.map((e) => ({
          plate_number: '',
          vehicle_image_url: e.shipment_vendor?.vehicles?.[0]?.photo_url,
          vehicle_name: e.shipment_vendor?.vehicles?.[0]?.name,
          driver_name: e.driver?.user?.name,
          mill: e.routes?.filter((e) => e.type === 'PICKUP')[0]?.pickup_drop_off_location_point?.identity,
          refinery: e.routes?.filter((e) => e.type === 'DROPOFF')[0]?.pickup_drop_off_location_point?.identity,
        }))"
        hide-default-footer
      >

        <template #item.vehicle_name="{ item }">
          <div style="display: flex; column-gap: 4px; align-items: center;">
            <v-img :src="item.vehicle_image_url" contain height="24" width="24"/>
            <div>{{ item.vehicle_name }}</div>
          </div>
        </template>

      </v-data-table>

      <v-divider class=" mt-4 mb-2" />
      <div class="mt-8 d-flex align-center">
        <p class="mb-0 subtitle-1">
          Add other vehicle in new order (optional)
        </p>
        <v-col cols="3">
          <v-autocomplete
            v-if="!isLoadingVendor"
            v-model="selectedVendor"
            :items="dataVendor.items"
            clearable
            dense
            hide-details
            item-text="name"
            item-value="id"
            label="Select Vendor"
            outlined
            @blur="getVehicles($event)"
          >
            <template #item="data">
              <div>
                <p class="mb-1 mt-3">
                  {{ data.item.name }}
                </p>
                <p class="blue--text">
                  {{ data.item.total_vehicle_detail }} <span>Vehicles Owned</span>
                </p>
              </div>
            </template>
          </v-autocomplete>
        </v-col>
      </div>

      <v-container v-if="!isLoadingVendor" class="pa-0" fluid>
        <v-container v-if="selectedVendor" class="pa-0" fluid>
          <v-sheet v-if="isLoadingVehicleList" class="d-flex mt-5 mb-5 mx-2 overflow-hidden" height="80">
            <v-skeleton-loader class="mr-2" height="80" type="image" width="250"/>
            <v-skeleton-loader class="ml-2" height="80" type="image" width="250"/>
          </v-sheet>

          <v-row v-for="(vendor, vendorName) in groupedByVendor" v-else :key="vendorName">
            <v-col class="d-flex">
              <h3 class="pa-2 ma-2">
                {{ vendor.vendor.name }}
              </h3>
              <div class="col-5">
                <FormattedNumberInput
                  :key="vendorName"
                  v-model="weightValue[vendor.vendor.id]"
                  :show-kg="true"
                  append="KG"
                  class="centered-input inputPrice ml-5"
                  style="width: 60%;"
                  @input="weightVehicle(vendor, $event)"
                />
              </div>
            </v-col>
            <v-container class="d-flex mb-6">
              <v-row style="overflow-x: auto; flex-wrap: nowrap; -webkit-overflow-scrolling: touch;">
                <v-col v-for="item in vendor.vehicles" :key="item.id" class="col-lg-4 col-sm-6 col-12 mb-3">
                  <v-card
                    :style="selectedVehicle?.id === item.id ? 'border: 1px solid #EF3434' : ''"
                    class="pa-5"
                    elevation="0"
                    outlined
                    @click="selectVehicle(item)"
                  >
                    <v-row class="pa-2 ma-2">
                      <image-component
                        :image="item.photo_url"
                        class="mr-6"
                      />
                      <div class="mb-5">
                        <p class="subtitle-1 mb-1">
                          {{ item.name.length > 10 ? `${item.name.slice(0, 10)}...` : item.name }}
                        </p>
                        <p class="caption text-secondary mb-1">
                          {{
                            item.vehicle_type.name.length > 10 ? `${item.vehicle_type.name.slice(0, 10)}...` : item.vehicle_type.name
                          }}
                        </p>
                        <p class="caption text-secondary mb-1">
                          {{ item.vendor?.name }}
                        </p>
                        <p class="caption text-secondary ma-0">
                          Available:
                          <span class="font-weight-bold black--text">
                          {{ item.iddle_vehicle_detail_count }} unit
                        </span>
                        </p>
                      </div>
                    </v-row>
                    <v-divider class="mx-5"/>
                    <v-sheet class="pa-2 mt-3 d-flex align-center rounded v-sheet--outlined" height="60">
                      <div class="d-flex align-center justify-space-between col-12">
                        <v-btn
                          :disabled="item.vehicle_details_count === 0 || !item.quantity"
                          class="ma-0"
                          color="transparent"
                          elevation="0"
                          small
                          @click="decreaseQuantity(item)"
                        >
                          <v-icon>mdi-minus</v-icon>
                        </v-btn>

                        <div class="mx-2 subtitle-1 text-center" style="width: 30px">
                          {{ item.quantity ?? 0 }}
                        </div>

                        <v-btn
                          :disabled="item.iddle_vehicle_detail_count === 0 || item.quantity === item.iddle_vehicle_detail_count"
                          class="ma-0"
                          color="transparent"
                          elevation="0"
                          small
                          @click="increaseQuantity(item)"
                        >
                          <v-icon>mdi-plus</v-icon>
                        </v-btn>
                      </div>
                    </v-sheet>
                  </v-card>
                </v-col>
              </v-row>
            </v-container>
          </v-row>

          <p v-if="checkedVehicles.length > 0" class="body-1 text-info ml-2" @click="dialogSelectVehicle = true">
            {{ checkedVehicles.length + moveVehiclesTracks.length }} vehicle selected
          </p>
          <v-dialog
            v-model="dialogSelectVehicle"
            persistent
            width="480"
          >
            <v-card class="pa-md-10 pa-2">
              <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
                <h4>Detail Vehicle Selected</h4>

                <v-icon color="black" @click="dialogSelectVehicle = false">
                  mdi-close
                </v-icon>
              </v-card-title>
              <v-data-table
                :headers="tableHeaders"
                :items="checkedVehicles"
                hide-default-footer
              >
                <template #item.totalWeight="{ item }">
                  {{ item.vendor.weight?.toLocaleString('en-US') }}
                </template>
                <template #item.totalUnit="{ item }">
                  {{ item.quantity }}
                </template>
              </v-data-table>
              <div class="pa-0 ma-0 d-flex justify-space-between">
                <p class="subtitle-1 mt-3">
                  Total Weight
                </p>
                <p class="subtitle-1 mt-3 text-primary mr-3">
                  {{ formatNumber(totalWeight()) }} KG
                </p>
              </div>
            </v-card>
          </v-dialog>
          <!--          <p v-if="overLoad" class="mt-3">-->
          <!--            {{ overLoad }} <span class="red&#45;&#45;text">reduce load by {{ formatNumber(calculationOrder?.totalWeight) }} Kg!-->
          <!--          </span>-->
          <!--          </p>-->
          <!--          <p v-else>-->
          <!--            You have-->
          <!--            <span class="red&#45;&#45;text">-->
          <!--            {{-->
          <!--                formatNumber(totalWeightSelected())-->
          <!--              }} KG-->
          <!--          </span>-->
          <!--            of your order load-->
          <!--          </p>-->
        </v-container>
      </v-container>
      <!--      <p>-->
      <!--        You have not allocated <span class="red&#45;&#45;text">1000 KG</span> of your order load-->
      <!--      </p>-->


      <v-row class="mt-2 pa-2">
        <v-btn
          x-large
          elevation="0"
          color="primary"
          class="text-capitalize"
          @click="isShowDialogSubmitVehicle = true"
        >
          Move
        </v-btn>
        <v-btn
          x-large
          outlined
          elevation="0"
          color="primary"
          class="text-capitalize ml-3"
          @click="$router.back()"
        >
          Cancel
        </v-btn>

        <v-dialog
          v-model="isShowDialogSubmitVehicle"
          persistent
          width="550"
        >
          <v-card class="pa-md-10 pa-5">

            <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
              <h4>Move Vehicle</h4>

              <v-icon color="black" @click="isShowDialogSubmitVehicle = false">
                mdi-close
              </v-icon>
            </v-card-title>

            <div class="pa-8 ma-2" style="border: 1px solid #CFCCCC">
              <v-row>
                <v-col>
                  <p class="subtitle-1">
                    From
                  </p>
                  <p class="red--text subtitle-1">
                    {{ moveVehiclesOrder?.identity }}
                  </p>
                </v-col>
                <v-col>
                  <p class="subtitle-1">
                    To
                  </p>
                  <p class="red--text subtitle-1">
                    {{ newIdentityOrder }}
                  </p>
                </v-col>
              </v-row>

              <v-divider />

              <v-row class="mt-4">
                <v-col>
                  <p>
                    Vehicle will be moved
                  </p>
                  <p>
                    Additional Vehicles
                  </p>
                </v-col>
                <v-col>
                  <p class="subtitle-1">
                    {{ moveVehiclesTracks.length }} Vehicle
                  </p>
                  <p class="subtitle-1">
                    {{ checkedVehicles.length }} Vehicle
                  </p>
                </v-col>
              </v-row>
            </div>

            <div class="mt-2">
              <p class="subtitle-1">
                Upload DO Amandement
              </p>
              <v-file-input
                v-model="form.file"
                outlined
                persistent-hint
                label="File"
                hint="Format: .jpg, .jpeg, .pdf"
                prepend-icon="mdi-paperclip"
                accept=".jpg, .jpeg, .pdf"
              />
            </div>

            <div class="d-flex mt-5">
              <v-btn
                class="text-capitalize"
                color="primary"
                x-large
                :loading="isLoadingMove"
                depressed
                @click="onSubmitMove"
              >
                Save
              </v-btn>
              <v-btn
                class="ml-5 text-capitalize"
                color="primary"
                x-large
                depressed
                outlined
                @click="isShowDialogSubmitVehicle = false"
              >
                Cancel
              </v-btn>
            </div>
          </v-card>
        </v-dialog>
      </v-row>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Order, PickupDropOffLocationPoint, Product } from '~/types/product'
import FormattedNumberInput from '~/components/fields/FormattedNumberInput.vue'
import { Track } from '~/types/shipment'
import { Vendor } from '~/types/user'
import { Vehicle } from '~/types/vehicle'
import { formatNumber } from '~/utils/functions'

export default Vue.extend({
  name: 'MoveVehiclePage',
  components: { FormattedNumberInput },


  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    isShowDialogSubmitVehicle: false,
    tableVehicleSelected: [
      {
        text: 'No',
        value: 'no'
      },
      {
        text: 'Vehicle',
        value: 'vehicle_name'
      },
      {
        text: 'Plate Number',
        value: 'plate_number'
      },
      {
        text: 'Driver',
        value: 'driver_name'
      },
      {
        text: 'Mill',
        value: 'mill'
      },
      {
        text: 'Refinery',
        value: 'refinery'
      }
    ],
    newIdentityOrder: '',
    form: {
      products: {
        pickup: [{
          product: '',
          value: '',
          unit: '',
          pivot: {
            quantity: 0,
            maxQuantity: 0
          }
        },],
        dropoff: [{
          product: '',
          value: '',
          unit: '',
          pivot: {
            quantity: 0,
            maxQuantity: 0
          }
        }]
      },
      location: {
        pickup: undefined,
        dropoff: undefined
      },
      dateTimeEstimation: {
        pickup: {
          date: '',
          time: ''
        },
        dropoff: {
          date: '',
          time: ''
        }
      },
      file: undefined
    },
    menuDateRange: {
      pickup: false,
      dropoff: false
    },
    selectedProducts: [{}] as any,
    selectedLocation: null as any,
    lastSelectedLocation: {
      latitude: -6.9344694,
      longitude: 107.6049539
    } as any,
    selectedVendor: null as string[] | null,
    searchKeyVehicle: null as string | null,
    weightValue: {},
    dialogSelectVehicle: false as boolean,
    tableHeaders: [
      {
        text: 'Vendor',
        value: 'vendor.name'
      },
      {
        text: 'Total Weight',
        value: 'totalWeight'
      },
      {
        text: 'Vehicle',
        value: 'name'
      },
      {
        text: 'Total Unit',
        value: 'totalUnit'
      }
    ],
    overLoad: ''
  }),

  computed: {
    moveVehiclesOrder (): Order {
      return this.$store.getters['shipment/move-vehicles/order']
    },

    moveVehiclesTracks (): Track[] {
      return this.$store.getters['shipment/move-vehicles/selectedTracks']
    },

    moveType (): any {
      return this.$store.getters['shipment/move-vehicles/moveType']
    },

    isLoadingMove (): any {
      return this.$store.getters['shipment/move-vehicles/isLoading']
    },

    dataProduct (): Product[] {
      return this.$store.getters['shipping-company/product/data'].items
    },

    dataLocation (): PickupDropOffLocationPoint[] {
      return this.$store.getters['pick-up-drop-off-location-point/data'].items
    },

    dataVendor (): { items: Vendor[], page: number, totalPage: number } {
      return this.$store.getters['vendor/data']
    },

    dataVehicle (): Vehicle[] {
      return this.$store.getters['vehicle/listVehicles']
    },

    groupedByVendor (): Record<string, { vendor: any; vehicles: any[] }> {
      const groupedByVendor: Record<string, { vendor: any; vehicles: any[] }> = {}

      this.dataVehicle.forEach((item: { vendor: { name: string } }) => {
        const vendorName = item.vendor.name

        if (!groupedByVendor[vendorName]) {
          groupedByVendor[vendorName] = {
            vendor: item.vendor,
            vehicles: []
          }
        }

        groupedByVendor[vendorName].vehicles.push(item)
      })

      return groupedByVendor
    },

    selectedVehicle (): Vehicle | null {
      return this.$store.getters['vehicle/selectedVehicle']
    },

    checkedVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/checkedVehicles']
    },

    isLoadingProduct (): boolean {
      return this.$store.getters['shipping-company/product/isLoading']
    },

    isLoadingLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },

    isLoadingSubOrder (): boolean {
      return this.$store.getters['shipping-company/order-shipment/create-order/sub-order/isLoading']
    },

    isLoadingVendor (): boolean {
      return this.$store.getters['vendor/isLoading']
    },

    isLoadingVehicleList (): boolean {
      return this.$store.getters['vehicle/isLoadingList']
    },

    // reformatPayload (): any {
    //   return {
    //     type: this.moveType,
    //       file: this.form.file,
    //     track_ids: (this.moveVehiclesTracks as any).map((e: any) => e.id),
    //     order_id: (this.moveVehiclesOrder as any).id,
    //     new_order_identity: this.newIdentityOrder,
    //     suborders: [
    //     {
    //       type: 'PICKUP',
    //       estimation_date: `${this.form.dateTimeEstimation.pickup.date} ${this.form.dateTimeEstimation.pickup.time}`,
    //       pickup_dropoff_location_point_id: (this.form.location.pickup as any)?.value,
    //       products: this.form.products.pickup?.map((e: any) => ({
    //         product_id: e.product.value,
    //         quantity: e.pivot.quantity
    //       }))
    //     },
    //     {
    //       type: 'DROPOFF',
    //       estimation_date: `${this.form.dateTimeEstimation.dropoff.date} ${this.form.dateTimeEstimation.dropoff.time}`,
    //       pickup_dropoff_location_point_id: (this.form.location.dropoff as any)?.value,
    //       products: this.form.products.dropoff?.map((e: any) => ({
    //         product_id: e.product.value,
    //         quantity: e.pivot.quantity
    //       }))
    //     }
    //   ],
    //     additionals: Object.keys(this.weightValue).map((e: any) => ({
    //     vendor_id: e,
    //     weight: (this.weightValue as any)[e],
    //     vehicles: (this.checkedVehicles as any).filter((k: any) => k.vendor_id === e)
    //       .map((e: any) => ({
    //         vehicle_id: e.id,
    //         quantity: e.quantity
    //       }))
    //   }))
    //   }
    // }
  },

  watch: {
    newIdentityOrder: {
      handler (value) {
        this.onSetNewIdentity(value)
      }
    },
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Move Vehicle')

    const selectedShippingCompanyId = this.$store.getters['shipment/move-vehicles/selectedShippingCompanyId']
    this.$store.dispatch('shipping-company/product/getItems', {
      filterKeys: selectedShippingCompanyId,
      entries: -1
    })

    this.$store.dispatch('pick-up-drop-off-location-point/getItems', {
      idSc: selectedShippingCompanyId,
      entries: -1
    })

    this.$store.dispatch('vehicle/clearVehicles')
    this.getVendors()
  },

  methods: {
    onSetNewIdentity (value: any) {
      this.$store.commit('shipment/move-vehicles/SET_NEW_ORDER_IDENTITY', value)
    },
    setProductQuantity (i: any, type: any, orderType: 'PICKUP' | 'DROPOFF') {
      if (orderType === 'PICKUP') {
        if (type === 'SUBTRACT') {
          if (this.form.products.pickup[i].pivot.quantity === 0) {
            return
          }
          this.form.products.pickup[i].pivot.quantity -= 1
        } else {
          this.form.products.pickup[i].pivot.quantity += 1
        }
      } else if (orderType === 'DROPOFF') {
        if (type === 'SUBTRACT') {
          if (this.form.products.dropoff[i].pivot.quantity === 0) {
            return
          }
          this.form.products.dropoff[i].pivot.quantity -= 1
        } else {
          this.form.products.dropoff[i].pivot.quantity += 1
        }
      }
    },

    addProductList (orderType: 'PICKUP' | 'DROPOFF') {
      if (orderType === 'PICKUP') {
        this.form.products.pickup.push({
          product: '',
          unit: '',
          value: '',
          pivot: {
            quantity: 0,
            maxQuantity: 0
          }
        })
        this.form.products.dropoff.push({
          product: '',
          unit: '',
          value: '',
          pivot: {
            quantity: 0,
            maxQuantity: 0
          }
        })
      }
    },

    removeProductList (index: any, orderType: 'PICKUP' | 'DROPOFF') {
      if (orderType === 'PICKUP') {
        this.selectedProducts[index] = null
        if (this.form.products.pickup.length > 1) {
          this.form.products.pickup.splice(index, 1)
        }
      } else if (orderType === 'DROPOFF') {
        this.selectedProducts[index] = null
        if (this.form.products.dropoff.length > 1) {
          this.form.products.dropoff.splice(index, 1)
        }
      }
    },

    setProductQuantityValue (i: any, value: any, orderType: 'PICKUP' | 'DROPOFF') {
      if (orderType === 'PICKUP') {
        this.form.products.pickup[i].pivot.quantity = parseInt(value)
      } else if (orderType === 'DROPOFF') {
        this.form.products.dropoff[i].pivot.quantity = parseInt(value)
      }
    },

    selectLocation (selectedLocation: any) {
      this.selectedLocation = selectedLocation

      if (selectedLocation != null && selectedLocation?.latitude && selectedLocation?.longitude) {
        this.lastSelectedLocation = {
          latitude: parseFloat(selectedLocation.latitude),
          longitude: parseFloat(selectedLocation.longitude)
        }
      }
    },

    getVendors () {
      this.$store.dispatch('vendor/getVendors', {
        entries: -1,
        filterColumns: 'logistics_service_providers.status',
        filterKeys: 'COLLABORATE'
      })
    },

    getVehicles ({ page = 1 }) {
      let filterKeys
      if (Array.isArray(this.selectedVendor)) {
        filterKeys = this.selectedVendor?.join('|')
      } else {
        filterKeys = this.selectedVendor
      }

      this.$store.dispatch('vehicle/getItems', {
        entries: -1,
        page: '',
        searchColumns: 'name',
        searchKey: this.searchKeyVehicle,
        filterColumns: 'vendor_id',
        filterKeys
      })
    },

    formatNumber,

    decreaseQuantity (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/decreaseVehicleQuantity', vehicle)
    },

    increaseQuantity (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/increaseVehicleQuantity', vehicle)
    },

    weightVehicle (vendor: any, weightValue: any) {
      this.$store.dispatch('vehicle/weightVehicle', {
        weightValue,
        vendor
      })
    },

    selectVehicle (vehicle: Vehicle) {
      this.$store.dispatch('vehicle/selectVehicle', vehicle)
    },

    totalWeight () {
      const selectedWeights = Object.values(this.weightValue).map((weight) => {
        const parsedWeight = parseFloat(weight as any)
        return isNaN(parsedWeight) ? 0 : parsedWeight
      })

      return selectedWeights.reduce((acc, weight) => acc + weight, 0)
    },
    // getRestOfProductQuantity (productId: string): number {
    //   const order = this.$store.getters['shipping-company/order-shipment/create-order/dataDraft'].itemDraft as Order
    //   const suborders = [] as SubOrder[]
    //
    //   order.suborders?.forEach((suborder: SubOrder) => {
    //     suborders.push(suborder)
    //   })
    //
    //   if (!order.suborders) {
    //     return 0
    //   }
    //
    //   const dropOffSubOrders = suborders.filter((subOrder: SubOrder) => subOrder.type === 'DROPOFF')
    //   const dropOffProducts = [] as Product[]
    //   dropOffSubOrders.forEach((subOrder: SubOrder) => {
    //     subOrder.products.forEach((product: Product) => {
    //       if (productId === product.id) {
    //         dropOffProducts.push(product)
    //       }
    //     })
    //   })
    //
    //   const pickupSubOrders = suborders.filter((subOrder: SubOrder) => subOrder.type === 'PICKUP')
    //   const pickupProducts = [] as Product[]
    //   pickupSubOrders.forEach((subOrder: SubOrder) => {
    //     subOrder.products.forEach((product: Product) => {
    //       if (productId === product.id) {
    //         pickupProducts.push(product)
    //       }
    //     })
    //   })
    //
    //   const suborderId = this.form.id as string | null
    //   let currentSuborder = null as SubOrder | null
    //
    //   suborders.forEach((suborder: SubOrder) => {
    //     if (suborder.id === suborderId) {
    //       currentSuborder = suborder
    //     }
    //   })
    //
    //   let currentMaxQty = 0 as number
    //
    //   currentSuborder?.products.forEach((product: Product) => {
    //     if (product.id === productId) {
    //       currentMaxQty += product.pivot?.quantity
    //     }
    //   })
    //
    //   const definedQty = this.form.products.map((product: any) => {
    //     return {
    //       id: product.product?.value,
    //       quantity: product.pivot?.quantity
    //     }
    //   }) as any[]
    //
    //   let filledQty = 0 as number
    //
    //   definedQty.forEach((item: any) => {
    //     if (item.id === productId) {
    //       filledQty += item.quantity
    //     }
    //   })
    //
    //   const dropOffQty = dropOffProducts.reduce((acc: number, product: Product) => {
    //     return acc + product.pivot.quantity
    //   }, 0)
    //
    //   const pickupQty = pickupProducts.reduce((acc: number, product: Product) => {
    //     return acc + product.pivot.quantity
    //   }, 0)
    //
    //   if (currentMaxQty) {
    //     return currentMaxQty - (filledQty || 0)
    //   } else {
    //     return pickupQty - dropOffQty - (filledQty || 0)
    //   }
    // },

    onSelectProduct (selectedProduct: any, i: any, orderType: 'PICKUP' | 'DROPOFF') {
      if (selectedProduct != null) {
        this.selectedProducts[i] = (this.dataProduct as any)?.find((product: any) => product.id === selectedProduct.value)
        if (orderType === 'PICKUP') {
          this.$set(this.form.products.pickup, i, {
            product: {
              text: this.selectedProducts[i].name,
              value: this.selectedProducts[i].id,
              unit: this.selectedProducts[i].unit
            },
            pivot: {
              quantity: 0,
              maxQuantity: 0
            }
          })
        } else if (orderType === 'DROPOFF') {
          this.$set(this.form.products.dropoff, i, {
            product: {
              text: this.selectedProducts[i].name,
              value: this.selectedProducts[i].id,
              unit: this.selectedProducts[i].unit
            },
            pivot: {
              quantity: 0,
              maxQuantity: 0
            }
          })
        }

        // const restResult = this.getRestOfProductQuantity(this.selectedProducts[i].id)
        //
        // this.form.products[i].pivot.quantity = restResult
        // this.form.products[i].pivot.maxQuantity = restResult
      } else {
        this.selectedProducts[i] = null
      }
    },

    onSubmitMove () {
      const requestBody = {
        type: this.moveType,
        file: this.form.file,
        track_ids: (this.moveVehiclesTracks as any).map((e: any) => e.id),
        order_id: (this.moveVehiclesOrder as any).id,
        new_order_identity: this.newIdentityOrder,
        suborders: [
          {
            type: 'PICKUP',
            estimation_date: `${this.form.dateTimeEstimation.pickup.date} ${this.form.dateTimeEstimation.pickup.time}`,
            pickup_dropoff_location_point_id: (this.form.location.pickup as any)?.value,
            products: this.form.products.pickup?.map((e: any) => ({
              product_id: e.product.value,
              quantity: e.pivot.quantity
            }))
          },
          {
            type: 'DROPOFF',
            estimation_date: `${this.form.dateTimeEstimation.dropoff.date} ${this.form.dateTimeEstimation.dropoff.time}`,
            pickup_dropoff_location_point_id: (this.form.location.dropoff as any)?.value,
            products: this.form.products.dropoff?.map((e: any) => ({
              product_id: e.product.value,
              quantity: e.pivot.quantity
            }))
          }
        ],
        additionals: Object.keys(this.weightValue).map((e: any) => ({
          vendor_id: e,
          weight: (this.weightValue as any)[e],
          vehicles: (this.checkedVehicles as any).filter((k: any) => k.vendor_id === e)
            .map((e: any) => ({
              vehicle_id: e.id,
              quantity: e.quantity
            }))
        }))
      }

      this.$store.dispatch('shipment/move-vehicles/submitMoveDO', requestBody)

      this.isShowDialogSubmitVehicle = true
    }
  }
})
</script>

<style scoped lang="scss"> </style>

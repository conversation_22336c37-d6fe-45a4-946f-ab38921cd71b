<template>
  <v-row class="ma-0 mb-10 px-md-10 px-5 d-flex flex-lg-row flex-column-reverse">
    <v-col class="pa-0 col-lg-5 col-12">
      <v-container class="white rounded pa-md-10 pa-5" fluid>
        <h4 class="mb-5">
          {{ $t('scDashboard.active_order') }}
        </h4>
        <v-text-field
          v-model="searchKey"
          outlined
          :label="$t('scDashboard.search_label')"
          append-icon="mdi-magnify"
          hide-details
          class="mb-5"
          @keydown.enter="searchOrderShipment($event)"
          @keyup.delete="searchOrderShipment($event)"
        />
        <div v-if="isLoading">
          <div v-for="i in 3" :key="i">
            <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
          </div>
        </div>
        <div v-else>
          <v-list v-if="data.length !== 0" class="pa-0">
            <v-card class="pa-5 mt-5 rounded v-sheet--outlined">
              <v-expansion-panels :value="0" accordion flat class="pa-0 custom-panel">
                <v-expansion-panel
                  v-for="(item, i) in data"
                  :key="i"
                  class="pa-5 mt-5 rounded v-sheet--outlined"
                >
                  <v-expansion-panel-header class="pa-0">
                    <div class="d-flex mx-4">
                      <v-icon
                        color="black"
                        size="40"
                        class="mr-5"
                      >
                        mdi-text-box-multiple
                      </v-icon>
                      <div class="d-block">
                        <p class="grey--text text-capitalize ma-0">
                          order shipment
                        </p>
                        <p class="subtitle-1 ma-0">
                          {{ item?.identity }}
                        </p>
                      </div>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="body-1 pa-0">
                    <v-divider class="mt-4" />
                    <div
                      v-for="(subitem, j) in item?.tracks"
                      :key="j"
                      class="d-flex mr-4 mt-5 align-center"
                    >
                      <v-img
                        class="mr-5"
                        :src="require(`~/assets/images/photo.png`)"
                        max-width="35"
                        max-height="35"
                        position="center center"
                      />
                      <div class="d-block">
                        <p class="subtitle-1 text-capitalize ma-0">
                          {{ subitem.driver.name }}  <span class="pl-2 red--text">{{ checkNew(subitem.track_created_at) }}</span>
                        </p>
                        <p class=" grey--text ma-0">
                          Track
                          <span class="text-decoration-underline">
                            <a
                              class="blue--text "
                              @click="getDetailTrack(subitem?.id)"
                            >
                              {{ subitem?.identity }}
                            </a>

                          </span>
                        </p>
                      </div>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card>
          </v-list>

          <div v-if="data.length === 0" class="d-flex flex-column align-center">
            <h4 class="mb-5">
              {{ $t('scDashboard.no_active_order_title') }}
            </h4>
            <p class="ma-0 text-secondary body-1 text-center">
              {{ $t('scDashboard.no_active_order_text') }}
            </p>
          </div>
        </div>
      </v-container>
    </v-col>

    <v-col class="pa-0 pl-lg-10 col-lg-7 col-12">
      <div v-if="isLoadingTrack">
        <div v-for="i in 4" :key="i">
          <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
        </div>
      </div>
      <v-responsive
        v-else
        :aspect-ratio="setAspectRatio"
        class="mb-10"
        style="z-index: 0"
      >
        <custom-map
          :latitude="centerLatLng?.lat"
          :longitude="centerLatLng?.lng"
          :polyline="polyline"
          :zoom="zoomMap"
          class="custom-map"
        >
          <template #marker>
            <div v-if="liveTracking">
              <v-rotated-marker
                v-for="(marker, i) in liveTracking?.vehicle_device"
                :key="i"
                :lat-lng="[marker?.lat, marker?.lng]"
                :rotation-angle="marker?.course"
              >
                <l-icon
                  :icon-size="[64, 64]"
                  :icon-url="require('~/assets/icons/driver.svg')"
                  :icon-anchor="[32, 32]"
                />

                <custom-map-popup
                  :driver-avatar="marker.track.vehicle_detail?.driver?.user?.avatar_url"
                  :driver-name="marker.track?.vehicle_detail?.driver?.user?.name"
                  :plate-number="marker.track?.vehicle_detail?.plate_number"
                  :identity-track="marker.track?.identity"
                  :status="marker.track?.current_status"
                />
              </v-rotated-marker>

              <l-marker
                v-for="marker in liveTracking?.driver_device"
                :key="marker.id"
                :lat-lng="[marker.latitude, marker.longitude]"
              >
                <l-icon
                  :icon-size="[32, 32]"
                  :icon-anchor="[16, 16]"
                >
                  <v-sheet
                    color="white"
                    width="100%"
                    height="100%"
                    class="d-flex align-center justify-center pa-1"
                    style="border-radius: 100%; box-shadow: 0 0 5px rgba(0, 0, 0, 0.25)"
                  >
                    <v-img
                      v-if="marker.track?.vehicle_detail?.driver?.user?.avatar_url"
                      width="100%"
                      height="100%"
                      :src="marker.track?.vehicle_detail?.driver?.user?.avatar_url"
                      style="border-radius: 100%"
                    />

                    <v-icon
                      v-else
                      color="primary"
                      size="28"
                    >
                      mdi-account-circle
                    </v-icon>
                  </v-sheet>
                </l-icon>

                <l-popup>
                  <div class="d-flex align-center">
                    <v-img
                      width="24px"
                      contain
                      :src="marker.track?.vehicle_detail?.driver?.user?.avatar_url"
                      class="mr-2"
                    />
                    <p>{{ marker.track?.vehicle_detail?.driver?.user?.name }}</p>
                  </div>
                </l-popup>
              </l-marker>
            </div>

            <l-marker
              v-for="(marker, i) in markers"
              :key="i"
              :lat-lng="[parseFloat(marker.lat), parseFloat(marker.lng)]"
            >
              <l-icon v-if="marker.type === 'PICKUP'" :icon-anchor="[20, 40]">
                <v-icon size="40" color="info">
                  mdi-map-marker
                </v-icon>
              </l-icon>

              <l-icon v-else :icon-anchor="[20, 40]">
                <v-icon size="40" color="success">
                  mdi-map-marker
                </v-icon>
              </l-icon>
            </l-marker>
          </template>
        </custom-map>
      </v-responsive>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '@/components/shipping-company/CustomMap.vue'
import { DashboardShipment } from '~/types/dashboard'
import { LiveTracking, Route, Track } from '~/types/shipment'
import {
  defaultLat, defaultLng,
  generateCenterLatLng,
  checkIsNew,
  zoom
} from '~/utils/functions'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'ShipmentCompanyDashboardPage',

  components: {
    CustomMap
  },

  beforeRouteLeave (_, __, next) {
    if (this.activeInterval) {
      clearInterval(this.activeInterval)
    }
    this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
    next()
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    searchKey: '',
    isShow: false,
    selectedTrackId: '',
    selectedRoutes: [] as Route[],
    polyline: null as Polyline | null,
    markers: [] as { lat: number, lng: number, type: string, name: string }[],
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4,
    orderIsExist: true,
    windowWidth: 0,
    activeInterval: null as any
  }),

  computed: {
    setAspectRatio () {
      let aspectRatio = 0

      if (this.windowWidth >= 600 && this.windowWidth < 1280) {
        aspectRatio = 16 / 9
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    data () {
      const value = this.$store.getters['dashboard/dataShipment'] as DashboardShipment[] | null
      return value ?? []
    },
    isLoading () {
      return this.$store.getters['dashboard/isLoadingShipmentCompany']
    },
    trackDetail () {
      return this.$store.getters['shipment/trackItem'] as Track | null
    },
    isLoadingTrack () {
      return this.$store.getters['shipment/isLoadingTrack']
    },
    liveTracking (): LiveTracking {
      return this.$store.getters['dashboard/liveTracking'] as LiveTracking
    }
  },

  watch: {
    trackDetail () {
      if (this.data && this.data[0]?.tracks?.length > 0) {
        this.selectedTrackId = this.data[0]?.tracks[0]?.id
        this.selectedRoutes = this.data[0]?.tracks[0]?.routes

        this.setupDirections()

        // this.polyline = generatePolyline()
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)

        this.$store.dispatch('dashboard/getLiveTracking', {
          shipmentId: this.trackDetail?.shipment_id
        })

        this.recurringTracking()
      }
    },
    data () {
      if (this.data.length > 0) {
        this.getDetailTrack(this.data[0]?.tracks[0]?.id)
      } else {
        this.$store.commit('shipment/SET_IS_LOADING_TRACK', false)
      }
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', this.$t('scDashboard.page_title'))
    this.getData(1)
  },

  beforeDestroy () {
    clearInterval(this.activeInterval)
  },

  methods: {
    getData (page: any, searchKey = '') {
      this.$store.dispatch('dashboard/getItemShipment', {
        page,
        searchKey
      })
    },

    setupDirections () {
      const track = this.trackDetail as Track
      const markers = [] as any
      const polyline = {
        mapBox: [] as object[],
        fms: [] as object[]
      }

      polyline.mapBox.push({
        id: track.id,
        latLng: track.directions
          .filter(direction => direction.type === 'MAPBOX')
          .map((d) => {
            return [d.latitude, d.longitude]
          })
      })

      polyline.fms.push({
        id: track.id,
        latLng: track.directions
          .filter(direction => direction.type === 'FMS')
          .map((d) => {
            return [d.latitude, d.longitude]
          })
      })

      this.trackDetail?.routes?.forEach((route) => {
        const lat = parseFloat(route.pickup_drop_off_location_point.latitude)
        const lng = parseFloat(route.pickup_drop_off_location_point.longitude)
        const type = route.pickup_drop_off_location_point.type

        markers.push({
          lat,
          lng,
          type
        })
      })

      this.markers = markers
      this.polyline = polyline
    },

    getDetailTrack (id: string) {
      this.$store.dispatch('shipment/getDetailTrack', {
        id
      })
    },
    searchOrderShipment () {
      this.getData(1, this.searchKey)
    },
    recurringTracking () {
      if (this.activeInterval) {
        clearInterval(this.activeInterval)
      }
      this.$store.commit('dashboard/SET_LIVE_TRACKING', null)
      this.activeInterval = setInterval(() => {
        this.$store.dispatch('dashboard/getLiveTracking', {
          shipmentId: this.trackDetail?.shipment_id
        })
      }, 30000)
    },

    checkNew (date: string) {
      return checkIsNew(date)
    }
  }
})
</script>

<style scoped>
.custom-map >>> .leaflet-popup { width: auto !important; }
.custom-map >>> .leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 8px 64px 0 rgba(10, 10, 10, 0.24);
}
.custom-map >>> .leaflet-popup-content { margin: 0; padding: 8px 16px; }
</style>

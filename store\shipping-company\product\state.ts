import { Product } from '../../../types/product'
import { ImportKey } from '~/types/import-key'

export interface ShipmentCompanyProductState {
  isLoading: boolean
  isLoadingForm: boolean
  isExistDialog: boolean
  items: Product[]
  itemsProductKey: ImportKey[]
  totalPage: number
  page: number
  isLoadingFormClearImage: boolean
}

export const state = (): ShipmentCompanyProductState => ({
  items: [] as Array<Product>,
  itemsProductKey: [] as ImportKey[],
  totalPage: 1,
  page: 1,
  isLoading: false,
  isLoadingForm: false,
  isExistDialog: false,
  isLoadingFormClearImage: false
})

export default state

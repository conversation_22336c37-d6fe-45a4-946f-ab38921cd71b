<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <header-datatable
      default-sort-column="identity"
      default-sort-type="desc"
      :sort-column-items="sortColumn"
      :sort-type-items="sortType"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-filter-change="getOrders({ filter: $event, page: $route.query?.page })"
      @on-search-icon-click="getOrders({ searchKey: $event })"
    >
      <!--      <template #button>-->
      <!--        <v-btn-->
      <!--          color="white"-->
      <!--          elevation="0"-->
      <!--          class="text-capitalize custom-btn"-->
      <!--          x-large-->
      <!--        >-->
      <!--          <v-icon-->
      <!--            class="mr-3 custom-icon"-->
      <!--          >-->
      <!--            mdi-file-download-->
      <!--          </v-icon>-->
      <!--          Export-->
      <!--        </v-btn>-->
      <!--      </template>-->
    </header-datatable>

    <table-order-shipment-loading v-if="isLoading" :heading-tabs="false" />
    <v-container v-else fluid class="rounded pa-0 d-flex flex-column align-end">
      <v-container fluid class="pa-0">
        <div v-if="data.items.length !== 0">
          <v-data-table
            :headers="tableHeaders"
            :items="data.items"
            :single-expand="singleExpand"
            :expanded.sync="expanded"
            :page.sync="data.page"
            :items-per-page="-1"
            show-expand
            hide-default-footer
            class="pa-md-10 pa-5 mb-10"
            @item-expanded="expandItem"
          >
            <template #item.totalProduct="{ item }">
              {{ calculateQuantityProduct(item) }}
            </template>
            <template #item.published_at="{ item }">
              <span v-if="item.published_at">{{ $moment.utc(item.published_at).local().format('DD/MM/YYYY - HH:mm') }}</span>
            </template>
            <template #expanded-item="{ headers }">
              <td :colspan="headers.length" class="pa-0 py-5" style="">
                <v-card elevation="0" class="pa-5" outlined>
                  <v-row class="ma-0 d-flex flex-lg-row flex-column">
                    <v-col class="pa-0">
                      <v-skeleton-loader
                        v-if="isLoadingDetail"
                        type="table-thead"
                        class="pa-0"
                      />
                      <v-simple-table v-else dense>
                        <template #default>
                          <thead>
                            <tr>
                              <th style="border: none" class="caption pa-0">
                                {{ $t('scHistoryOrder.identity_product') }}
                              </th>
                              <th style="border: none" class="caption pa-0">
                                {{ $t('scHistoryOrder.product') }}
                              </th>
                              <th style="border: none" class="caption pa-0">
                                {{ $t('scHistoryOrder.quantity') }}
                              </th>
                            </tr>
                          </thead>
                          <tbody
                            v-for="(suborder, i) in detailData?.suborders"
                            :key="i"
                          >
                            <tr
                              v-for="(product, j) in suborder.products"
                              :key="j"
                            >
                              <td style="border: none" class="body-1 pa-0">
                                {{ product.identity }}
                                <a class="caption"> ( {{ suborder.type }} )</a>
                              </td>
                              <td style="border: none" class="body-1 pa-0">
                                {{ product.name }}
                              </td>
                              <td style="border: none" class="body-1 pa-0">
                                {{ product.pivot.quantity }}
                              </td>
                            </tr>
                          </tbody>
                        </template>
                      </v-simple-table>

                      <v-divider class="my-5" />

                      <v-skeleton-loader
                        v-if="isLoadingDetail"
                        type="image"
                        class="pa-0"
                      />

                      <v-container v-else class="pa-0">
                        <div
                          v-for="(shipment, i) in detailData?.shipments"
                          :key="i"
                          class="d-flex mb-3"
                        >
                          <div
                            v-for="(track, j) in shipment.tracks"
                            :key="j"
                            class="d-flex ma-2"
                          >
                            <v-img
                              :src="track?.vehicle_detail?.vehicle?.photo_url"
                              min-width="120"
                              max-width="120"
                              aspect-ratio="1"
                              contain
                              class="mr-5"
                            />

                            <div>
                              <p class="subtitle-1 mb-2">
                                {{ track?.vehicle_detail?.vehicle?.name }}
                              </p>
                              <p class="caption mb-2">
                                {{ $t('scHistoryOrder.features') }}
                              </p>
                              <div class="d-flex flex-column flex-sm-row">
                                <v-chip
                                  v-for="(feature, k) in track?.vehicle_detail
                                    ?.vehicle?.vehicle_features"
                                  :key="k"
                                  outlined
                                  label
                                  class="mb-2 mr-sm-2"
                                >
                                  <p class="ma-0 body-1">
                                    {{ feature.name }}
                                  </p>
                                </v-chip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </v-container>

                      <v-divider class="mb-5" />

                      <v-btn
                        v-if="$vuetify.breakpoint.lgAndUp"
                        :loading="isLoadingDetail"
                        :disabled="!detailData?.invoice_detail?.id"
                        x-large
                        outlined
                        class="text-capitalize px-3"
                        style="border: 1px solid #cfcccc"
                        @click="
                          $router.push(
                            '/shipping-company/order-shipment/invoice-order/' +
                              detailData?.invoice_detail?.id
                          )
                        "
                      >
                        <v-icon class="mr-3">
                          mdi-text-box
                        </v-icon>
                        <p class="ma-0 subtitle-1">
                          {{ $t('scHistoryOrder.button_detail') }}
                        </p>
                      </v-btn>
                    </v-col>

                    <v-divider
                      v-if="$vuetify.breakpoint.lgAndUp"
                      vertical
                      class="mx-5"
                    />

                    <v-col class="pa-0">
                      <v-container class="pa-0">
                        <p class="subtitle-1 mb-5">
                          E-POD
                        </p>
                        <v-skeleton-loader
                          v-if="isLoadingDetail"
                          type="card-heading"
                          class="pa-0 mb-5"
                        />
                        <v-list v-else class="pa-0 mb-5">
                          <v-list-item-group color="primary">
                            <v-row>
                              <v-col
                                v-for="(
                                  shipmentHistory, i
                                ) in dataShipmentHistory.items"
                                :key="i"
                                class="col-6 col-sm-4"
                              >
                                <v-card
                                  flat
                                  outlined
                                  @click="
                                    selectedShipmentHistory = shipmentHistory
                                  "
                                >
                                  <v-list-item
                                    color="primary"
                                    class="py-1 d-flex"
                                  >
                                    <v-list-item-content
                                      class="subtitle-2 pa-0 d-flex flex-column align-center"
                                    >
                                      <p class="mb-1 text-center">
                                        {{ shipmentHistory.route?.type }}
                                      </p>
                                      <p class="ma-0">
                                        #{{
                                          shipmentHistory.id
                                            ?.slice(-4)
                                            .toUpperCase()
                                        }}
                                      </p>
                                    </v-list-item-content>
                                  </v-list-item>
                                </v-card>
                              </v-col>
                            </v-row>
                          </v-list-item-group>
                        </v-list>
                      </v-container>

                      <v-row v-if="selectedShipmentHistory" class="ma-0 mb-5">
                        <v-col class="mr-md-10 mr-5 pa-0">
                          <p class="caption mb-2">
                            {{ $t('scHistoryOrder.receiver') }}
                          </p>
                          <p class="ma-0 pa-0">
                            {{
                              selectedShipmentHistory.proof_receiver_name || '-'
                            }}
                          </p>
                        </v-col>

                        <v-col class="pa-0">
                          <p class="caption mb-2">
                            {{ $t('scHistoryOrder.status_shipment') }}
                          </p>
                          <div
                            v-if="selectedShipmentHistory.status === undefined"
                          >
                            -
                          </div>
                          <v-chip
                            v-else
                            label
                            :style="
                              statusColorContainerEPOD(
                                selectedShipmentHistory.status
                              )
                            "
                          >
                            <p
                              :class="
                                statusColorEPOD(selectedShipmentHistory.status)
                              "
                            >
                              {{ selectedShipmentHistory.status }}
                            </p>
                          </v-chip>
                        </v-col>
                      </v-row>

                      <v-row class="ma-0 mb-5">
                        <v-col class="pa-0 mr-md-10 mr-5 d-flex flex-column">
                          <p class="caption mb-2">
                            {{ $t('scHistoryOrder.signature') }}
                          </p>

                          <image-component
                            v-if="
                              selectedShipmentHistory?.proof_sign_photo_url !=
                                null
                            "
                            min-width="100"
                            max-width="100"
                            :image="
                              selectedShipmentHistory?.proof_sign_photo_url
                            "
                            :is-has-detail-view="true"
                          />
                          <span v-else> - </span>
                        </v-col>

                        <v-col class="pa-0">
                          <p class="caption mb-2">
                            {{ $t('scHistoryOrder.photo') }}
                          </p>

                          <span
                            v-if="
                              !selectedShipmentHistory.shipment_history_photos
                            "
                          >
                            -
                          </span>
                          <div
                            v-for="(
                              photo, index
                            ) in selectedShipmentHistory.shipment_history_photos"
                            v-else
                            :key="index"
                          >
                            <image-component
                              v-if="
                                photo?.proof_delivery_photo_path_url != null
                              "
                              min-width="100"
                              max-width="100"
                              :image="photo?.proof_delivery_photo_path_url"
                              :is-has-detail-view="true"
                            />
                          </div>
                        </v-col>
                      </v-row>

                      <v-container
                        v-if="selectedShipmentHistory.detail !== undefined"
                        class="pa-5 rounded"
                        style="background-color: #f5f5f5"
                      >
                        <p class="mb-2 caption">
                          {{ $t('scHistoryOrder.note') }}
                        </p>
                        <p class="ma-0 body-1 font-italic">
                          {{ selectedShipmentHistory?.detail }}
                        </p>
                      </v-container>

                      <div v-if="$vuetify.breakpoint.mdAndDown">
                        <v-divider class="my-5" />

                        <v-btn
                          x-large
                          link
                          block
                          :to="localePath('/shipping-company/order-shipment/history-order/detail')"
                          outlined
                          class="text-capitalize px-3"
                        >
                          <v-icon class="mr-3">
                            mdi-text-box
                          </v-icon>
                          <p class="ma-0 subtitle-1">
                            {{ $t('scHistoryOrder.button_detail') }}
                          </p>
                        </v-btn>
                      </div>
                    </v-col>
                  </v-row>
                </v-card>
              </td>
            </template>
          </v-data-table>

          <pagination-component
            :page="data.page"
            :total-page="data.totalPage"
            page-id="page"
            class="float-end"
            @on-change-page="getOrders({
              page: $event,
              filter: {
                sortColumn: $route.query?.sort_column,
                sortType: $route.query?.sort_type
              }
            })"
          />
        </div>
        <div v-else>
          <div class="justify-center align-center fill-height">
            <empty-placeholder
              hero="empty-placeholder.svg"
              :message-title="$t('scHistoryOrder.empty_placeholder_title')"
              :message-description="$t('scHistoryOrder.empty_placeholder_description')"
            />
          </div>
        </div>
      </v-container>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Order } from '~/types/product'
import { ShipmentHistory } from '~/types/shipment-history'
import TableOrderShipmentLoading from '~/components/loading/TableOrderShipmentLoading.vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'HistoryOrderOrderPage',

  components: {
    TableOrderShipmentLoading,
    HeaderDatatable,
    EmptyPlaceholder,
    PaginationComponent
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],
  data: () => ({
    expanded: [],
    searchKey: '',
    dialog: false,
    singleExpand: true,
    selectedShipmentHistory: {} as ShipmentHistory,
    sortColumn: {
      identity: {
        label: 'Identity',
        value: 'identity'
      }
    },
    sortType: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    }
  }),

  computed: {
    tableHeaders (): Object[] {
      return [
        { text: this.$t('scHistoryOrder.identity_order'), value: 'identity' },
        { text: this.$t('scHistoryOrder.total_product'), value: 'totalProduct' },
        { text: this.$t('scHistoryOrder.date'), value: 'published_at' },
        { text: this.$t('scHistoryOrder.status_order'), value: 'status' },
        { text: '', value: 'data-table-expand' }
      ]
    },
    data () {
      return this.$store.getters['order/data'] as {
        items: Order[]
        totalPage: number
        page: number
      }
    },

    detailData () {
      return this.$store.getters['order/selectedOrder']
    },

    isLoading () {
      return this.$store.getters['order/isLoading']
    },

    isLoadingDetail () {
      return this.$store.getters['order/isLoadingDetail']
    },

    dataShipmentHistory () {
      return this.$store.getters['shipment-history/data'] as {
        items: ShipmentHistory[]
      }
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', this.$t('scHistoryOrder.page_title'))
  },

  mounted () {
    this.getOrders({
      page: this.$route.query?.page as string
    })
  },

  methods: {
    getOrders ({
      page = '',
      searchKey = '',
      filter = { sortColumn: 'identity', sortType: 'desc' }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('order/getOrders', {
        searchColumns: 'identity',
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'status',
        filterKeys: 'PUBLISHED',
        page
      })
    },

    getDetailOrder (id: string) {
      this.$store.dispatch('order/getDetailOrder', { id })
    },

    getShipmentHistory (id: string) {
      this.$store.dispatch('shipment-history/getItems', {
        filterColumns: 'route.track.shipment.orders.id',
        filterKeys: id
      })
    },

    calculateQuantityProduct (item: Order): any {
      const productIds = [] as any

      item.suborders?.forEach((subOrder: any) => {
        subOrder.products.forEach((product: any) => {
          productIds.push(product.id)
        })
      })

      return (
        productIds.filter((value: any, index: any, array: any) => {
          return index === array.indexOf(value)
        }).length + ' item'
      )
    },

    expandItem (event: any) {
      if (event.value) {
        this.getDetailOrder(event.item.id)
        this.getShipmentHistory(event.item.id)

        this.selectedShipmentHistory = {} as ShipmentHistory
      }
    },

    statusColorContainerEPOD (status: string) {
      switch (status) {
        case 'ACCEPTED':
          return 'background-color: #EAF6EC'
        case 'DECLINED':
          return 'background-color: #FDE0E0'
        default:
          return 'primary'
      }
    },

    statusColorEPOD (status: string) {
      switch (status) {
        case 'ACCEPTED':
          return 'ma-0 subtitle-1 text-success'
        case 'DECLINED':
          return 'ma-0 subtitle-1 text-error'
        default:
          return 'ma-0 subtitle-1 text-primary'
      }
    }
  }
})
</script>

<style lang="scss" scoped>
tbody {
  tr:hover {
    background-color: transparent !important;
  }
}
.custom-btn {
  transition: 0.28s !important;
}
.custom-icon {
  transition: 0s !important;
}
.custom-btn:hover {
  background-color: #ef3434 !important;
  color: white !important;
}
</style>

<template>
  <v-container fluid class="pa-0 px-5 px-md-10 mb-10">
    <v-btn
      link
      plain
      class="text-capitalize mb-10 px-3"
      @click="$router.back()"
    >
      <v-icon
        color="black"
        class="mr-2"
      >
        mdi-chevron-left
      </v-icon>
      <p class="ma-0 subtitle-1">
        {{ $t('lspHistoryShipment.back_to_list') }}
      </p>
    </v-btn>

    <detail-invoice-order-loading v-if="isLoading" />

    <detail-invoice-lsp
      v-else
      :selected-invoice="dataInvoiceDetail"
      :products="products"
      :pickup-sub-order="pickupSubOrder"
      :drop-off-sub-order="dropOffSubOrder"
    >
      <template #date-invoice>
        {{ $moment(dataInvoiceDetail?.created_at).format('DD MMMM YYYY') }}
      </template>
      <template #invoice-number>
        <div>
          <p class="caption text-secondary mb-2">
            {{ $t('lspHistoryShipment.invoice_number') }}
          </p>
          <h3>{{ dataInvoiceDetail?.order?.identity }}</h3>
        </div>
      </template>

      <template #download-invoice>
        <v-btn
          x-large
          outlined
          class="text-capitalize"
          :loading="isLoadingDownloadInvoice"
          style="border: 1px solid #CFCCCC;"
          @click="downloadInvoice"
        >
          <v-icon>
            mdi-file-download
          </v-icon>
          <p class="subtitle-1 ma-0">
            {{ $t('lspHistoryShipment.download_invoice') }}
          </p>
        </v-btn>
      </template>

      <template #header>
        <v-row>
          <v-col class="col-12 col-sm-3">
            <div class="mr-0 mr-sm-10">
              <h4 class="mb-2">
                {{ $t('lspHistoryShipment.from') }}
              </h4>
              <div class="d-flex">
                <image-component
                  :image="dataInvoiceDetail?.invoice?.shipment?.logistics_service_provider?.logo_url"
                  min-width="60"
                  max-width="60"
                  class="mr-5"
                />
                <h4>{{ dataInvoiceDetail?.invoice?.shipment?.logistics_service_provider?.name }}</h4>
              </div>
            </div>
          </v-col>
          <v-col class="col-12 col-sm-3">
            <div class="ml-0 ml-sm-10">
              <h4 class="mb-2">
                {{ $t('lspHistoryShipment.to') }}
              </h4>
              <div class="d-flex">
                <image-component
                  :image="dataInvoiceDetail?.order?.shipment_company?.logo_url"
                  min-width="60"
                  max-width="60"
                  class="mr-5"
                />
                <h4>{{ dataInvoiceDetail?.order?.shipment_company?.name }}</h4>
              </div>
            </div>
          </v-col>
        </v-row>
      </template>

      <template #shipping-cost>
        <h4>{{ dataInvoiceDetail?.cost ?? 0 | toCurrency }}</h4>
      </template>

      <template #additional-fee>
        <v-container v-if="dataInvoiceDetail?.invoice?.fees.length === 0">
          <p class="body-1 ma-0">
            {{ $t('lspHistoryShipment.no_additional_fee') }}
          </p>
        </v-container>
        <div v-else>
          <v-container
            v-for="fee in dataInvoiceDetail?.invoice?.fees"
            :key="fee.id"
            fluid
            class="mb-2 pa-0 d-flex align-center justify-space-between"
          >
            <p class="ma-0 body-1 text-secondary">
              {{ fee.description }}
            </p>
            <p class="ma-0 body-1 black--text">
              {{ parseInt(fee.cost) | toCurrency }}
            </p>
          </v-container>
        </div>
      </template>

      <template #total-cost>
        <h3 class="text-primary">
          {{ totalAdditionalCost + +dataInvoiceDetail?.cost | toCurrency }}
        </h3>
      </template>
    </detail-invoice-lsp>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailInvoiceOrderLoading from '~/components/loading/DetailInvoiceOrderLoading.vue'
import DetailInvoiceLsp from '~/components/DetailInvoiceLsp.vue'
import ImageComponent from '~/components/ImageComponent.vue'
import { Fee, InvoiceDetail } from '~/types/invoice'
import { Product, SubOrder } from '~/types/product'

export default Vue.extend({
  name: 'ShipmentCompanyInvoiceOrderDetail',

  components: { DetailInvoiceOrderLoading, DetailInvoiceLsp, ImageComponent },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    pickupSubOrder: [] as SubOrder[] | null,
    dropOffSubOrder: [] as SubOrder[] | null,
    products: [] as Product[] | null,
    totalAdditionalCost: 0 as number
  }),

  computed: {
    dataInvoiceDetail (): InvoiceDetail | null {
      return this.$store.getters['invoice-details/detailData'] as InvoiceDetail | null
    },
    isLoading () {
      return this.$store.getters['invoice-details/isLoadingDetail']
    },

    isLoadingDownloadInvoice () {
      return this.$store.getters['invoice-details/isLoadingDownloadInvoice']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Invoice Order Shipment')
  },

  mounted () {
    this.$store.dispatch('invoice-details/getItemDetail', {
      id: this.$route.params.id
    }).then(() => {
      this.dataInvoiceDetail?.order?.suborders?.forEach((item:any) => {
        if (item.type === 'PICKUP') {
          this.pickupSubOrder?.push(item)
        } else {
          this.dropOffSubOrder?.push(item)
        }
      })

      this.products = this.pickupSubOrder?.map((subOrder: SubOrder) => {
        return subOrder.products
      }).flat() as Product[] | null

      this.dataInvoiceDetail?.invoice?.fees.forEach((fee: Fee) => {
        if (fee.cost == null) {
          this.totalAdditionalCost = 0
        }

        this.totalAdditionalCost += parseInt(fee.cost ?? '0')
      })
    })
  },

  methods: {
    async downloadInvoice () {
      await this.$store.dispatch('invoice-details/downloadInvoiceDetails', {
        id: this.$route.params.id,
        number: this.dataInvoiceDetail?.order.identity
      })
    }
  }
})
</script>

<style lang="scss" scoped>
tbody {
  tr:hover {
    background-color: transparent !important;
  }
}
</style>

<template>
  <v-container fluid class="pa-0 px-10 mb-10">
    <v-container fluid class="pa-10 mb-10 white rounded">
      <v-container fluid class="pa-0 mb-6 d-flex justify-space-between">
        <h4>{{ $t('vendorCreateOrder.list_shipment') }}</h4>
        <!-- <div>
          <v-icon
            :disabled="
              tab === 0 ? pageProposed === 1 :
              pageDispatch === 1
            "
            @click="
              tab === 0 ? getDataShipmentVendor({page: pageProposed - 1}) :
              getDataShipmentDispatch({page: pageDispatch - 1})
            "
          >
            mdi-chevron-left
          </v-icon>
          <v-icon
            :disabled="
              tab === 0 ? pageProposed === dataShipmentVendorProposed.totalPage :
              pageDispatch === dataShipmentDispatch.totalPage
            "
            class="ml-6"
            @click="
              tab === 0 ? getDataShipmentVendor({page: pageProposed + 1}) :
              getDataShipmentDispatch({page: pageDispatch + 1})
            "
          >
            mdi-chevron-right
          </v-icon>
        </div> -->
      </v-container>

      <v-tabs v-model="tab">
        <v-tab>
          <p class="ma-0 subtitle-1 text-capitalize">
            {{ $t('vendorCreateOrder.request') }}
          </p>
        </v-tab>
        <v-tab>
          <p class="ma-0 subtitle-1 text-capitalize">
            {{ $t('vendorCreateOrder.recent_accepted') }}
          </p>
        </v-tab>
        <v-tab>
          <p class="ma-0 subtitle-1 text-capitalize">
            Expired
          </p>
        </v-tab>
      </v-tabs>

      <v-tabs-items v-model="tab">
        <v-tab-item>
          <v-container fluid class="pa-0 mt-6">
            <create-order-loading v-if="isLoading" loader-type="card_shipment" />

            <v-container v-else fluid class="pa-0">
              <div class="d-flex flex-row align-center justify-space-between pa-0" style="position: absolute; width: 100%; top: 0; bottom: 0;">
                <v-btn
                  fab
                  small
                  color="primary"
                  style="z-index:10"
                  :disabled="
                    tab === 0 ? pageProposed === 1 :
                    pageDispatch === 1
                  "
                  @click="
                    tab === 0 ? getDataShipmentVendor({page: pageProposed - 1}) :
                    getDataShipmentDispatch({page: pageDispatch - 1})
                  "
                >
                  <v-icon dark>
                    mdi-chevron-left
                  </v-icon>
                </v-btn>
                <v-btn
                  fab
                  small
                  color="primary"
                  style="z-index:10"
                  :disabled="
                    tab === 0 ? pageProposed === dataShipmentVendorProposed.totalPage :
                    pageDispatch === dataShipmentDispatch.totalPage
                  "
                  @click="
                    tab === 0 ? getDataShipmentVendor({page: pageProposed + 1}) :
                    getDataShipmentDispatch({page: pageDispatch + 1})
                  "
                >
                  <v-icon dark>
                    mdi-chevron-right
                  </v-icon>
                </v-btn>
              </div>
              <v-list
                v-if="dataShipmentVendor?.items?.length > 0"
                flat
                class="pa-0 mx-2"
              >
                <v-list-item-group mandatory>
                  <v-row class="ma-n3">
                    <v-col
                      v-for="(shipment, i) in dataShipmentVendor?.items"
                      :key="shipment.id"
                      cols="4"
                      class="pa-5 col-lg-4 col-md-6 col-12"
                    >
                      <v-list-item
                        class="pa-0 rounded"
                        :style="isSelectedShipment.proposed[i] ? `border: 1px solid #EF3434;` : 'border: 1px solid #CFCCCC;'"
                        @click="
                          isSelectedShipment.proposed = [null];
                          isSelectedShipment.dispatch = [null];
                          isSelectedVehicle = [null];
                          selectedTrack = null;
                          $set(isSelectedShipment.proposed, i, !isSelectedShipment.proposed[i]);
                          getDetailShipment(shipment.shipment_id);
                        "
                      >
                        <v-container
                          fluid
                          class="pa-6"
                        >
                          <div class="d-flex justify-space-between">
                            <h4>
                              {{ shipment.shipment.logistics_service_provider.name }} <span class="pl-2 red--text">{{ checkNew(shipment.created_at) }}</span>
                            </h4>
                            <v-chip
                              v-if="shipment?.status === 'EXPIRED'"
                              label
                              class="chip-danger font-weight-medium mr-1"
                            >
                              <p class="ma-0 subtitle-1 text-primary">
                                Expired
                              </p>
                            </v-chip>
                          </div>

                          <div class="d-flex justify-space-between">
                            <p class="ma-0 subtitle-1 mt-2 mr-3 red--text">
                              {{ shipment.shipment.orders[0]?.identity }}
                            </p>
                            <p />
                            <p class="ma-0 mt-2 text-secondary">
                              {{ $moment(shipment?.created_at).format('DD-MM-YY | HH:mm') }}
                            </p>
                          </div>

                          <v-divider class="my-4" />

                          <div class="ma-0 mb-6">
                            <v-row class="ma-0 d-flex justify-space-between">
                              <p class="body-1 text-secondary">
                                Remaining Time
                              </p>
                              <div v-if="shipment?.expired_at === null && shipment?.set_expired_at === null">
                                <h5 class="text-bold" :class="getColor(shipment.updated_at)">
                                  {{ remainingHours(shipment?.created_at) }}
                                </h5>
                              </div>
                              <div v-else>
                                <h5 class="text-bold" :class="getColor(shipment.updated_at)">
                                  {{ remainingHoursManualExpired(shipment?.set_expired_at) }}
                                </h5>
                              </div>
                            </v-row>
                            <div v-if="shipment.type === 'RITASE'">
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Order
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment.ritase_identity === null ? 1 : shipment.ritase_identity + 1 }}
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Vehicles Types
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment.vehicles.length }} Types
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Units
                                </p>
                                <h5 class="text-bold">
                                  {{ totalVehicle(shipment) }} Unit
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Commodity
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment?.shipment?.orders[0]?.suborders[0]?.products[0]?.name }}
                                </h5>
                              </v-row>
                            </div>
                            <div v-else>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Order
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment.ritase_identity === null ? 1 : shipment.ritase_identity + 1 }}
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Vehicles Types
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment.vehicles.length }} Types
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Units
                                </p>
                                <h5 class="text-bold">
                                  {{ totalVehicle(shipment) }} Unit
                                </h5>
                              </v-row>
                              <v-row class="ma-0 d-flex justify-space-between">
                                <p class="body-1 text-secondary">
                                  Commodity
                                </p>
                                <h5 class="text-bold">
                                  {{ shipment?.shipment?.orders[0]?.suborders[0]?.products[0]?.name }}
                                </h5>
                              </v-row>
                            </div>
                            <v-row class="ma-0 d-flex justify-space-between">
                              <p class="body-1 text-secondary">
                                PMKS/Mill
                              </p>
                              <h5 class="text-bold">
                                {{ shipment?.shipment?.orders[0]?.suborders[0]?.pickup_drop_off_location_point?.name }}
                              </h5>
                            </v-row>
                            <v-row class="ma-0 d-flex justify-space-between">
                              <p class="body-1 text-secondary">
                                Refinery
                              </p>
                              <h5 class="text-bold">
                                {{ shipment?.shipment?.orders[0]?.suborders[1]?.pickup_drop_off_location_point?.name }}
                              </h5>
                            </v-row>
                            <div v-if="shipment.disable" class="body-1 text-primary pa-2" style="background: #FDEBEB">
                              You need to accept previous order first before you can accept this order!
                            </div>
                            <div v-else class="body-1 pa-2" style="background: transparent; color: transparent;">
                              You need to accept previous order first before you can accept this order!
                            </div>
                            <div v-if="shipment.note" class="body-1 pa-2 mt-2" style="background: #F5F5F5">
                              <div class="pa-2">
                                <p class="subtitle-1 p">
                                  Note :
                                </p>
                                <p>{{ shipment?.note }}</p>
                              </div>
                            </div>
                          </div>
                          <v-row class="pa-2 d-flex justify-center">
                            <accept-order-shipment-dialog
                              :disabled="shipment.disable"
                              dialog-title="Request Order Shipment"
                              btn-name="Accept"
                              :vehicle-detail="vehicleDetail"
                              :data-vehicle="listVehicles"
                              :driver="drivers"
                              :shipment="shipment"
                              :is-loading-detail="isLoadingDialogDetail"
                              @on-dialog-close="
                                $store.commit('shipment/SET_DIALOG_ITEM', null)
                              "
                              @on-accept-order="acceptShipment($event)"
                              @on-vehicle-change="onVehicleDialogChange"
                              @on-driver-change="onDriverDialogChange"
                            />
                            <reject-order-shipment-dialog
                              :disabled="shipment?.status !== 'PROPOSED' && shipment?.status !== 'EXPIRED'"
                              dialog-title="Request Order Shipment"
                              btn-name="Reject"
                              :shipment="requestShipmentDetail || shipment"
                              :is-loading-detail="isLoadingDialogDetail"
                              @on-dialog-open="rejectOrderDialog = true"
                              @on-reject-order="rejectShipment"
                              @on-dialog-close="rejectOrderDialog = false"
                            />
                          </v-row>
                        </v-container>
                      </v-list-item>
                    </v-col>
                  </v-row>
                </v-list-item-group>
              </v-list>
              <empty-placeholder
                v-else
                hero="empty-request-order.svg"
                :max-width="160"
                :message-title="$t('vendorCreateOrder.empty_request_order_title')"
                :message-description="$t('vendorCreateOrder.empty_request_order_description')"
              />
            </v-container>
          </v-container>
        </v-tab-item>

        <v-tab-item>
          <v-container fluid class="pa-0 mt-6">
            <create-order-loading v-if="isLoading" loader-type="card_shipment" />

            <v-container v-else fluid class="pa-0">
              <v-list
                v-if="dataShipmentDispatch?.items?.length > 0"
                flat
                class="pa-0"
              >
                <v-list-item-group mandatory>
                  <v-row class="ma-n3">
                    <v-col
                      v-for="(shipment, i) in dataShipmentDispatch?.items"
                      :key="shipment.id"
                      cols="4"
                      class="pa-5 col-lg-4 col-md-6 col-12"
                    >
                      <v-list-item
                        class="pa-0 rounded"
                        :style="isSelectedShipment.dispatch[i] ? `border: 1px solid #EF3434;` : 'border: 1px solid #CFCCCC;'"
                        @click="
                          isSelectedShipment.proposed = [null]
                          isSelectedShipment.dispatch = [null]
                          isSelectedVehicle = [null]
                          selectedTrack = null
                          $set(isSelectedShipment.dispatch, i, !isSelectedShipment.dispatch[i])
                          getDetailShipment(shipment.id)
                        "
                      >
                        <v-container
                          fluid
                          class="pa-6"
                        >
                          <h4>{{ shipment.logistics_service_provider.name }}</h4>

                          <v-divider class="my-4" />

                          <div>
                            <p class="ma-0 subtitle-1">
                              {{ shipment.orders[0]?.identity }}
                            </p>
                            <p class="ma-0 body-1">
                              - {{ $t('vendorCreateOrder.vehicle') }}
                            </p>
                          </div>
                        </v-container>
                      </v-list-item>
                    </v-col>
                  </v-row>
                </v-list-item-group>
              </v-list>

              <empty-placeholder
                v-else
                hero="empty-request-order.svg"
                :max-width="160"
                message-title="Shipment is Empty"
                message-description="Right now you don't have a Request Shipment from LSA"
              />
            </v-container>
          </v-container>
        </v-tab-item>

        <v-tab-item>
          <v-container fluid class="pa-0 mt-6">
            <create-order-loading v-if="isLoading" loader-type="card_shipment" />

            <v-container v-else fluid class="pa-0">
              <v-data-table
                :headers="expiredTableHeaders"
                :items="expiredShipments"
                :loading="isLoading"
                :items-per-page="10"
                hide-default-footer
                class="elevation-1"
              >
                <template #item.no="{ index }">
                  {{ index + 1 }}
                </template>
                <template #item.order="{ item }">
                  {{ item?.shipment?.orders?.[0]?.identity }}
                </template>
                <template #item.created_at="{ item }">
                  {{ $moment(item.created_at).format('DD-MM-YYYY HH:mm') }}
                </template>
                <template #item.company="{ item }">
                  {{ item?.shipment?.logistics_service_provider?.name }}
                </template>
                <template #item.reorder="{ item }">
                  {{ item?.ritase_identity === null ? 1 : item?.ritase_identity + 1 }}
                </template>
                <template #item.units="{ item }">
                  {{ totalVehicle(item) }}
                </template>
                <template #item.quantity="{ item }">
                  {{ formatNumber(item?.weight) }} Kg
                </template>
                <template #item.mill="{ item }">
                  {{ item?.shipment?.orders[0]?.suborders[0]?.pickup_drop_off_location_point?.name }}
                </template>
                <template #item.refinery="{ item }">
                  {{ item?.shipment?.orders[0]?.suborders[1]?.pickup_drop_off_location_point?.name }}
                </template>
              </v-data-table>
            </v-container>
          </v-container>
        </v-tab-item>
      </v-tabs-items>
    </v-container>

    <create-order-loading v-if="isLoadingDetail" loader-type="detail_shipment" />

    <v-row v-else class="ma-n5">
      <v-col class="pa-5 d-flex flex-column col-md-5 col-12">
        <v-container v-if="selectedTrackId !== null" fluid class="pa-10 mb-10 rounded white">
          <v-container fluid class="pa-0 mb-4 d-flex justify-space-between align-center">
            <p class="ma-0 subtitle-1">
              {{ $t('vendorCreateOrder.detail_vehicle') }}
            </p>
            <!-- <form-update-track-dialog
              v-if="detailShipment.status === 'DISPATCH'"
              :dialog="dialogUpdateTrack"
              :tracks="detailShipment.tracks"
              :vehicle-details="vehicleDetail"
              :drivers="drivers"
              :is-loading-form="isLoadingFormShipment"
              @on-close-dialog="dialogUpdateTrack = false"
              @on-click-update="updateAssignShipment($event)"
            >
              <template #activator>
                <v-btn
                  v-if="selectedTrack !== null"
                  large
                  outlined
                  color="primary"
                  class="px-3"
                  @click="onOpenUpdateTrackDialog"
                >
                  <v-icon class="mr-2">
                    mdi-pencil
                  </v-icon>
                  <p class="ma-0 subtitle-2">
                    {{ $t('vendorCreateOrder.update_button') }}
                  </p>
                </v-btn>
              </template>
            </form-update-track-dialog> -->
          </v-container>
          <h3>
            {{ detailShipment?.vendor?.name }}
          </h3>
          <v-list flat class="pa-0">
            <v-list-item-group
              v-for="(vehicle, i) in detailShipment?.shipment_vendors"
              :key="vehicle.id"
            >
              <div v-if="!isHasRitase && vehicle.type === 'GENERAL'">
                <p class="subtitle-1 mb-2 black--text mt-3">
                  {{ vehicle?.vendor?.name }}
                </p>
                <v-list-item
                  class="pa-0 mt-2 rounded d-flex flex-column"
                  :style="isSelectedVehicle[i] ? `border: 1px solid #EF3434;` : 'border: 1px solid #CFCCCC;'"
                  @click="
                    isSelectedVehicle = [null]
                    $set(isSelectedVehicle, i, !isSelectedVehicle[i])
                    selectedTrack = track
                    getDetailShipment(detailShipment.id)
                  "
                >
                  <v-container
                    v-for="(detailVehicle) in vehicle?.vehicles"
                    :key="detailVehicle.id"
                    class="pa-5 d-flex"
                  >
                    <image-component
                      v-if="$vuetify.breakpoint.smAndUp"
                      :image="detailVehicle?.photo_url"
                      class="mr-5"
                    />

                    <div class="d-flex flex-column align-start">
                      <v-img
                        v-show="$vuetify.breakpoint.xs"
                        :src="vehicle.photo_url"
                        width="80"
                        aspect-ratio="1"
                        contain
                        class="mr-5"
                      />
                      <p class="subtitle-1 mb-2 black--text">
                        <span class="red--text">
                          {{ detailVehicle?.pivot?.quantity }}x
                        </span>{{ detailVehicle?.name }}
                      </p>
                      <p class="subtitle-1 text-secondary">
                        {{ detailVehicle?.plate_number }}
                      </p>
                    <!-- <p class="caption mb-2 text-secondary">
                      {{ $t('vendorCreateOrder.features') }}
                    </p>
                    <v-row class="ma-n1">
                      <v-col
                        class="pa-1 col-auto"
                      >
                        <v-chip
                          label
                          outlined
                        >
                          {{ vehicle?.features }}
                        </v-chip>
                      </v-col>
                    </v-row> -->
                    </div>
                  </v-container>
                </v-list-item>
              </div>
              <div v-else-if="isHasRitase && vehicle.type === 'RITASE' && vehicle.status === 'PROPOSED'">
                <p class="subtitle-1 mb-2 black--text mt-3">
                  {{ vehicle?.vendor?.name }}
                </p>
                <v-list-item
                  class="pa-0 mt-2 rounded d-flex flex-column"
                  :style="isSelectedVehicle[i] ? `border: 1px solid #EF3434;` : 'border: 1px solid #CFCCCC;'"
                  @click="
                    isSelectedVehicle = [null]
                    $set(isSelectedVehicle, i, !isSelectedVehicle[i])
                    selectedTrack = track
                    getDetailShipment(detailShipment.id)
                  "
                >
                  <v-container
                    v-for="(detailVehicle) in vehicle?.vehicles"
                    :key="detailVehicle.id"
                    class="pa-5 d-flex"
                  >
                    <image-component
                      v-if="$vuetify.breakpoint.smAndUp"
                      :image="detailVehicle?.photo_url"
                      class="mr-5"
                    />

                    <div class="d-flex flex-column align-start">
                      <v-img
                        v-show="$vuetify.breakpoint.xs"
                        :src="vehicle.photo_url"
                        width="80"
                        aspect-ratio="1"
                        contain
                        class="mr-5"
                      />
                      <p class="subtitle-1 mb-2 black--text">
                        <span class="red--text">
                          {{ detailVehicle?.pivot?.quantity }}x
                        </span>{{ detailVehicle?.name }}
                      </p>
                      <p class="subtitle-1 text-secondary">
                        {{ detailVehicle?.plate_number }}
                      </p>
                    <!-- <p class="caption mb-2 text-secondary">
                      {{ $t('vendorCreateOrder.features') }}
                    </p>
                    <v-row class="ma-n1">
                      <v-col
                        class="pa-1 col-auto"
                      >
                        <v-chip
                          label
                          outlined
                        >
                          {{ vehicle?.features }}
                        </v-chip>
                      </v-col>
                    </v-row> -->
                    </div>
                  </v-container>
                </v-list-item>
              </div>
            </v-list-item-group>
          </v-list>
          <p class="pa-0 subtitle-1 mb-5 mt-6">
            Detail Order Shipment
          </p>
          <v-col class="pa-0 d-flex justify-space-between">
            <!-- <div>
              <p class="mb-2 body-1 text-secondary">
                Volume:
              </p>
              <h1>
                {{ +( detailShipment?.total_volume)?.toFixed(2) }} CBM
              </h1>
            </div> -->
            <div>
              <p class="mb-2 body-1 text-secondary">
                {{ $t('lspHistoryShipment.total_weight') }}:
              </p>
              <h1>
                {{ formatNumber(parseFloat(detailShipment?.shipment_vendors[0]?.weight)) }} KG
              </h1>
            </div>
          </v-col>
          <!-- <p class="subtitle-1 mb-5">
            Detail
          </p>

          <v-col class="d-flex justify-space-between pa-0">
            <div>
              <p class="caption text-secondary ma-0">
                {{ $t('lspHistoryShipment.total_length') }}
              </p>
              <p class="subtitle-1 ma-0">
                {{ detailShipment?.total_dimension_length }} CM
              </p>
            </div>
            <div>
              <p class="caption text-secondary ma-0">
                {{ $t('lspHistoryShipment.total_width') }}
              </p>
              <p class="subtitle-1 ma-0">
                {{ detailShipment?.total_dimension_width }} CM
              </p>
            </div>
            <div>
              <p class="caption text-secondary ma-0">
                {{ $t('lspHistoryShipment.total_height') }}
              </p>
              <p class="subtitle-1 ma-0">
                {{ detailShipment?.total_dimension_height }} CM
              </p>
            </div>
          </v-col> -->
        </v-container>

        <v-container fluid class="pa-0">
          <v-responsive aspect-ratio="1" class="rounded" style="z-index: 0">
            <custom-map
              :latitude="centerLatLng?.lat"
              :longitude="centerLatLng?.lng"
              :polyline="polyline"
              :zoom="zoomMap"
            >
              <template #marker>
                <l-marker
                  v-for="(marker, index) in markers"
                  :key="index"
                  :lat-lng="[parseFloat(marker.lat), parseFloat(marker.lng)]"
                >
                  <l-icon
                    v-if="marker.type === 'PICKUP'"
                    :icon-anchor="[20, 30]"
                  >
                    <v-icon
                      size="40"
                      color="info"
                    >
                      mdi-map-marker
                    </v-icon>
                  </l-icon>

                  <l-icon
                    v-else
                    :icon-anchor="[20, 30]"
                  >
                    <v-icon
                      size="40"
                      color="success"
                    >
                      mdi-map-marker
                    </v-icon>
                  </l-icon>

                  <l-popup>
                    <div>
                      <p
                        class="subtitle-3 mb-1"
                        :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                      >
                        {{ marker.type }}
                      </p>
                      <p class="caption ma-0 text--primary">
                        {{ marker.name }}
                      </p>
                    </div>
                  </l-popup>
                </l-marker>

                <l-marker
                  v-for="marker in driverMarker"
                  :key="marker.id"
                  :lat-lng="marker.latLng"
                >
                  <l-icon :icon-anchor="[20, 40]">
                    <v-icon
                      color="primary"
                      size="40"
                      style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                    >
                      mdi-map-marker-account
                    </v-icon>
                  </l-icon>
                </l-marker>
              </template>
            </custom-map>
          </v-responsive>
        </v-container>
      </v-col>

      <v-col class="pa-5">
        <v-container fluid class="pa-10 white rounded">
          <v-container fluid class="pa-0 mb-10 d-flex justify-space-between">
            <h4>Detail Order Shipment</h4>
            <div>
              <p v-if="selectedTrackId !== null" class="ma-0 body-1 text-secondary">
                {{ detailShipment?.orders[0]?.identity }}
              </p>
              <!-- <p v-if="selectedTrackId !== null" class="ma-0 body-1 text-secondary">
                {{ detailShipment?.identity }}
              </p> -->
            </div>
          </v-container>

          <timeline-detail-shipment
            v-if="selectedTrackId !== null"
            :selected-routes="selectedRoutes"
          />

          <empty-placeholder
            v-else
            hero="empty-detail-order-shipment.svg"
            :max-width="200"
            :message-title="$t('vendorCreateOrder.empty_detail_shipment_title')"
            :message-description="$t('vendorCreateOrder.empty_detail_shipment_description')"
          />
        </v-container>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import CustomMap from '@/components/shipping-company/CustomMap.vue'
import AcceptOrderShipmentDialog from '~/components/vendor/AcceptOrderShipmentDialog.vue'
import RejectOrderShipmentDialog from '~/components/RejectOrderShipmentDialog.vue'
import TimelineDetailShipment from '~/components/detail-shipment/TimelineDetailShipment.vue'
import { Route, Shipment, Track } from '~/types/shipment'
import CreateOrderLoading from '~/components/loading/CreateOrderVendorLoading.vue'
import {
  checkIsNew,
  defaultLat,
  defaultLng,
  formatNumber,
  generateCenterLatLng,
  generateDriverMarker,
  generatePolyline,
  zoom
} from '~/utils/functions'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import FormUpdateTrackDialog from '~/components/vendor/FormUpdateTrackDialog.vue'
import { Driver } from '~/types/driver'
import { Vehicle, VehicleDetail } from '~/types/vehicle'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'CreateOrderPage',

  components: {
    TimelineDetailShipment,
    CustomMap,
    AcceptOrderShipmentDialog,
    RejectOrderShipmentDialog,
    CreateOrderLoading,
    EmptyPlaceholder,
    FormUpdateTrackDialog,
    ImageComponent
  },
  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    searchKey: '',
    selectedTrackId: null as string | null,
    selectedRoutes: [] as Route[] | null,
    rejectOrderDialog: false,
    polyline: null as Polyline | null,
    markers: [] as any[],
    centerLatLng: {
      lat: defaultLat,
      lng: defaultLng
    } as { lat: number, lng: number },
    zoomMap: 4,
    tab: null as number | null,
    itemGroup: null,
    isSelectedShipment: {
      proposed: [] as any[],
      dispatch: [] as any[]
    },
    isSelectedVehicle: [] as any[],
    page: 1,
    pageProposed: 1,
    pageDispatch: 1,
    selectedTrack: null as Track | null,
    dialogUpdateTrack: false,
    maxRemainingTime: 6 * 60 * 60 * 1000,
    expiredTableHeaders: [
      { text: 'No', value: 'no', sortable: false },
      { text: 'Date', value: 'created_at', sortable: false },
      { text: 'Order', value: 'order', sortable: false },
      { text: 'Company', value: 'company', sortable: false },
      { text: 'Reorder', value: 'reorder', sortable: false },
      { text: 'Units', value: 'units', sortable: false },
      { text: 'Quantity', value: 'quantity', sortable: false },
      { text: 'Mill', value: 'mill', sortable: false },
      { text: 'Refinery', value: 'refinery', sortable: false },
    ]
  }),

  computed: {
    dataShipment () {
      return this.$store.getters['shipment/dataShipments']
    },
    dataShipmentProposed () {
      return this.$store.getters['shipment/dataShipmentProposed']
    },
    dataShipmentVendorProposed () {
      return this.$store.getters['shipment/dataShipmentVendorProposed']
    },
    dataShipmentVendor () {
      return this.$store.getters['shipment/dataShipmentVendor']
    },
    dataShipmentDispatch () {
      return this.$store.getters['shipment/dataShipmentDispatch']
    },
    detailShipment (): Shipment {
      return this.$store.getters['shipment/detailShipment']
    },
    isHasRitase (): boolean {
      return this.$store.getters['shipment/detailShipment']?.shipment_vendors?.filter((e:any) => e.type === 'RITASE')?.length > 0
    },
    requestShipmentDetail () {
      return this.$store.getters['shipment/dialogDetailData']
    },
    drivers () {
      return this.$store.getters['vehicle/drivers/data'].items as Driver[]
    },
    vehicleDetail () {
      return this.$store.getters['vehicle/details/data'].items as VehicleDetail[]
    },
    isLoading () {
      return this.$store.getters['shipment/isLoadingShipmentVendor']
    },
    isLoadingDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    },
    isLoadingFormShipment () {
      return this.$store.getters['shipment/isLoadingForm']
    },
    isLoadingDialogDetail () {
      return this.$store.getters['shipment/isLoadingDialog']
    },
    vendorId (): string {
      return this.$auth.$state.user.data.vendor_id
    },
    driverMarker (): object[] {
      return generateDriverMarker(this.polyline?.fms!)
    },
    listVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/listVehicles']
    },
    expiredShipments() {
      return this.$store.getters['shipment/dataShipmentVendor']?.items?.filter((item: { status: string }) => item.status === 'EXPIRED') || []
    }
  },

  watch: {
    tab () {
      if (this.tab === 0) {
        this.getDataShipmentVendor({})
      } else if (this.tab === 1 && this.dataShipmentDispatch?.items === null) {
        this.getDataShipmentDispatch({})
      } else if (this.tab === 2) {
        this.getDataShipmentExpired({})
      }
    },

    detailShipment () {
      if (this.detailShipment && this.detailShipment?.tracks?.length > 0) {
        this.selectedTrackId = this.detailShipment?.tracks?.[0].id
        this.selectedRoutes = this.detailShipment?.tracks?.[0].routes

        this.setupDirections()

        this.polyline = generatePolyline(this.detailShipment?.tracks)
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)
      } else {
        this.selectedTrackId = null
        this.selectedRoutes = null
      }
    },
    requestShipment () {
      if (this.dataShipment.items.length > 0) {
        this.getDetailShipment(this.isSelectedShipment.proposed[0].id)
      }
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')
  },

  mounted () {
    setTimeout(() => {
      // this.getDataShipmentProposed({})
      this.getDataShipmentVendor({})
      // this.$store.dispatch('vehicle/getItems', {
      //   page: 0,
      //   entries: -1
      // })
      this.getLicenseCategories()
      this.getData({})
      this.getDataVehicles({ page: this.$route?.query?.page as string })
      this.getDataVehicleDetails()
    }, 100)
  },

  methods: {
    formatNumber,
    onOpenUpdateTrackDialog () {
      this.dialogUpdateTrack = true

      this.getVehicleDetailData(1)
      this.getDriverData(1)
    },

    getDataVehicles ({
      page = '',
      searchKey = '',
      entries = 0,
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/getItems', {
        filterColumns: 'vendor_id',
        filterKeys: user?.vendor?.id,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        searchKey,
        page,
        entries
      })
    },

    getLicenseCategories () {
      this.$store.dispatch('vehicle/drivers/license-category/getItems')
    },

    getData ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/drivers/getItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        mode: "simplified",
        filterKeys: user?.vendor?.id,
        entries: -1
      })
    },

    getDataVehicleDetails () {
      this.$store.dispatch('vehicle/details/getItems', {
        mode: "simplified"
      })
    },

    // getDataShipmentProposed ({
    //   page = 1,
    //   entries = 3
    // }) {
    //   if (page < 1 || page > this.dataShipmentProposed?.totalPage) {
    //     return
    //   }

    //   this.pageProposed = page

    //   this.$store.dispatch('shipment/getItemProposed', {
    //     page,
    //     entries,
    //     filterColumns: 'shipment_vendors.status,shipment_vendors.vendor_id',
    //     filterKeys: `PROPOSED,${this.vendorId}`
    //   })
    // },

    async getDataShipmentVendor ({
      page = 1,
      entries = 3
    }) {
      if (page < 1 || page > this.dataShipmentVendor?.totalPage) {
        return
      }

      this.pageProposed = page

      await this.$store.dispatch('shipment/getItemShipmentVendor', {
        page,
        entries,
        filterColumns: 'status,vendor_id,user_accepted_at,expired_at',
        filterKeys: `PROPOSED|DISPATCH|ON_PROGRESS,${this.vendorId},<null>,<null>`
      })
    },

    async getDataShipmentExpired ({
      page = 1,
      entries = 10
    }) {
      if (page < 1 || page > this.dataShipmentVendor?.totalPage) {
        return
      }

      this.pageProposed = page

      await this.$store.dispatch('shipment/getItemShipmentVendor', {
        page,
        entries,
        filterColumns: 'status,vendor_id,user_accepted_at,expired_at',
        filterKeys: `EXPIRED,${this.vendorId},<null>,<!null>`
      })
    },

    getDataShipmentDispatch ({
      page = 1,
      entries = 3
    }) {
      if (page < 1 || page > this.dataShipmentDispatch?.totalPage) {
        return
      }

      this.pageDispatch = page

      this.$store.dispatch('shipment/getItemDispatch', {
        page,
        entries,
        filterColumns: 'shipment_vendors.status,shipment_vendors.vendor_id',
        filterKeys: `DISPATCH,${this.vendorId}`
      })
    },
    getDetailShipment (id: any) {
      this.$store.dispatch('shipment/getItemDetailDashboard', {
        id
      })
    },

    // async getAcceptData (shipment: any) {
    //   this.getDetailShipment(shipment.shipment_id)

    //   await this.$store.dispatch('shipment/getDialogDetailShipment', {
    //     id: shipment.shipment_id
    //   })
    //   this.getDriverData(1)
    //   this.getVehicleDetailData(1)
    // },

    async acceptShipment (orderDataShipment: any) {
      const response = await this.$store.dispatch('shipment/acceptItem', orderDataShipment)

      if (response) {
        this.getDataShipmentDispatch({
          page: 1,
          entries: 3
        })
        this.getDataShipmentVendor({
          page: 1,
          entries: 3
        })

        await this.$router.push(this.localePath('/vendor/order-shipment/create-order/success-accept-shipment'))
      }
    },

    async rejectShipment (shipment: Shipment) {
      const response = await this.$store.dispatch('shipment/rejectItem', {
        id: shipment.id
      })

      if (response) {
        this.getDataShipmentDispatch({
          page: 1,
          entries: 3
        })
        this.getDataShipmentVendor({
          page: 1,
          entries: 3
        })
      }

      this.rejectOrderDialog = false
    },

    async updateAssignShipment (formValues: any[]) {
      const tracks = [] as any[]

      formValues.forEach((form) => {
        tracks.push({
          id: form.id,
          vehicle_detail_id: form.vehicle?.value,
          driver_id: form.driver?.value
        })
      })

      const response = await this.$store.dispatch('shipment/updateAssignShipment', {
        id: this.detailShipment?.id,
        tracks
      })

      if (response) {
        this.dialogUpdateTrack = false
      }
    },

    getDriverData (page: any) {
      this.$store.dispatch('vehicle/drivers/getItems', {
        searchKey: this.searchKey,
        page,
        entries: -1
      })
    },

    getVehicleDetailData (page: any) {
     this.$store.dispatch('vehicle/details/getItems', {
        page
      })
    },

    onVehicleDialogChange (selectedVehicle: any, track: any, removeIndex: any) {
      const vehicleDetailId: any = selectedVehicle?.id
      const vehicleId: any = track.id

      this.$store.commit('shipment/SET_DIALOG_ITEM_TRACK_VEHICLE_DETAIL_ID', {
        vehicleDetailId,
        vehicleId,
        removeIndex
      })
    },

    onDriverDialogChange (selectedDriver: any, track: any, removeIndex: any) {
      const driverId: any = selectedDriver?.value
      const trackId: any = track.id

      this.$store.commit('shipment/SET_DIALOG_ITEM_TRACK_DRIVER_ID', {
        driverId,
        trackId,
        removeIndex
      })
    },

    setupDirections () {
      const markers = [] as any

      this.detailShipment?.tracks?.forEach((track) => {
        track.routes?.forEach((route) => {
          const lat = parseFloat(route.pickup_drop_off_location_point.latitude)
          const lng = parseFloat(route.pickup_drop_off_location_point.longitude)
          const type = route.type
          const name = route.pickup_drop_off_location_point.name

          markers.push({
            lat,
            lng,
            type,
            name
          })
        })
      })

      this.markers = markers
    },

    checkNew (date: string) {
      return checkIsNew(date)
    },

    getColor (createdAt: Date) {
      const tomorrow9AM = this.$moment().add(1, 'day').set({ hour: 9, minute: 0, second: 0, millisecond: 0 })
      const diffInHours = tomorrow9AM.diff(createdAt, 'hours')

      return diffInHours > 24 ? 'ma-0 body-1 red--text font-weight-bold' : 'ma-0 body-1'
    },

    remainingHours (time: Date): string {
      const now = new Date()
      now.setHours(0, 0, 0, 0)

      const inputTime = new Date(time)
      inputTime.setHours(0, 0, 0, 0)

      if (inputTime.getTime() !== now.getTime()) {
        return ''
      }

      const currentTime = new Date()
      const tomorrow10AM = new Date(currentTime)
      tomorrow10AM.setDate(tomorrow10AM.getDate() + 1)
      tomorrow10AM.setHours(10, 0, 0, 0)

      const elapsedTime = tomorrow10AM.getTime() - currentTime.getTime()
      const remainingHours = Math.floor(elapsedTime / 3600000)
      const formattedHours = String(remainingHours).padStart(2, '0')

      return `${formattedHours} Hours`
    },

    remainingHoursManualExpired (expiredAt: Date): string {
      if (!expiredAt) {
        return ''
      }

      const now = new Date()
      const expiredTime = new Date(expiredAt)

      expiredTime.setUTCHours(expiredTime.getUTCHours() + 3)
      
      if (now >= expiredTime) {
        return '00 Hours'
      }
      const remainingTime = expiredTime.getTime() - now.getTime()

      const remainingHours = Math.floor(remainingTime / (1000 * 60 * 60))
      
      const formattedHours = String(remainingHours).padStart(2, '0')

      return `${formattedHours} Hours`
    },

    hideShipments (createdAt: Date) {
      const currentTime = new Date()

      const tomorrow9AM = currentTime

      if (tomorrow9AM.getHours() > 9) {
        tomorrow9AM.setDate(currentTime.getDate() + 1)
      }
      tomorrow9AM.setHours(9, 0, 0, 0)

      const shipmentTime = new Date(createdAt)

      const elapsedTime = tomorrow9AM.getTime() - shipmentTime.getTime()
      const remainingHours = Math.floor(elapsedTime / 3600000)
      const formattedHours = String(remainingHours).padStart(2, '0')
      return formattedHours
    },
    totalVehicle (shipment: any) {
      const totalQuantity = shipment.vehicles.reduce((sum: any, item: any) => {
        return sum + parseInt(item.pivot.quantity, 10)
      }, 0)
      return totalQuantity
    }

  }
})
</script>

<style scoped lang="scss">
$skeleton-loader-image-height: 100% !important;

.f-resize {
  font-size: 16px !important;
}

.chip-info {
  background: #E6F4F8 !important;
}

.chip-danger {
  background: #fde0e0 !important;
}

@media (min-width: 280px) and (max-width: 576px) {

  .f-resize {
    font-size: 12px !important;
  }

  .f-resize-16 {
    font-size: 16px !important;
  }

  .f-resize-14 {
    font-size: 14px !important;
  }
  .icon-resize {
    font-size: 16px !important;
  }

}

</style>

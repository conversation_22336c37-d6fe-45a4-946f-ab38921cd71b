<template>
  <div v-if="isLoading || isLoadingDropOffLocation">
    <v-progress-linear
      indeterminate
      color="primary"
    />
  </div>
  <div v-else>
    <v-divider />
    <v-timeline
      align-top
      dense
    >
      <v-timeline-item v-for="(route, i) in selectedRoutes" :key="route.id">
        <div class="d-flex flex-column ml-2 ma-0 align-start">
          <div class="d-flex justify-space-between align-center" style="width: 100%">
            <div class="d-flex align-center">
              <h5 class="mr-3">
                {{ route.pickup_drop_off_location_point?.name }}
              </h5>
              <div :class="colorType(route.type)" class="bg-info-color px-3 py-1 text-info rounded font-weight-bold">
                {{ route.type }}
              </div>
            </div>

            <change-drop-off-destination-dialog
              v-if="route.type === 'DROPOFF' && userRole === 'LOGISTIC_SERVICE_PROVIDER'"
              :dialog="dialog[i]"
              :route="route"
              :orders="orders"
              :is-loading="isLoadingChangeDestination"
              :drop-off-locations="dropOffLocations"
              @on-click-close="$set(dialog, i, false)"
              @on-click-change="changeDestination($event, i)"
            >
              <template #activator>
                <v-icon
                  size="24"
                  color="secondary"
                  :disabled="shipmentStatus !== 'DISPATCH'"
                  @click="$set(dialog, i, true)"
                >
                  mdi-pencil
                </v-icon>
              </template>
            </change-drop-off-destination-dialog>
          </div>
          <v-card outlined class="d-inline-flex pa-2 col-sm-12 col-xl-10 col-lg-12 col-8 mt-5">
            <div class="flex flex-column">
              <v-col class="d-md-flex pa-0">
                <div class="col-sm-12 col-md-8 flex">
                  <h4 class="font-weight-medium black--text">
                    Detail
                  </h4>
                  <div class="d-flex flex-row justify-space-between mt-4">
                    <h5 class="font-weight-medium black--text">
                      {{ $t('lspHistoryShipment.product') }}
                    </h5>

                    <!-- <h5 class="font-weight-medium black--text">
                      {{ $t('lspHistoryShipment.quantity') }}
                    </h5> -->
                  </div>
                  <div class="d-flex flex-row justify-space-between">
                    <p class="my-1">
                      {{ route?.products?.[0]?.name }}
                    </p>

                    <!-- <p class="my-1">
                      {{ formatNumber(route?.products[0]?.weight) }} KG
                    </p> -->
                  </div>
                </div>
              </v-col>
              <v-divider v-if="$vuetify.breakpoint.mdAndUp" class="mx-5" horizontal />
              <v-divider v-else class="mx-5" />
              <div class="col-sm-12 col-md-10 flex">
                <div class="d-flex align-start justify-space-between">
                  <h5 class="font-weight-medium black--text">
                    Estimate
                  </h5>
                  <v-col class="col-6" />
                  <p class="my-1 d-flex justify-end">
                    {{ route.estimation_date }} WIB
                  </p>
                </div>
              </div>
              <!-- <div class="col-sm-12 col-md-10 flex">
                <div class="d-flex align-start justify-space-between">
                  <h5 class="font-weight-medium black--text">
                    Receive at
                  </h5>
                  <v-col class="col-6" />
                  <div>
                    <p v-if="route.type === 'PICKUP' && selectedRoutes[0]?.shipment_history" class="my-1 d-flex justify-end">
                      {{ selectedRoutes[0]?.shipment_history?.reported_at.slice(0, 16).replace("T", " | ") + " WIB" }}
                    </p>
                  </div>
                  <div v-if="route.type === 'DROPOFF' && selectedRoutes[1]?.shipment_history">
                    <p v-if="dataShipmentHistory.items.length > 0" class="my-1 d-flex justify-end">
                      {{ selectedRoutes[1]?.shipment_history?.reported_at.slice(0, 16).replace("T", " | ") + " WIB" }}
                    </p>
                  </div>
                </div>
              </div> -->
              <div class="col-sm-12 col-md-10 flex">
                <div class="d-flex align-start justify-space-between">
                  <h5 class="font-weight-medium black--text">
                    {{ $t('lspHistoryShipment.operational_time') }}
                  </h5>
                  <v-col class="col-5" />
                  <p class="my-1 d-flex justify-end">
                    {{ route.pickup_drop_off_location_point?.start_operation_hour }} - {{ route.pickup_drop_off_location_point?.end_operation_hour }} WIB
                  </p>
                </div>
              </div>
              <div class="col-sm-12 col-md-12 flex">
                <div class="d-flex align-start justify-space-between mt-3">
                  <h5 class="font-weight-medium black--text">
                    {{ $t('lspHistoryShipment.location') }}
                  </h5>
                  <v-col class="col-5" />
                  <p class="my-1 ml-6 d-flex justify-end">
                    {{ route.pickup_drop_off_location_point?.address }}
                  </p>
                </div>
              </div>
            </div>
          </v-card>
        </div>
      </v-timeline-item>
    </v-timeline>
    <v-divider />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Route } from '~/types/shipment'
import { colorType, formatNumber } from '~/utils/functions'
import ChangeDropOffDestinationDialog from '~/components/ChangeDropOffDestinationDialog.vue'
import { Order, PickupDropOffLocationPoint } from '~/types/product'
import { ShipmentHistory } from '~/types/shipment-history'

export default Vue.extend({
  name: 'TimelineDetailShipment',
  components: { ChangeDropOffDestinationDialog },

  props: {
    orders: {
      type: Array as () => Order[],
      default: () => []
    },

    shipmentStatus: {
      type: String,
      default: ''
    },

    selectedRoutes: {
      type: Array as () => Array<Route>,
      default: () => []
    },

    isLoading: {
      type: Boolean,
      default: false
    },

    shipmentId: {
      type: String || null,
      default: null
    }
  },

  data: () => ({
    dialog: [] as boolean[]
  }),

  computed: {
    userRole (): string {
      return this.$auth.$state.user.data.role
    },

    isLoadingChangeDestination (): boolean {
      return this.$store.getters['shipping-company/order-shipment/create-order/sub-order/isLoadingForm']
    },

    isLoadingDropOffLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },

    dropOffLocations (): PickupDropOffLocationPoint[] {
      return this.$store.getters['pick-up-drop-off-location-point/data'].items
    },

    dataShipmentHistory () {
      return this.$store.getters['shipment-history/data'] as {
        items: ShipmentHistory[]
      }
    }
  },

  mounted () {
    if (this.selectedRoutes !== undefined && this.selectedRoutes.length > 0 && this.shipmentId !== null) {
      this.getShipmentHistory()
    }

    if (this.userRole === 'LOGISTIC_SERVICE_PROVIDER') {
      this.getDropOffLocations()
    }
  },

  methods: {
    formatNumber,
    colorType (type: string):string {
      return colorType(type)
    },

    getDropOffLocations () {
      if (this.orders.length === 0) { return }
      const shippingCompanyIds = this.orders.map(order => (
        order.shipment_company_id
      ))

      this.$store.dispatch('pick-up-drop-off-location-point/getItems', {
        idSc: shippingCompanyIds.join('|'),
        filterColumns: 'type',
        filterKeys: 'PICKUP DROPOFF|DROPOFF',
        entries: -1
      })
    },

    async changeDestination (formValues: {
      subOrderId: string,
      orderNumber: string,
      locationId: string,
      note: string
    }, index: number) {
      const res = await this.$store.dispatch('shipping-company/order-shipment/create-order/sub-order/changeDestination', {
        ...formValues
      })

      if (res) {
        this.$set(this.dialog, index, false)
        this.$emit('on-success-change-destination')
        window.scrollTo(0, 0)
      }
    },

    getShipmentHistory () {
      this.$store.dispatch('shipment-history/getItems', {
        filterColumns: 'route.pickup_drop_off_location_point.id,route.track.shipment.id',
        filterKeys: this.selectedRoutes?.[0]?.pickup_drop_off_location_point?.id + ',' + this.shipmentId
      })
    }
  }
})
</script>

<style lang="scss" scoped>

</style>

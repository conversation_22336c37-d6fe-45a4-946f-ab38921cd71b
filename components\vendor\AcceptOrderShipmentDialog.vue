<template>
  <v-dialog
    v-model="dialog"
    max-width="750px"
    persistent
  >
    <template #activator="{ on, attrs }">
      <v-btn
        :disabled="disabled"
        elevation="0"
        color="primary"
        class="text-capitalize mr-6 col-md-4 col-12"
        v-bind="attrs"
        x-large
        v-on="on"
        @click="$emit('on-dialog-open', shipment,selectedDriver = [null], selectedVehicleDetailIds = [null]);"
      >
        {{ btnName }}
      </v-btn>
    </template>

    <v-card class="pa-10">
      <v-form>
        <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
          <h4>
            {{ dialogTitle }}
          </h4>

          <v-icon color="black" @click="dialog = false; selectedDriver = [null]; selectedVehicleDetailIds = [null];">
            mdi-close
          </v-icon>
        </v-card-title>

        <div class="text-start">
          <p>
            {{ $t('acceptOrderShipmentDialog.select_plate_number') }} <span class="font-weight-bold">#{{ shipment?.shipment?.orders[0]?.identity }}</span> ?
          </p>
          <div class="d-flex">
            <p>
              PMKS/Mill :
            </p>
            <p class="font-weight-bold ml-3">
              {{ filterMill }}
            </p>
            <p class="d-flex mx-10" />
            <p>
              Refinery :
            </p>
            <p class="font-weight-bold ml-3">
              {{ filterRefinery }}
            </p>
          </div>
        </div>

        <div v-for="(track, index) in shipment.vehicles" :key="index">
          <p class="mb-2 subtitle-1">
            {{ track?.name }}
          </p>
          <!-- <v-row
            v-for="i in getVehiclePivot(track)"
            :key="`${i}-${track?.id}`"
            class="mb-2"
          > -->
          <div v-if="form.shipment.tracks.length > 0 && form.shipment.tracks[index].vehicle_detail_id.length > 0" class="mb-2">
            <v-row v-for="(val, idx) in form.shipment.tracks[index].vehicle_detail_id" :key="idx" class="mt-2">
              <v-col class="col-6">
                <v-combobox
                  v-model="form.shipment.tracks[index].vehicle_detail_id[idx].vehicle_id"
                  :loading="vehicleDetail.length === 0"
                  :disabled="isLoadingDetail"
                  :items="
                    vehicleDetail.filter((e) => e?.vehicle_id === track.id)
                      .map(d => {
                        return { 
                          text: d.fms_identity 
                                    ? `${d.plate_number}`
                                    : `${d.plate_number}`,
                          value: d.id,
                          hasFmsIdentity: !!d.fms_identity
                        }
                      })
                  "
                  label="Vehicle"
                  hide-details
                  outlined
                  clearable
                  persistent-hint
                  validate-on
                  @change="onChangeVehicleDetail($event, track, idx)"
                >
                  <template v-slot:item="{ item }">
                    <span>{{ item.text }} </span>
                    <span v-if="item.hasFmsIdentity" class="red-fms-sync">&nbsp; &nbsp; (FMS Sync)</span>
                  </template>
                  <template #append-item>
                    <div class="fixed-append-item">
                      <vehicle-plate-form-dialog
                        :dialog="dialogCreatePlate"
                        :vendor-id="vendorId"
                        :selected-vehicle="vehicle"
                        @on-close-dialog="dialogCreatePlate = false"
                        @on-success-create="getDataVehicleDetails()"
                      >
                        <template #activator="{ on, attrs }">
                          <v-btn
                            elevation="0"
                            class="pa-0 col-12"
                            color="transparent"
                            v-bind="attrs"
                            v-on="on"
                            @click="dialogCreatePlate = true; clearForm = false"
                          >
                            <div class="d-flex justify-start mt-3 red--text">
                              <v-icon class="mb-4">
                                mdi-plus
                              </v-icon>
                              <p class="pa-1 text-capitalize">
                                Plate Number
                              </p>
                            </div>
                          </v-btn>
                        </template>
                      </vehicle-plate-form-dialog>
                    </div>
                  </template>
                </v-combobox>
                <p class="red--text" v-if="errorMessages[`${track.id}-${idx}`]">{{ errorMessages[`${track.id}-${idx}`] }}</p>
              </v-col>
              <v-col class="col-6">
                <v-combobox
                  v-model="form.shipment.tracks[index].vehicle_detail_id[idx].driver_id"
                  :loading="driver.length === 0"
                  :items="driver.filter(d => d.blocked_at === null).map(d => ({ text: d.name, value: d.id }))"
                  class="d-flex align-center"
                  label="Driver"
                  hide-details
                  outlined
                  clearable
                  persistent-hint
                  @change="onChangeDriver($event, track, idx)"
                >
                  <template #append-item>
                    <div class="fixed-append-item">
                      <add-driver-dialog
                        :license-categories="licenseCategories"
                        :submit-type="`create`"
                        :is-loading-form="isLoadingFormDriver"
                        :dialog="dialogCreateDriver"
                        :clear-form="clearForm"
                        @on-click-add="createItemDriver"
                        @on-close-dialog="dialogCreateDriver = false; clearForm = true"
                      >
                        <template #activator="{ on, attrs }">
                          <v-btn
                            elevation="0"
                            class="pa-0 col-12"
                            color="transparent"
                            v-bind="attrs"
                            v-on="on"
                            @click="dialogCreateDriver = true; clearForm = false"
                          >
                            <div class="d-flex justify-start mt-3 red--text">
                              <v-icon class="mb-4">
                                mdi-plus
                              </v-icon>
                              <p class="pa-1 text-capitalize">
                                Add Driver
                              </p>
                            </div>
                          </v-btn>
                        </template>
                      </add-driver-dialog>
                    </div>
                  </template>
                </v-combobox>
              </v-col>
              <!-- <v-col class="col-3">
                <v-btn
                  outlined
                  fab
                  style="border-color: #CFCCCC; border-radius: 4px"
                  @click="addOtherVehicle(track?.vehicle_detail?.vehicle_id, index)"
                >
                  <v-icon>
                    mdi-plus
                  </v-icon>
                </v-btn>
                <v-btn
                  outlined
                  fab
                  class="ml-2"
                  elevation="0"
                  style="border-color: #CFCCCC; border-radius: 4px"
                  :disabled="form.shipment.tracks[index].vehicle_detail_id.length === 1"
                  @click="removeVehicleList(track, index, idx)"
                >
                  <v-icon>
                    mdi-close
                  </v-icon>
                </v-btn>
              </v-col> -->
            </v-row>
          </div>
          <!-- </v-row> -->
        </div>

        <div v-if="shipment.tracks" class="my-4">
          <v-expansion-panels v-if="shipment.tracks?.filter((d) => d.tag === 'AUTO_WB').length > 0">
            <v-expansion-panel>
              <v-expansion-panel-header>
                Auto WB
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row v-for="(track, index) in shipment.tracks?.filter((d) => d.tag === 'AUTO_WB')" :key="index" class="my-2">
                  <v-col class="col-6">
                    <v-text-field
                      :disabled="true"
                      :value="track.vehicle_detail.plate_number"
                      hide-details
                      outlined
                      persistent-hint
                    />
                  </v-col>
                  <v-col class="col-6">
                    <v-text-field
                      :disabled="true"
                      :value="track.driver?.user.name"
                      class="d-flex align-center"
                      hide-details
                      outlined
                      persistent-hint
                    />
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>

        <v-card-actions class="mt-10 pa-0 d-flex justify-space-around">
          <v-row class="ma-0">
            <v-col class="mr-5 pa-0">
              <v-btn
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                :loading="isLoadingDetail"
                @click="onClickAccept"
              >
                {{ $t('acceptOrderShipmentDialog.accept_order') }}
              </v-btn>
            </v-col>
            <v-col class="pa-0">
              <v-btn
                elevation="0"
                outlined
                color="primary"
                class="text-capitalize ma-0"
                x-large
                block
                @click="dialog = false; selectedDriver = [null]; selectedVehicleDetailIds = [null]"
              >
                {{ $t('acceptOrderShipmentDialog.cancel') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Vehicle, VehicleDetail } from '~/types/vehicle'
import { Driver, LicenseCategory } from '~/types/driver'
import { Shipment, ShipmentVendor, Track } from '~/types/shipment'
import AddDriverDialog from '~/components/AddDriverDialog.vue'
import VehiclePlateFormDialog from '~/components/VehiclePlateFormCreateDialog.vue'
import { toastError } from '~/utils/toasts'

export default Vue.extend({
  name: 'AcceptOrderShipmentDialog',

  components: {
    AddDriverDialog,
    VehiclePlateFormDialog
  },

  props: {
    dialogTitle: {
      type: String,
      default: ''
    },
    shipment: {
      type: Object as () => ShipmentVendor,
      required: true
    },
    btnName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isLoadingDetail: {
      type: Boolean,
      default: false
    },
    vehicleDetail: {
      type: Array as () => Array<VehicleDetail>,
      default: () => []
    },
    driver: {
      type: Array as () => Array<Driver>,
      default: () => []
    },
    detailShipment: {
      type: Object as () => Array<Shipment>,
      default: () => {}
    }
  },

  data: () => ({
    message: '',
    errorMessages: {} as any,
    dialog: false,
    dialogCreateDriver: false,
    clearForm: false,
    selectedVehicleDetailIds: [] as any,
    dialogCreatePlate: false,
    selectedDriver: [] as any,
    form: {
      shipment: {
        tracks: [{
          vehicle_id: {
            text: '',
            value: ''
          } as any,
          driver_id: null,
          vehicle_detail_id: [{
            vehicle_id: '',
            driver_id: ''
          }] as any[]
        }]
      }
    }
  }),

  computed: {
    licenseCategories (): LicenseCategory[] {
      return this.$store.getters['vehicle/drivers/license-category/items']
    },
    isLoadingFormDriver (): boolean {
      return this.$store.getters['vehicle/drivers/isLoadingForm']
    },
    vendorId (): String {
      return this.$auth.$state.user.data.vendor_id
    },
    vehicles () {
      return this.$store.getters['vehicle/data']
    },
    filterMill (): string {
      const pickup = this.shipment?.shipment?.orders[0]?.suborders?.find(suborder => suborder.type === 'PICKUP')
      return pickup?.pickup_drop_off_location_point?.name || ''
    },
    filterRefinery (): string {
      const dropoff = this.shipment?.shipment?.orders[0]?.suborders?.find(suborder => suborder.type === 'DROPOFF')
      return dropoff?.pickup_drop_off_location_point?.name || ''
    }
  },

  watch: {
    dialog (val) {
      if (!val) {
        this.$emit('on-dialog-close')
      } else {
        this.$store.commit('shipment/SET_DIALOG_ITEM', this.shipment)
      }
    },

    shipment: {
      handler () {
        this.form.shipment.tracks = []
        this.shipment.vehicles.forEach((vehicle, i) => {
          this.form.shipment.tracks.push({
            vehicle_id: vehicle.id,
            driver_id: null,
            vehicle_detail_id: []
          })
          for (let index = 0; index < vehicle.pivot.quantity; index++) {
            this.form.shipment.tracks[i].vehicle_detail_id.push({
              vehicle_id: '',
              driver_id: ''
            })
          }
        })
      },
      immediate: true
    }

  },

  mounted () {
    // this.getData({})
    // this.getLicenseCategories()
    // this.getDataVehicles({
    //   page: this.$route?.query?.page as string
    // })
    // this.getDataVehicleDetails()
  },

  methods: {
    onChangeVehicleDetail(selectedItem: any, track: Vehicle, idx: any) {
      const vehicleId = track.selected_vehicle_detail_id?.[idx];
      const trackId = track.id;
      
      if (!selectedItem) {
        this.$store.commit('shipment/REMOVE_DIALOG_ITEM_TRACK_VEHICLE_ID', {
          vehicleId,
          trackId,
          removeIndex: idx
        });
        this.$set(this.errorMessages, `${trackId}-${idx}`, '');
        return;
      }

      let selectedVehicleDetail;
      
      if (!selectedItem.value) {
        const results = this.vehicleDetail.filter((e: any) => e?.vehicle_id === track.id && e.plate_number.toUpperCase() === (selectedItem as string).toUpperCase())
          .map(d => ({
            text: d.plate_number,
            value: d.id
          }));

        if (results.length > 0) {
          selectedVehicleDetail = results[0];
        } else {
          toastError('Kendaraan tidak ditemukan', this);
        }
      } else {
        selectedVehicleDetail = this.vehicleDetail.find(item => (item.id === selectedItem?.value)) as VehicleDetail;
      }

      this.$emit('on-vehicle-change', selectedVehicleDetail, track, idx);

      if (selectedVehicleDetail && 'plate_number' in selectedVehicleDetail && !selectedVehicleDetail.fms_identity) {
        this.$set(this.errorMessages, `${trackId}-${idx}`, `No plat [${selectedVehicleDetail.plate_number}] -> GPS belum terdaftar di sistem. Silakan pasang/daftarkan GPS kendaraan tersebut dan hubungi Apical agar truk dapat ditugaskan untuk mengangkut DO. Terima kasih.`);
      } else {
        this.$set(this.errorMessages, `${trackId}-${idx}`, '');
      }
    },

    onChangeDriver (selectedItem: any, track: Vehicle, idx: any) {
      const driverId = track.selected_driver_id?.[idx]
      const trackId = track.id

      if (!selectedItem) {
        this.$store.commit('shipment/REMOVE_DIALOG_ITEM_TRACK_DRIVER_ID', {
          driverId,
          trackId,
          removeIndex: idx
        })
        return
      }

      let selectedDriver = selectedItem

      if (!selectedItem.value) {
        const results = this.driver.filter(d => d.block_counts === 0 && d.blocked_at === null && selectedItem.toUpperCase() === d.user?.name.toUpperCase()
        ).map(d => ({
          text: d.user?.name,
          value: d.id
        }))

        if (results.length > 0) {
          selectedDriver = results[0]
        } else {
          toastError('Driver tidak ditemukan', this)
        }
      }

      this.$emit('on-driver-change', selectedDriver, track, idx)
    },

    onClickAccept () {
      this.$emit('on-accept-order', this.shipment)
    },

    async createItemDriver (value: any) {
      const user = this.$auth.user?.data as any
      const response = await this.$store.dispatch('vehicle/drivers/createItem', {
        value,
        vendorId: user.vendor.id
      })
      if (response) {
        this.dialogCreateDriver = false
        this.clearForm = true

        this.getData({})
      }
    },

    getData ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/drivers/getItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterKeys: user.vendor.id,
        mode: "simplified",
        entries: -1
      })
    },

    // getLicenseCategories () {
    //   this.$store.dispatch('vehicle/drivers/license-category/getItems')
    // },

    // getVehiclePivot (track: Track): number {
    //   const vehicles = this.shipment?.shipment_vendors?.filter((e: any) => e.status === 'PROPOSED')?.[0]?.vehicles?.filter((e: any) => e.id === track.vehicle_detail?.vehicle_id)
    //   return vehicles.length > 0 ? JSON.parse(vehicles[0].pivot.quantity) : 1
    // },

    // eslint-disable-next-line camelcase
    addOtherVehicle (i: number) {
      this.form.shipment.tracks[i].vehicle_detail_id.push({
        // eslint-disable-next-line camelcase
        vehicle_id: '',
        driver_id: ''
      })
    },

    // eslint-disable-next-line camelcase
    removeVehicleList (track: Track, i: number, j: number) {
      if (this.form.shipment.tracks[i].vehicle_detail_id.length > 0) {
        this.form.shipment.tracks[i].vehicle_detail_id.splice(j, 1)
        if (track?.selected_driver_id && track?.selected_vehicle_detail_id) {
          const driverId: any = track?.selected_driver_id.find((_, idx) => idx === j)
          const vehicleId: any = track?.selected_vehicle_detail_id.find((_, idx) => idx === j)
          const trackId: any = track.id
          this.$store.commit('shipment/REMOVE_DIALOG_ITEM_TRACK_DRIVER_ID', {
            driverId,
            trackId
          })
          this.$store.commit('shipment/REMOVE_DIALOG_ITEM_TRACK_VEHICLE_ID', {
            vehicleId,
            trackId
          })
        }
      }
    },

    getDataVehicleDetails () {
      this.$store.dispatch('vehicle/details/getItems')
    }
  }
})
</script>

<style lang="scss" scoped>
  .fixed-append-item {
    position: sticky;
    bottom: 0;
    background-color: white;
    z-index: 1;
    padding: 10px;
    border-top: 1px solid #ccc;
  }

  .red-fms-sync {
  color: red; /* Menambahkan color merah untuk kelas ini */
}
</style>

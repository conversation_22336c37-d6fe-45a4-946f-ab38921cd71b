<template>
  <v-container fill-height fluid>
    <v-row justify="space-between">
      <v-col class="pa-10">
        <v-row align="center" justify="center">
          <v-img
            :src="require(`~/assets/images/logo-lsi.png`)"
            max-width="80"
            contain
          >
            <template #placeholder>
              <v-img
                min-width="80"
                max-width="80"
                aspect-ratio="1"
                contain
                :src="require(`~/assets/images/placeholder-company-logo.svg`)"
              />
            </template>
          </v-img>
          <div class="ml-4 mr-4" />
          <div class="text-heading-6 spacer-y-lg" style="max-width: 10em">
            Logistic Service Integrator
          </div>
        </v-row>
        <v-container class="col-8">
          <button-back title="Back to Login" class="mt-7" />
          <div class="text-heading-5 mt-3">
            Forgot Password
          </div>
          <div class="text-body">
            Enter your email so we can send you a message requesting a password change.
          </div>
          <div class="spacer-y-2lg" />
          <v-form ref="form" v-model="isValid">
            <custom-text-field
              v-model="email"
              type="email"
              :hint="$t('Email')"
              prepend-inner-icon="mdi-email"
              :rules="[rulesRequired, rulesEmail]"
              is-required
              data-testid="input-email"
              @on-enter="login"
            />
            <div class="spacer-y-lg" />
            <v-btn
              height="52"
              color="primary"
              block
              class="text-capitalize"
              :disabled="!isValid"
              :loading="isLoading"
              @click="sendEmail"
            >
              Send Link
            </v-btn>
            <div class="spacer-y-lg" />
            <div class="mt-16" />
            <v-row class="mt-7" align="center" justify="center">
              <v-img
                :src="require(`~/assets/icons/powered.svg`)"
                max-width="150"
              />
            </v-row>
          </v-form>
        </v-container>
      </v-col>

      <v-container v-if="$device.isDesktop" class="pa-0 col-6">
        <div class="py-5 pr-5 fill-height" data-testid="image-side">
          <v-img
            :src="require('~/assets/images/banner-forget-password.png')"
            class="fill-height"
          />
        </div>
      </v-container>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '@/components/CustomTextField.vue'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'ForgetPasswordPage',

  components: {
    CustomTextField
  },

  middleware: 'is-logged-in',

  data: () => ({
    isValid: false as boolean,
    email: ''
  }),

  computed: {
    isLoading () {
      return this.$store.getters['password/isLoading']
    }
  },

  methods: {
    async sendEmail () {
      const response = await this.$store.dispatch('password/sendEmail', {
        email: this.email
      })
      if (response) {
        this.$router.push(('/login/forgot-password/check-email'))
      }
    },
    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    }
  }
})
</script>

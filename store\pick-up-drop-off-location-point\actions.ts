import { ActionTree } from 'vuex'
import { RootState } from '../index'
import { PickupDropOffLocationPointState } from './state'
import { toastError, toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<PickupDropOffLocationPointState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    const filterColumns: string = payload?.filterColumns
      ? `shipment_company_id,${payload?.filterColumns}`
      : 'shipment_company_id'

    const filterKeys: string = payload?.filterKeys
      ? (payload?.idSc ? `${payload?.idSc},${payload?.filterKeys}` : `${payload?.filterKeys}`)
      : payload?.idSc

    this.$axios
      .get('/v1/pickup-dropoff', {
        params: {
          search_columns: 'name',
          search_key: payload?.searchKey ?? '',
          filter_columns: filterColumns,
          filter_keys: filterKeys,
          entries: payload?.entries ?? -1
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  getItemsLocations ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/pickup-dropoff', {
        params: {
          search_columns: 'name',
          search_key: payload?.searchKey ?? '',
          filter_columns: payload.filter_columns,
          filter_keys: payload.filter_keys,
          entries: payload?.entries ?? -1
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .post('/v1/pickup-dropoff', {
        name: payload.name,
        identity: payload.identity,
        longitude: payload.longitude,
        latitude: payload.latitude,
        start_operation_hour: payload.startOperationHour,
        end_operation_hour: payload.endOperationHour,
        type: payload.type,
        address: payload.address,
        category: payload.category,
        shipment_company_id: payload.idSc
      })
      .then((response: any) => {
        dispatch('getItems', { idSc: payload.idSc })
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async updateItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .put('/v1/pickup-dropoff/' + payload.id, {
        identity: payload.identity,
        name: payload.name,
        longitude: payload.longitude,
        latitude: payload.latitude,
        start_operation_hour: payload.startOperationHour,
        end_operation_hour: payload.endOperationHour,
        type: payload.type,
        address: payload.address,
        category: payload.category,
        shipment_company_id: payload.idSc
      })
      .then((response: any) => {
        dispatch('getItems', { idSc: payload.idSc })
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios
      .delete('/v1/pickup-dropoff/' + payload.idLocation)
      .then((response: any) => {
        dispatch('getItems', { idSc: payload.idSc })
        toastSuccess(response.data.message, this)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  getItemsLocationKey ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/pickup-dropoff/import')
      .then((response: any) => {
        commit('SET_ITEMS_LOCATION_KEY', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => { commit('SET_IS_LOADING', false) })
  },

  async importLocations ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const reformattedFormValues = [] as object[]

    try {
      payload.locations.forEach((value: any) => {
        reformattedFormValues.push({
          identity: value.identity,
          name: value.name,
          longitude: value.longitude,
          latitude: value.latitude,
          start_operation_hour: value.start_operation_hour,
          end_operation_hour: value.end_operation_hour,
          type: value.type,
          address: value.address
        })
      })
    } catch (_) {
      toastError('Required Data is Empty', this)
      commit('SET_IS_LOADING_FORM', false)
      return false
    }

    return await this.$axios.post('/v1/pickup-dropoff/import', {
      locations: reformattedFormValues,
      shipment_company_id: payload.shipmentCompanyId
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  },

  async getTimeSuggestion({ commit }, params: any) {
    try {
      commit('SET_IS_LOADING', true)
      const { data } = await this.$axios.get('/v1/pickup-dropoff/time-suggestion', {
        params
      })
      return data
    } catch (error) {
      exceptionHandler(error, this)
      return null
    } finally {
      commit('SET_IS_LOADING', false)
    }
  }

}

export default actions

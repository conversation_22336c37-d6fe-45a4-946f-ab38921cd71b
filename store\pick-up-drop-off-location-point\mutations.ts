import { MutationTree } from 'vuex'
import { PickupDropOffLocationPointState } from './state'
import { ImportKey } from '~/types/import-key'

export const mutations: MutationTree<PickupDropOffLocationPointState> = {
  SET_ITEMS (state, items) {
    state.items = items
  },

  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  },

  SET_ITEMS_LOCATION_KEY (state, items: ImportKey[]) {
    state.itemsLocationKey = items
  }
}

export default mutations

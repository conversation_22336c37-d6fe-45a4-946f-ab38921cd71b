<template>
  <v-container fluid class="pa-10 white rounded">
    <v-container fluid class="mb-5 pa-0 d-flex justify-space-between align-start">
      <div>
        <p class="caption text-secondary mb-2">
          {{ $t('detailHistoryInvoice.invoice_number') }}
        </p>
        <h3>{{ dataInvoice?.invoice?.shipment?.identity }}</h3>
      </div>

      <div class="text-right">
        <p class="body-1 mb-5 text-secondary">
          {{ $moment(dataInvoice?.invoice?.shipment?.created_at).format('DD MMMM YYYY') }}
        </p>
        <slot name="download-invoice" />
      </div>
    </v-container>

    <div class="d-flex">
      <div class="mr-10">
        <p class="subtitle-1 mb-2">
          {{ $t('detailHistoryInvoice.from') }}
        </p>
        <div class="d-flex align-center">
          <image-component
            :image="dataInvoice?.invoice?.shipment?.vendor?.logo_url"
            min-width="60"
            max-width="60"
            class="mr-5"
          />
          <h4>{{ dataInvoice?.invoice?.shipment?.vendor?.name }}</h4>
        </div>
      </div>

      <div class="ml-10">
        <p class="subtitle-1 mb-2">
          {{ $t('detailHistoryInvoice.to') }}
        </p>
        <div class="d-flex align-center">
          <image-component
            :image="dataInvoice?.invoice?.shipment?.logistics_service_provider?.logo_url"
            min-width="60"
            max-width="60"
            class="mr-5"
          />
          <h4>{{ dataInvoice?.invoice?.shipment?.logistics_service_provider?.name }}</h4>
        </div>
      </div>
    </div>

    <v-divider class="my-5" />

    <v-row class="ma-0">
      <v-col cols="auto" class="pa-0">
        <p class="subtitle-1">
          Detail Order Shipment
        </p>

        <div class="mb-5 d-flex">
          <div class="mr-10">
            <p class="body-1 mb-1 text-secondary">
              Volume:
            </p>
            <h1>{{ dataInvoice?.invoice?.shipment?.total_volume }} CBM</h1>
          </div>

          <div class="ml-10">
            <p class="body-1 mb-1 text-secondary">
              {{ $t('detailHistoryInvoice.total_weight') }}
            </p>
            <h1>{{ dataInvoice?.invoice?.shipment?.total_weight }} KG</h1>
          </div>
        </div>

        <div class="d-flex mb-10">
          <div>
            <p class="caption ma-0 text-secondary">
              {{ $t('detailHistoryInvoice.total_length') }}:
            </p>
            <p class="subtitle-1 ma-0">
              {{ dataInvoice?.invoice?.shipment?.total_dimension_length }} CM
            </p>
          </div>
          <div class="mx-10">
            <p class="caption ma-0 text-secondary">
              {{ $t('detailHistoryInvoice.total_width') }}:
            </p>
            <p class="subtitle-1 ma-0">
              {{ dataInvoice?.invoice?.shipment?.total_dimension_width }} CM
            </p>
          </div>
          <div>
            <p class="caption ma-0 text-secondary">
              {{ $t('detailHistoryInvoice.total_height') }}:
            </p>
            <p class="subtitle-1 ma-0">
              {{ dataInvoice?.invoice?.shipment?.total_dimension_height }} CM
            </p>
          </div>
        </div>

        <p class="body-1 text-secondary">
          {{ $t('detailHistoryInvoice.estimated_delivery_distance') }}:
          <span class="ml-5 subtitle-1 black--text">{{ dataInvoice?.invoice_details?.at(0)?.total_odometer }} KM</span>
        </p>
      </v-col>

      <v-divider vertical class="mx-10" />

      <v-col class="pa-0">
        <p class="subtitle-1">
          Detail Transporter
        </p>

        <h3>
          {{ dataInvoice?.invoice?.shipment?.vendor?.name }}
        </h3>

        <v-container
          v-for="invoiceDetail in dataInvoice?.invoice_details"
          :key="invoiceDetail.id"
          fluid
          class="mt-5 pa-0"
        >
          <div class="d-flex">
            <image-component
              :image="invoiceDetail.vehicle_detail?.vehicle?.photo_url"
              min-width="120"
              max-width="120"
              class="mr-5"
            />

            <div>
              <h4 class="mb-1">
                {{ invoiceDetail.vehicle_detail?.vehicle?.name }}
              </h4>

              <div class="d-flex mb-5">
                <p class="body-1 text-secondary ma-0 mr-2">
                  {{ invoiceDetail.vehicle_detail?.plate_number }}
                </p>
              </div>

              <p class="caption mb-1 text-secondary">
                {{ $t('detailHistoryInvoice.features') }}
              </p>
              <div class="d-flex">
                <v-chip
                  v-for="vehicleFeature in invoiceDetail.vehicle_detail?.vehicle?.vehicle_features"
                  :key="vehicleFeature.id"
                  label
                  outlined
                  class="mr-2"
                >
                  {{ vehicleFeature.name }}
                </v-chip>
              </div>
            </div>
          </div>
        </v-container>
      </v-col>
    </v-row>

    <v-divider class="my-5" />

    <v-row class="ma-0">
      <v-col cols="auto" class="pa-0 mr-10">
        <p class="body-1 text-secondary ma-0">
          {{ $t('detailHistoryInvoice.shipment_fee') }}
        </p>
      </v-col>

      <v-col class="pa-0 ml-10">
        <v-container
          v-for="invoiceDetail in dataInvoice?.invoice_details"
          :key="invoiceDetail.id"
          fluid
          class="mb-5 pa-0 d-flex justify-space-between"
        >
          <p class="body-1 text-secondary ma-0">
            {{ invoiceDetail.vehicle_detail?.vehicle?.name }}
          </p>
          <p class="body-1 ma-0">
            {{ invoiceDetail.cost | toCurrency }}
          </p>
        </v-container>
      </v-col>
    </v-row>

    <v-container
      v-for="fee in dataInvoice?.invoice?.fees"
      :key="fee.id"
      fluid
      class="pa-0 d-flex justify-space-between"
    >
      <p class="body-1 ma-0 text-secondary">
        {{ fee.description }}
      </p>
      <p class="body-1 ma-0">
        {{ parseInt(fee.cost) | toCurrency }}
      </p>
    </v-container>

    <v-divider class="my-5" />

    <v-container fluid class="pa-0 d-flex align-center justify-space-between">
      <p class="ma-0 body-1">
        {{ $t('detailHistoryInvoice.amount_must_paid') }}
      </p>

      <h3 class="text-primary">
        {{ totalCost | toCurrency }}
      </h3>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'
import { InvoiceOrder } from '~/types/invoice'

export default Vue.extend({
  name: 'DetailHistoryInvoice',

  components: { ImageComponent },

  props: {
    dataInvoice: {
      type: Object as () => InvoiceOrder,
      default: () => {}
    },
    totalCost: {
      type: Number,
      default: 0
    }
  }
})
</script>

<style scoped lang="scss"> </style>

import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleDriversState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<VehicleDriversState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/drivers', {
        params: {
          page: payload == null ? 1 : payload.page,
          sort_column: payload.sortColumn,
          sort_type: payload.sortType,
          mode: payload.mode,
          search_columns: 'user.name',
          search_key: payload.searchKey,
          filter_columns: 'vendor_id',
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          entries: payload.entries ?? 9
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },
  getItemsFilterVendor ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v1/drivers', {
        params: {
          page: payload == null ? 1 : payload.page,
          sort_column: payload.sortColumn,
          sort_type: payload.sortType,
          mode: payload.mode,
          search_columns: 'user.name',
          search_key: payload.searchKey,
          filter_columns: payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          entries: payload.entries ?? 9
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },
  getItemFilters ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)
    this.$axios
      .get('/v2/drivers', {
        params: {
          page: payload == null ? 1 : payload.page,
          sort_column: payload.sortColumn,
          sort_type: payload.sortType,
          mode: payload.mode,
          search_columns: 'user.name',
          search_key: payload.searchKey,
          filter_columns: 'vendor_id',
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          entries: payload.entries ?? 9
        }
      })
      .then((response: any) => {
        commit('SET_RESULT_FILTER', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('vendor_id', payload.vendorId)
    formData.append('national_identity', payload.value.nationalIdentity)
    formData.append('user_name', payload.value.userName)
    formData.append('user_phone_country_code', payload.value.phoneCountryCode)
    formData.append('user_phone_number', payload.value.phoneNumber)

    payload.value.driverLicenses.forEach((item: {
      license_category_id: string,
      license_number: number
    }, i: number) => {
      formData.append(`drivers[${i}][license_category_id]`, item.license_category_id)
      formData.append(`drivers[${i}][license_number]`, String(item.license_number))
    })

    if (payload.value.avatar) {
      formData.append('avatar', payload.value.avatar)
    }

    return await this.$axios
      .post('/v1/drivers', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async updateItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    return await this.$axios
      .put('/v1/users/' + payload.value.userId, {
        name: payload.value.name,
        email: payload.value.email,
        phone_country_code: payload.value.phoneCountryCode,
        phone_number: payload.value.phone,
        password: payload.value.password,
        driver_id: payload.value.driverId,
        role: 'DRIVER'
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async updateDriver ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    
    const formData = new FormData()
    formData.append('name', payload.value.name)
    formData.append('phone_country_code', payload.value.phoneCountryCode)
    formData.append('phone_number', payload.value.phone)
    formData.append('national_identity', payload.national_identity)
    formData.append('vendor_id', payload.vendor_id)
    formData.append('_method', 'PUT')

    if (payload.value.avatar && typeof payload.value.avatar !== 'string') {
      formData.append('avatar', payload.value.avatar)
    }

    payload.driverLicenses.forEach((license: any, index: number) => {
      formData.append(`licenses[${index}][license_category_id]`, license.license_category_id)
      formData.append(`licenses[${index}][license_number]`, license.license_number)
    })

    return await this.$axios.post(`/v1/drivers/${payload.id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    .then((response: any) => {
      toastSuccess(response.data.message, this)
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => { 
      commit('SET_IS_LOADING_FORM', false) 
    })
  },

  async deleteItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    return await this.$axios
      .delete('/v1/drivers/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async blockUnblockDriver ({ commit }, payload: { id: string, type: string }) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.put(`/v1/drivers/${payload.id}/${payload.type}`)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }
}

export default actions

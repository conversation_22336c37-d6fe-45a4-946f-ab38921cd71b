import { ActionTree } from 'vuex'
import { saveAs } from 'file-saver'
import { InvoiceDetailsState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastError } from '~/utils/toasts'

export const actions: ActionTree<InvoiceDetailsState, InvoiceDetailsState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/invoice-details', {
      params: {
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns: payload.searchColumns == null ? 'order.identity' : payload.searchColumns,
        sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
        sort_type: payload.sortType == null ? '' : payload.sortType,
        filter_columns: payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page == null ? '' : payload.page,
        entries: payload.page == null ? '15' : payload.entries
      }
    }).then((response: any) => {
      commit('SET_RESULT', response.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async getItemDetail ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)
    await this.$axios.get(`v1/invoice-details/${payload.id}`).then((response: any) => {
      commit('SET_ITEM', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_DETAIL', false)
    })
  },

  async downloadInvoiceDetails ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DOWNLOAD_INVOICE', true)
    await this.$axios.get(`v1/invoice-details/${payload.id}/print`, {
      responseType: 'blob'
    }).then((response: any) => {
      commit('SET_BLOB_INVOICE', response.data)

      const blob = new Blob([response.data], { type: 'application/pdf;charset=utf-8' })
      saveAs(blob, `invoice-${payload.number}.pdf`)
    }).catch((_: any) => {
      toastError('Failed to generate PDF', this)
    }).finally(() => {
      commit('SET_IS_LOADING_DOWNLOAD_INVOICE', false)
    })
  }
}

export default actions

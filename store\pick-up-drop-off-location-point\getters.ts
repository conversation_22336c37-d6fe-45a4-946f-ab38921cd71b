import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { PickupDropOffLocationPointState } from './state'
import { ImportKey } from '~/types/import-key'

export const getters: GetterTree<PickupDropOffLocationPointState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  dataItemsLocationKey (state): ImportKey[] {
    return state.itemsLocationKey
  }
}

export default getters

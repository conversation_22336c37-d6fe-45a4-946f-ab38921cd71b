<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column align-end">
    <header-datatable
      default-sort-column="name"
      default-sort-type="asc"
      :sort-column-items="sortColumnItems"
      :sort-type-items="sortTypeItems"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      @on-filter-change="getVendors({filter: $event, page: $route.query?.page})"
      @on-search-icon-click="getVendors({searchKey: $event})"
    >
      <template #toggle-button>
        <toggle-button />
      </template>
    </header-datatable>
    <display-mode class="mb-10">
      <template #card-mode>
        <v-row v-if="isLoading" class="ma-n5">
          <v-col
            v-for="i in 3"
            :key="i"
            md="4"
            sm="6"
            class="pa-5"
          >
            <v-sheet height="250" class="pa-5">
              <div class="d-flex flex-row">
                <v-skeleton-loader type="image" height="50" width="50" class="mt-3" />
                <div class="d-flex flex-column">
                  <v-skeleton-loader type="text" width="225" class="pl-5 mt-5 mb-2" />
                  <v-skeleton-loader type="text" width="200" class="pl-5" />
                </div>
              </div>
              <div class="d-flex flex-column pt-5 pb-6">
                <v-skeleton-loader type="text" width="100" class="pb-2" />
                <v-skeleton-loader type="text" width="200" />
              </div>
              <div class="d-flex flex-row">
                <v-skeleton-loader type="image" height="50" width="250" class="mr-5" />
                <v-skeleton-loader type="image" height="50" width="250" />
              </div>
            </v-sheet>
          </v-col>
        </v-row>
        <v-container v-else fluid class="pa-0">
          <v-row v-if="data.items.length !== 0" class="ma-n5">
            <v-col
              v-for="(item, index) in data.items"
              :key="item.id"
              md="4"
              sm="6"
              class="pa-5"
            >
              <user-role-card-item
                :item="item"
                :is-loading-form="isLoadingForm"
                :is-loading-form-status="isLoading"
                :is-account-count-visible="false"
                :is-has-menu="false"
                :is-has-new="true"
                :is-has-accept-reject="true"
                :is-has-detail-account="true"
                detail-route="/logistic-service-provider/list-vendor"
              >
                <template #accept>
                  <v-btn
                    class="mr-2 flex-grow-1 py-6"
                    color="primary"
                    depressed
                    :loading="isLoadingFormStatus.accept && data.items===index"
                    @click="acceptVendor(item.id);index=data.items"
                  >
                    {{ $t('lspRequestVendor.accept') }}
                  </v-btn>
                </template>
                <template #reject>
                  <v-btn
                    class="ml-2 flex-grow-1 py-6"
                    color="primary"
                    outlined
                    depressed
                    :loading="isLoadingFormStatus.reject && data.items===index"
                    @click="rejectVendor(item.id);index=data.items"
                  >
                    {{ $t('lspRequestVendor.reject') }}
                  </v-btn>
                </template>
              </user-role-card-item>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-shipment.svg"
                :message-title="`${ $t('lspRequestVendor.empty_title') }`"
                :message-description="`${ $t('lspRequestVendor.empty_desc') }`"
              />
            </v-col>
          </v-row>
        </v-container>
      </template>
      <template #data-table-mode>
        <div class="mt-0">
          <div v-if="data.items.length !== 0">
            <v-data-table
              :loading="isLoading"
              loading-text="Loading... Please wait"
              :headers="tableHeaders"
              :items="data.items"
              :page.sync="page"
              :single-expand="singleExpand"
              :expanded.sync="expanded"
              :items-per-page="-1"
              hide-default-footer
              class="pa-md-10 pa-5"
              style=""
              @page-count="pageCount = $event"
            >
              <template #item.image="{ item, index }">
                <image-component :image="item.logo_url" max-width="40" max-height="40" class="mr-5" />
              </template>
              <template #item.date="{ item }">
                {{ $moment(item.created_at).format('DD MMMM YYYY') }}
              </template>
              <template #item.status="{ item }">
                <v-chip
                  v-if="item.status === 'PROPOSED'"
                  label
                  class="chip-success font-weight-medium"
                >
                  <p class="ma-0 subtitle-1 text-success">
                    {{ item.status }}
                  </p>
                </v-chip>
                <v-chip
                  v-else-if="item.status === 'REJECT'"
                  label
                  class="chip-danger font-weight-medium"
                >
                  <p class="ma-0 subtitle-1 text-primary">
                    {{ item.status }}
                  </p>
                </v-chip>
                <v-chip v-else label class="chip-success font-weight-medium">
                  <p class="ma-0 subtitle-1 text-info">
                    {{ item.status }}
                  </p>
                </v-chip>
              </template>
              <template #item.detail="{ item }">
                <div class="d-flex">
                  <v-btn
                    class="font-weight-medium flex-grow-1 py-6 text-capitalize"
                    text
                    plain
                    @click="
                      $router.push(
                        '/logistic-service-provider/list-vendor/' + `${item.id}`
                      )
                    "
                  >
                    Detail
                    <v-icon> mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn
                    class="mr-2 flex-grow-1 py-6"
                    color="primary"
                    depressed
                    :loading="isLoadingFormStatus.accept && data.items===index"
                    @click="acceptVendor(item.id);index=data.items"
                  >
                    {{ $t('lspRequestVendor.accept') }}
                  </v-btn>

                  <v-btn
                    class="ml-2 flex-grow-1 py-6"
                    color="primary"
                    outlined
                    depressed
                    :loading="isLoadingFormStatus.reject && data.items===index"
                    @click="rejectVendor(item.id);index=data.items"
                  >
                    {{ $t('lspRequestVendor.reject') }}
                  </v-btn>
                </div>
              </template>
            </v-data-table>
          </div>

          <div v-else class="d-flex">
            <v-col class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-shipment.svg"
                :message-title="`${ $t('lspListVendor.empty_title') }`"
                :message-description="`${ $t('lspListVendor.empty_desc') }`"
              />
            </v-col>
          </div>
        </div>
      </template>
    </display-mode>
    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getVendors({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DisplayMode from '~/components/DisplayMode.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'RequestVendorPage',

  components: {
    DisplayMode,
    PaginationComponent
  },
  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    searchKey: '',
    expanded: [],
    dialog: false,
    singleExpand: true,
    button: true,
    page: 1,
    pageCount: 0,
    tableHeaders: [
      {
        text: 'Image',
        value: 'image'
      },
      {
        text: 'Company',
        value: 'name'
      },
      {
        text: 'Vehicles',
        value: 'vehicles_count'
      },
      {
        text: 'Date',
        value: 'date'
      },
      {
        text: 'Status',
        value: 'status'
      },
      {
        text: '',
        value: 'detail'
      }
    ],
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    }
  }),

  computed: {
    data () {
      return this.$store.getters['vendor/data']
    },
    isLoading () {
      return this.$store.getters['vendor/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['vendor/isLoadingForm']
    },
    isLoadingFormStatus () {
      return this.$store.getters['vendor/isLoadingFormStatus']
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', `${this.$t('lspRequestVendor.request_cooperation')}`)
    this.getVendors({
      page: this.$route.query?.page as string
    })
  },

  methods: {
    getVendors ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.searchKey = searchKey

      this.$store.dispatch('vendor/getVendors', {
        searchKey: this.searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'logistics_service_providers.status',
        filterKeys: 'PROPOSED',
        page
      })
    },

    async acceptVendor (id: string) {
      await this.$store.dispatch('vendor/updateCollaborationStatus', {
        id,
        status: 'COLLABORATE'
      })

      this.getVendors({})
    },

    async rejectVendor (id: string) {
      await this.$store.dispatch('vendor/updateCollaborationStatus', {
        id,
        status: 'REJECTED'
      })

      this.getVendors({})
    }
  }
})
</script>

<style scoped> </style>

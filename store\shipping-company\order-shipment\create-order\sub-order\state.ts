import { SubOrder } from '~/types/product'

export interface ShipmentCompanyCreateOrderSubOrderState {
  isLoading: boolean
  isLoadingForm: boolean
  itemsPickUp: SubOrder[]
  itemsDropOff: SubOrder[]
  totalPage: number
  page: number
}

export const state = (): ShipmentCompanyCreateOrderSubOrderState => ({
  isLoading: false,
  isLoadingForm: false,
  itemsPickUp: [],
  itemsDropOff: [],
  totalPage: 1,
  page: 1
})

export default state

<template>
  <div>
    <l-popup style="width: 100% !important;">
      <div class="d-flex">
        <image-component
          :image="driverAvatar"
          min-width="28"
          max-width="28"
          class="mr-2"
        />
        <div>
          <p class="ma-0 subtitle-2">
            {{ driverName }}
          </p>
          <p class="ma-0 caption black--text">
            {{ plateNumber }}
          </p>
        </div>
      </div>

      <v-divider class="my-2" />
      
      <div class="ma-1 pa-2">
        <div class="mx-n4 mb-2 d-flex align-center">
          <p class="col-6 pa-0 px-2 ma-0 caption text-secondary">
            No Tracking
          </p>
          <v-tooltip v-if="identityTrack && identityTrack.length > 20" bottom>
            <template #activator="{ on, attrs }">
              <p 
                class="col-6 pa-0 px-2 ma-0 caption text-primary text-truncate hover-text" 
                v-bind="attrs"
                v-on="on"
              >
                {{ identityTrack }}
              </p>
            </template>
            <span>{{ identityTrack }}</span>
          </v-tooltip>
          <p v-else class="col-6 pa-0 px-2 ma-0 caption text-primary">
            {{ identityTrack }}
          </p>
        </div>

        <div class="mx-n4 d-flex align-center">
          <p class="col-6 pa-0 px-2 ma-0 caption text-secondary">
            Status
          </p>
          <p class="col-6 pa-0 px-2 ma-0 caption black--text" :class="{'text-truncate': status && status.length > 20}">
            {{ status }}
          </p>
        </div>
      </div>

      <!-- <div class="mx-n2 d-flex align-center">
        <p class="col-6 pa-0 px-2 ma-0 caption text-secondary">
          Estimate time arrival
        </p>
        <p class="col-6 pa-0 px-2 ma-0 caption black--text">
          {{ estimateTime }}
        </p>
      </div> -->
    </l-popup>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageComponent from '~/components/ImageComponent.vue'

export default Vue.extend({
  name: 'CustomMapPopup',

  components: { ImageComponent },

  props: {
    driverAvatar: {
      type: String,
      default: '~/assets/images/placeholder-company-logo.svg'
    },
    driverName: {
      type: String,
      default: ''
    },
    plateNumber: {
      type: String,
      default: ''
    },
    identityTrack: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: ''
    },
    estimateTime: {
      type: String,
      default: ''
    }
  }
})
</script>

<style scoped> </style>

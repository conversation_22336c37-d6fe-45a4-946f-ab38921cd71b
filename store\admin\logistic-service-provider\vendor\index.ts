import { GetterTree, ActionTree, MutationTree } from 'vuex'
import { exception<PERSON><PERSON><PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

// eslint-disable-next-line no-use-before-define
export type VuexState = ReturnType<typeof state>

export const state = () => ({
  isLoading: false,
  isLoadingForm: false,
  items: [],
  totalPage: 1,
  page: 1
})

export const getters: GetterTree<VuexState, VuexState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export const mutations: MutationTree<VuexState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }
}

export const actions: ActionTree<VuexState, VuexState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/vendors', {
      params: {
        search_key: payload.searchKey,
        search_columns: payload.searchColumns,
        sort_column: payload.sortColumn,
        sort_type: payload.sortType,
        filter_columns: 'logistics_service_providers.id',
        filter_keys: payload.selectedId,
        page: payload.page,
        entries: 9
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  createItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios.post('/v1/vendors', {
      name: payload.value.companyName,
      user_name: payload.value.name,
      user_phone_number: payload.value.phone,
      user_phone_country_code: '62',
      user_email: payload.value.email,
      user_password: payload.value.password,
      user_password_confirmed: payload.value.passwordConfirmation,
      logistics_service_provider_id: payload.selectedId
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, name: '', selectedId: payload.selectedId })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  editItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios.put('/v1/vendors/' + payload.value.id, {
      name: payload.value.name,
      address: payload.value.address
    }).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, name: '', selectedId: payload.selectedId })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  },

  deleteItem ({ commit, dispatch, state }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios.delete('/v1/vendors/' + payload.id).then((response: any) => {
      toastSuccess(response.data.message, this)

      dispatch('getItems', { page: state.page, name: '', selectedId: payload.selectedId })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }

}

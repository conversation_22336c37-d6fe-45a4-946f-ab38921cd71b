<template>
  <v-container fill-height fluid>
    <v-row
      align="center"
      justify="center"
    >
      <v-card class="pa-4 ma-8" width="550">
        <v-col class="justify-center">
          <v-row justify="space-between">
            <v-btn
                :loading="isLoadingLogout"
                color="primary"
                depressed
                height="52"
                text
                @click="logout"
            >
              <v-icon left>
                mdi-logout
              </v-icon>
              Logout
            </v-btn>
            <v-btn
                color="primary"
                depressed
                height="52"
                text
                @click="navigateToDashboard"
            >
              <v-icon left>
                mdi-view-dashboard
              </v-icon>
              Dashboard
            </v-btn>
          </v-row>

          <v-row>
            <v-col>
              <div class="text-heading-6 spacer-y-lg">
                Your User
              </div>
              <div class="text-body">
                Insert your profile information here.
              </div>
            </v-col>
          </v-row>
          <div class="spacer-y-2lg" />
          <v-form>
            <image-select
              label="Logo"
              :avatar-url="data?.logo_url"
              :is-loading-form-clear-image="isLoadingFormCompany"
              @on-image-selected="logo = $event"
              @on-clear-image="removeLogoCompany"
            />
            <custom-text-field
              v-model="name"
              :is-loading="isLoading || isLoadingSC"
              :hidden-hint-outlined="true"
              label="Company Name"
              hint="Enter your name here"
              prepend-inner-icon="mdi-account"
              is-required
            />
            <custom-text-field
              v-if="role === 'LOGISTIC_SERVICE_PROVIDER'"
              v-model="domain"
              :is-loading="isLoading"
              :hidden-hint-outlined="true"
              label="Company Domain"
              hint="Enter your domain here"
              prepend-inner-icon="mdi-domain"
              is-required
            />
            <custom-text-field
              v-if="role === 'SHIPMENT_COMPANY'"
              v-model="$store.getters.domain"
              :is-loading="isLoadingSC"
              :hidden-hint-outlined="true"
              label="Company Domain"
              hint="Enter your domain here"
              prepend-inner-icon="mdi-domain"
              is-required
            />
            <custom-textarea
              v-model="address"
              :is-loading="isLoading || isLoadingSC"
              label="Company Address"
              hint="Enter your address"
              prepend-inner-icon="mdi-map-marker"
              is-required
            />
            <div class="spacer-y-lg" />
            <v-btn
              height="52"
              color="primary"
              block
              depressed
              :loading="isLoadingForm || isLoadingFormSC"
              @click="saveForm"
            >
              Save User
            </v-btn>
          </v-form>
        </v-col>
      </v-card>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '~/components/CustomTextField.vue'
import CustomTextarea from '~/components/CustomTextarea.vue'

export default Vue.extend({
  name: 'FormProfilePage',
  components: {
    CustomTextField,
    CustomTextarea
  },

  middleware: 'auth',

  data: () => ({
    name: '',
    domain: '',
    address: '',
    logo: null,
    isLoadingLogout: false
  }),

  computed: {
    data () {
      if (this.role === 'LOGISTIC_SERVICE_PROVIDER') {
        return this.$store.getters['profile/logisticServiceProvider']
      } else if (this.role === 'VENDOR') {
        return this.$store.getters['profile/vendor']
      } else if (this.role === 'SHIPMENT_COMPANY') {
        return this.$store.getters['profile/shipping-company/shippingCompany']
      }
    },
    isLoading () {
      return this.$store.getters['profile/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['profile/isLoadingForm']
    },
    isLoadingSC () {
      return this.$store.getters['profile/shipping-company/isLoading']
    },
    isLoadingFormSC () {
      return this.$store.getters['profile/shipping-company/isLoadingForm']
    },
    isLoadingFormCompany (): Boolean {
      let isLoading = false

      switch (this.role) {
        case 'LOGISTIC_SERVICE_PROVIDER' : isLoading = this.$store.getters['logistic-service-provider/isLoadingForm']; break
        case 'SHIPMENT_COMPANY' : isLoading = this.$store.getters['shipping-company/isLoadingForm']; break
        case 'VENDOR' : isLoading = this.$store.getters['vendor/isLoadingForm']; break
      }

      return isLoading
    },
    role () {
      const user = this.$auth.user as any

      return user?.data?.role
    }
  },

  watch: {
    data () {
      this.name = this.data?.name
      this.domain = this.data?.domain
      this.address = this.data?.address
    }

  },

  mounted () {
    if (this.role === 'LOGISTIC_SERVICE_PROVIDER') {
      this.$store.dispatch('profile/getFormLSP')
    } else if (this.role === 'VENDOR') {
      this.$store.dispatch('profile/getFormVendor')
    } else if (this.role === 'SHIPMENT_COMPANY') {
      this.$store.dispatch('profile/shipping-company/getData')
    }

    const lastUpdatedPassword = this.$auth.$state.user.data.last_updated_password

    const lastUpdatedPasswordDate = new Date(lastUpdatedPassword) as any

    const oneMonthInMilliseconds = 30 * 24 * 60 * 60 * 1000
    const currentTime = new Date() as any
    const timeDifference = currentTime - lastUpdatedPasswordDate

    if (timeDifference > oneMonthInMilliseconds) {
      this.$router.push(this.localePath('/create-new-password'))
    }
  },

  methods: {
    navigateToDashboard () {
      switch (this.role) {
        case 'LOGISTIC_SERVICE_PROVIDER' :
          this.$router.replace('/logistic-service-provider/dashboard')
          break
        case 'SHIPMENT_COMPANY' :
          this.$router.replace('/shipping-company/dashboard')
          break
        case 'VENDOR' :
          this.$router.replace('/vendor/dashboard')
          break
      }
    },
    removeLogoCompany () {
      switch (this.role) {
        case 'LOGISTIC_SERVICE_PROVIDER' :
          this.$store.dispatch('logistic-service-provider/removeLogo', this.$auth.$state.user.data.logistics_service_provider_id)
          break
        case 'SHIPMENT_COMPANY' :
          this.$store.dispatch('shipping-company/removeLogo', this.$auth.$state.user.data.shipment_company_id)
          break
        case 'VENDOR' :
          this.$store.dispatch('vendor/removeLogo', this.$auth.$state.user.data.vendor_id)
          break
      }
    },

    saveForm () {
      if (this.role === 'LOGISTIC_SERVICE_PROVIDER') {
        this.$store.dispatch('profile/saveFormLSP', {
          name: this.name,
          domain: this.domain,
          address: this.address ?? '',
          logo: this.logo
        })
      } else if (this.role === 'VENDOR') {
        this.$store.dispatch('profile/saveFormVendor', {
          name: this.name,
          address: this.address ?? '',
          logo: this.logo
        })
      } else if (this.role === 'SHIPMENT_COMPANY') {
        const domainSC = this.$store.getters.domain
        this.$store.dispatch('profile/shipping-company/saveData', {
          name: this.name,
          address: this.address ?? '',
          domainLsp: domainSC,
          logo: this.logo
        })
      }
    },
    onImageSelected (value: any) {
      this.logo = value
    },
    async logout () {
      this.isLoadingLogout = true
      await this.$auth.logout()
      this.isLoadingLogout = false
    }
  }
})
</script>

<style scoped>
</style>

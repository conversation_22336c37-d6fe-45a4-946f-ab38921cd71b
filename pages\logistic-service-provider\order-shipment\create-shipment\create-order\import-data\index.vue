<template>
  <v-container fluid class="pa-0 px-10 mb-10">
    <button-back title="Back to Create Order" class="mb-10" />

    <import-data-component
      type="ORDER"
      :import-keys="importKeys"
      @on-change-form-values="formValues = $event"
    >
      <template #button-download-template>
        <v-btn
          x-large
          outlined
          class="subtitle-1 text-capitalize"
          style="border: 1px solid #CFCCCC"
          @click="onClickDownload"
        >
          <v-icon class="mr-3">
            mdi-file-download
          </v-icon>
          Download Template
        </v-btn>
      </template>

      <template #footer-bulk-import>
        <div>
          <v-dialog
            v-model="dialogConfirmation"
            width="480"
            persistent
          >
            <template #activator="{on, attrs}">
              <v-btn
                v-bind="attrs"
                x-large
                depressed
                color="primary"
                :disabled="formValues.length === 0"
                class="mr-5 subtitle-1 text-capitalize"
                v-on="on"
              >
                Import Data
              </v-btn>
            </template>

            <v-card>
              <v-card-title class="pa-10 d-flex align-center justify-space-between">
                <h3>Import Data</h3>
                <v-icon @click="dialogConfirmation = false">
                  mdi-close
                </v-icon>
              </v-card-title>

              <v-card-text class="pa-0 px-10">
                <p class="ma-0 body-1 text-secondary text-center">
                  Data with the
                  <span class="subtitle-1 text-primary">same identity Product</span>
                  will be
                  <span class="subtitle-1 text-primary">overwritten</span>
                  with new data. Are you sure you want to continue importing data?
                </p>
              </v-card-text>

              <v-card-actions class="pa-10">
                <v-row class="ma-0 mx-n3">
                  <v-col class="pa-0 px-3">
                    <v-btn
                      block
                      x-large
                      depressed
                      color="primary"
                      :loading="isLoadingForm"
                      class="subtitle-1 text-capitalize"
                      @click="onClickConfirmImportData"
                    >
                      Import Data
                    </v-btn>
                  </v-col>
                  <v-col class="pa-0 px-3">
                    <v-btn
                      block
                      x-large
                      outlined
                      color="primary"
                      class="subtitle-1 text-capitalize"
                      @click="dialogConfirmation = false"
                    >
                      Cancel
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card-actions>
            </v-card>
          </v-dialog>

          <v-btn
            x-large
            outlined
            color="primary"
            class="text-capitalize"
            @click="$router.back()"
          >
            Cancel
          </v-btn>
        </div>
      </template>
    </import-data-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import ButtonBack from '~/components/ButtonBack.vue'
import ImportDataComponent from '~/components/ImportDataComponent.vue'
import { ImportKey } from '~/types/import-key'
import { ShippingCompany } from '~/types/user'

export default Vue.extend({
  name: 'ImportDataOrderPage',

  components: {
    ButtonBack,
    ImportDataComponent
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    dialogConfirmation: false as boolean,
    formValues: [] as object[],
    importKeys: [
      { key: 'identity_order', name: 'Identity Order', is_required: true, type: 'string' },
      { key: 'type', name: 'Type', is_required: true, type: 'string' },
      { key: 'identity_location', name: 'Identity Location', is_required: true, type: 'string' },
      { key: 'identity_product', name: 'Identity Product', is_required: true, type: 'string' },
      { key: 'quantity', name: 'Quantity (kg)', is_required: true, type: 'decimal' },
      { key: 'estimation', name: 'Time Estimation', is_required: true, type: 'string' }
    ] as ImportKey[]
  }),

  computed: {
    selectedSc (): ShippingCompany | null {
      return this.$store.getters['logistic-service-provider/selectedShipmentCompany']
    },

    isLoadingForm (): boolean {
      return this.$store.getters['order/isLoadingForm']
    }
  },

  mounted () {
    if (!this.selectedSc) {
      this.$router.push(this.localePath('/logistic-service-provider/order-shipment/create-shipment'))
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Import Data')
  },

  methods: {
    onClickDownload () {
      const downloadLink = '/template-bulk-import-order.xlsx'
      const anchor = document.createElement('a')
      anchor.href = downloadLink
      anchor.download = 'template-bulk-import-order.xlsx'
      anchor.click()
    },

    async onClickConfirmImportData () {
      const res = await this.$store.dispatch('order/bulkImportOrders', {
        shipment_company_id: this.selectedSc?.id,
        data: this.formValues
      })

      if (res) {
        this.dialogConfirmation = false
        await this.$router.push(
          this.localePath('/logistic-service-provider/order-shipment/create-shipment/create-order')
        )
      }
    }
  }
})
</script>

<style scoped>

</style>

import { GetterTree, ActionTree, MutationTree } from 'vuex'
import { exception<PERSON>and<PERSON> } from '~/utils/functions'

// eslint-disable-next-line no-use-before-define
export type VuexState = ReturnType<typeof state>

export const state = () => ({
  isLoading: false,
  isLoadingForm: false,
  items: [],
  totalPage: 1,
  page: 1
})

export const getters: GetterTree<VuexState, VuexState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  }
}

export const mutations: MutationTree<VuexState> = {
  SET_ITEMS (state, items: any) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }
}

export const actions: ActionTree<VuexState, VuexState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/logistics-service-providers', {
      params: {
        filter_columns: 'vendors.id',
        filter_keys: payload.vendorId,
        sort_column: payload.sortColumn,
        sort_type: payload.sortType,
        search_columns: 'name',
        search_key: payload.searchKey,
        page: payload.page,
        entries: 9
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  }
}

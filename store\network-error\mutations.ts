import { MutationTree } from 'vuex'
import { NetworkErrorState } from '~/store/network-error/state'

export const mutations: MutationTree<NetworkErrorState> = {
  SET_IS_NETWORK_ERROR (state, isNetworkError: boolean) {
    state.isNetworkError = isNetworkError
  },

  LAST_DISPATCHED_ACTION (state, lastAction) {
    if (!lastAction.name.includes('network-error') && lastAction.name.length > 0) {
      state.lastAction = lastAction.name
      state.lastPayload = lastAction.payload
    }
  }
}

export default mutations

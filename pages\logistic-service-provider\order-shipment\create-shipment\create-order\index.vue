<template>
  <create-order-page-component
    v-if="company || !isLoadingOrder"
    :id="company?.id"
    :checked-orders="checkedOrders"
    title="Create Order"
    @on-success-publish-order="undefined"
  >
    <template #dialog-draft-order>
      <v-dialog
        v-model="dialogDraftOrder"
        width="480"
        persistent
      >
        <v-card class="pa-md-10 pa-5">
          <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
            <h4>Edit Order Number</h4>

            <v-icon color="black" @click="dialogDraftOrder = false, automateCreateOrder()">
              mdi-close
            </v-icon>
          </v-card-title>
          <div class="mt-2 d-flex justify-center">
            <p>
              You have draft orders. Will you continue with your order?
            </p>
          </div>
          <div class="d-flex mt-2">
            <v-btn
              depressed
              color="primary"
              @click="dialogSelectOrder = true, dialogDraftOrder = false"
            >
              Select Order
            </v-btn>
            <v-btn
              outlined
              depressed
              class="ml-5"
              color="primary"
              @click="showDialogOrderNumber = true, dialogDraftOrder = false"
            >
              Create New
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
    </template>
    <template #dialog-order-number>
      <add-order-number-dialog
        :id="company?.id"
        :dialog="showDialogOrderNumber"
        @on-close-dialog="showDialogOrderNumber = false"
      />
    </template>

    <template #menu-dots>
      <v-menu
        bottom
        transition="slide-y-transition"
      >
        <template #activator="{on, attrs}">
          <v-btn
            icon
            v-bind="attrs"
            v-on="on"
          >
            <v-icon color="black">
              mdi-dots-vertical
            </v-icon>
          </v-btn>
        </template>

        <v-list>
          <v-list-item>
            <v-btn
              depressed
              color="transparent"
              class="pa-0 ma-0 text-capitalize"
              @click="dialogEditOrder = true"
            >
              Edit Order
            </v-btn>
          </v-list-item>
          <v-list-item>
            <v-btn
              depressed
              color="transparent"
              class="pa-0 ma-0 text-capitalize"
              @click="dialogSelectOrder = true"
            >
              Select Order
            </v-btn>
          </v-list-item>
          <v-list-item>
            <v-btn
              depressed
              color="transparent"
              class="pa-0 ma-0 text-capitalize"
              @click="dialogCreateOrder = true"
            >
              Create Order
            </v-btn>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
    <template #header>
      <v-row class="d-flex ma-0 mt-4 mb-8">
        <v-btn tile text class="text-capitalize" @click="$router.back()">
          <v-icon left color="black">
            mdi-chevron-left
          </v-icon>
          {{ $t('lspCreateShipment.back_to_list_lsp') }}
        </v-btn>
      </v-row>
      <v-row class="ma-0 mb-8 mx-0 pa-5 rounded white">
        <div class="d-flex flex-row align-center">
          <div class="ma-8">
            <image-component v-if="company?.logo_url" :image="company?.logo_url" max-width="40" max-height="40" />

            <v-icon v-else size="55">
              mdi-domain
            </v-icon>
          </div>

          <h4 class="ml-2 text-wrap ma-4">
            {{ checkedOrders[0]?.shipment_company?.name ? checkedOrders[0]?.shipment_company?.name : company?.name }}
          </h4>
        </div>

        <v-divider vertical />
        <div
          class="flex flex-column justify-content-center align-items-center ma-8"
        >
          <div class="body-1 text-secondary">
            {{ $t('lspCreateShipment.address') }}
          </div>
          <div class="body-1 black--text">
            {{ checkedOrders[0]?.shipment_company?.address ? checkedOrders[0]?.shipment_company?.address : company?.address }}
          </div>
        </div>
      </v-row>
    </template>

    <template #update-order-dialog>
      <v-dialog
        v-model="dialogEditOrder"
        width="480"
        persistent
      >
        <v-card class="pa-md-10 pa-5">
          <v-form ref="form">
            <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
              <h4>Edit Order Number</h4>

              <v-icon color="black" @click="dialogEditOrder = false">
                mdi-close
              </v-icon>
            </v-card-title>

            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="updateOrderNumber"
                depressed
                class="mr-2"
                :hint="'Input Order Number'"
                color="primary"
              >
                {{ $t('lspCreateShipment.save_customer') }}
              </custom-text-field>
            </v-col>
          </v-form>
          <div class="d-flex mt-2">
            <v-btn
              color="primary"
              class="mr-2 col-2"
              depressed
              :loading="isLoadingCreateOrder"
              :disabled="updateOrderNumber === null"
              @click="saveNumber"
            >
              {{ $t('userFormItem.button_save') }}
            </v-btn>
            <v-btn
              outlined
              depressed
              color="primary"
              @click="dialogEditOrder = false"
            >
              {{ $t('lspCreateShipment.cancel') }}
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
    </template>
    <template #select-order>
      <v-dialog
        v-model="dialogSelectOrder"
        width="480"
        persistent
      >
        <v-card>
          <v-card-title class="pa-10 d-flex align-center justify-space-between">
            <h3>Select Order</h3>
            <v-icon @click="dialogSelectOrder = false, automateCreateOrder()">
              mdi-close
            </v-icon>
          </v-card-title>

          <v-card-text class="pa-0 px-10">
            <v-autocomplete
              v-model="selectedOrderId"
              outlined
              hide-details
              placeholder="Select Order"
              :items="orders.map(order => ({
                text: order.identity,
                value: order.id
              }))"
            />
          </v-card-text>

          <v-card-actions class="pa-10">
            <v-row class="ma-0 mx-n3">
              <v-col class="pa-0 px-3">
                <v-btn
                  block
                  x-large
                  depressed
                  color="primary"
                  :disabled="!selectedOrderId"
                  class="text-capitalize subtitle-1"
                  @click="getDetailOrder"
                >
                  Confirm
                </v-btn>
              </v-col>
              <v-col class="pa-0 px-3">
                <v-btn
                  block
                  x-large
                  outlined
                  color="primary"
                  class="text-capitalize subtitle-1"
                  @click="dialogSelectOrder = false, automateCreateOrder()"
                >
                  Cancel
                </v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>
    <template #create-order>
      <v-dialog
        v-model="dialogCreateOrder"
        width="480"
        persistent
      >
        <v-card class="pa-md-10 pa-5">
          <v-form ref="form">
            <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
              <h4>Create Order Number</h4>

              <v-icon color="black" @click="dialogCreateOrder = false">
                mdi-close
              </v-icon>
            </v-card-title>

            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="createOrderNumber"
                depressed
                class="mr-2"
                :hint="'Create Order Number'"
                color="primary"
              >
                {{ $t('lspCreateShipment.save_customer') }}
              </custom-text-field>
            </v-col>
          </v-form>
          <div class="d-flex mt-2">
            <v-btn
              color="primary"
              class="mr-2 col-2"
              depressed
              :loading="isLoadingCreateOrder"
              :disabled="createOrderNumber === null"
              @click="saveCreateOrder"
            >
              {{ $t('userFormItem.button_save') }}
            </v-btn>
            <v-btn
              outlined
              depressed
              color="primary"
              @click="dialogCreateOrder = false"
            >
              {{ $t('lspCreateShipment.cancel') }}
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
    </template>

    <template #activator-import>
      <v-btn
        x-large
        outlined
        color="primary"
        class="text-capitalize subtitle-1 mr-5"
        @click="$router.push(`${$route.path}/import-data`)"
      >
        <v-icon class="mr-3">
          mdi-plus
        </v-icon>
        Import
      </v-btn>
    </template>
  </create-order-page-component>
</template>

<script lang="ts">
import Vue from 'vue'
import CreateOrderPageComponent from '~/components/create-order-page-component/index.vue'
import { ShippingCompany } from '~/types/user'
import AddOrderNumberDialog from '~/components/AddOrderNumberDialog.vue'
import { Order } from '~/types/product'
import { toastSuccess } from '~/utils/toasts'

export default Vue.extend({
  name: 'LSACreateOrderPage',

  components: {
    CreateOrderPageComponent,
    AddOrderNumberDialog
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    updateOrderNumber: null as string | null,
    createOrderNumber: null as string | null,
    selectedOrderId: null as string | null,
    showDialogOrderNumber: false as boolean,
    dialogSelectOrder: false as boolean,
    dialogEditOrder: false as boolean,
    dialogCreateOrder: false as boolean,
    dialogDraftOrder: true
  }),

  computed: {
    company (): ShippingCompany | null {
      return this.$store.getters['logistic-service-provider/selectedShipmentCompany']
    },

    isLoadingOrder (): boolean {
      return this.$store.getters['order/isLoading']
    },

    orders (): Order[] {
      return this.$store.getters['order/data'].items
    },

    isLoadingCreateOrder (): boolean {
      return this.$store.getters['shipping-company/order-shipment/create-order/isLoading']
    },

    checkedOrders (): Order[] {
      return this.$store.getters['order/checkedOrders']
    },
    newOrderId (): any {
      return this.$store.getters['shipping-company/order-shipment/create-order/newOrderId']
    }
  },

  watch: {
    checkedOrders: {
      handler () {
        if (this.checkedOrders.length > 0) {
          this.dialogDraftOrder = false
          this.getDetailOrder()
        }
      },
      immediate: true
    }
  },

  mounted () {
    this.getOrders()
    if (this.checkedOrders.length === 0 && !this.company) {
      this.$router.push(this.localePath('/logistic-service-provider/order-shipment/create-shipment'))
    }
  },

  methods: {
    getOrders () {
      if (this.company?.id !== undefined) {
        this.$store.dispatch('order/getOrders', {
          filterColumns: 'shipment_company_id,status',
          filterKeys: `${this.company?.id},DRAFT`,
          entries: -1
        })
      } else {
        toastSuccess('Success', this)
      }
    },

    async getDetailOrder () {
      const id = this.selectedOrderId || this.checkedOrders[0].id || this.newOrderId
      const res = await this.$store.dispatch('shipping-company/order-shipment/create-order/getDetailOrder', id)
      if (res) {
        this.dialogSelectOrder = false
      }
    },

    async saveNumber () {
      let res
      const lastOrderChecked = this.checkedOrders.length - 1
      const idOrder = this.selectedOrderId || this.$route.query.id || this.newOrderId
      if (this.checkedOrders.length > 0) {
        res = await this.$store.dispatch('shipping-company/order-shipment/create-order/updateOrder', {
          identityNumber: this.updateOrderNumber,
          id: this.checkedOrders[lastOrderChecked]?.id
        })
      } else if (this.checkedOrders.length === 0) {
        res = await this.$store.dispatch('shipping-company/order-shipment/create-order/updateOrder', {
          identityNumber: this.updateOrderNumber,
          id: idOrder
        })
      }

      if (res) {
        await this.getOrders()
        if (this.checkedOrders.length > 0) {
          await this.$store.dispatch('shipping-company/order-shipment/create-order/getDetailOrder', this.checkedOrders[lastOrderChecked].id)
          this.dialogEditOrder = false
        } else if (this.checkedOrders.length === 0) {
          await this.$store.dispatch('shipping-company/order-shipment/create-order/getDetailOrder', idOrder)
          this.dialogEditOrder = false
        }
      }
    },

    async saveCreateOrder () {
      const res = await this.$store.dispatch('shipping-company/order-shipment/create-order/createOrder', {
        identityNumber: this.createOrderNumber,
        idSc: this.checkedOrders[0]?.id ? this.checkedOrders[0]?.id : this.company?.id
      })
      if (res) {
        await this.getOrders()
        this.dialogCreateOrder = false
      }
    },

    automateCreateOrder () {
      if (this.checkedOrders.length > 0) {
        this.$emit('on-close-dialog')
      } else if (this.checkedOrders.length === 0) {
        if (!this.selectedOrderId) {
          this.$store.dispatch('shipping-company/order-shipment/create-order/createOrder', {
            identityNumber: this.selectedOrderId,
            idSc: this.company?.id
          })
        } else {
          this.$store.dispatch('order/getOrders', {
            filterColumns: 'shipment_company_id,status',
            filterKeys: `${this.company?.id},DRAFT`,
            entries: -1
          })
        }
        this.$emit('on-close-dialog')
      }
    }

  }
})
</script>

<style scoped>

</style>

<template>
  <v-sheet max-height="1500" color="pa-7 overflow-hidden">
    <v-container class="d-flex justify-space-between pa-0 mb-4">
      <v-skeleton-loader type="text" width="150" />
      <v-skeleton-loader type="text" width="150" />
    </v-container>
    <v-container class="d-sm-flex d-block align-center justify-space-between pa-0 mb-9">
      <v-skeleton-loader type="image" width="150" height="35" style="border-radius: 20px !important;" />
      <v-skeleton-loader v-if="$vuetify.breakpoint.xs" type="image" width="200" height="35" class="mt-5" />
      <v-skeleton-loader v-else type="image" width="200" height="35" />
    </v-container>
    <div class="row align-center">
      <div class="col-12 col-sm-3">
        <div class="d-flex mt-4 ">
          <div class="d-flex align-center">
            <v-skeleton-loader type="image" width="50" height="50" class="mt-3" />
            <div class="d-flex flex-column">
              <v-skeleton-loader type="list-item-two-line" width="200" class="mr-5" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-3">
        <div class="d-flex mt-4 ">
          <div class="d-flex align-center">
            <v-skeleton-loader type="image" width="50" height="50" class="mt-3" />
            <div class="d-flex flex-column">
              <v-skeleton-loader type="list-item-two-line" width="200" class="mr-5" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <v-divider class="mt-7 mb-5" />
    <div class="row">
      <div class="col-sm-6 col-12">
        <v-skeleton-loader type="heading" />
        <div class="d-flex flex-row align-center my-5">
          <div class="col-6 pa-0">
            <v-skeleton-loader type="text" max-width="200" class="mb-6" />
            <v-skeleton-loader type="image" max-width="200" height="50" style="border-radius: 20px !important;" />
          </div>
          <div class="col-6 pa-0">
            <v-skeleton-loader type="text" max-width="200" class="mb-6" />
            <v-skeleton-loader type="image" max-width="200" height="50" style="border-radius: 20px !important;" />
          </div>
        </div>
        <v-skeleton-loader type="heading" width="200" />
        <div class="d-flex flex-row align-center my-5">
          <div v-for="i in 3" :key="i" class="d-flex justify-space-between">
            <div class="col-4 pa-0">
              <v-skeleton-loader type="text" width="100" class="mb-1" />
              <v-skeleton-loader type="heading" width="200" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6 col-12">
        <v-skeleton-loader type="heading" class="mb-5" />
        <v-skeleton-loader type="heading" width="200" />
        <div class="row align-start my-5">
          <div class="col-12 col-sm-2 text-center mr-8">
            <v-skeleton-loader type="image" width="120" height="120" />
          </div>
          <div class="col-12 col-sm-8">
            <v-skeleton-loader type="list-item-three-line" class="mt-4" />
          </div>
        </div>
      </div>
    </div>
    <v-divider class="mt-5 mb-6" />
    <v-skeleton-loader type="text" class="mb-10" />
    <v-skeleton-loader type="text" />
    <v-divider class="mt-10 mb-10" />
    <v-skeleton-loader type="text" />
    <v-divider class="mt-10" />
  </v-sheet>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'DetailHistoryInvoiceLoading'
})
</script>

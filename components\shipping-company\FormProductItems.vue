<template>
  <v-form ref="form">
    <image-select
      outlined
      :clear-image="clearForm"
      :avatar-url="product?.photo_url"
      :is-loading-form-clear-image="isLoadingFormClearImage"
      @on-image-selected="onImageSelected"
      @on-clear-image="$emit('on-clear-image')"
    />

    <v-text-field
      v-model="form.identity"
      outlined
      :label="$t('scFormProductItems.label_identity')"
      :hint="$t('scFormProductItems.hint_identity')"
      persistent-hint
      class="mb-5"
    />

    <v-text-field
      v-model="form.name"
      outlined
      :label="$t('scFormProductItems.label_product')"
      hide-details
      class="mb-5"
    />

    <v-text-field
      v-model="form.productType"
      outlined
      :label="$t('scFormProductItems.label_type')"
      :hint="$t('scFormProductItems.hint_type')"
      persistent-hint
      class="mb-5"
    />

    <v-text-field
      v-model="form.unit"
      outlined
      :label="$t('scFormProductItems.label_unit')"
      :hint="$t('scFormProductItems.hint_unit')"
      persistent-hint
      class="mb-5"
    />

    <v-divider class="mb-5" />

    <p class="mb-5 subtitle-1">
      {{ $t('scFormProductItems.unit_type') }}
    </p>

    <v-radio-group
      v-model="form.unitType"
      hide-details
      row
      class="mb-5 pa-0"
    >
      <v-row class="ma-0">
        <v-col class="mr-sm-5 mb-sm-0 mb-5 pa-0 col-sm-6 col-12">
          <v-card elevation="0" outlined>
            <v-radio value="PACKAGE" class="ma-0 pa-2" color="info">
              <template #label>
                <div class="d-flex flex-column">
                  <p class="body-1 black--text mb-2">
                    {{ $t('scFormProductItems.label_package') }}
                  </p>
                  <p class="caption ma-0">
                    {{ $t('scFormProductItems.hint_package') }}
                  </p>
                </div>
              </template>
            </v-radio>
          </v-card>
        </v-col>

        <v-col class="pa-0">
          <v-card elevation="0" outlined>
            <v-radio value="NON-PACKAGE" class="ma-0 pa-2" color="info">
              <template #label>
                <div class="d-flex flex-column">
                  <p class="body-1 black--text mb-2">
                    {{ $t('scFormProductItems.label_non_package') }}
                  </p>
                  <p class="caption ma-0">
                    {{ $t('scFormProductItems.hint_non_package') }}
                  </p>
                </div>
              </template>
            </v-radio>
          </v-card>
        </v-col>
      </v-row>
    </v-radio-group>

    <p class="mb-5 subtitle-1">
      {{ $t('scFormProductItems.product_size') }}
    </p>

    <v-row class="ma-0 mb-5">
      <v-col class="pa-0">
        <!-- <v-text-field
          v-model="form.length"
          type="number"
          outlined
          :label="$t('scFormProductItems.label_length')"
          hint="cm"
          persistent-hint
          hide-spin-buttons
          @input="calculateVolume"
        /> -->
      </v-col>
      <v-col class="mx-5 pa-0">
        <!-- <v-text-field
          v-model="form.width"
          type="number"
          outlined
          :label="$t('scFormProductItems.label_width')"
          hint="cm"
          persistent-hint
          hide-spin-buttons
          @input="calculateVolume"
        /> -->
      </v-col>
      <v-col class="pa-0">
        <!-- <v-text-field
          v-model="form.height"
          type="number"
          outlined
          :label="$t('scFormProductItems.label_height')"
          hint="cm"
          persistent-hint
          hide-spin-buttons
          @input="calculateVolume"
        /> -->
      </v-col>
    </v-row>

    <v-row class="ma-0 mb-10">
      <!-- <v-col class="mr-5 pa-0 d-flex justify-start">
        <v-text-field
          :value="form.volume"
          outlined
          readonly
          :label="$t('scFormProductItems.label_volume')"
          hint="cbm"
          persistent-hint
          disabled
          hide-spin-buttons
        />
      </v-col> -->
      <v-col class="pa-0 col-12">
        <!-- <v-text-field
          v-model="form.weight"
          type="number"
          outlined
          :label="$t('scFormProductItems.label_weight')"
          hint="kg"
          persistent-hint
          hide-spin-buttons
        /> -->

        <FormattedNumberInput
          v-model="form.weight"
          :label="$t('scFormProductItems.label_weight')"
          hint="kg"
        />
      </v-col>
    </v-row>

    <v-card-actions class="pa-0 d-flex justify-space-around">
      <v-row class="ma-0 d-flex flex-sm-row flex-column">
        <v-col
          v-if="!product"
          class="mr-sm-5 mb-sm-0 mb-5 pa-0"
        >
          <v-btn
            elevation="0"
            color="primary"
            class="text-capitalize"
            x-large
            block
            :loading="isLoadingForm"
            @click="onClickAdd"
          >
            {{ $t('scFormProductItems.button_add') }}
          </v-btn>
        </v-col>
        <v-col
          v-else
          class="mr-sm-5 mb-sm-0 mb-5 pa-0"
        >
          <v-btn
            elevation="0"
            color="primary"
            class="text-capitalize"
            x-large
            block
            :loading="isLoadingForm"
            @click="onClickSave"
          >
            {{ $t('scFormProductItems.button_edit') }}
          </v-btn>
        </v-col>
        <v-col class="pa-0">
          <v-btn
            elevation="0"
            outlined
            color="primary"
            class="text-capitalize ma-0"
            x-large
            block
            @click="onClickClose"
          >
            {{ $t('scFormProductItems.button_cancel') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-card-actions>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { Product } from '~/types/product'
import FormattedNumberInput from '~/components/fields/FormattedNumberInput.vue'

export default Vue.extend({
  name: 'FormProductItems',

  components: {
    FormattedNumberInput
  },

  props: {
    product: {
      type: Object as () => Product,
      default: null
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    isLoadingFormClearImage: {
      type: Boolean,
      default: false
    },
    clearForm: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    form: {
      id: '',
      photoUrl: '',
      photo: '',
      identity: '',
      name: '',
      productType: '',
      unit: '',
      unitType: '',
      length: '',
      width: '',
      height: '',
      volume: '',
      weight: ''
    }
  }),

  watch: {
    clearForm () {
      if (this.clearForm) {
        const form = this.$refs.form as HTMLFormElement
        form.reset()
      }
    }
  },

  mounted () {
    const product = this.product as any

    if (this.product) {
      this.form = {
        id: product.id,
        photoUrl: product.photo_url,
        photo: product.photo,
        identity: product.identity,
        name: product.name,
        productType: product.product_type,
        unit: product.unit,
        unitType: product.unit_type,
        length: product.dimension_length,
        width: product.dimension_width,
        height: product.dimension_height,
        volume: product.volume,
        weight: product.weight
      }
    } else {
      this.form = {
        id: '',
        photoUrl: '',
        photo: '',
        identity: '',
        name: '',
        productType: '',
        unit: '',
        unitType: '',
        length: '',
        width: '',
        height: '',
        volume: '',
        weight: ''
      }
    }
  },

  methods: {
    onClickAdd () {
      this.$emit('on-click-add', this.form)
    },
    onImageSelected (image : any) {
      this.form.photo = image
    },
    onClickSave () {
      this.$emit('on-click-save', this.form)
    },
    onClickClose () {
      this.$emit('on-click-close')
    },
    calculateVolume () {
      const length = +this.form.length
      const width = +this.form.width
      const height = +this.form.height

      if (length && width && height) {
        this.form.volume = ((length * width * height) / 1000000).toFixed(2)
      } else {
        this.form.volume = ''
      }
    }
  }
})
</script>

<style scoped lang="scss"></style>

export interface Route {
  id: string
  type: string
  estimated_time_arrival: string
  duration: number
  wait: number
  odometer: number
  order: number
  track_id: string
  pickup_drop_off_location_point_id: string,
  pickup_drop_off_location_point: any
  deleted_at?: any
  created_at: Date
  updated_at: Date
}

export interface ShipmentHistoryPhoto {
  id: string
  proof_delivery_photo_path: string
  shipment_history_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  proof_delivery_photo_path_url: string
}

export interface ShipmentHistory {
  id: string
  status: string
  detail: string
  reported_at: Date
  proof_receiver_name: string
  proof_sign_photo_path: string
  route_id: string
  deleted_at?: any
  created_at: Date
  updated_at: Date
  proof_delivery_photo_url?: any
  proof_sign_photo_url: string
  route: Route
  shipment_history_photos: ShipmentHistoryPhoto[]
}

<template>
  <v-container fluid class="pa-5">
    <v-dialog
      v-model="dialogLogOrder"
      width="780"
    >
      <v-card class="pa-md-10 pa-5">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Log Order</h4>

          <v-icon color="black" @click="dialogLogOrder = false">
            mdi-close
          </v-icon>
        </v-card-title>

        <div v-if="shipmentDetail?.shipment_vehicle_log?.length > 0">
          <v-timeline align-top dense>
            <v-timeline-item v-for="(logChangeVehicle, i) in shipmentDetail?.shipment_vehicle_log" :key="i">
              <div v-if="logChangeVehicle?.type === null">
                <div>
                  <p class="subtitle-1">
                    Change Vehicle <span class="blue--text"> <br>{{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}</span>
                  </p>
                </div>
                <div class="ml-2 ma-3" style="border: 1px solid #CFCCCC">
                  <div class="pa-2 ma-2">
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            From
                          </p>
                        </div>
                        <h2>
                          {{ logChangeVehicle?.old_vehicle_detail?.plate_number }}
                        </h2>
                        <p class="mt-2">
                          {{ logChangeVehicle?.old_vehicle_detail?.vehicle?.name }}
                        </p>
                        <p>{{ logChangeVehicle?.old_driver?.user?.name }}</p>
                        <p class="ma-0 pa-0">
                          {{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            To
                          </p>
                        </div>
                        <h2>
                          {{ logChangeVehicle?.new_vehicle_detail?.plate_number }}
                        </h2>
                        <p class="mt-2">
                          {{ logChangeVehicle?.new_vehicle_detail?.vehicle?.name }}
                        </p>
                        <p>{{ logChangeVehicle?.new_driver?.user?.name }}</p>
                        <p class="ma-0 pa-0">
                          {{ $moment(logChangeVehicle?.updated_at).format('DD MMMM YYYY HH:mm') }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex align-start justify-space-between">
                      <v-col cols="3">
                        <div>
                          <p class="subtitle-1">
                            Proof
                          </p>
                          <div v-if="logChangeVehicle?.shipment_vehicle_photo?.[0]?.file.split('.')[1] === 'pdf'">
                            <a :href="logChangeVehicle?.shipment_vehicle_photo?.[0]?.files_url" target="_blank">Open File</a>
                          </div>
                          <image-component
                            v-else
                            :image="logChangeVehicle?.shipment_vehicle_photo?.[0]?.files_url"
                            class="zoom-image zoom-image:hover"
                            max-width="100"
                            min-width="100"
                          />
                        </div>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex align-start justify-space-between">
                      <v-col cols="3">
                        <div>
                          <p class="subtitle-1">
                            Note
                          </p>
                        </div>
                        <p v-if="logChangeVehicle?.note !== 'null'" class="mt-2">
                          {{ logChangeVehicle?.note }}
                        </p>
                      </v-col>
                    </v-row>
                  </div>
                </div>
              </div>
              <div v-if="logChangeVehicle?.type === 'weight_bridge:manual_deliver_weight'">
                <div>
                  <p class="subtitle-1">
                    Insert Refinery Data by <span class="blue--text"> {{ logChangeVehicle?.user?.name }} <br>{{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}</span>
                  </p>
                </div>
                <div class="ml-2 ma-3" style="border: 1px solid #CFCCCC">
                  <div class="pa-2 ma-2">
                    <p class="subtitle-1">{{ logChangeVehicle?.body_json?.ticketnumber }}</p>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Plate Number
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.plat_number }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Driver
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.body_json?.driver }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Out Date & Time Refinery
                          </p>
                        </div>
                        <p>
                          {{ $moment(logChangeVehicle?.response_json?.data?.outtime).format('DD MMMM YYYY HH:mm') }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Out Date & Time Refinery
                          </p>
                        </div>
                        <p>
                          {{ $moment(logChangeVehicle?.response_json?.data?.outtime).format('DD MMMM YYYY HH:mm') }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Quantity In Refenery
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.body_json?.refinerynetweight }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            FFA Quality
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.ffa_quality }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            MMI Quality
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.water_quality }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex align-start justify-space-between">
                      <v-col cols="3">
                        <div>
                          <p class="subtitle-1">
                            Note
                          </p>
                        </div>
                        <p v-if=" logChangeVehicle?.response_json?.data?.note !== 'null'" class="mt-2">
                          {{ logChangeVehicle?.response_json?.data?.note }}
                        </p>
                      </v-col>
                    </v-row>
                  </div>
                </div>
            </div>
            <div v-if="logChangeVehicle?.type === 'weight_bridge:manual_load_weight'">
                <div>
                  <p class="subtitle-1">
                    Insert Mill Data by <span class="blue--text"> {{ logChangeVehicle?.user?.name }} <br>{{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}</span>
                  </p>
                </div>
                <div class="ml-2 ma-3" style="border: 1px solid #CFCCCC">
                  <div class="pa-2 ma-2">
                    <p class="subtitle-1">{{ logChangeVehicle?.body_json?.ticketnumber }}</p>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Plate Number
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.plat_number }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Driver
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.body_json?.driver }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Out Date & Time Mill
                          </p>
                        </div>
                        <p>
                          {{ $moment(logChangeVehicle?.response_json?.data?.outtime).format('DD MMMM YYYY HH:mm') }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex justify-space-between">
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            Quantity In Refenery
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.weight }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            FFA Quality
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.ffa_quality }}
                        </p>
                      </v-col>
                      <v-col>
                        <div>
                          <p class="subtitle-1">
                            MMI Quality
                          </p>
                        </div>
                        <p>
                          {{ logChangeVehicle?.response_json?.data?.water_quality }}
                        </p>
                      </v-col>
                    </v-row>
                    <v-row class="d-flex align-start justify-space-between">
                      <v-col cols="3">
                        <div>
                          <p class="subtitle-1">
                            Note
                          </p>
                        </div>
                        <p v-if=" logChangeVehicle?.response_json?.data?.note !== 'null'" class="mt-2">
                          {{ logChangeVehicle?.response_json?.data?.note }}
                        </p>
                      </v-col>
                    </v-row>
                  </div>
                </div>
            </div>
            </v-timeline-item>
          </v-timeline>
        </div>

        <div v-if="logs.length > 0">
          <v-timeline align-top dense>
            <v-timeline-item v-for="(log, i) in logs" :key="i">
              <div v-if="log?.type === 'shipment:cancel_vendor'">
                <div>
                  <p class="subtitle-1">
                    Cancel Vendor by <span class="blue--text"> {{ log?.user?.name }} <br>{{ $moment(log?.created_at).format('DD MMMM YYYY HH:mm') }}</span>
                  </p>
                </div>
              </div>
            </v-timeline-item>
          </v-timeline>
        </div>

        <div v-else>
          <p class="text-secondary">
            Empty
          </p>
        </div>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="markerDialog"
      max-width="800px"
    >
      <v-card class="pa-8">
        <v-card-title class="pa-0 mb-6 d-flex justify-space-between">
          <h4>WB data has not been entered.</h4>
          <v-icon color="black" @click="markerDialog = false">
            mdi-close
          </v-icon>
        </v-card-title>
        
        <v-card-text v-if="markerData" class="pa-0">
          <div class="d-flex justify-space-between mb-6">
            <div>
              <p :style="{ marginBottom: '4px' }">DO Number</p>
              <p class="subtitle-1" :style="{ marginTop: '0', marginBottom: '8px' }">{{ markerData[0]?.order_identity }}</p>
            </div>
            <v-btn
              x-large
              depressed
              color="white"
              class="mb-2 subtitle-1 text-capitalize"
              @click="handleExportMarker"
            >
              <v-icon size="24" class="mr-2">
                mdi-file-download
              </v-icon>
              Export
            </v-btn>
          </div>

          <v-data-table
            :headers="[
              { text: 'No', value: 'no', sortable: false },
              { text: 'Quantity in Pickup', value: 'pickup', sortable: false },
              { text: 'Quantity in Dropoff', value: 'dropoff', sortable: false },
              { text: 'Plate Number', value: 'plate_number', sortable: false },
              { text: 'Description', value: 'description', sortable: false }
            ]"
            :items="markerData.map((item, index) => ({
              no: index + 1,
              pickup: item.pickup || '-',
              dropoff: item.dropoff || '-', 
              plate_number: item.plate_number || '-',
              description: item.description || '-'
            }))"
            hide-default-footer
            class="mb-6"
          ></v-data-table>
        </v-card-text>

        <v-card-text v-else class="pa-0 text-center">
          No data available
        </v-card-text>
      </v-card>
    </v-dialog>
    <div class="d-flex justify-space-between pa-4 ma-4">
      <div>
        <div class="d-flex align-center">
          <h1>
            {{ shipmentDetail?.orders?.[0]?.identity }}
          </h1>
          <v-tooltip v-if="shipmentDetail" bottom content-class="custom-tooltip">
             <template #activator="{ on, attrs }">
                <div 
                 v-if="shipmentDetail?.marker_notification" 
                 class="ml-2 d-inline-flex align-center justify-center rounded-circle ml-4"
                 style="width: 20px; height: 20px; border: 2px solid #EF3434; cursor: pointer"
                 v-bind="attrs"
                 v-on="on"
                 @click="handleMarkerClick(shipmentDetail?.id)"
              >
                 <span style="color: #EF3434; font-weight: bold; font-size: 12px;">!</span>
              </div>
            </template>
              <div class="pa-2" style="background: black; color: white; max-width: 300px;">
               <p class="mb-0">WB data has not been entered.</p>
              <p class="mb-0">The WB data has not been entered yet.</p>
              <p class="mb-0">Please check the WB data for this DO.</p>
              </div>
           </v-tooltip>
        </div>
        <div class="d-flex align-center">
          <v-btn
            :loading="$store.getters['logs/isItemLoading'](shipmentDetail?.id)"
            elevation="0"
            text
            small
            class="text-capitalize pa-0 blue--text"
            @click="refreshWB(shipmentDetail)"
          >
            <div class="d-flex align-center">
              <v-icon color="blue--text" class="mr-1">
                mdi-refresh
              </v-icon>
              <span>Reload WB</span>
            </div>
          </v-btn>

          <v-divider vertical class="mx-3 my-2" style="height: 24px;" />

          <v-btn
            elevation="0"
            color="primary"
            text
            class="text-capitalize pa-0"
            @click="dialogLogOrder = true"
          >
            <span class="mr-1">Log Order</span>
            <v-icon small>mdi-chevron-right</v-icon>
          </v-btn>
        </div>
        <!-- <div>
          <p class="red--text">Log Order</p> <span><v-icon>mdi-chevron-right</v-icon></span>
        </div> -->
      </div>
      <v-chip
        label
        outlined
      >
        <p class="text-capitalize subtitle-1 ma-0">
          {{ shipmentDetail?.orders?.[0]?.completion_status === 'FINISHED' ? 'FINISHED' : shipmentDetail?.status }}
        </p>
      </v-chip>
    </div>
    <div class="pa-6 ma-6 mt-5" style="border: 1px solid #CFCCCC">
      <v-row class="pa-4">
        <v-col cols="7">
          <div class="mb-2">
            <h2>Detail Shipment</h2>
          </div>
          <div>
            <v-row>
              <v-col cols="12">
                <p class="subtitle-1">
                  Product
                </p>
                <v-row>
                  <v-col>
                    <p>Product</p>
                    <p>Quantity</p>
                    <p>Remaining Quantity</p>
                  </v-col>
                  <v-col>
                    <p class="subtitle-1">
                      : {{ shipmentDetail?.orders?.[0]?.pickup_suborders?.[0]?.products?.[0]?.name }}
                    </p>
                    <p class="subtitle-1">
                      : {{ formatNumber(parseFloat(shipmentDetail?.orders?.[0]?.total_weight)) }} KG
                    </p>
                    <p class="subtitle-1 blue--text">
                      : {{ shipmentDetail?.orders?.[0]?.remaining_weight.toString().includes('-') ? formatNumber(shipmentDetail?.orders?.[0]?.remaining_weight).toString().replace('-', '+') : formatNumber(shipmentDetail?.orders?.[0]?.remaining_weight) }} KG
                      <span v-if="shipmentDetail?.orders?.[0]?.remaining_weight.toString().includes('-')" class="red--text caption">(Exceeded Quantity)</span>
                    </p>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </div>
              <div class=" mt-5">
                <h2>WB Accumulation</h2>
                <v-row class="mt-2">
                  <v-col cols="6">
                    <p>Net Mill</p>
                    <p>Net Refinery</p>
                    <p>Total Different Net</p>
                  </v-col>
                  <v-col cols="6">
                    <p class="subtitle-1">
                      : {{ totalMill | toThousand }}
                    </p>
                    <p class="subtitle-1">
                      : {{ totalRefinery | toThousand }}
                    </p>
                    <p class="subtitle-1">
                      : {{ totalDifferent | toThousand }}
                    </p>
                  </v-col>
                </v-row>
            </div>
          <!-- <v-responsive :aspect-ratio="setAspectRatio" width="1000px" height="300px" class="mb-10" style="z-index: 5">
            <v-container v-if="shipmentDetail?.route_state === 'PENDING'" fluid class="pa-1 white rounded">
              <div class="d-flex justify-center">
                <animation-component
                  :animation-data="require('~/assets/animations/loading-map.json')"
                  :auto-play="true"
                  :loop="true"
                />
              </div>
              <h4 class="mb-3 text-center">
                {{ $t('general.wait_map_process') }}
              </h4>
              <p class="body-1 text-secondary mb-10 text-center">
                {{ $t('general.wait_map_subtitle') }}
              </p>
            </v-container>
            <v-container
              v-else-if="shipmentDetail?.route_state === 'ERRORED' || shipmentDetail?.route_state === 'UNSOLVABLE' "
              fluid
              class="pa-10 white rounded"
            >
              <div class="d-flex justify-center">
                <animation-component
                  :animation-data="require('~/assets/animations/failure-load-map.json')"
                  :auto-play="true"
                  :loop="true"
                />
              </div>
              <h4 class="mb-3 text-center">
                {{ shipmentDetail?.route_state }}
              </h4>
              <p class="body-1 text-secondary mb-10 text-center">
                {{ $t('general.failed_map_subtitle') }}
              </p>
            </v-container>
            <custom-map
              v-else-if="shipmentDetail?.route_state === 'FULFILLED'"
              :latitude="centerPosition?.lat"
              :longitude="centerPosition?.lng"
              :polyline="polyline"
              :zoom="zoom"
            >
              <template #marker>
                <l-marker
                  v-for="(marker, i) in markers"
                  :key="i"
                  :lat-lng="[parseFloat(marker.lat), parseFloat(marker.lng)]"
                >
                  <l-icon v-if="marker.type === 'PICKUP'" :icon-anchor="[20, 40]">
                    <v-icon size="40" color="info">
                      mdi-map-marker
                    </v-icon>
                  </l-icon>
                  <l-icon v-else :icon-anchor="[20, 40]">
                    <v-icon size="40" color="success">
                      mdi-map-marker
                    </v-icon>
                  </l-icon>
                  <l-popup>
                    <div>
                      <p
                        class="subtitle-3 mb-1"
                        :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                      >
                        {{ marker.type }}
                      </p>
                      <p class="caption ma-0 text--primary">
                        {{ marker.name }}
                      </p>
                    </div>
                  </l-popup>
                </l-marker>
                <l-marker
                  v-for="marker in driverMarker"
                  :key="marker.id"
                  :lat-lng="marker.latLng"
                >
                  <l-icon :icon-anchor="[20, 40]">
                    <v-icon
                      color="primary"
                      size="40"
                      style="text-shadow: 0 0 4px rgba(0, 0, 0, .25)"
                    >
                      mdi-map-marker-account
                    </v-icon>
                  </l-icon>
                </l-marker>
              </template>
            </custom-map>
          </v-responsive> -->
        </v-col>
        <v-col cols="5">
          <div class="timeline-wrapper">
            <v-timeline align-top dense>
              <v-timeline-item v-for="(route) in sortedSelectedRoutes" :key="route.id">
                <div class="ml-2 ma-3">
                  <div class="d-flex align-start">
                    <p>{{ route?.type === 'PICKUP' ? 'MILL' : (route?.type === 'DROPOFF' ? 'REFINERY' : '') }}</p>
                    <change-drop-off-destination-dialog
                      v-if="route.type === 'DROPOFF'"
                      :dialog="dialog[i]"
                      :route="route"
                      :orders="shipmentDetail?.orders"
                      :is-loading="isLoadingChangeDestination"
                      :drop-off-locations="dropOffLocations"
                      @on-click-close="$set(dialog, i, false)"
                      @on-click-change="changeDestination($event, i)"
                    >
                      <template #activator>
                        <v-icon
                          v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'"
                          size="24"
                          class="ml-3"
                          color="secondary"
                          :disabled="shipmentDetail?.status !== 'PROPOSED' && shipmentDetail?.status !== 'EXPIRED'"
                          @click="$set(dialog, i, true)"
                        >
                          mdi-pencil
                        </v-icon>
                      </template>
                    </change-drop-off-destination-dialog>
                  </div>
                  <v-row class="d-flex justify-space-between">
                    <v-col cols="3">
                      <div>
                        <p>{{ route?.type === 'PICKUP' ? 'Estimate Mill' : (route?.type === 'DROPOFF' ? 'Estimate Refinery' : '') }}</p>
                      </div>
                    </v-col>
                    <v-col cols="9">
                      <p class="subtitle-1">
                        : {{ route.estimation_date }} WIB
                      </p>
                    </v-col>
                  </v-row>
                  <v-row class="d-flex align-start justify-space-between">
                    <v-col cols="3">
                      <div>
                        {{ $t('lspHistoryShipment.operational_time') }}
                      </div>
                    </v-col>
                    <v-col cols="9">
                      <p class="subtitle-1">
                        : {{ route.pickup_drop_off_location_point?.start_operation_hour }} - {{ route.pickup_drop_off_location_point?.end_operation_hour }} WIB
                      </p>
                    </v-col>
                  </v-row>
                  <v-row class="d-flex align-start justify-space-between">
                    <v-col cols="3">
                      <div>
                        {{ $t('lspHistoryShipment.location') }}
                      </div>
                    </v-col>
                    <v-col cols="9">
                      <p class="subtitle-1">
                        : {{ route.pickup_drop_off_location_point?.identity }}
                      </p>
                    </v-col>
                  </v-row>
                </div>
              </v-timeline-item>
            </v-timeline>
          </div>
        </v-col>
      </v-row>
      <v-divider class="pa-2 ma-3" />
      <div class="pa-2 ma-2">
        <p>Created by {{ shipmentDetail?.created_by?.name }} {{ $moment(shipmentDetail?.created_at).format('DD-MM-yyyy | HH:mm') }}</p>
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import { Polyline } from 'leaflet'
import Vue from 'vue'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import ChangeDropOffDestinationDialog from '~/components/ChangeDropOffDestinationDialog.vue'
import { Route, Shipment } from '~/types/shipment'
import { colorType, formatNumber } from '~/utils/functions'
import { PickupDropOffLocationPoint } from '~/types/product'

export default Vue.extend({
  name: 'OrderHeader',

  components: {
    CustomMap,
    ChangeDropOffDestinationDialog
  },

  props: {
    shipmentDetail: {
      type: Object as () => Shipment | null,
      default: null
    },
    centerPosition: {
      type: Object as () => { lat: number, lng: number },
      default: () => {
      }
    },
    zoom: {
      type: Number,
      default: 50
    },
    polyline: {
      type: Object as () => Polyline,
      default: () => ({
        mapBox: [],
        fms: []
      })
    },
    markers: {
      type: Array,
      default: () => []
    },
    isLoadingShipmentDetail: {
      type: Boolean,
      default: false
    },
    shipmentId: {
      type: String || null,
      default: null
    },
    selectedRoutes: {
      type: Array as () => Route[],
      default: []
    }
  },

  data: () => ({
    dialogLogOrder: false,
    dialog: [] as boolean[],
    totalMill: 0,
    totalRefinery: 0,
    totalDifferent: 0,
    totalPersentage: 0 as any,
    markerDialog: false,
    markerData: null as any,
  }),

  computed: {
    setAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 3
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },
    userRole (): string {
      return this.$auth.$state.user.data.role
    },
    isLoadingChangeDestination (): boolean {
      return this.$store.getters['shipping-company/order-shipment/create-order/sub-order/isLoadingForm']
    },

    isLoadingDropOffLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },
    dropOffLocations (): PickupDropOffLocationPoint[] {
      return this.$store.getters['pick-up-drop-off-location-point/data'].items
    },
    logs (): { items: any[], page: number, totalPage: number, map: any } {
      return this.$store.getters['logs/data'].items
    },
    orderIdentity (): string | undefined {
      return this.shipmentDetail?.orders[0]?.identity
    },
    sortedSelectedRoutes (): Route[] {
      return [...this.selectedRoutes].sort((a, b) => {
        if (a.type === 'PICKUP') {
          return -1
        }
        if (b.type === 'PICKUP') {
          return 1
        }
        return 0
      })
    }
  },

  watch: {
  dialogLogOrder(newVal) {
    if (newVal && this.orderIdentity) {
      if (this.userRole === 'LOGISTIC_SERVICE_PROVIDER') {
        this.getDataLog()
      }
    }
  }
 },

  mounted () {
    this.calculateWeights()
    if (this.userRole === 'LOGISTIC_SERVICE_PROVIDER') {
      this.getDropOffLocations()
    }
  },

  methods: {
    formatNumber,
    colorType (type: string):string {
      return colorType(type)
    },
    async handleMarkerClick(id: string) {
        this.markerData = await this.$store.dispatch('shipment/getMarkerList', { id })
        this.markerDialog = true
    },
    async handleExportMarker() {
      await this.$store.dispatch('shipment/exportMarkerList', { 
        id: this.shipmentDetail?.id
      })
    },
    getDropOffLocations () {
      if (this.shipmentDetail?.orders.length === 0) { return }
      const shippingCompanyIds = this.shipmentDetail?.orders.map(order => (
        order.shipment_company_id
      )) as any

      this.$store.dispatch('pick-up-drop-off-location-point/getItems', {
        idSc: shippingCompanyIds?.join('|'),
        filterColumns: 'type',
        filterKeys: 'PICKUP DROPOFF|DROPOFF',
        entries: -1
      })
    },
    async changeDestination (formValues: {
      subOrderId: string,
      orderNumber: string,
      locationId: string,
      note: string
    }, index: number) {
      const res = await this.$store.dispatch('shipping-company/order-shipment/create-order/sub-order/changeDestination', {
        ...formValues
      })

      if (res) {
        this.$set(this.dialog, index, false)
        this.$emit('on-success-change-destination')
        window.scrollTo(0, 0)
      }
    },
    calculateWeights () {
      let pickupQuantity = 0
      let dropoffQuantity = 0

      this.shipmentDetail?.tracks?.forEach((track) => {
        if (track?.weight_bridge?.quantity_in_pickup) {
          pickupQuantity += parseFloat(track.weight_bridge.quantity_in_pickup) || 0;
        }
        if (track?.weight_bridge?.quantity_in_dropoff) {
          dropoffQuantity += parseFloat(track.weight_bridge.quantity_in_dropoff) || 0;
        }
      });

      this.totalMill = pickupQuantity
      this.totalRefinery = dropoffQuantity
      this.totalDifferent = this.totalRefinery - this.totalMill
    },

    async getDataLog () {
      await this.$store.dispatch('logs/getItems', {
        search_columns: 'body',
        searchKey: this.shipmentDetail?.orders[0]?.identity,
        filter_columns: 'type',
        filter_keys: 'shipment:cancel_vendor'
      })
    },

    async refreshWB(shipmentDetail: any) {
      if (!shipmentDetail?.id) {
        console.error('Item ID is undefined')
        return
      }

      try {
        const params = {
          id: shipmentDetail?.id,
          log_ids: [],
          start_date: '',
          end_date: '',
          do: shipmentDetail?.orders?.[0]?.identity || ''
        }
        
        await this.$store.dispatch('logs/refreshWeighbridge', params)
      } catch (error) {
        console.error('Error refreshing WB:', error)
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.timeline-wrapper {
  width: 800px;
}
.zoom-image {
  transition: transform 0.3s;
}

.zoom-image:hover {
  transform: scale(3.0);
}

.custom-tooltip {
  background-color: black !important;
  opacity: 1 !important;
}
 </style>

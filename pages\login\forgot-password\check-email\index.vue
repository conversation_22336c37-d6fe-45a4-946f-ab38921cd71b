<template>
  <v-container
    fluid
    class="pa-5 pa-sm-10 d-flex fill-height"
  >
    <div class="ma-auto d-flex flex-column align-center" style="width: 600px">
      <v-icon class="mr-2" color="primary" size="80">
        mdi-email
      </v-icon>

      <h3 class="mb-6 text-center">
        Check Your Inbox.
      </h3>

      <p class="body-1 text-secondary mb-5 text-center">
        We have sent a forgot password email to <span style="font-weight: bold">{{ dataUser?.email }}</span> account. Please check your inbox or spam folder as it may expire within 24 hours..
      </p>

      <v-col class="col-6">
        <div class="d-flex justify-center">
          <v-btn
            block
            x-large
            depressed
            color="primary"
            class="subtitle-1 text-capitalize mb-2"
            @click="openGmail()"
          >
            <v-icon class="mr-2">
              mdi-email
            </v-icon>
            Open Gmail
          </v-btn>
        </div>

        <div class="d-flex justify-center">
          <v-btn
            x-large
            text
            :block="!$vuetify.breakpoint.smAndUp"
            color="primary"
            class="subtitle-1 text-capitalize mt-5"
            @click="$router.push('/login')"
          >
            Back to Login
          </v-btn>
        </div>
      </v-col>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '~/types/user'

export default Vue.extend({
  name: 'CheckEmailPage',

  computed: {
    dataUser () {
      return this.$store.getters['password/data'] as User | null
    }
  },

  methods: {
    openGmail () {
      const gmailUrl = 'https://mail.google.com'
      window.open(gmailUrl, '_blank')
    }
  }
})
</script>

<style scoped>

</style>

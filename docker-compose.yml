version: '3'
services:
    application:
        build:
            context: .
            dockerfile: Dockerfile
        image: ${IMAGE_NAME}
        restart: unless-stopped
        tty: true
        environment:
            SERVICE_NAME: ${IMAGE_NAME}-${APP_ENV}-application
            SERVICE_TAGS: dev
        working_dir: /home/<USER>/app
        deploy:
            replicas: ${COMPOSE_REPLICAS:-1}
        volumes:
            - ./:/var/www
        networks:
            - proxy
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}.entrypoints=http"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}.rule=Host(`${APP_HOST}`)"
            - "traefik.http.middlewares.${IMAGE_NAME}-${APP_ENV}-https-redirect.redirectscheme.scheme=https"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}.middlewares=${IMAGE_NAME}-${APP_ENV}-https-redirect"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}-secure.entrypoints=https"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}-secure.rule=Host(`${APP_HOST}`)"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}-secure.tls=true"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}-secure.tls.certresolver=http"
            - "traefik.http.routers.${IMAGE_NAME}-${APP_ENV}-secure.service=${IMAGE_NAME}-${APP_ENV}"
            - "traefik.http.services.${IMAGE_NAME}-${APP_ENV}.loadbalancer.server.port=3000"
            - "traefik.docker.network=proxy"

#Docker Networks
networks:
    proxy:
        external: true

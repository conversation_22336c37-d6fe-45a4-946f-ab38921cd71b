<template>
  <v-container fluid class="pa-0 px-5 px-md-10 mb-10">
    <v-btn
      plain
      class="text-capitalize subtitle-1 mb-6"
      @click="$router.back()"
    >
      <v-icon class="mr-2">
        mdi-chevron-left
      </v-icon>
      Back to List
    </v-btn>

    <import-data-component
      v-if="importType === 'LOCATION' ? !isLoadingKeyLocation : !isLoadingKeyProduct"
      :type="importType"
      :import-keys="importType === 'LOCATION' ? locationKeys : productKeys"
      @on-change-form-values="formValues = $event"
    >
      <template #button-download-template>
        <v-btn
          v-if="importType === 'PRODUCT'"
          x-large
          outlined
          class="subtitle-1 text-capitalize"
          style="border: 1px solid #CFCCCC"
          @click="onClickDownloadBulkImportProduct"
        >
          <v-icon class="mr-3">
            mdi-file-download
          </v-icon>
          Download Template
        </v-btn>
        <v-btn
          v-if="importType === 'LOCATION'"
          x-large
          outlined
          class="subtitle-1 text-capitalize"
          style="border: 1px solid #CFCCCC"
          @click="onClickDownloadBulkImportLocation"
        >
          <v-icon class="mr-3">
            mdi-file-download
          </v-icon>
          Download Template
        </v-btn>
      </template>
      <template #footer-bulk-import>
        <v-row class="ma-0 mx-n3">
          <v-col class="col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              depressed
              color="primary"
              :loading="importType === 'LOCATION' ? isLoadingFormKeyLocation : isLoadingFormKeyProduct"
              :disabled="formValues?.length === 0"
              class="subtitle-1 text-capitalize mr-6"
              @click="onClickImport"
            >
              Import Data
            </v-btn>
          </v-col>
          <v-col class="col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              outlined
              color="primary"
              class="subtitle-1 text-capitalize"
              @click="$router.back()"
            >
              Cancel
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </import-data-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import ImportDataComponent from '~/components/ImportDataComponent.vue'
import { ImportKey } from '~/types/import-key'

export default Vue.extend({
  name: 'ImportDataPage',

  components: {
    ImportDataComponent
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    formValues: [] as object[]
  }),

  computed: {
    importType (): string {
      return this.$route.query?.type as string
    },

    shipmentCompanyId (): string {
      return this.$route.params.sc
    },

    isLoadingKeyProduct (): boolean {
      return this.$store.getters['shipping-company/product/isLoading']
    },

    isLoadingKeyLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoading']
    },

    isLoadingFormKeyProduct (): boolean {
      return this.$store.getters['shipping-company/product/isLoadingForm']
    },

    isLoadingFormKeyLocation (): boolean {
      return this.$store.getters['pick-up-drop-off-location-point/isLoadingForm']
    },

    locationKeys (): ImportKey[] {
      return this.$store.getters['pick-up-drop-off-location-point/dataItemsLocationKey']
    },

    productKeys (): ImportKey[] {
      return this.$store.getters['shipping-company/product/dataItemsProductKey']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Import Data')
  },

  mounted () {
    this.importType === 'LOCATION'
      ? this.getLocationKeys()
      : this.getProductKeys()
  },

  methods: {
    getLocationKeys () {
      this.$store.dispatch('pick-up-drop-off-location-point/getItemsLocationKey')
    },

    getProductKeys () {
      this.$store.dispatch('shipping-company/product/getItemsProductKey')
    },

    onClickImport () {
      this.importType === 'LOCATION'
        ? this.bulkImportLocations()
        : this.bulkImportProducts()
    },

    async bulkImportProducts () {
      const response = await this.$store.dispatch('shipping-company/product/importProducts', {
        products: this.formValues,
        shipmentCompanyId: this.shipmentCompanyId
      })

      if (response) {
        this.$router.back()
      }
    },

    async bulkImportLocations () {
      const response = await this.$store.dispatch('pick-up-drop-off-location-point/importLocations', {
        locations: this.formValues,
        shipmentCompanyId: this.shipmentCompanyId
      })

      if (response) {
        this.$router.back()
      }
    },

    onClickDownloadBulkImportProduct () {
      const downloadLink = '/template-bulk-import-product.xlsx'
      const anchor = document.createElement('a')
      anchor.href = downloadLink
      anchor.download = 'template-bulk-import-product.xlsx'
      anchor.click()
    },

    onClickDownloadBulkImportLocation () {
      const downloadLink = '/template-bulk-import-location.xlsx'
      const anchor = document.createElement('a')
      anchor.href = downloadLink
      anchor.download = 'template-bulk-import-location.xlsx'
      anchor.click()
    }

  }
})
</script>

<style scoped> </style>

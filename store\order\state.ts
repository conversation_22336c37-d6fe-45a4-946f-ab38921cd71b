import { Order } from '~/types/product'

export interface OrderState {
  isLoading: boolean
  isLoadingForm: boolean
  isLoadingDetail: boolean
  items: Order[]
  selectedOrder: Order | null
  totalPage: number
  page: number
}

export const state = (): OrderState => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDetail: false,
  items: [],
  totalPage: 1,
  page: 1,
  selectedOrder: null
})

export default state

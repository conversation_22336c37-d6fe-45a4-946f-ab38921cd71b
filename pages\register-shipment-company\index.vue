<template>
  <v-row class="ma-0 d-flex">
    <v-col class="pa-10 d-flex">
      <v-container fluid class="pa-0 col-12 col-sm-10 d-flex flex-column align-center justify-space-between">
        <div class="mb-10 d-flex align-center">
          <image-component :image="personalize.logo_url" max-width="64" max-height="64" class="mr-5" />

          <h3>{{ personalize.name }}</h3>
        </div>

        <form-user-registration
          :token="token"
        />

        <v-img
          :src="require(`~/assets/icons/powered.svg`)"
          max-width="150"
          max-height="50"
          contain
          class="mt-10"
        />
      </v-container>
    </v-col>

    <v-col v-if="$vuetify.breakpoint.lgAndUp" class="pa-0">
      <v-img
        :src="require(`~/assets/images/register-side-image.png`)"
      />
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import { Personalize } from '~/types/user'

export default Vue.extend({
  name: 'RegisterCustomerPage',

  data: () => ({
    token: null as string | null
  }),

  computed: {
    isLoadingPersonalize () {
      return this.$store.getters['logistic-service-provider/personalize/isLoading']
    },

    personalize () {
      return this.$store.getters['logistic-service-provider/personalize/data']
    }
  },

  mounted () {
    const domain = this.$store.getters.domain

    this.$store.dispatch('logistic-service-provider/personalize/getPersonalize', {
      domain
    })

    this.getPersonalize()

    const urlParams = new URLSearchParams(window.location.search)
    this.token = urlParams.get('token')
  },

  methods: {
    getPersonalize () {
      const ctx = this
      setTimeout(function () {
        const personalize = ctx.$store.getters['logistic-service-provider/personalize/data'] as Personalize
        ctx.$vuetify.theme.themes.light.primary = personalize.primary_color ?? ctx.$vuetify.theme.themes.light.primary
      }, 100)
    }
  }
})
</script>

<style lang="scss" scoped></style>

<template>
  <v-container
    fluid
    class="pa-md-10 pa-5 ma-0"
  >
    <v-btn
      tile
      text
      class="text-capitalize mb-md-10 mb-5"
      @click="goToDashboard()"
    >
      <v-icon left color="black">
        mdi-chevron-left
      </v-icon>
      <p class="subtitle-1 ma-0">
        {{ $t('profile.back') }}
      </p>
    </v-btn>
    <v-sheet v-if="isLoading" class="pa-10 ma-0">
      <div class="mb-5 pa-0 d-flex justify-space-between">
        <v-skeleton-loader type="image" width="250" height="30" style="border-radius: 15px !important;" class="mx-md-0 mx-auto text-secondary text-center" />
        <v-skeleton-loader v-if="!$vuetify.breakpoint.xs" type="image" height="50" width="120" />
      </div>
      <div class=" d-flex flex-md-row flex-column pt-8" style="width: 100%;">
        <v-skeleton-loader type="image" width="75" height="75" class="mx-md-0 mx-auto mb-md-0 mt-3" />
        <div v-if="!$vuetify.breakpoint.xs" class="ml-5" />
        <div class="mt-2" style="width: 100%">
          <v-skeleton-loader type="text" width="100%" class="mx-md-0 mx-auto mb-md-0 mt-5" />
          <v-skeleton-loader type="text" width="75%" class="mx-md-0 mx-auto mb-md-0 mt-3" />
        </div>
      </div>
      <v-skeleton-loader type="text" width="100%" class="mt-8" />
      <v-skeleton-loader type="text" width="75%" class="mt-2" />
      <v-skeleton-loader type="text" width="200" class="pt-2 mb-10 mt-5" />
      <v-skeleton-loader v-if="$vuetify.breakpoint.xs" x-large type="image" height="50" width="100%" />
    </v-sheet>
    <v-container
      v-else
      fluid
      class="pa-10 rounded white"
    >
      <div class="flex-column">
        <div class="mb-10 pa-0 d-flex justify-space-between align-center">
          <h3 class="mx-md-0 mx-auto text-secondary text-center">
            {{ heading }}
          </h3>

          <v-btn
            v-if="!$vuetify.breakpoint.xs"
            :loading="isLoadingLogout"
            x-large
            elevation="0"
            class="primary text-capitalize"
            @click="logout"
          >
            {{ $t('profile.logout') }}
          </v-btn>
        </div>

        <v-row fluid class="ma-0 pa-0 d-flex flex-md-row flex-column">
          <v-col v-if="company" class="pa-0 d-flex flex-md-row flex-column">
            <div class="mx-md-0 mx-auto mb-md-0 mb-5">
              <v-img
                class="mr-md-5"
                :src="company.logo_url"
                aspect-ratio="1"
                width="100"
                contain
              >
                <template #placeholder>
                  <v-img
                    min-width="80"
                    max-width="80"
                    aspect-ratio="1"
                    contain
                    :src="require(`~/assets/images/placeholder-company-logo.svg`)"
                  />
                </template>
              </v-img>
            </div>

            <div>
              <h2 class="text-md-left text-center">
                {{ company.name }}
              </h2>
              <a v-if="company.domain" class="text-md-left caption">
                {{ company.domain }}
              </a>
              <p class="ma-0 mt-5 mb-2 body-1 text-secondary">
                {{ $t('profile.address') }}
              </p>
              <p class="ma-0 mb-5 body-1 black--text">
                {{ company.address }}
              </p>
              <a class="subtitle-1 black--text text-decoration-underline" @click="$router.push('/profile/form-profile')">
                {{ $t('profile.update_company') }}
              </a>
            </div>
          </v-col>

          <div v-if="company">
            <v-divider v-if="!$vuetify.breakpoint.xs" vertical class="mx-10" />

            <v-divider v-else class="my-10" />
          </div>
          <v-col class="pa-0">
            <div
              class="mb-5 d-flex justify-md-start flex-sm-row flex-column justify-center align-center"
            >
              <image-component
                v-if="dataUser?.avatar_url"
                min-width="64"
                max-width="64"
                :image="dataUser?.avatar_url"
                class="mr-md-5 mr-sm-5 mr-0 mb-sm-0 mb-5"
              />
              <v-avatar
                v-else
                size="64"
                rounded
                color="primary"
                class="mr-md-5 mr-sm-5 mr-0 mb-sm-0 mb-5"
              >
                <p class="white--text ma-0 body-1">
                  {{ initialName }}
                </p>
              </v-avatar>

              <div>
                <h3 class="mb-2">
                  {{ personName }}
                </h3>
                <div class="d-flex">
                  <v-icon color="black" class="mr-2">
                    mdi-phone
                  </v-icon>
                  <p class="ma-0">
                    {{ personPhone }}
                  </p>
                </div>
              </div>
            </div>

            <div class="d-flex mb-2">
              <v-icon size="20" color="black" class="mr-2">
                mdi-email
              </v-icon>
              <p class="ma-0">
                {{ personEmail }}
              </p>
            </div>

            <div class="d-flex mb-5">
              <v-icon size="20" color="black" class="mr-2">
                mdi-lock
              </v-icon>
              <p class="ma-0">
                ******
              </p>
            </div>

            <!--            <a class="black&#45;&#45;text subtitle-1" @click="$router.push(routeManageUsers)">Update User</a>-->

            <user-form-item
              :user="dataUser"
              :is-loading-form="isLoadingForm"
              :is-loading-form-clear-image="isLoadingFormClearImage"
              :dialog="dialogUpdateUser"
              @on-click-save="updateUser($event)"
              @on-clear-image="clearImage"
              @on-close-dialog="dialogUpdateUser = false"
                          >
              <template #activator>
                <v-container fluid class="pa-0 d-flex justify-start">
                  <p
                    class="subtitle-1 ma-0 text-decoration-underline"
                    style="cursor: pointer"
                    @click="dialogUpdateUser = true"
                  >
                    {{ $t('profile.update_user') }}
                  </p>
                </v-container>
              </template>
            </user-form-item>
          </v-col>
        </v-row>

        <div class="primary my-12 rounded" style="width: 100%; height: 32px;" />

        <v-row fluid class="ma-0 pa-0 d-flex flex-md-row flex-column align-center">
          <v-row v-if="data?.logistics_service_provider" class="ma-0 pa-0 d-flex flex-md-row flex-column">
            <v-col class="pa-0 d-flex flex-column">
              <h2 class="text-md-left text-center text-lg-body-1">
                Custom Theme Color
              </h2>
              <div class="d-flex flex-row mt-2 align-center">
                <div class="primary rounded" style="width: 48px; height: 48px;">
                  <v-icon color="white" class="ma-auto" style="height: 100%; width: 100%">
                    mdi-check
                  </v-icon>
                </div>

                <v-dialog
                  v-model="colorDialog"
                  width="500"
                >
                  <template #activator="{ on, attrs }">
                    <v-img
                      v-bind="attrs"
                      class="mr-md-5 ml-4 flex"
                      :src="require(`~/assets/images/colors.png`)"
                      aspect-ratio="1"
                      max-width="48"
                      contain
                      v-on="on"
                      @click="colorDialog = true"
                    />
                  </template>

                  <v-card class="pa-4">
                    <div class="d-flex justify-space-between">
                      <v-color-picker
                        v-model="primaryColor"
                        dot-size="25"
                        mode="hexa"
                        @update:color="previewColor"
                      />
                      <div class="d-flex flex-column">
                        <v-btn
                          :loading="isLoadingSaveForm"
                          class="primary white--text mb-4"
                          @click="saveThemeColor"
                        >
                          Save
                        </v-btn>
                        <v-btn
                          depressed
                          class="black--text"
                          @click="resetThemeColor"
                        >
                          Cancel
                        </v-btn>
                      </div>
                    </div>
                  </v-card>
                </v-dialog>
              </div>
            </v-col>
            <div v-if="data?.logistics_service_provider">
              <v-divider v-if="!$vuetify.breakpoint.xs" vertical class="mx-10" />

              <v-divider v-else class="my-10" />
            </div>
            <v-col class="pa-0">
              <h4 class="mb-5">
                {{ $t('profile.banner_for_marketing') }}
              </h4>
              <v-skeleton-loader v-if="isLoadingSaveForm" type="image" width="125" height="160" />
              <div v-else>
                <input
                  ref="file"
                  type="file"
                  class="d-none"
                  accept="image/png, image/jpeg, image/bmp"
                  @change="onChangeFile"
                >
                <v-btn
                  v-if="data?.logistics_service_provider?.register_banner_url === null"
                  width="125"
                  height="160"
                  outlined
                  class="mb-1 custom-img-btn"
                  @click="$refs.file.click()"
                >
                  <v-icon>
                    mdi-plus
                  </v-icon>
                </v-btn>
                <div v-else style="position: relative; height: 160px; width: 125px">
                  <v-img
                    :src="data?.logistics_service_provider?.register_banner_url"
                    width="125"
                    height="160"
                    contain
                    style="position: relative"
                  />
                  <v-menu
                    v-model="menu"
                    transition="slide-y-transition"
                  >
                    <template #activator="{on, attrs}">
                      <v-btn
                        v-bind="attrs"
                        style="position: absolute; right: 0; top: 0; opacity: 60%"
                        class="black pa-0 ma-0"
                        height="32"
                        x-small
                        v-on="on"
                      >
                        <v-icon color="white">
                          mdi-dots-vertical
                        </v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        @click="$refs.file.click()"
                      >
                        <v-list-item-title>
                          {{ $t('userCardItem.edit') }}
                        </v-list-item-title>
                      </v-list-item>
                    </v-list>
                    <v-list>
                      <v-list-item
                        @click="clearImageBanner"
                      >
                        <v-list-item-title>
                          {{ $t('userCardItem.delete') }}
                        </v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-row>
      </div>
      <div class="my-5">
        <nuxt-link class="ma-1" :to="switchLocalePath('id')">
          Indonesia
        </nuxt-link>
        <nuxt-link class="ma-1" :to="switchLocalePath('en')">
          English
        </nuxt-link>
      </div>
      <v-btn
        v-if="$vuetify.breakpoint.xs"
        x-large
        :loading="isLoadingLogout"
        block
        elevation="0"
        class="mt-10 primary text-capitalize"
        @click="logout"
      >
        {{ $t('profile.logout') }}
      </v-btn>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserFormItem from '~/components/UserFormItem.vue'
import ImageComponent from '~/components/ImageComponent.vue'
import { User } from '~/types/user'

export default Vue.extend({
  name: 'AccountSettingPage',

  components: { UserFormItem, ImageComponent },

  layout: 'body-account-setting',

  middleware: ['auth'],

  data: () => ({
    menu: false,
    colorDialog: false,
    dialogUpdateUser: false,
    isLoadingLogout: false,
    primaryColor: null as any,
    url: '' as string | null
  }),

  computed: {
    dataUser () {
      return this.$store.getters['profile/account-setting/data'] as User | null
    },
    isLoadingForm () {
      return this.$store.getters['users/isLoadingForm']
    },
    isLoadingFormClearImage () {
      return this.$store.getters['users/isLoadingFormClearImage']
    },

    // routeManageUsers () {
    //   const data = this.$store.getters['profile/account-setting/data'] as User | null
    //
    //   if (data?.shipment_company !== null) {
    //     return '/shipping-company/users'
    //   } else if (data?.vendor !== null) {
    //     return '/vendor/users'
    //   } else if (data?.logistics_service_provider !== null) {
    //     return '/logistic-service-provider/users'
    //   }
    //
    //   return ''
    // },
    heading (): string {
      const data = this.$store.getters['profile/account-setting/data'] as User | null

      if (data?.shipment_company !== null) {
        return 'Product Owner'
      } else if (data?.vendor !== null) {
        return 'Transporter'
      } else if (data?.logistics_service_provider !== null) {
        return 'Logistic Provider'
      }

      return 'Admin'
    },
    company (): any {
      const data = this.$store.getters['profile/account-setting/data'] as User | null

      if (data?.shipment_company !== null) {
        return data?.shipment_company
      } else if (data?.vendor !== null) {
        return data.vendor
      } else if (data?.logistics_service_provider !== null) {
        return data?.logistics_service_provider
      }

      return null
    },
    data (): User | null {
      return this.$store.getters['profile/account-setting/data'] as User | null
    },
    isLoading () {
      return this.$store.getters['profile/account-setting/isLoading']
    },
    isLoadingSaveForm () {
      return this.$store.getters['profile/isLoadingForm']
    },
    initialName () {
      const user = this.$auth.user as any
      const split = user.data.name.split(' ')
      if (split.length > 1) {
        return split[0].charAt(0) + split[1].charAt(0)
      } else {
        return split[0].charAt(0)
      }
    },
    personName () {
      const data = this.data as any
      return data?.name
    },
    personEmail () {
      const data = this.data as any
      return data?.email
    },
    personPhone () {
      const data = this.data as any
      return '+' + data?.phone_country_code + data?.phone_number
    }
  },

  mounted () {
    this.$store.dispatch('profile/account-setting/getItem')
    this.primaryColor = this.$vuetify.theme.themes.light.primary
  },

  methods: {
    onChangeFile (event : any) {
      const file = event.target.files[0]

      if (!file) {
        return
      }

      this.url = URL.createObjectURL(file)
      const primaryColor = this.primaryColor.replace('#', '').slice(0, 6)
      this.$store.dispatch('profile/saveFormLSP', {
        name: this.company.name,
        domain: this.company.domain,
        address: this.company.address ?? '',
        primaryColor,
        registerBanner: file
      }).then(() => {
        this.$store.dispatch('profile/account-setting/getItem')
        this.$router.push('/profile/account-setting')
      })
    },
    clearImageBanner () {
      const primaryColor = this.primaryColor.replace('#', '').slice(0, 6)
      this.$store.dispatch('profile/saveFormLSP', {
        name: this.company.name,
        domain: this.company.domain,
        address: this.company.address ?? '',
        primaryColor,
        registerBanner: null
      }).then(() => {
        this.$store.dispatch('profile/account-setting/getItem')
        this.$router.push('/profile/account-setting')
      })
    },
    clearImage () {
      this.$store.dispatch('users/removeAvatar', this.dataUser?.id)
        .then(() => {
          this.$store.dispatch('profile/account-setting/getItem')
        })
    },
    async updateUser (value: any) {
      let selectedId = null as string | null

      switch (this.dataUser?.role) {
        case 'LOGISTIC_SERVICE_PROVIDER':
          selectedId = this.dataUser?.logistics_service_provider_id
          break
        case 'SHIPMENT_COMPANY':
          selectedId = this.dataUser?.shipment_company_id
          break
        case 'VENDOR':
          selectedId = this.dataUser?.vendor_id
          break
        case 'DRIVER':
          selectedId = this.dataUser?.driver_id
          break
        default:
          break
      }

      const response = await this.$store.dispatch('users/editProfile', {
        value,
        role: this.dataUser?.role,
        isUpdateProfile: true,
        selectedId,
      })

      if (response) {
        this.dialogUpdateUser = false
      }
    },
    async logout () {
      this.isLoadingLogout = true
      await this.$store.dispatch('firebase/detachListener')
      await this.$auth.logout()
      this.isLoadingLogout = false
    },

    async saveThemeColor () {
      const primaryColor = this.primaryColor.replace('#', '').slice(0, 6)
      await this.$store.dispatch('profile/saveFormLSP', {
        name: this.company.name,
        domain: this.company.domain,
        address: this.company.address ?? '',
        primaryColor,
        registerBanner: this.company.registerBanner ?? ''
      })

      await this.$store.dispatch('logistic-service-provider/personalize/getPersonalize', {
        domain: this.company.domain
      })

      this.$vuetify.theme.themes.light.primary = primaryColor
    },

    resetThemeColor () {
      this.primaryColor = this.company.primary_color

      this.$vuetify.theme.themes.light.primary = this.company.primary_color

      this.colorDialog = false
    },

    previewColor () {
      this.$vuetify.theme.themes.light.primary = this.primaryColor.replace('#', '').slice(0, 6)
    },

    goToDashboard () {
      switch (this.dataUser?.role) {
        case 'LOGISTIC_SERVICE_PROVIDER':
          this.$router.replace(this.localePath('/logistic-service-provider/dashboard'))
          break
        case 'SHIPMENT_COMPANY':
          this.$router.replace(this.localePath('/shipping-company/dashboard'))
          break
        case 'VENDOR':
          this.$router.replace(this.localePath('/vendor/dashboard'))
          break
        case 'ADMIN':
          this.$router.replace(this.localePath('/admin/dashboard'))
          break
        default:
          this.$router.replace(this.localePath('/'))
          break
      }
    }
  }
})
</script>

<style lang="scss" scoped>

.custom-img-btn {
  cursor: pointer;
  transition: .28s;
}
.custom-img-btn:hover {
  opacity: .85;
}
</style>

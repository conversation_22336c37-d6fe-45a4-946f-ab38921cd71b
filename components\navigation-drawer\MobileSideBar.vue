<template>
  <div>
    <v-app-bar-nav-icon @click="navigationDrawer = true" />

    <v-navigation-drawer v-model="navigationDrawer" temporary>
      <div class="my-10 px-5 d-flex align-center">
        <v-img
          class="mr-5"
          :src="logo"
          max-width="50"
          max-height="50"
          position="center center"
        />
        <h4>
          {{ title }}
        </h4>
      </div>

      <slot name="navigation-list" />
    </v-navigation-drawer>
  </div>
</template>

<script lang="ts">
export default {
  name: 'MobileSideBar',

  props: {
    title: {
      type: String,
      required: true
    },
    logo: {
      type: String,
      required: true
    }
  },

  data: () => ({
    navigationDrawer: false
  })
}
</script>

<style lang="scss" scoped></style>

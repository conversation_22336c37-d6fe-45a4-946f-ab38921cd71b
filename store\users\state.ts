import { User } from '~/types/user'

export interface UserState {
  items: User[],
  itemLogs: User[],
  itemsPermission: any,
  totalPage: number
  totalPageLogs: number
  page: number,
  pageLogs: number
  isLoading: boolean
  isLoadingForm: boolean
  isLoadingFormClearImage: boolean
}

export const state = (): UserState => ({
  items: [],
  itemLogs: [],
  itemsPermission: [],
  totalPage: 1,
  totalPageLogs: 1,
  page: 1,
  pageLogs: 1,
  isLoading: false,
  isLoadingForm: false,
  isLoadingFormClearImage: false
})

export default state

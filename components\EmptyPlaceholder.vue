<template>
  <v-container class="text-center mt-8 mt-md-0">
    <div class="d-flex justify-center">
      <v-img
        :src="require(`~/assets/images/${hero}`)"
        :max-width="maxWidth"
      />
    </div>
    <h4 class="mt-6">
      {{ messageTitle }}
    </h4>
    <div class="d-flex justify-center">
      <p class="grey--text font-weight-medium ma-0 mt-2" style="max-width: 500px;">
        {{ messageDescription }}
      </p>
    </div>
  </v-container>
</template>

<script>
import Vue from 'vue'

export default Vue.extend({
  name: 'EmptyPlaceholder',

  props: {
    hero: {
      type: String,
      default: null
    },
    maxWidth: {
      type: Number,
      default: 400
    },
    maxHeight: {
      type: Number,
      default: 400
    },
    messageTitle: {
      type: String,
      default: null
    },
    messageDescription: {
      type: String,
      default: null
    }
  }
})
</script>

<style scoped>

</style>

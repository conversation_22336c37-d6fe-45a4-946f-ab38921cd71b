import { User } from '~/types/user'

export interface LicenseCategory {
  id: string;
  name: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface License {
  id: string;
  identity: string;
  driver_id: string;
  license_category_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
  license_category: LicenseCategory;
}

export interface HistoryLogDriver {
  id: string;
  driver_id: string;
  national_identity: null;
  type: string;
  user: User;
  created_id: string;
  deleted_at: null;
  created_at: Date;
  updated_at: Date;
}

export interface Driver {
  name: string;
  block_counts: number;
  vendor: any;
  id: string;
  vendor_id: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
  blocked_at?: Date;
  user: User;
  licenses: License[];
  driver_blocked_by: any;
  history_log_drivers: HistoryLogDriver[];
}

<template>
  <div>
    <div class="text-body mb-2">
      {{ 'Enter OTP Code' }}
    </div>
    <div class="text-caption mb-4">
      {{ 'We have sent a verification code to your email' }}
    </div>
    <div class="d-flex justify-space-between mb-4">
      <v-text-field
        v-for="(digit, index) in otpDigits"
        :key="index"
        v-model="otpDigits[index]"
        class="otp-input mr-2"
        outlined
        dense
        maxlength="1"
        @input="onInput(index)"
        @keydown="onKeyDown($event, index)"
        @paste="onPaste"
        ref="otpInput"
      ></v-text-field>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'OtpInput',

  data: () => ({
    otpDigits: ['', '', '', '', '', '']
  }),

  computed: {
    otpValue: {
      get (): string {
        return this.otpDigits.join('')
      },
      set (value: string) {
        const digits = value.split('')
        for (let i = 0; i < this.otpDigits.length; i++) {
          this.otpDigits[i] = digits[i] || ''
        }
      }
    }
  },

  watch: {
    otpValue (newValue) {
      this.$emit('input', newValue)
    }
  },

  methods: {
    onInput (index: number) {
      // Move to next input if current input is filled
      if (this.otpDigits[index] && index < this.otpDigits.length - 1) {
        const inputs = this.$refs.otpInput as HTMLInputElement[]
        inputs[index + 1].focus()
      }
    },

    onKeyDown (event: KeyboardEvent, index: number) {
      // Handle backspace
      if (event.key === 'Backspace' && !this.otpDigits[index] && index > 0) {
        const inputs = this.$refs.otpInput as HTMLInputElement[]
        this.otpDigits[index - 1] = ''
        inputs[index - 1].focus()
      }
    },

    onPaste (event: ClipboardEvent) {
      event.preventDefault()
      const pastedData = event.clipboardData?.getData('text')
      if (pastedData && /^\d+$/.test(pastedData)) {
        this.otpValue = pastedData.substring(0, this.otpDigits.length)
      }
    },

    reset () {
      this.otpDigits = ['', '', '', '', '', '']
    }
  }
})
</script>

<style scoped>
.otp-input {
  width: 48px;
  text-align: center;
}
</style>

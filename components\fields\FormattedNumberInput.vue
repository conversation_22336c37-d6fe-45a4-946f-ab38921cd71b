<template>
  <v-text-field
    :class="inputClass"
    :value="displayedValue"
    :prepend-inner-icon="prependInnerIcon"
    :append-icon="appendIcon"
    :label="textLabel"
    hide-details
    outlined
    type="text"
    @input="updateValue"
    @keypress="restrictInput"
    @click:prepend-inner="emitPrependInner"
    @click:append="emitAppend"
  >
    <template v-if="showKg" #append>
      KG
    </template>
  </v-text-field>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    value: {
      type: Number,
      default: null
    },
    inputClass: {
      type: String,
      default: null
    },
    prependInnerIcon: {
      type: String,
      default: null
    },
    textLabel: {
      type: String,
      default: null
    },
    appendIcon: {
      type: String,
      default: null
    },
    showKg: {
      type: Boolean,
      default: null
    }
  },
  computed: {
    displayedValue: {
      get (): string | null {
        if (this.value !== null) {
          return this.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        }
        return null
      },
      set (value: string | null): void {
        if (value) {
          const numericValue = value.replace(/[^0-9.]/g, '')
          this.$emit('input', parseFloat(numericValue) || 0)
        } else {
          this.$emit('input', 0)
        }
      }
    }
  },
  methods: {
    updateValue (value: string | null): void {
      if (value !== null && value !== undefined) {
        const numericValue = value.replace(/[^0-9.]/g, '')
        this.displayedValue = numericValue
      } else {
        this.displayedValue = ''
      }
    },
    restrictInput (event: KeyboardEvent): void {
      const key = String.fromCharCode(event.keyCode)
      const isValid = /^[0-9.]$/.test(key)
      if (!isValid) {
        event.preventDefault()
      }
    },
    emitPrependInner (): void {
      this.$emit('click:prepend-inner')
    },
    emitAppend (): void {
      this.$emit('click:append')
    }
  }
})
</script>

import { Order } from '~/types/product'

export interface ShipmentCompanyCreateOrderState {
  isLoading: boolean
  isLoadingForm: boolean
  isLoadingDetail: boolean
  draftIsExist: boolean
  itemDraft: Order | null
  itemPublished: Order[]
  errorMessage: Object
  newOrderId: string | null
}

export const state = () => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDetail: false,
  itemDraft: null,
  itemPublished: [],
  draftIsExist: false,
  errorMessage: null,
  newOrderId: null
})

export default state

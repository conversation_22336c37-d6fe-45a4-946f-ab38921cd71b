import { Invoice, InvoiceOrder } from '~/types/invoice'

export interface LogisticServiceProviderInvoiceShipmentState {
  isLoading: boolean,
  isLoadingForm: boolean,
  isLoadingDialog: boolean,
  isLoadingDetail: boolean,
  isLoadingVendor: boolean,
  isLoadingPublish: boolean,
  items: Invoice[],
  vendorItems: Invoice[],
  item: InvoiceOrder | null,
  totalPage: number,
  page: number,
  totalPageVendor: number,
  pageVendor: number
}

export const state = () : LogisticServiceProviderInvoiceShipmentState => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDialog: false,
  isLoadingDetail: false,
  isLoadingVendor: false,
  isLoadingPublish: false,
  items: [],
  vendorItems: [],
  item: null,
  totalPage: 1,
  page: 1,
  totalPageVendor: 1,
  pageVendor: 1
})

export default state

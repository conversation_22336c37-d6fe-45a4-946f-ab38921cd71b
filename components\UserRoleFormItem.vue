<template>
  <v-dialog
    v-model="dialog"
    persistent
    max-width="600px"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>
    <v-card class="pa-md-10 pa-5">
      <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
        <h4>{{ $t('userRoleFormItem.add') + " "}} {{ label }}</h4>
        <v-icon color="black" @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </v-card-title>
      <v-container class="pa-4">
        <v-form v-model="isValid" ref="form">
          <v-row>
            <v-col class="pa-0" cols="12">
              <image-select
                :clear-image="clearForm"
                @on-image-selected="onImageSelected"
              />
            </v-col>
            <v-col v-if="logisticsServiceProviderDomains.length > 0" class="pa-0" cols="12">
              <custom-autocomplete
                v-model="form.logisticsServiceProviderDomain"
                hint="Logistic Provider Domain"
                :items="logisticsServiceProviderDomains"
                :rules="[rulesRequired]"
              />
            </v-col>
            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="form.companyName"
                :hint="$t('userRoleFormItem.company_name')"
                :rules="[rulesRequired]"
              />
              <custom-text-field
                v-if="isHasDomainField"
                v-model="form.companyDomain"
                :hint="$t('userRoleFormItem.company_domain')"
                :rules="[rulesRequired, rulesDomain]"
              />
              <custom-text-field
                v-model="form.companyAddress"
                :hint="$t('userRoleFormItem.company_address')"
                :rules="[rulesRequired]"
              />
            </v-col>
          </v-row>
          <v-row class="mt-4 mb-8">
            <v-divider />
          </v-row>
          <v-row>
            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="form.name"
                :hint="$t('userRoleFormItem.username')"
                :rules="[rulesRequired]"
              />
            </v-col>
            <v-col class="pa-0 d-flex" cols="12">
              <v-col class="pa-0 col-md-4 col-6">
                <v-combobox
                  v-model="form.phoneCountryCode"
                  :items="itemsPhoneCountryCode"
                  outlined
                  hide-details
                  prefix="+"
                  class="mr-5"
                  :rules="[rulesRequired]"
                  @keydown="$event.target.blur()"
                  @keypress="$event.target.blur()"
                  @keyup="$event.target.blur()"
                  @change="form.phoneCountryCode = $event"
                />
              </v-col>
              <v-col class="pa-0">
                <custom-text-field
                  v-model="form.phone"
                  hint="8954 xxx xxx xx"
                  type="number"
                  :rules="[rulesRequired, rulesPhoneMaxDigit, rulesFirstZeroPhoneNumber]"
                />
              </v-col>
            </v-col>
            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="form.email"
                :hint="$t('userRoleFormItem.email_hint')"
                prepend-inner-icon="mdi-email"
                :rules="[rulesRequired, rulesEmail]"
              />
            </v-col>
            <v-col class="pa-0" cols="12">
              <custom-text-field
                v-model="form.password"
                :hint="$t('userRoleFormItem.pass_hint')"
                :type="isShowFormPassword ? 'text' : 'password'"
                prepend-inner-icon="mdi-lock"
                :rules="[rulesRequired, rulesPasswordMinLength]"
                :append-icon="isShowFormPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="isShowFormPassword = !isShowFormPassword"
              />
              <custom-text-field
                v-model="form.passwordConfirmation"
                :hint="$t('userRoleFormItem.pass_confirm_hint')"
                :type="isShowFormPassword ? 'text' : 'password'"
                prepend-inner-icon="mdi-lock"
                :rules="[rulesRequired, rulesPasswordConfirmation]"
                :append-icon="isShowFormPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="isShowFormPassword = !isShowFormPassword"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-container>
      <v-card-actions class="pa-0 mt-2">
        <v-btn
          height="52"
          color="primary"
          depressed
          x-large
          :disabled="!isValid"
          :loading="isLoadingForm"
          @click="onClickSave"
        >
          {{ $t('userRoleFormItem.save') }}
        </v-btn>
        <v-col cols="4">
          <v-btn
            height="52"
            color="primary"
            depressed
            outlined
            x-large
            @click="$emit('on-close-dialog')"
          >
            {{ $t('userRoleFormItem.cancel') }}
          </v-btn>
        </v-col>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '~/components/CustomTextField.vue'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'UserRoleFormItem',

  components: { CustomTextField },

  props: {
    label: {
      type: String,
      required: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    logisticsServiceProviderDomains: {
      type: Array,
      default: () => []
    },
    isHasDomainField: {
      type: Boolean,
      default: true
    },
    dialog: {
      type: Boolean,
      default: true
    },
    clearForm: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isValid: false,
    isShowFormPassword: false,
    itemsPhoneCountryCode: [
      '1', // USA/Canada
      '7', // Russia
      '20', // Egypt
      '27', // South Africa
      '30', // Greece
      '31', // Netherlands
      '32', // Belgium
      '33', // France
      '34', // Spain
      '36', // Hungary
      '39', // Italy
      '40', // Romania
      '41', // Switzerland
      '43', // Austria
      '44', // UK
      '45', // Denmark
      '46', // Sweden
      '47', // Norway
      '48', // Poland
      '49', // Germany
      '51', // Peru
      '52', // Mexico
      '54', // Argentina
      '55', // Brazil
      '56', // Chile
      '57', // Colombia
      '60', // Malaysia
      '61', // Australia
      '62', // Indonesia
      '63', // Philippines
      '64', // New Zealand
      '65', // Singapore
      '66', // Thailand
      '81', // Japan
      '82', // South Korea
      '84', // Vietnam
      '86', // China
      '90', // Turkey
      '91', // India
      '92', // Pakistan
      '93', // Afghanistan
      '94', // Sri Lanka
      '95', // Myanmar
      '98', // Iran
      '212', // Morocco
      '213', // Algeria
      '216', // Tunisia
      '218', // Libya
      '220', // Gambia
      '221', // Senegal
      '234', // Nigeria
      '254', // Kenya
      '261', // Madagascar
      '351', // Portugal
      '352', // Luxembourg
      '353', // Ireland
      '358', // Finland
      '359', // Bulgaria
      '370', // Lithuania
      '371', // Latvia
      '372', // Estonia
      '380', // Ukraine
      '420', // Czech Republic
      '421', // Slovakia
      '852', // Hong Kong
      '853', // Macau
      '855', // Cambodia
      '856', // Laos
      '880', // Bangladesh
      '886', // Taiwan
      '960', // Maldives
      '961', // Lebanon
      '962', // Jordan
      '966', // Saudi Arabia
      '971', // UAE
      '972', // Israel
      '974', // Qatar
      '977', // Nepal
      '998' // Uzbekistan
    ],
    form: {
      companyLogoUrl: '',
      companyLogo: null,
      companyName: '',
      companyDomain: '',
      companyAddress: '',
      name: '',
      phone: '',
      phoneCountryCode: '',
      email: '',
      password: '',
      passwordConfirmation: '',
      logisticsServiceProviderDomain: ''
    }
  }),

  watch: {
    clearForm () {
      if (this.clearForm) {
        const form = this.$refs.form as HTMLFormElement
        form.reset()
      }
    }
  },

  mounted () {
    this.$emit('on-created')
  },

  methods: {
    onClickSave () {
      this.$emit('on-click-save', this.form)
    },
    onImageSelected (image : any) {
      this.form.companyLogo = image
    },
    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    },
    rulesPhoneMaxDigit (value: string) {
      return rules.phoneMaxDigit(value)
    },
    rulesDomain (value: string) {
      return rules.domain(value)
    },
    rulesFirstZeroPhoneNumber (value: string) {
      return rules.firstZeroPhoneNumber(value)
    },
    rulesPasswordMinLength (value: string) {
      return rules.passwordMinLength(value)
    },
    rulesPasswordConfirmation (value: string) {
      return rules.passwordConfirm(value, this.form.password)
    }
  }
})
</script>

<style scoped lang="scss"> </style>

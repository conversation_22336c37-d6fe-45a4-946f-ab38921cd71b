import { MutationTree } from 'vuex'
import { ShipmentState } from './state'
// import { Track } from '~/types/shipment'
import { Vehicle } from '~/types/vehicle'

export const mutations: MutationTree<ShipmentState> = {
  SET_ITEMS (state, response: any) {
    state.dataShipment = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
    state.totalData = response.meta.total
  },

  SET_ITEMS_ACTIVE_ORDERS (state, response: any) {
    state.dataShipmentActiveOrders = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
    state.totalData = response.meta.total
  },

  ADD_ITEMS (state, response: any) {
    if (state.dataShipment) {
      state.dataShipment = [...state.dataShipment, ...response.data]
      state.totalPage = response.meta.last_page
      state.page = response.meta.current_page
      state.totalData = response.meta.total
    }
  },

  ADD_ITEMS_ACTIVE_ORDERS (state, response: any) {
    if (state.dataShipmentActiveOrders) {
      state.dataShipmentActiveOrders = [...state.dataShipmentActiveOrders, ...response.data]
      state.totalPage = response.meta.last_page
      state.page = response.meta.current_page
      state.totalData = response.meta.total
    }
  },

  SET_ITEM_PROPOSED (state, response: any) {
    state.dataShipmentProposed = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEM_SHIPMENT_VENDOR (state, response: any) {
    state.dataShipmentVendor = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEM_DISPATCH (state, response: any) {
    state.dataShipmentDispatch = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEM (state, response: any) {
    state.detailShipment = response
  },

  SET_ITEM_RITASE (state, response: any) {
    state.detailShipmentRitase = response
  },

  SET_RESPONSE_ACCEPT (state, response: any) {
    state.responseAccept = response
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_SHIPMENT_LOADMORE (state, isLoadingShipmentLoadmore) {
    state.isLoadingShipmentLoadmore = isLoadingShipmentLoadmore
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  },

  SET_IS_LOADING_SHIPMENT_VENDOR (state, isLoadingShipmentVendor) {
    state.isLoadingShipmentVendor = isLoadingShipmentVendor
  },

  SET_SHIPMENT_PROPOSED (state, response: any) {
    state.dataShipmentProposed = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_SHIPMENT_DISPATCH (state, response: any) {
    state.dataShipmentDispatch = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_TRACK_ITEM (state, trackItem: any) {
    state.trackItem = trackItem
  },

  SET_IS_LOADING_TRACK (state, isLoadingTrack) {
    state.isLoadingTrack = isLoadingTrack
  },

  SET_IS_LOADING_TRACK_FORM (state, isLoadingTrackForm) {
    state.isLoadingTrackForm = isLoadingTrackForm
  },

  SET_DIALOG_ITEM (state, dialogItem: any) {
    // const ritaseShipmentVendors = dialogItem?.shipment_vendors?.filter((e: any) => e.type === 'RITASE')
    // const ritaseShipmentVendorsStatus = dialogItem?.shipment_vendors?.filter((e: any) => e.status === 'PROPOSED')
    // const vehicles = [] as any[]
    // ritaseShipmentVendors && ritaseShipmentVendorsStatus?.forEach((e: any) => {
    //   vehicles.push(...e.vehicles)
    // })

    const item = dialogItem
    if (item) {
      const vehicles = [] as any[]

      // if (ritaseShipmentVendors.length > 0) {
      //   tracks.push(...vehicles?.map((e: any) => ({
      //     vehicle_detail: {
      //       vehicle_id: e.id,
      //       vehicle: e
      //     }
      //   })))
      // }

      // if (ritaseShipmentVendors.length <= 0) {
      vehicles.push(
        ...dialogItem?.vehicles
      )
      // }

      item.vehicles = vehicles
    }

    state.dialogItem = item
  },

  SET_DIALOG_ITEM_TRACK_VEHICLE_DETAIL_ID (state, payload: any) {
    const { vehicleDetailId, vehicleId, removeIndex } = payload

    if (state.dialogItem != null) {
      const item = state.dialogItem

      item.vehicles.forEach((vehicle: Vehicle) => {
        if (vehicle.id === vehicleId) {
          if (!vehicle.selected_vehicle_detail_id) {
            vehicle.selected_vehicle_detail_id = [vehicleDetailId]
          } else {
            const existingIndex = vehicle.selected_vehicle_detail_id.indexOf(vehicleDetailId)

            if (existingIndex !== -1) {
              vehicle.selected_vehicle_detail_id[removeIndex] = vehicleDetailId
            } else {
              vehicle.selected_vehicle_detail_id[removeIndex] = vehicleDetailId
            }
          }
        }
      })

      state.dialogItem = item
    }
  },

  SET_DIALOG_ITEM_TRACK_DRIVER_ID (state, payload: any) {
    const { driverId, trackId, removeIndex } = payload

    if (state.dialogItem != null) {
      const item = state.dialogItem

      item.vehicles.forEach((vehicle: Vehicle) => {
        if (vehicle.id === trackId) {
          if (!vehicle.selected_driver_id) {
            vehicle.selected_driver_id = [driverId]
          } else {
            const existingIndex = vehicle.selected_driver_id.indexOf(driverId)

            if (existingIndex !== -1) {
              vehicle.selected_driver_id[removeIndex] = driverId
            } else {
              vehicle.selected_driver_id[removeIndex] = driverId
            }
          }
        }
      })
      state.dialogItem = item
    }
  },

  REMOVE_DIALOG_ITEM_TRACK_DRIVER_ID (state, payload: any) {
    const { driverId, trackId, removeIndex } = payload

    if (state.dialogItem) {
      const item = state.dialogItem

      item.vehicles.forEach((vehicle: Vehicle) => {
        if (vehicle.id === trackId && vehicle.selected_driver_id) {
          if (vehicle.selected_driver_id[removeIndex] === driverId) {
            vehicle.selected_driver_id[removeIndex] = ''
          }
        }
      })

      state.dialogItem = item
    }
  },

  REMOVE_DIALOG_ITEM_TRACK_VEHICLE_ID (state, payload: any) {
    const { vehicleId, trackId, removeIndex } = payload

    if (state.dialogItem) {
      const item = state.dialogItem

      item.vehicles.forEach((vehicle: Vehicle) => {
        if (vehicle.id === trackId && vehicle.selected_vehicle_detail_id) {
          if (vehicle.selected_vehicle_detail_id[removeIndex] === vehicleId) {
            vehicle.selected_vehicle_detail_id[removeIndex] = ''
          }
        }
      })

      state.dialogItem = item
    }
  },

  SET_IS_LOADING_DIALOG (state, isLoadingDialog) {
    state.isLoadingDialog = isLoadingDialog
  },

  SET_IS_LOADING_DOWNLOAD (state, isLoadingDownload) {
    state.isLoadingDownload = isLoadingDownload
  },

  SET_ID_MANUAL_LOAD_WEIGHT (state, idManualLoadWeight) {
    state.idManualLoadWeight = idManualLoadWeight
  }
}

export default mutations

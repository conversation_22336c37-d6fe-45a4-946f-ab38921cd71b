# Dockerfile
FROM node:18.18.0

# Install Yarn
RUN curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add - \
    && echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list \
    && apt-get update \
    && apt-get install -y yarn

# Create destination directory
RUN mkdir -p /home/<USER>/app
WORKDIR /home/<USER>/app

# Copy package.json and yarn.lock first to leverage Docker cache
COPY package.json yarn.lock* ./

# Install dependencies
RUN yarn install --frozen-lockfile --network-timeout 600000

# Copy the rest of the app
COPY . .

# Build the application
RUN yarn build

# Expose the application port
EXPOSE 3000

# Set environment variables
ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000

# Start the application
CMD [ "yarn", "start" ]

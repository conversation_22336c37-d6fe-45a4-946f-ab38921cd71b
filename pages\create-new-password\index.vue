<template>
  <v-row class="ma-0 d-flex">
    <v-col class="pa-10 d-flex">
      <change-password
        @on-click-send="changePassword($event)"
      />
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import ChangePassword from '~/components/ChangePassword.vue'
import { User } from '~/types/user'

export default Vue.extend({
  name: 'ChangePasswordPage',

  components: { ChangePassword },

  computed: {
    data (): User {
      return this.$store.getters['profile/account-setting/data']
    }
  },

  mounted () {
    this.$store.dispatch('profile/account-setting/getItem')
  },

  methods: {
    async changePassword (form: any) {
      const response = await this.$store.dispatch('profile/account-setting/updatePassword', {
        name: this.data.name,
        email: this.data.email,
        password: form.password,
        password_confirmation: form.passwordConfirmation,
        password_old: form.passwordOld,
        phone_country_code: this.data.phone_country_code,
        phone_number: this.data.phone_number
      })

      if (response) {
        if (this.$auth.$state.user.data.role === 'LOGISTIC_SERVICE_PROVIDER') {
          this.$router.push('/logistic-service-provider/dashboard')
        } else if (this.$auth.$state.user.data.role === 'VENDOR') {
          this.$router.push('/vendor/dashboard')
        }
      }
    }
  }
})
</script>

<style lang="scss" scoped></style>

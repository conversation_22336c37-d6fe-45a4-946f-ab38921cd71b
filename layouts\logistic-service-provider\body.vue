<template>
  <v-app>
    <network-error-overlay v-if="isNetworkError" />

    <side-bar
      v-if="$vuetify.breakpoint.smAndUp"
      :logo="
        $auth.$state.user?.data.logistics_service_provider?.logo_url ||
          require(`~/assets/images/placeholder-company-logo.svg`)
      "
      :title="$auth.$state.user?.data.logistics_service_provider?.name"
    >
      <template #navigation-list>
        <navigation-list />
      </template>
    </side-bar>

    <head-bar v-if="$vuetify.breakpoint.smAndUp" />

    <mobile-head-bar
      v-else
      :logo="require(`~/assets/images/placeholder-company-logo.svg`)"
    >
      <template #navigation-list>
        <navigation-list />
      </template>
    </mobile-head-bar>

    <v-main class="bg-color">
      <nuxt />
      <v-snackbar v-model="snackbar" vertical right>
        <div class="text-subtitle-1 pb-2 font-weight-bold">
          Requesting browser notification
        </div>

        <p>We need to access notification for better experience.</p>

        <template #action>
          <v-btn class="mr-3" color="primary" text @click="snackbar=false">
            Close
          </v-btn>
          <v-btn color="primary" @click="requestNotification">
            Request
          </v-btn>
        </template>
      </v-snackbar>
    </v-main>
  </v-app>
</template>

<script lang="ts">
import Vue from 'vue'
import SideBar from '~/components/navigation-drawer/SideBar.vue'
import HeadBar from '~/components/head-bar/HeadBar.vue'
import MobileHeadBar from '~/components/head-bar/MobileHeadBar.vue'
import NavigationList from '~/components/logistic-service-provider/NavigationList.vue'
import { Personalize } from '~/types/user'
import { toastNetworkSuccess } from '~/utils/functions'

export default Vue.extend({
  name: 'LayoutLogisticServiceProviderBody',

  components: { SideBar, HeadBar, MobileHeadBar, NavigationList },

  data: () => ({
    snackbar: false
  }),

  computed: {
    isNetworkError (): Boolean {
      return this.$store.getters['network-error/isNetworkError']
    }
  },

  watch: {
    $route: {
      handler (currentRoute) {
        let childPath = ''

        if (currentRoute.path.includes('create-shipment')) {
          childPath = '/badge-lsp-create-shipment'
          this.$store.dispatch('firebase/setDataRealtime', childPath)
        } else if (currentRoute.path.includes('invoice-order')) {
          childPath = '/badge-lsp-invoice-shipment'
          this.$store.dispatch('firebase/setDataRealtime', childPath)
        }
      },
      immediate: true
    },

    isNetworkError () {
      if (!this.isNetworkError) {
        toastNetworkSuccess(this)
      }
    }
  },

  mounted () {
    if (
      Notification.permission === 'default' ||
      Notification.permission === 'denied'
    ) {
      this.snackbar = true
    }

    this.getPersonalize()

    const user = this.$auth.user as any
    const userData = user.data as any
    const firebasePath = `/users/${userData.id}`

    this.$store.dispatch('firebase/getDataRealtime', {
      path: firebasePath,
      appEnv: this.$config.appEnv
    })
  },

  methods: {
    getPersonalize () {
      const ctx = this
      setTimeout(function () {
        const personalize = ctx.$store.getters[
          'logistic-service-provider/personalize/data'
        ] as Personalize
        ctx.$vuetify.theme.themes.light.primary =
          personalize.primary_color ?? ctx.$vuetify.theme.themes.light.primary
      }, 100)
    },
    requestNotification () {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          this.snackbar = false
        }
      })
    }
  }
})
</script>

<style lang="scss"></style>

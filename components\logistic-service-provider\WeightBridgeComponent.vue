<template>
  <v-container fluid class="pa-5">
    <div class="pa-4 ma-4">
      <div class="d-flex justify-space-between align-center">
        <h1>
          Weight Bridge Status
        </h1>
        <v-btn v-if="selectedDeleteVehicle.length > 0" elevation-0 outlined color="primary" @click="dialogDeleteVehicle = true">
          <p class="pa-0 ma-0 subtitle-1 text-capitalize">Delete Vehicle</p>
        </v-btn>
      </div>
      <div class="d-flex mt-2">
        <!-- <v-btn
          elevation="0"
          outlined
          color="primary"
          class="text-capitalize"
          x-large
        >
          <p class="pa-0 ma-0">
            Move Vehicle
          </p>
        </v-btn> -->
        <!-- <v-btn
          elevation="0"
          outlined
          color="primary"
          class="text-capitalize ml-2"
          x-large
        >
          <p class="pa-0 ma-0">
            Delete Vehicle
          </p>
        </v-btn> -->
      </div>
    </div>
    <v-dialog
      v-model="dialogDeleteVehicle"
      max-width="540px"
      persistent
    >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex align-center justify-space-between">
        <h4>Remove Vehicle</h4>

        <v-icon color="black" @click.stop="dialogDeleteVehicle = false">
          mdi-close
        </v-icon>
      </v-container>

      <v-data-table
        :headers="tableHeaderRemoveVehicle"
        :items="selectedDeleteVehicle"
        hide-default-footer
        disable-pagination
      >
      <template #item.plateNumber="{ item }">
        {{ item?.plateNumber }}
      </template>
      <template #item.reorder="{ item }">
        {{ item?.reopenOrder }}
      </template>
      </v-data-table>


      <p class="mb-10 mt-6 body-1 text-start">
        Are you sure the vehicle will be removed from DO ?
      </p>

      <v-row class="ma-0 mx-n3 d-flex">
        <v-col class="pa-0 px-3">
          <v-btn
            block
            x-large
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="onRemoveVehicle"
          >
            Remove Vehicle
          </v-btn>
        </v-col>
        <v-col class="pa-0 px-3">
          <v-btn
            block
            outlined
            x-large
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="dialogDeleteVehicle = false"
          >
            Cancel
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>
    <v-dialog
      v-model="dialogCancelVendor"
      max-width="540px"
      persistent
    >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex align-center justify-space-between">
        <h4>Cancel Transporter</h4>

        <v-icon color="black" @click.stop="dialogCancelVendor = false">
          mdi-close
        </v-icon>
      </v-container>

      <p class="mb-10 body-1 text-center">
        Are you sure you want to cancel <span class="subtitle-1">{{ `Order ${reopenCancel + 1}` }}</span> from <span class="subtitle-1">{{ vendorCancelName }}</span> ?
      </p>

      <v-row class="ma-0 mx-n3 d-flex">
        <v-col class="pa-0 px-3">
          <v-btn
            block
            x-large
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="cancelVendor"
          >
            Yes, Cancel Transporter
          </v-btn>
        </v-col>
        <v-col class="pa-0 px-3">
          <v-btn
            block
            outlined
            x-large
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="dialogCancelVendor = false"
          >
            No, Keep Transporter
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>
    <v-dialog
      v-model="dialogChangeVehicle"
      width="780"
      persistent
    >
      <v-card class="pa-md-10 pa-5">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Change Vehicle</h4>
          <v-icon color="black" @click="dialogChangeVehicle = false">
            mdi-close
          </v-icon>
        </v-card-title>
        <div class="mt-6 d-flex justify-space-between">
          <v-autocomplete
            v-model="selectedVehicle"
            outlined
            hide-details
            :rules="[ruleRequired]"
            :loading="isLoadingVehicles"
            :label="'Vehicle Type'"
            :items="filteredVehicles"
          />
          <v-autocomplete
            v-model="selectedVehicleDetail"
            outlined
            hide-details
            class="ml-3"
            :rules="[ruleRequired]"
            :loading="isLoadingVehicleDetails"
            :label="'Plate Number'"
            :items="
              vehicleDetail
                .filter(e => e.vehicle_id === selectedVehicle)
                .map(d => {
                  return { text: d.plate_number, value: d.id }
                })
                .filter(item => item.text !== null && item.text !== '')
            "
          />
          <v-autocomplete
            v-model="selectedDriver"
            outlined
            hide-details
            class="ml-3"
            :rules="[ruleRequired]"
            :loading="isLoadingDrivers"
            :label="'Driver'"
            :items="filteredDrivers"
          />
        </div>
        <div class="d-flex my-5">
          <v-col class="pa-0 mr-5">
            <v-menu
              v-model="isShowDateMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              min-width="auto"
              offset-y
              transition="scale-transition"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="dateSelected"
                  append-icon="mdi-calendar"
                  clearable
                  hide-details
                  label="Date Accident"
                  outlined
                  readonly
                  v-bind="attrs"
                  v-on="on"
                />
              </template>

              <v-date-picker
                v-model="dateSelected"
                :min="minDate"
                :max="maxDate"
                @input="isShowDateMenu = false"
              />
            </v-menu>
          </v-col>
          <v-col class="pa-0">
            <vue-timepicker
              :value="timeSelected"
              auto-scroll
              close-on-complete
              drop-direction="up"
              fixed-dropdown-button
              input-class="vtimepicker"
              input-width="100%"
              manual-input
              style="z-index: 1000;"
              @input="timeSelected = $event"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
        </div>
        <div class="d-flex justify-space-between mt-5">
          <v-file-input
            v-model="filePhoto"
            outlined
            persistent-hint
            label="File"
            accept=".jpg, .jpeg, .pdf"
            append-icon="mdi-paperclip"
            hint="Format: .jpg, .jpeg, .pdf"
            prepend-icon=""
          />
          <v-text-field
            v-model="textNote"
            class="ml-5"
            outlined
            :label="'Note'"
          />
        </div>
        <div class="d-flex mt-4">
          <v-btn
            depressed
            class="text-capitalize"
            color="primary"
            @click="changeVehicle(selectedVehicleDetailId)"
          >
            Change
          </v-btn>
          <v-btn
            outlined
            depressed
            class="ml-5 text-capitalize"
            color="primary"
            @click="dialogChangeVehicle = false"
          >
            Cancel
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="dialogAddWB"
      width="780"
      persistent
    >
      <v-card class="pa-md-10 pa-5">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Add WB Pickup</h4>
          <v-icon color="black" @click="dialogAddWB = false">
            mdi-close
          </v-icon>
        </v-card-title>
        <div class="mt-2 d-flex justify-space-between">
          <v-text-field
            v-model="ticketNumberWBPickup"
            outlined
            :label="'Ticket Number'"
          />
        </div>
        <div class="d-flex justify-space-between">
          <v-text-field
            :value="selectedDriverName"
            label="Driver"
            outlined
            readonly
            disabled
          />
          <v-text-field
            :value="selectedVehicleDetailName"
            label="Plate Number"
            outlined
            class="ml-3"
            readonly
            disabled
          />
         
        </div>

        <div class="d-flex">
          <v-col class="pa-0 mr-3">
            <v-menu
              v-model="isShowDateMenuWBPickup"
              :close-on-content-click="false"
              :nudge-right="40"
              min-width="auto"
              offset-y
              transition="scale-transition"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="dateWBPickup"
                  append-icon="mdi-calendar"
                  clearable
                  hide-details
                  label="Out Date Pickup"
                  outlined
                  readonly
                  v-bind="attrs"
                  v-on="on"
                />
              </template>

              <v-date-picker
                v-model="dateWBPickup"
                @input="isShowDateMenuWBPickup = false"
              />
            </v-menu>
          </v-col>
          <v-col class="pa-0">
            <vue-timepicker
              :value="timeOutDateSelectedWBPickup"
              auto-scroll
              close-on-complete
              drop-direction="up"
              fixed-dropdown-button
              input-class="vtimepicker"
              input-width="100%"
              manual-input
              style="z-index: 1000;"
              @input="timeOutDateSelectedWBPickup = $event"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
        </div>

        <div class="d-flex justify-space-between mb-2 mt-4">
          <FormattedNumberInput
            v-model="quantityRefineryWBPickup"
            :show-kg="false"
            class="centered-input inputPrice"
            :text-label="'Quantity'"
          />
          <FormattedNumberInput
            v-model="ffaQualityWBPickup"
            :show-kg="false"
            class="centered-input inputPrice ml-3"
            :text-label="'FFA Quality'"
          />
          <FormattedNumberInput
            v-model="mmiQualityWBPickup"
            :show-kg="false"
            class="centered-input inputPrice ml-3"
            :text-label="'M&I Quality'"
          />
        </div>
        <div class="d-flex justify-space-between mt-5">
          <v-file-input
            v-model="filePhotoWBPickup"
            outlined
            persistent-hint
            label="File"
            accept=".jpg, .jpeg, .pdf"
            append-icon="mdi-paperclip"
            hint="Format: .jpg, .jpeg, .pdf"
            prepend-icon=""
          />
          <v-text-field
            v-model="textNoteWBPickup"
            class="ml-5"
            outlined
            :label="'Note'"
          />
        </div>
        <div class="d-flex mt-3">
          <v-btn
            depressed
            class="text-capitalize"
            color="primary"
            :disabled="buttonDisabledAddWB"
            :loading="isLoadingForm"
            @click="addWB"
          >
            Add
          </v-btn>
          <v-btn
            outlined
            depressed
            class="ml-5 text-capitalize"
            color="primary"
            @click="dialogAddWB = false"
          >
            Cancel
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="dialogInsertRefinery"
      width="780"
      persistent
    >
      <v-card class="pa-md-10 pa-5">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Fixing Manual Refinery Data</h4>
          <v-icon color="black" @click="dialogInsertRefinery = false">
            mdi-close
          </v-icon>
        </v-card-title>
        <div class="mt-2 d-flex justify-space-between">
          <v-text-field
            v-model="ticketNumber"
            outlined
            :label="'Ticket Number'"
          />
        </div>
        <div class="mt-2 d-flex justify-space-between">
          <v-text-field
            :value="selectedDriverName"
            label="Driver"
            outlined
            readonly
            disabled
          />
          <v-text-field
            :value="selectedVehicleDetailName"
            label="Plate Number"
            outlined
            class="ml-3"
            readonly
            disabled
          />
        </div>

        <div class="d-flex my-3">
          <v-col class="pa-0 mr-3">
            <v-menu
              v-model="isShowDateMenuInDateRefineryData"
              :close-on-content-click="false"
              :nudge-right="40"
              min-width="auto"
              offset-y
              transition="scale-transition"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="inDateSelected"
                  append-icon="mdi-calendar"
                  clearable
                  hide-details
                  label="In Date Refinery"
                  outlined
                  readonly
                  v-bind="attrs"
                  v-on="on"
                />
              </template>

              <v-date-picker
                v-model="inDateSelected"
                :min="minDate"
                :max="maxDate"
                @input="isShowDateMenu = false"
              />
            </v-menu>
          </v-col>
          <v-col class="pa-0">
            <vue-timepicker
              :value="timeInDateSelected"
              auto-scroll
              close-on-complete
              drop-direction="up"
              fixed-dropdown-button
              input-class="vtimepicker"
              input-width="100%"
              manual-input
              style="z-index: 1000;"
              @input="timeInDateSelected = $event"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
        </div>
        <div class="d-flex my-7">
          <v-col class="pa-0 mr-3">
            <v-menu
              v-model="isShowDateMenuOutDateRefineryData"
              :close-on-content-click="false"
              :nudge-right="40"
              min-width="auto"
              offset-y
              transition="scale-transition"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="outDateSelected"
                  append-icon="mdi-calendar"
                  clearable
                  hide-details
                  label="Out Date Refinery"
                  outlined
                  readonly
                  v-bind="attrs"
                  v-on="on"
                />
              </template>
              <v-date-picker
                v-model="outDateSelected"
                :min="minDate"
                :max="maxDate"
                @input="isShowDateMenu = false"
              />
            </v-menu>
          </v-col>
          <v-col class="pa-0">
            <vue-timepicker
              :value="timeOutDateSelected"
              auto-scroll
              close-on-complete
              drop-direction="up"
              fixed-dropdown-button
              input-class="vtimepicker"
              input-width="100%"
              manual-input
              style="z-index: 1000;"
              @input="timeOutDateSelected = $event"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
        </div>
        <div class="d-flex justify-space-between mb-5">
          <FormattedNumberInput
            v-model="quantityRefinery"
            :show-kg="false"
            class="centered-input inputPrice"
            :text-label="'Quantity in Refinery'"
          />
          <FormattedNumberInput
            v-model="ffaQuality"
            :show-kg="false"
            class="centered-input inputPrice ml-3"
            :text-label="'FFA Quality'"
          />
          <FormattedNumberInput
            v-model="mmiQuality"
            :show-kg="false"
            class="centered-input inputPrice ml-3"
            :text-label="'MMI Quality'"
          />
        </div>
        <div class="d-flex justify-space-between">
          <v-file-input
            v-model="fileInsertRefinery"
            outlined
            persistent-hint
            class="mr-3"
            label="File"
            accept=".jpg, .jpeg, .pdf"
            append-icon="mdi-paperclip"
            hint="Format: .jpg, .jpeg, .pdf"
            prepend-icon=""
          />
          <v-text-field
            v-model="textNote"
            outlined
            :rules="[ruleRequired]"
            :label="'Note'"
          />
        </div>
        <div class="d-flex mt-3">
          <v-btn
            depressed
            class="text-capitalize"
            color="primary"
            :loading="isLoadingForm"
            :disabled="buttonDisabled"
            @click="fixData"
          >
            Fix Data
          </v-btn>
          <v-btn
            outlined
            depressed
            class="ml-5 text-capitalize"
            color="primary"
            @click="dialogInsertRefinery = false"
          >
            Cancel
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <div class="pa-4 ma-4">
      <v-row
        v-for="(track, index ) in dataTracks"
        :key="index"
      >
        <v-col>
          <div class="d-flex">
            <h2>
              {{ track?.vendor?.name }}
            </h2>
            <div class="text-center">
              <v-menu
                v-model="track.menu"
                :close-on-content-click="false"
                :nudge-width="200"
                offset-x
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    elevation="0"
                    color="transparent"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-icon style="color: #0094BC">
                      mdi-information-outline
                    </v-icon>
                  </v-btn>
                </template>

                <v-card>
                  <v-list>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title>
                          <p class="subtitle-1 ma-0">
                            Status Transporter
                          </p>
                        </v-list-item-title>
                      </v-list-item-content>

                      <v-list-item-action>
                        <v-btn
                          icon
                          class="pa-0"
                          @click="track.menu = false"
                        >
                          <v-icon class="">
                            mdi-close
                          </v-icon>
                        </v-btn>
                      </v-list-item-action>
                    </v-list-item>
                  </v-list>
                  <v-divider class="my-1" />
                  <v-card-text>
                      <v-expansion-panels v-for="status in track?.data_status" :key="status.i" class="pa-1 mb-1">
                        <v-expansion-panel elevation="0">
                          <v-expansion-panel-header>
                            <div class="d-flex flex-column">
                              <div class="pa-2">
                                <p class="subtitle-2">
                                  {{ `Order ${status?.ritase_identity + 1}` }}
                                </p>
                              </div>
                              <div class="d-flex align-center">
                                <div class="mt-4">
                                  <p class="subtitle-3 text-secondary">
                                    {{ $moment(status?.updated_at).format('DD-MM-yyyy HH:mm [WIB]') }}
                                  </p>
                                </div>
                                <div class="d-flex mx-8">
                                  <v-chip v-if="status.status === 'PROPOSED'" label small class="chip-secondary font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-secondary">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'REJECT'" label small class="chip-danger font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-primary">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'DISPATCH'" label small class="chip-success font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-success">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'FINISHED'" label small class="chip-success font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-info">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'EXPIRED'" label small class="chip-danger font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-primary">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'REMOVED'" label small class="font-weight-medium">
                                    <p class="ma-0 subtitle-2">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                  <v-chip v-else-if="status.status === 'ON_PROGRESS'" label small class="chip-success font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-info">
                                      ON PROGRESS
                                    </p>
                                  </v-chip>
                                  <v-chip v-else label class="chip-success font-weight-medium">
                                    <p class="ma-0 subtitle-2 text-info">
                                      {{ status.status }}
                                    </p>
                                  </v-chip>
                                </div>
                                <div class="mr-4">
                                  <v-btn
                                    v-if="(status?.status === 'PROPOSED' || status?.status === 'REJECT' || status?.status === 'EXPIRED') && track?.data_status.filter((d) => d.tag === 'AUTO_WB').length === 0"
                                    elevation="0"
                                    color="primary"
                                    class="subtitle-3 ml-5"
                                    small
                                    @click="navigateToChangeVendor(track, status.id)"
                                  >
                                    Change Transporter
                                  </v-btn>
                                </div>
                                <div>
                                  <v-btn
                                    elevation="0"
                                    outlined
                                    class="ml-4 mr-8"
                                    v-if="(status?.status === 'PROPOSED' || status?.status === 'REJECT' || status?.status === 'EXPIRED' || status?.status === 'DISPATCH' && userRole === 'LOGISTIC_SERVICE_PROVIDER' )"
                                    style="border-color: #ff5330; border-radius: 4px"
                                    @click.stop="dialogCancelVendor = true; clickCancelVendor(status)"
                                  >
                                    <v-icon color="red">
                                      mdi-close
                                    </v-icon>
                                  </v-btn>
                                </div>
                              </div>
                            </div>
                          </v-expansion-panel-header>
                          <v-expansion-panel-content style="background-color:#F5F5F5;">
                            <div>
                              <p class="mt-2 subtitle-2">
                                Log Status
                              </p>
                              <div class="d-flex pa-2 mb-1 subtitle-3 text-secondary">
                                <p class="subtitle-2">
                                  Order <span class="mr-2 text-info">Proposed</span> at <span class="ml-2">{{ $moment(status?.created_at).format('DD-MM-yyyy HH:mm [WIB]') }}</span>
                                </p>
                              </div>
                              <div v-if="status?.user_accepted_at" class="d-flex pa-2 mb-1 subtitle-3 text-secondary">
                                <p class="subtitle-2">
                                  Order <span class="mr-2" style="color:#D77400;">Dispatch</span> at <span class="ml-2">{{ $moment(status?.user_accepted_at).format('DD-MM-yyyy HH:mm [WIB]') }}</span>
                                </p>
                              </div>
                            </div>
                            <div>
                              <div v-if="status?.expired_at" class="d-flex pa-2 mb-1 subtitle-3 text-secondary">
                                <p class="subtitle-2">
                                  Order <span class="mr-2" style="color:#EF3434;">Expired</span> at <span class="ml-2">{{ $moment(status?.expired_at).format('DD-MM-yyyy HH:mm [WIB]') }}</span>
                                </p>
                              </div>
                            </div>
                          </v-expansion-panel-content>
                        </v-expansion-panel>
                      </v-expansion-panels>
                  </v-card-text>
                </v-card>
              </v-menu>
            </div>
            <!-- <v-btn
              v-if="track?.status !== 'DISPATCH' && track?.status !== 'FINISHED'"
              elevation="0"
              outlined
              color="primary"
              class="ml-2 pa-0"
              @click="navigateToChangeVendor(track)"
            >
              <v-col class="d-flex flex-column align-center">
                <v-icon size="15">
                  mdi-arrow-right
                </v-icon>
                <v-icon size="15">
                  mdi-arrow-left
                </v-icon>
              </v-col>
            </v-btn> -->
          </div>
          <div class="pa-1 d-flex">
            <p class="text-secondary">
              Total Pickup Quantity
            </p>
            <p class="subtitle-1 ml-2">
              {{ formatNumber(dataTotalPickup(track)) }} KG
            </p>
          </div>
          <tab-component>
            <template #tab>
              <v-tabs default>
                <v-tab
                  :class="{ 'subtitle-1': true, 'text-capitalize': true, 'active': selectedTabs[index] === 'progress' }"
                  @click="updateTab(index, 'progress')"
                >
                  Process-Finished
                </v-tab>
                <v-tab
                  :class="{ 'subtitle-1': true, 'text-capitalize': true, 'active': selectedTabs[index] === 'dispatch' }"
                  @click="updateTab(index, 'dispatch')"
                >
                  Dispatch
                </v-tab>
              </v-tabs>
            </template>
          </tab-component>
          <div v-if="Object.keys(selectedTabs).length === 0" class="ma-3 mt-2">
            <v-skeleton-loader type="heading" class="ml-4" width="3100" />
          </div>
          <div v-else>
            <v-data-table
              v-if="selectedTabs[index] === 'progress'"
              :headers="tableHeaders"
              hide-default-footer
              :items="trackData(track?.tracks).items.filter(item => item.statusTruck !== 'DISPATCH' && item.statusTruck !== 'REMOVED')"
              disable-pagination
            >
              <template #item.no="{ index }">
                <p>
                  {{ index + 1 }}
                </p>
              </template>
              <template #item.geofencingInRefinery="{ item }">
                <p v-if="item?.geofencingInRefinery !== null" class="ma-1">
                  {{ $moment(item?.geofencingInRefinery).format('DD-MM-yyyy HH:mm:DD') }}
                </p>
              </template>
              <template #item.geofencingOutRefinery="{ item }">
                <p v-if="item?.geofencingOutRefinery !== null" class="ma-1">
                  {{ $moment(item?.geofencingOutRefinery).format('DD-MM-yyyy HH:mm:DD') }}
                </p>
              </template>
              <template #item.geofencingOutMill="{ item }">
                <p v-if="item?.geofencingOutMill !== null" class="ma-1">
                  {{ $moment(item?.geofencingOutMill).format('DD-MM-yyyy HH:mm:DD') }}
                </p>
              </template>
              <!-- <template #item.geofencingInMill="{ item }">
                <p v-if="item?.geofencingInMill !== null" class="ma-1">
                  {{ $moment(item?.geofencingInMill).format('DD-MM-yyyy HH:mm:DD') }}
                </p>
              </template> -->
              <template #item.phoneNumber="{ item }">
                <p class="ma-0" v-if="item?.phoneNumber">
                  {{ `+${item?.phoneCountryCode} ${item?.phoneNumber}` }}
                </p>
                <p class="ma-0" v-else>-</p>
              </template>
              <template #item.action="{ index , item }">
                <v-menu bottom transition="slide-y-transition">
                  <template #activator="{ on, attrs }">
                    <v-btn icon v-bind="attrs" v-on="on">
                      <v-icon color="black">
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'" :disabled="item?.statusTruck !== 'DISPATCH'" @click="deleteVehicle(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-delete</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Delete</v-list-item-title>
                    </v-list-item>
                    <v-list-item v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'" :disabled="item?.statusTruck !== 'PROCESS'" @click="dialogInsertRefinery = true; clickInsertWb(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-plus</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Insert Refinery</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="handleChangeVehicle(item); clickChangeVehicle(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-car</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Change Vehicle</v-list-item-title>
                    </v-list-item>
                    <v-list-item
                       v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'"
                      @click="$router.push(
                        localePath('/logistic-service-provider/order-shipment/history-order/detail-history-order/' +
                          `${$route.params.id}/playback?trackid=${item.id}`)
                      )"
                    >
                      <v-list-item-icon>
                        <v-icon>mdi-history</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Playback</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
              <template #item.vendorName="{ item }">
                <div>
                  <span v-if="item?.vendorName">
                    {{ item?.vendorName }}
                  </span>
                  <span v-else>-</span>
                </div>
              </template>
              <template #item.vehicle="{ item }">
                <div v-if="item?.driver">
                  {{ item?.vehicle }}
                </div>
              </template>
              <template #item.stopInSusLoc="{ item }">
                <div v-if="item?.weight_bridges?.stop_in_suspicious_location">
                  {{ item?.weight_bridges?.stop_in_suspicious_location }}
                </div>
              </template>
              <template #item.deleteVehicle="{ item }">
                <v-btn v-if="item?.statusTruck === 'DISPATCH'" color="primary" outlined icon @click="deleteVehicle(item)">
                  <v-icon>
                    mdi-delete
                  </v-icon>
                </v-btn>
              </template>
              <template #item.plateNumber="{ item }">
                <div v-if="item?.driver" class="my-4">
                  {{ item?.plateNumber }} <br>
                  <p v-if="item?.tag === 'AUTO_WB'" class="text-secondary caption">
                    Added by WB mill
                  </p>
                  <p v-else-if="item?.tag === 'SYSTEM_LSI'" class="text-secondary caption" />
                </div>
              </template>
              <template #item.reopenOrder="{ item }">
                <div class="my-4">
                  {{ item?.reopenOrder }} <br>
                </div>
              </template>
              <template #item.statusTruck="{ item }">
                <div v-if="item?.driver">
                  <v-chip
                    v-if="item.statusTruck === 'PROPOSED'"
                    label
                    class="chip-secondary font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-secondary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'REJECT'"
                    label
                    class="chip-danger font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-primary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'DISPATCH'"
                    label
                    class="chip-orange font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-orange">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'FINISHED'"
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-success">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'EXPIRED'"
                    label
                    class="chip-danger font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-primary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'REMOVED'"
                    label
                    smallclass="font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'ON_PROGRESS'"
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-info">
                      ON PROGRESS
                    </p>
                  </v-chip>
                  <v-chip
                    v-else
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-info">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                </div>
              </template>
              <template #item.quantityInMill="{ item }">
                <p v-if="item?.quantityInMill">
                  {{ item?.quantityInMill | toThousand }} KG
                </p>
              </template>
              <template #item.quantityInRefinery="{ item }">
                <p v-if="item?.quantityInRefinery">
                  {{ item?.quantityInRefinery | toThousand }} KG
                </p>
              </template>
              <template #item.downloadWBPickup="{ item }">
                <v-btn
                  v-if="item?.weight_bridges?.wb_mill_file_url !== null"
                  icon
                  color="primary"
                  :href="item?.weight_bridges?.wb_mill_file_url"
                  target="_blank"
                  download
                >
                  <span class="mr-2 text-capitalize" style="text-decoration: underline; color: #EF3434;">Download</span>
                  <v-icon>mdi-file-download</v-icon>
                </v-btn>
              </template>
              <template #item.downloadWBDropoff="{ item }">
                <v-btn
                  v-if="item?.weight_bridges?.wb_refinery_file_url !== null"
                  icon
                  color="primary"
                  :href="item?.weight_bridges?.wb_refinery_file_url"
                  target="_blank"
                  download
                >
                  <span class="mr-2 text-capitalize" style="text-decoration: underline; color: #EF3434;">Download</span>
                  <v-icon>mdi-file-download</v-icon>
                </v-btn>
              </template>
            </v-data-table>
            <v-data-table
              class="custom-checkbox"
              v-if="selectedTabs[index] === 'dispatch'"
              v-model="selectedDeleteVehicle"
              :headers="tableHeadersDispatch"
              hide-default-footer
              :items="trackData(track?.tracks).items.filter(item => item.statusTruck === 'DISPATCH' || item.statusTruck === 'REMOVED')"
              disable-pagination
              item-key="id"
              show-select
              :single-select="false"
              :item-class="row => row.statusTruck !== 'DISPATCH' ? 'hide-checkbox' : ''"
              color="red"
              @item-selected="onItemSelected"
              @input="onSelectionChanged"
            >
              <template #item.no="{ index }">
                <p>
                  {{ index + 1 }}
                </p>
              </template>
              <template #item.phoneNumber="{ item }">
                <p class="ma-0" v-if="item?.phoneNumber">
                  {{ `+${item?.phoneCountryCode} ${item?.phoneNumber}` }}
                </p>
                <p v-else>-</p>
              </template>
              <template #item.vendorName="{ item }">
                <div>
                  <span v-if="item?.vendor_name">
                    {{ item?.vendor_name }}
                  </span>
                  <span v-else>-</span>
                </div>
              </template>
              <template #item.vehicle="{ item }">
                <div v-if="item?.driver">
                  {{ item?.vehicle }}
                </div>
              </template>
              <template #item.action="{ index , item }">
                <v-menu v-if="item.statusTruck !== 'REMOVED'" bottom transition="slide-y-transition">
                  <template #activator="{ on, attrs }">
                    <v-btn icon v-bind="attrs" v-on="on">
                      <v-icon color="black">
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-if="userRole === 'LOGISTIC_SERVICE_PROVIDER'" @click="dialogAddWB = true; clickAddWB(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-file</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Add WB</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="deleteVehicle(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-delete</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Delete</v-list-item-title>
                    </v-list-item>
                    <v-list-item :disabled="item?.statusTruck === 'FINISHED'" @click="dialogChangeVehicle = true; handleChangeVehicle(item); clickChangeVehicle(item)">
                      <v-list-item-icon>
                        <v-icon>mdi-car</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Change Vehicle</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
              <template #item.plateNumber="{ item }">
                <div v-if="item?.driver" class="my-4">
                  {{ item?.plateNumber }} <br>
                  <p v-if="item?.tag === 'AUTO_WB'" class="text-secondary caption">
                    Added by WB mill
                  </p>
                  <p v-else-if="item?.tag === 'SYSTEM_LSI'" class="text-secondary caption" />
                </div>
              </template>
              <template #item.statusTruck="{ item }">
                <div v-if="item?.driver">
                  <v-chip
                    v-if="item.statusTruck === 'PROPOSED'"
                    label
                    class="chip-secondary font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-secondary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'REJECT'"
                    label
                    class="chip-danger font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-primary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'DISPATCH'"
                    label
                    class="chip-orange font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-orange">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'FINISHED'"
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-info">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'EXPIRED'"
                    label
                    class="chip-danger font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-primary">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'REMOVED'"
                    label
                    smallclass="font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                  <v-chip
                    v-else-if="item.statusTruck === 'ON_PROGRESS'"
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-info">
                      ON PROGRESS
                    </p>
                  </v-chip>
                  <v-chip
                    v-else
                    label
                    class="chip-success font-weight-medium"
                  >
                    <p class="ma-0 subtitle-1 text-info">
                      {{ item.statusTruck }}
                    </p>
                  </v-chip>
                </div>
              </template>
              <template #item.quantityInMill="{ item }">
                <p v-if="item?.quantityInMill">
                  {{ item?.quantityInMill | toThousand }} KG
                </p>
              </template>
              <template #item.quantityInRefinery="{ item }">
                <p v-if="item?.quantityInRefinery">
                  {{ item?.quantityInRefinery | toThousand }} KG
                </p>
              </template>
            </v-data-table>
          </div>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Driver } from '~/types/driver'
import { Shipment, Track } from '~/types/shipment'
import { Vehicle, VehicleDetail } from '~/types/vehicle'
import { formatNumber, formatDateTime, mergeDateTime, rules } from '~/utils/functions'
import FormattedNumberInput from '~/components/fields/FormattedNumberInput.vue'

export default Vue.extend({
  name: 'WeightBridgeComponent',

  components: { FormattedNumberInput },

  props: {
    shipmentDetail: {
      type: Object as () => Shipment | null,
      default: null
    },
    vehicles: {
      type: Array as () => (Vehicle | null)[],
      default: null
    },
    vehicleDetail: {
      type: Array as () => Array<VehicleDetail>,
      default: () => []
    },
    driver: {
      type: Array as () => Array<Driver>,
      default: () => []
    },
    isLoadingDrivers: {
      type: Boolean,
      default: false
    },
    isLoadingVehicles: {
      type: Boolean,
      default: false
    },
    isLoadingVehicleDetails: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    selectedTabs: {} as any,
    selectedDeleteVehicle: [] as any,
    menu: false,
    dialogChangeVehicle: false,
    dialogDeleteVehicle: false,
    dialogCancelVendor: false,
    dialogInsertRefinery: false,
    dialogAddWB: false,
    dataTracks: [] as any,
    tableHeaders: [
      { text: 'No', value: 'no', sortable: false },
      { text: 'Action', value: 'action', sortable: false },
      { text: 'Transporter', value: 'vendorName', width: 150, sortable: false },
      { text: 'Vehicle', value: 'vehicle', width: 200, sortable: false },
      { text: 'Driver', value: 'driver', width: 150, sortable: false },
      { text: 'Plate Number', value: 'plateNumber', width: 150, sortable: false },
      { text: 'Phone Number', value: 'phoneNumber', width: 150, sortable: false },
      { text: 'Order', value: 'reopenOrder', width: 150, sortable: false },
      { text: 'Status Truck', value: 'statusTruck', width: 150, sortable: false },
      { text: 'Geofencing In Mill', value: 'geofencingInMIll', width: 150, sortable: false },
      { text: 'Out Date & Time Mill', value: 'outDateTimeMill', width: 150, sortable: false },
      { text: 'Geofencing Out Mill', value: 'geofencingOutMill', width: 150, sortable: false },
      { text: 'Quantity In Mill', value: 'quantityInMill', width: 150, sortable: false },
      { text: 'FFA Quality in Mill', value: 'ffaQualityInMill', width: 150, sortable: false },
      { text: 'MMI Quality in Mill', value: 'mmiQualityInMill', width: 150, sortable: false },
      { text: 'Geofencing In Refinery', value: 'geofencingInRefinery', width: 150, sortable: false },
      { text: 'In Date & Time Refinery', value: 'inDateTimeRefinery', width: 150, sortable: false },
      { text: 'Out Date & Time Refinery', value: 'outDateTimeRefinery', width: 150, sortable: false },
      { text: 'Geofencing Out Refinery', value: 'geofencingOutRefinery', width: 150, sortable: false },
      { text: 'Quantity In Refinery', value: 'quantityInRefinery', width: 150, sortable: false },
      { text: 'FFA Quality in Refinery', value: 'ffaQualityInRefinery', width: 150, sortable: false },
      { text: 'MMI Quality in Refinery', value: 'mmiQualityInRefinery', width: 150, sortable: false },
      { text: 'Distance Trip', value: 'distanceTrip', width: 150, sortable: false },
      { text: 'Stop in Sus Loc', value: 'stopInSusLoc', width: 150, sortable: false },
      { text: 'Stop Duration', value: 'stopDuration', width: 150, sortable: false },
      { text: 'Doc WB Pickup', value: 'downloadWBPickup',  width: 150, sortable: false, align: 'center' },
      { text: 'Doc WB Dropoff', value: 'downloadWBDropoff',  width: 150, sortable: false, align: 'center' }
    ],
    tableHeadersDispatch: [
      { text: 'No', value: 'no', sortable: false },
      { text: 'Action', value: 'action', sortable: false },
      { text: 'Vehicle', value: 'vehicle', sortable: false },
      { text: 'Driver', value: 'driver', sortable: false },
      { text: 'Plate Number', value: 'plateNumber', sortable: false },
      { text: 'Phone Number', value: 'phoneNumber', sortable: false },
      { text: 'Order', value: 'reopenOrder', sortable: false },
      { text: 'Status Truck', value: 'statusTruck', sortable: false }
    ],
    tableHeaderRemoveVehicle: [
      { text: 'Plate Number', value: 'plateNumber' },
      { text: 'Driver', value: 'driver' },
      { text: 'Reorder', value: 'reorder' }
    ],
    isShowDateMenu: false,
    isShowDateMenuInDateRefineryData: false,
    isShowDateMenuOutDateRefineryData: false,
    isShowDateMenuWBPickup: false,
    minDate: null,
    maxDate: new Date().toISOString().slice(0, 10),
    selectedVehicle: null as string[] | null,
    filePhoto: [] as any[],
    dateSelected: '' as any,
    timeSelected: '' as any,
    inDateSelected: '' as any,
    outDateSelected: '' as any,
    dateWBPickup: '' as any,
    selectedVehicleDetailId: '',
    selectedVehicleDetail: '',
    selectedDriver: '',
    textNote: null as any,
    quantityRefinery: '',
    ffaQuality: '',
    mmiQuality: '',
    ticketNumber: '',
    ticketNumberWBPickup: '',
    quantityRefineryWBPickup: '',
    ffaQualityWBPickup: '',
    mmiQualityWBPickup: '',
    timeOutDateSelectedWBPickup: '' as any,
    filePhotoWBPickup: null as any,
    fileInsertRefinery: null as any,
    textNoteWBPickup: null as any,
    selectedDriverName: '',
    selectedVehicleDetailName: '',
    trackId: '',
    filterVendorId: '',
    timeInDateSelected: '' as any,
    timeOutDateSelected: '' as any,
    vendorCancelName: '' as string,
    reopenCancel: '' as string,
    shipmentId: '' as string
  }),
  computed: {
    trackArray () {
      return [this.shipmentDetail]
    },

    userRole (): string {
      return this.$auth.$state.user.data.role
    },

    filteredVehicles () {
      return this.vehicles
        .filter(vehicle => vehicle?.vendor?.id === this.filterVendorId && vehicle !== null)
        .map((vt) => {
          if (vt) {
            return {
              text: vt.name,
              value: vt.id
            }
          }
          return null
        })
        .filter(item => item !== null)
    },

    filteredDrivers () {
      return this.driver
        .filter(driver => driver?.vendor_id === this.filterVendorId)
        .map(d => ({
          text: d.name,
          value: d.id
        }))
    },

    buttonDisabled () {
      return !this.trackId ||
        !this.ticketNumber ||
        !this.selectedVehicleDetailName ||
        !this.selectedDriverName ||
        !this.inDateSelected ||
        !this.timeInDateSelected ||
        !this.mmiQuality ||
        !this.ffaQuality ||
        !this.quantityRefinery ||
        !this.textNote
    },

    buttonDisabledAddWB () {
      return !this.trackId ||
        !this.ticketNumberWBPickup ||
        !this.selectedVehicleDetailName ||
        !this.selectedDriverName ||
        !this.dateWBPickup ||
        !this.timeOutDateSelectedWBPickup ||
        !this.mmiQualityWBPickup ||
        !this.ffaQualityWBPickup ||
        !this.quantityRefineryWBPickup
    },

    idManualLoadWeight (): number {
      return this.$store.getters['shipment/idManualLoadWeight']
    },

    isLoadingForm (): boolean {
      return this.$store.getters['shipment/isLoadingForm']
    }
    
  },

  watch: {
    dialogChangeVehicle (newVal) {
      if (!newVal) {
        this.selectedVehicle = []
        this.selectedVehicleDetailId = ''
        this.selectedVehicleDetail = ''
        this.selectedDriver = ''
        this.textNote = ''
        this.filePhoto = []
      }
    }
  },

  created () {
    this.initializeSelectedTabs()
  },

  mounted () {
    this.dataTrack()
  },
  methods: {
    formatNumber,

    // eslint-disable-next-line camelcase
    clickChangeVehicle (item: any) {
      this.selectedVehicleDetailId = item.vehicleDetailId
      this.filterVendorId = item.vendorId
    },

    handleChangeVehicle(item: any) {
      this.dialogChangeVehicle = true
      this.clickChangeVehicle(item)
      this.$emit('on-change-vehicle', { dialog: true, item })
    },
    
    clickCancelVendor (status: any) {
      this.vendorCancelName = status.name
      this.reopenCancel = status.ritase_identity
      this.shipmentId = status.id
    },
    clickInsertWb (item: any) {
      this.trackId = item.id
      this.filterVendorId = item.vendorId
      this.selectedVehicleDetailName = item.plateNumber
      this.selectedDriverName = item.driver
    },
    clickAddWB (item: any) {
      this.trackId = item.id
      this.filterVendorId = item.vendorId
      this.selectedVehicleDetailName = item.plateNumber
      this.selectedDriverName = item.driver
    },
    ruleRequired (value: string) {
      return rules.required(value)
    },
    initializeSelectedTabs () {
      const defaultValue = 'progress'
      const newSelectedTabs: any = {}

      setTimeout(() => {
        this.dataTracks.forEach((_: any, index: string | number) => {
          newSelectedTabs[index] = this.selectedTabs[index] || defaultValue
        })

        this.selectedTabs = newSelectedTabs
      }, 1000)
    },
    updateTab (index: number, value: string) {
      this.$set(this.selectedTabs, index, value)
    },
    // eslint-disable-next-line camelcase
    navigateToChangeVendor (track: any, id: string) {
      // eslint-disable-next-line camelcase
      this.$emit('change-vendor', track, id)
    },
    deleteVehicle (item: any) {
      this.$emit('delete-vehicle', item)
    },

    // eslint-disable-next-line camelcase
    getExitMillDate (vehicle_detail: any) {
      // eslint-disable-next-line camelcase
      const alerts = vehicle_detail?.alerts
      // eslint-disable-next-line camelcase
      const location = vehicle_detail?.location

      const filteredAlerts = alerts
        .filter(
          (alert: { type: string; pickup_drop_off_location_point?: { identity: any } }) =>
            alert.type === 'zone_out' &&
        alert.pickup_drop_off_location_point?.identity === location
        )
        .sort(
          (a: { created_at: string }, b: { created_at: string }) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )

      return filteredAlerts.length > 0 ? filteredAlerts?.[0].alert_time : ''
    },

    geofencingInMill (item: any) {
      if (item.routes && Array.isArray(item.routes)) {
        const dropOffRoutes = item.routes.filter((route: { type: string }) => route.type === 'PICKUP')
        const locationPoints = dropOffRoutes.map((route: { pickup_drop_off_location_point_id: any }) => route.pickup_drop_off_location_point_id)

        if (item?.vehicle_detail?.alerts && Array.isArray(item.vehicle_detail.alerts)) {
          const zoneInAlerts = item.vehicle_detail.alerts.filter((alert: { type: string; pickup_dropoff_location_point_id: any }) =>
            alert.type === 'zone_in' && locationPoints.includes(alert.pickup_dropoff_location_point_id)
          )
          if (zoneInAlerts.length > 0) {
            const latestAlert = zoneInAlerts?.reduce((latest: { alert_time: string|number|Date }, current: { alert_time: string|number|Date }) =>
              new Date(latest.alert_time) > new Date(current.alert_time) ? latest : current
            )
            const alertDate = new Date(latestAlert.alert_time);
            alertDate.setHours(alertDate.getHours() + 7);
            const year = alertDate.getFullYear();
            const month = String(alertDate.getMonth() + 1).padStart(2, '0');
            const day = String(alertDate.getDate()).padStart(2, '0');
            const hours = String(alertDate.getHours()).padStart(2, '0');
            const minutes = String(alertDate.getMinutes()).padStart(2, '0');
            const seconds = String(alertDate.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          }
        }
      }
    },

    geofencingoOutMill (item: any) {
      if (item.routes && Array.isArray(item.routes)) {
        const dropOffRoutes = item.routes.filter((route: { type: string }) => route.type === 'PICKUP')
        const locationPoints = dropOffRoutes.map((route: { pickup_drop_off_location_point_id: any }) => route.pickup_drop_off_location_point_id)

        if (item?.vehicle_detail?.alerts && Array.isArray(item.vehicle_detail.alerts)) {
          const zoneInAlerts = item.vehicle_detail.alerts.filter((alert: { type: string; pickup_dropoff_location_point_id: any }) =>
            alert.type === 'zone_out' && locationPoints.includes(alert.pickup_dropoff_location_point_id)
          )

          if (zoneInAlerts.length > 0) {
            const latestAlert = zoneInAlerts?.reduce((latest: { alert_time: string|number|Date }, current: { alert_time: string|number|Date }) =>
              new Date(latest.alert_time) > new Date(current.alert_time) ? latest : current
            )
            const alertDate = new Date(latestAlert.alert_time);
            alertDate.setHours(alertDate.getHours() + 7);
            const year = alertDate.getFullYear();
            const month = String(alertDate.getMonth() + 1).padStart(2, '0');
            const day = String(alertDate.getDate()).padStart(2, '0');
            const hours = String(alertDate.getHours()).padStart(2, '0');
            const minutes = String(alertDate.getMinutes()).padStart(2, '0');
            const seconds = String(alertDate.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          }
        }
      }
    },

    geofencingInRefinery (item: any) {
      if (item.routes && Array.isArray(item.routes)) {
        const dropOffRoutes = item.routes.filter((route: { type: string }) => route.type === 'DROPOFF')
        const locationPoints = dropOffRoutes.map((route: { pickup_drop_off_location_point_id: any }) => route.pickup_drop_off_location_point_id)

        if (item?.vehicle_detail?.alerts && Array.isArray(item.vehicle_detail.alerts)) {
          const zoneInAlerts = item.vehicle_detail.alerts.filter((alert: { type: string; pickup_dropoff_location_point_id: any }) =>
            alert.type === 'zone_in' && locationPoints.includes(alert.pickup_dropoff_location_point_id)
          )

          if (zoneInAlerts.length > 0) {
            const latestAlert = zoneInAlerts?.reduce((latest: { alert_time: string|number|Date }, current: { alert_time: string|number|Date }) =>
              new Date(latest.alert_time) > new Date(current.alert_time) ? latest : current
            )
            const alertDate = new Date(latestAlert.alert_time);
            alertDate.setHours(alertDate.getHours() + 7);
            const year = alertDate.getFullYear();
            const month = String(alertDate.getMonth() + 1).padStart(2, '0');
            const day = String(alertDate.getDate()).padStart(2, '0');
            const hours = String(alertDate.getHours()).padStart(2, '0');
            const minutes = String(alertDate.getMinutes()).padStart(2, '0');
            const seconds = String(alertDate.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          }
        }
      }
    },

    geofencingOutRefinery (item: any) {
      if (item.routes && Array.isArray(item.routes)) {
        const dropOffRoutes = item.routes.filter((route: { type: string }) => route.type === 'DROPOFF')
        const locationPoints = dropOffRoutes.map((route: { pickup_drop_off_location_point_id: any }) => route.pickup_drop_off_location_point_id)

        if (item?.vehicle_detail?.alerts && Array.isArray(item.vehicle_detail.alerts)) {
          const zoneInAlerts = item.vehicle_detail.alerts.filter((alert: { type: string; pickup_dropoff_location_point_id: any }) =>
            alert.type === 'zone_out' && locationPoints.includes(alert.pickup_dropoff_location_point_id)
          )

          if (zoneInAlerts.length > 0) {
            const latestAlert = zoneInAlerts?.reduce((latest: { alert_time: string|number|Date }, current: { alert_time: string|number|Date }) =>
              new Date(latest.alert_time) > new Date(current.alert_time) ? latest : current
            )
            const alertDate = new Date(latestAlert.alert_time);
            alertDate.setHours(alertDate.getHours() + 7);
            const year = alertDate.getFullYear();
            const month = String(alertDate.getMonth() + 1).padStart(2, '0');
            const day = String(alertDate.getDate()).padStart(2, '0');
            const hours = String(alertDate.getHours()).padStart(2, '0');
            const minutes = String(alertDate.getMinutes()).padStart(2, '0');
            const seconds = String(alertDate.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          }
        }
      }
    },

    dataTrack () {
      const mergedData: any[] = []
      const seenVendorIds = new Set()
      const dataTracks: any[] = []

      const groupedVendors = this.shipmentDetail?.shipment_vendors?.reduce((acc: any, curr: any) => {
        const vendorId = curr.vendor.name;
        if (!acc[vendorId]) {
          acc[vendorId] = [];
        }
        acc[vendorId].push(curr);
        return acc;
      }, {});

      interface Vendor {
          id: any;
      }

      interface Track {
          shipment_vendor_id: any;
      }

        const result = Object.fromEntries(
          Object.entries(groupedVendors || {}).map(([key, vendorList]) => {
            const typedVendorList = vendorList as Array<Vendor>;
            const matchedTracks = this.shipmentDetail?.tracks
              ? this.shipmentDetail.tracks
                  .filter(t => typedVendorList.some(v => v.id === t.shipment_vendor_id))
                  .map(t => t)
              : [];
            dataTracks.push({
              id: typedVendorList[0]?.id,
              name: key,
              tracks: matchedTracks
            });
              
            return [key, {
              id: typedVendorList[0]?.id,
              name: key,
              tracks: matchedTracks
            }];
          })
        );

  
      this.shipmentDetail?.shipment_vendors?.forEach((shipmentVendor: { vendor_id: any, type?: string, id: string }) => {
        if (!seenVendorIds.has(shipmentVendor.vendor_id)) {
          const matchingTracks = this.shipmentDetail?.tracks?.filter(track => track?.shipment_vendor_id === shipmentVendor?.id && track?.driver_id !== null) ?? []
          const matchingVendors = this.shipmentDetail?.shipment_vendors?.filter((vendor: { vendor_id: any; }) => vendor.vendor_id === shipmentVendor.vendor_id) ?? []
          
          const dataStatus = matchingVendors
            .filter((vendor: { tag: any }) => vendor.tag !== 'AUTO_WB')
            .map((vendor: { id: string, created_at: any, status: any; updated_at: any; ritase_identity: any, expired_at: any, user_accepted_at: any, system_accepted_at: any, type: any, vendor: any }) => ({
              id: vendor.id,
              name: vendor.vendor.name,
              created_at: vendor.created_at,
              ritase_identity: vendor?.ritase_identity,
              status: vendor.status,
              expired_at: vendor.expired_at,
              user_accepted_at: vendor.user_accepted_at,
              system_accepted_at: vendor.system_accepted_at,
              updated_at: vendor.updated_at,
              type: vendor.type
            }))

          const sortedDataStatus = dataStatus.sort((a: any, b: any) => {
            const ritaseA = a.ritase_identity === null ? 1 : a.ritase_identity + 1
            const ritaseB = b.ritase_identity === null ? 1 : b.ritase_identity + 1

            return ritaseA - ritaseB
          })

          const matchingDataTrack = dataTracks.find(track => track.id === shipmentVendor.id);
          
          const mergedItem = {
            ...shipmentVendor,
            tracks: matchingDataTrack ? matchingDataTrack.tracks : matchingTracks,
            data_status: sortedDataStatus
          }

          mergedData.push(mergedItem)
          seenVendorIds.add(shipmentVendor.vendor_id)
        }
      })
      
      this.dataTracks = mergedData
    },

    // isVehicleDetailVisible (shipmentDetail: any, item: any) {
    //   const status = shipmentDetail.shipment_vendors?.find((e: any) => e?.vendor?.id === item?.shipment_vendor?.vendor_id)?.status
    //   return status !== 'PROPOSED' && status !== 'REJECT' && status !== 'EXPIRED'
    // },

    dataTotalPickup (track: any) {
      let totalQuantity = 0;
      
      if (track?.tracks && Array.isArray(track.tracks)) {
        track.tracks.forEach((trackItem: any) => {
          if (trackItem?.weight_bridge?.quantity_in_pickup) {
            totalQuantity += parseFloat(trackItem.weight_bridge.quantity_in_pickup) || 0;
          }
        });
      }
      
      return totalQuantity;
    },

    trackData (track: any) {
      if (track?.[0]?.driver_id === null || !track || track.length === 0) {
        return {
          items: []
        }
      } else if (track?.[0]?.driver_id !== null) {
        const items = track.map((item: any) => {
          const ritaseData = item?.ritase_identity === null ? 0 : item?.ritase_identity
          return {
            id: item?.id,
            vendorId: item?.vehicle_detail?.vendor_id,
            vehiclePhoto: item?.vehicle_detail?.vehicle?.photo_url,
            vehicle: item?.vehicle_detail?.vehicle?.name || item?.vehicle_detail_with_trashed?.vehicle_with_trashed?.name,
            vehicleDetailId: item.vehicle_detail?.id,
            driver: item?.driver?.user?.name,
            plateNumber: item?.vehicle_detail?.plate_number || item?.vehicle_detail_with_trashed?.plate_number,
            vendorName: item?.vendor_name,
            weight_bridges: item?.weight_bridge,
            tag: item?.tag,
            phoneCountryCode: item?.driver?.user?.phone_country_code,
            phoneNumber: item?.driver?.user?.phone_number,
            reopenOrder: item.tag === 'AUTO_WB' ? 'Auto WB' : `Order ${ritaseData + 1}`,
            statusTruck: item?.status,
            geofencingInMIll: item?.weight_bridge?.geofencing_date_in_pickup ? this.$moment(item.weight_bridge.geofencing_date_in_pickup).format('DD-MM-YYYY HH:mm:ss') : null,
            outDateTimeMill: item?.weight_bridge?.wb_date_out_pickup,
            geofencingOutMill: item?.weight_bridge?.geofencing_date_out_pickup,
            quantityInMill: item?.weight_bridge?.quantity_in_pickup,
            ffaQualityInMill: item?.weight_bridge?.ffa_quality_in_pickup,
            mmiQualityInMill: item?.weight_bridge?.['m&i_quality_in_pickup'],
            geofencingInRefinery: item?.weight_bridge?.geofencing_date_in_dropoff,
            inDateTimeRefinery: item?.weight_bridge?.wb_date_in_dropoff,
            outDateTimeRefinery: item?.weight_bridge?.wb_date_out_dropoff,
            geofencingOutRefinery: item?.weight_bridge?.geofencing_date_out_dropoff,
            quantityInRefinery: item?.weight_bridge?.quantity_in_dropoff,
            ffaQualityInRefinery: item?.weight_bridge?.ffa_quality_in_dropoff,
            mmiQualityInRefinery: item?.weight_bridge?.['m&i_quality_in_dropoff'],
            distanceTrip: item?.weight_bridge?.distance_trip,
            stopInSusLoc: item?.wight_bridge?.stop_in_suspicious_location,
            stopDuration: item?.weight_bridge?.total_stop_duration
          }
        })

        return {
          items
        }
      }
      return {
        items: []
      }
    },

    async changeVehicle () {
      let fileArray = []

      if (Array.isArray(this.filePhoto)) {
        fileArray = this.filePhoto
      } else if (this.filePhoto) {
        fileArray.push(this.filePhoto)
      }

      const date = this.dateSelected ? formatDateTime(mergeDateTime(this.dateSelected, this.timeSelected)) : ''
      const files = fileArray || []

      const res = await this.$store.dispatch('shipment/changeVehicle', {
        id: this.shipmentDetail?.id,
        old_vehicle_detail_id: this.selectedVehicleDetailId,
        new_vehicle_detail_id: this.selectedVehicleDetail,
        driver_id: this.selectedDriver,
        note: this.textNote,
        date,
        files
      })

      if (res) {
        this.dialogChangeVehicle = false
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id,
        })
      }
    },

    async fixData () {
      const itemrecords = [
        {
          donumber: this.shipmentDetail?.orders?.[0].identity,
          totaldeliver: this.quantityRefinery
        }
      ]

      const dateIntime = this.inDateSelected ? formatDateTime(mergeDateTime(this.inDateSelected, this.timeInDateSelected)) : ''
      const dateOuttime = this.outDateSelected ? formatDateTime(mergeDateTime(this.outDateSelected, this.timeOutDateSelected)) : ''

      const res = await this.$store.dispatch('shipment/manualDeliverWeight', {
        track_id: this.trackId,
        ticketnumber: this.ticketNumber,
        trucknumber: this.selectedVehicleDetailName,
        driver: this.selectedDriverName,
        intime: dateIntime,
        outtime: dateOuttime,
        water_quality: this.mmiQuality,
        ffa_quality: this.ffaQuality,
        refinerynetweight: this.quantityRefinery,
        note: this.textNote,
        itemrecords
      })

      if (res && this.fileInsertRefinery !== null) {
        await this.$store.dispatch('shipment/uploadWeightBridgeFiles', {
          weight_bridge_id: this.$store.getters['shipment/idManualLoadWeight'],
          files: this.fileInsertRefinery
        })
      }

      if (res) {
        this.dialogInsertRefinery = false
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id,
          type: 'detail'
        })
      }
    },

    async addWB () {
      const itemrecords = [
        {
          donumber: this.shipmentDetail?.orders?.[0].identity,
          deliveryweight: this.quantityRefineryWBPickup
        }
      ]

      const [year, month, day] = this.dateWBPickup.split('-')
      const [hours, minutes] = this.timeOutDateSelectedWBPickup.split(':')
      const outtime = new Date(
        Number(year),
        Number(month) - 1,
        Number(day),
        Number(hours),
        Number(minutes)
      ).toISOString()

      const quality = `${this.ffaQualityWBPickup} 0.13 ${this.mmiQualityWBPickup} 0.12`

      const res = await this.$store.dispatch('shipment/manualLoadWeight', {
        track_id: this.trackId,
        ticketnumber: this.ticketNumberWBPickup,
        trucknumber: this.selectedVehicleDetailName,
        driver: this.selectedDriverName,
        outtime,
        quality,
        note: this.textNoteWBPickup,
        itemrecords
      })

      if (res && this.filePhotoWBPickup !== null) {
        await this.$store.dispatch('shipment/uploadWeightBridgeFiles', {
          weight_bridge_id: this.$store.getters['shipment/idManualLoadWeight'],
          files: this.filePhotoWBPickup
        })
      }

      if (res) {
        this.dialogAddWB = false
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id,
          type: 'detail'
        })
      }
    },

    // getFileExtension(url: string): string {
    //     if (url.includes('.')) {
    //       return url.split('.').pop()?.toLowerCase() || 'pdf'
    //     }
    //     return 'pdf'
    //   },

    // async downloadFile(url: string, baseFilename: string) {
    //   try {
    //     const response = await fetch(url)
    //     const contentType = response.headers.get('content-type')
    //     const blob = await response.blob()
        
    //     let extension = 'pdf'
    //     if (contentType) {
    //       if (contentType.includes('image/jpeg')) extension = 'jpg'
    //       else if (contentType.includes('image/png')) extension = 'png'
    //       else if (contentType.includes('image/webp')) extension = 'webp'
    //       else if (contentType.includes('application/pdf')) extension = 'pdf'
    //     } else {
    //       extension = this.getFileExtension(url)
    //     }

    //     const filename = `${baseFilename}.${extension}`
    //     const downloadUrl = window.URL.createObjectURL(blob)
    //     const link = document.createElement('a')
    //     link.href = downloadUrl
    //     link.setAttribute('download', filename)
    //     document.body.appendChild(link)
    //     link.click()
    //     link.remove()
    //     window.URL.revokeObjectURL(downloadUrl)
    //   } catch (error) {
    //     console.error('Download failed:', error)
    //     this.$toast.error('Failed to download file')
    //   }
    // },

    async cancelVendor() {
      const response = await this.$store.dispatch('shipment/cancelVendor', {
        id: this.shipmentId
      });
      if (response) {
        this.dialogCancelVendor = false
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id
        })
      }
    },

    async onRemoveVehicle () {
      const trackIds = this.selectedDeleteVehicle.map((vehicle: { id: string }) => vehicle.id);

      const result = await this.$store.dispatch('shipment/bulkRemoveTrack', {
        track_ids: trackIds
      });

      if (result) {
        this.$store.dispatch('shipment/getItemDetail', {
          id: this.$route.params.id,
          type: 'detail'
        })
        this.dialogDeleteVehicle = false;
      }
    },
    onItemSelected(item: any) {
    if (item.item.statusTruck !== 'DISPATCH') {
      const index = this.selectedDeleteVehicle.findIndex((selected: { id: any }) => selected.id === item.item.id)
      if (index !== -1) {
        this.selectedDeleteVehicle.splice(index, 1)
      }
    }
  },

  onSelectionChanged(items: any[]) {
    this.selectedDeleteVehicle = items.filter(item => item.statusTruck === 'DISPATCH')
  }
  }
})
</script>

<style lang="scss" scoped>
.chip-success {
  background: #eaf6ec !important;
}

.chip-orange {
  background: #ffdfaa !important;
}

.chip-danger {
  background: #fde0e0 !important;
}

.chip-info {
  background: #e6f4f8 !important;
}

.hide-checkbox {
  display: none;
}

.custom-checkbox {
  ::v-deep .v-data-table__checkbox {
    .v-input--selection-controls__input {
      display: none;
    }
  }

  ::v-deep tr:not(.hide-checkbox) {
    .v-data-table__checkbox {
      .v-input--selection-controls__input {
        display: inline-flex;
      }

      // Warna merah untuk checkbox
      .v-input--selection-controls__ripple {
        &::before {
          color: red !important;
          caret-color: red !important;
          background-color: red !important;
        }
      }

      .v-icon {
        color: red !important;
        caret-color: red !important;
      }

      // Warna background saat hover
      &:hover .v-input--selection-controls__ripple::before {
        opacity: 0.04;
        background-color: red !important;
      }

      // Warna background saat focus
      &.v-input--is-focused .v-input--selection-controls__ripple::before {
        opacity: 0.12;
        background-color: red !important;
      }
    }
  }
}
 </style>

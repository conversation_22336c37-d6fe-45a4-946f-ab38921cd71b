import { MutationTree } from 'vuex'
import { VehicleTypeState } from './state'

export const mutations: MutationTree<VehicleTypeState> = {
  SET_RESULT (state, response) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEMS (state, items) {
    state.items = items
  },

  SET_TOTAL_PAGE (state, totalPage) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_DELETE (state, isLoadingDelete) {
    state.isLoadingDelete = isLoadingDelete
  }
}

export default mutations

<template>
  <v-container fluid class="pa-0">
    <v-tabs
      :key="tabKey"
      v-model="tab"
      background-color="transparent"
      class="mb-6"
      @change="onChangeTab($event)"
    >
      <slot name="tab" />
    </v-tabs>

    <v-tabs-items
      v-model="tab"
      style="background-color: transparent"
    >
      <slot name="tab-item" />
    </v-tabs-items>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'TabComponent',

  props: {
    tabKey: {
      type: String,
      default: ''
    }
  },

  data: () => ({
    tab: 0 as number,
    routeQuery: null as any
  }),

  watch: {
    '$route.query': {
      handler (currentQuery) {
        this.routeQuery = { ...currentQuery }
      }
    }
  },

  created () {
    this.routeQuery = { ...this.$route.query }

    const isEmptyQuery = Object.entries(this.routeQuery).length === 0

    if (!isEmptyQuery) {
      this.tab = parseInt(this.$route.query?.tab as string)
    }
  },

  methods: {
    onChangeTab (tab: number) {
      if (this.routeQuery?.tab !== tab.toString()) {
        this.routeQuery.tab = tab.toString()

        this.$router.replace({
          query: { ...this.routeQuery }
        })

        this.$store.dispatch('tab/changeTab', tab)
      }
    }
  }
})
</script>

<style scoped> </style>

import { ActionTree } from 'vuex'
import { LicenseCategoryState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'

export const actions: ActionTree<LicenseCategoryState, LicenseCategoryState> = {
  getItems ({ commit }) {
    commit('SET_IS_LOADING', true)
    this.$axios.get('/v1/license-category')
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

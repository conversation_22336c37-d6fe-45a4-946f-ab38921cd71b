<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="mb-10 pa-0">
      <div class="mb-sm-10 mb-5 d-flex align-center">
        <v-btn
          text
          plain
          class="text-capitalize pa-0"
          @click="navigateBack"
        >
          <v-icon
            class="mr-3 custom-icon"
          >
            mdi-chevron-left
          </v-icon>
          {{ $t('vendorHistoryOrder.back_to_invoice_to_lsp') }}
        </v-btn>
      </div>

      <detail-history-invoice-loading v-if="isLoadingDetailInvoice"/>

      <detail-history-invoice
        v-else
        :data-invoice="dataInvoice"
        :total-cost="totalAmount"
      >
        <template #download-invoice>
          <v-btn
            x-large
            outlined
            :loading="isLoadingDownloadInvoice"
            class="text-capitalize"
            style="border: 1px solid #CFCCCC;"
            @click="downloadInvoice"
          >
            <v-icon>
              mdi-file-download
            </v-icon>
            <p class="subtitle-1 ma-0">
              Download Invoice
            </p>
          </v-btn>
        </template>
      </detail-history-invoice>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailHistoryInvoiceLoading from '~/components/loading/DetailHistoryInvoiceLoading.vue'
import DetailHistoryInvoice from '~/components/DetailHistoryInvoice.vue'
import { InvoiceOrder } from '~/types/invoice'

export default Vue.extend({

  name: 'DetailHistoryInvoicePage',

  components: {
    DetailHistoryInvoiceLoading,
    DetailHistoryInvoice
  },

  layout: 'vendor/body',
  middleware: ['auth', 'is-vendor'],

  data: () => ({
    tabs: 0,
    expanded: [],
    form: {
      products: [{
        product_id: '',
        quantity: 0
      }]
    }
  }),

  computed: {
    isLoadingDetailInvoice () {
      return this.$store.getters['invoice/isLoadingDetail']
    },
    dataInvoice () {
      return this.$store.getters['invoice/detailData'].item as InvoiceOrder
    },

    additionalCosts () {
      return this.$store.getters['invoice/additional-fee/data'].items
    },

    totalAmount () {
      let amountCost = 0

      this.dataInvoice.invoice_details?.forEach((invoiceDetail) => {
        amountCost += Number(invoiceDetail.cost)
      })

      this.additionalCosts.forEach((additionalCost: any) => {
        amountCost += Number(additionalCost.cost)
      })

      return amountCost
    },

    feeVehicles () {
      const dataInvoice = this.$store.getters['invoice/detailData'].item as InvoiceOrder
      return dataInvoice.invoice_details?.reduce((acc, invoiceDetail) => acc + Number(invoiceDetail.cost), 0)
    },

    isLoadingDownloadInvoice () {
      return this.$store.getters['invoice/isLoadingDownloadInvoice']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'History Order Shipment')
  },

  mounted () {
    this.$store.dispatch('invoice/getItemDetail', {
      id: this.$route.params.id
    })

    this.getAdditionalCost({})
  },

  methods: {
    addProductList () {
      this.form.products.push({
        product_id: '',
        quantity: 0
      })
    },
    removeProductList (index: any) {
      if (this.form.products.length > 1) {
        this.form.products.splice(index, 1)
      }
    },

    onClickSave () {
      this.$emit('on-click-save', this.form)
    },

    async getAdditionalCost ({ page = 1 }) {
      await this.$store.dispatch('invoice/additional-fee/getItems', {
        filterColumns: 'invoice_id',
        filterKeys: this.$route.params.id,
        page
      })
    },
    async downloadInvoice () {
      await this.$store.dispatch('invoice/downloadInvoices', {
        id: this.$route.params.id,
        number: this.dataInvoice?.invoice?.shipment?.identity
      })
    },
    navigateBack () {
      this.$router.push({
        path: this.localePath('/vendor/order-shipment/history-order'),
        query: {
          tab: '1'
        }
      })
    }
  }
})
</script>

<style lang="scss" scoped>

.custom-icon {
  transition: 0s !important;
}

.w-custom {
  max-width: 250px;
}

.border-grey {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

.fw-36 {
  font-size: 36px !important;
}

.fw-16 {
  font-size: 16px !important;
}
</style>

import { ActionTree } from 'vuex'
import { saveAs } from 'file-saver'
import { RootState } from '../index'
import { ShipmentState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'
import { toastError, toastSuccess } from '~/utils/toasts'
import { Track } from '~/types/shipment'
import { Vehicle } from '~/types/vehicle'

export const actions: ActionTree<ShipmentState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/shipment', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          sort_column: payload?.sortColumn,
          sort_type: payload?.sortType,
          search_columns: payload?.searchColumns,
          search_key: payload?.searchKey,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          filter_date_column: payload?.filterDateColumn,
          filter_date_start: payload?.filterDateStart,
          filter_date_end: payload?.filterDateEnd,
          mill: payload?.mill
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  getItemActiveOrders ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/shipment/active-orders', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          sort_column: payload?.sortColumn,
          sort_type: payload?.sortType,
          search_columns: payload?.searchColumns,
          search_key: payload?.searchKey,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          filter_date_column: payload?.filterDateColumn,
          filter_date_start: payload?.filterDateStart,
          filter_date_end: payload?.filterDateEnd,
          mill: payload?.mill
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS_ACTIVE_ORDERS', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  getItemsV2 ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v2/shipment', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          sort_column: payload?.sortColumn,
          sort_type: payload?.sortType,
          search_columns: payload?.searchColumns,
          search_key: payload?.searchKey,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          filter_date_column: payload?.filterDateColumn,
          filter_date_start: payload?.filterDateStart,
          filter_date_end: payload?.filterDateEnd,
          mill: payload?.mill
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  loadMoreItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING_SHIPMENT_LOADMORE', true)
    this.$axios
      .get('/v1/shipment', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          sort_column: payload?.sortColumn,
          sort_type: payload?.sortType,
          search_columns: payload?.searchColumns,
          search_key: payload?.searchKey,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          filter_date_column: payload?.filterDateColumn,
          filter_date_start: payload?.filterDateStart,
          filter_date_end: payload?.filterDateEnd
        }
      })
      .then((response: any) => {
        if (response.data.success) {
          commit('ADD_ITEMS', response.data)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_SHIPMENT_LOADMORE', false)
      })
  },

  loadMoreActiveOrders ({ commit }, payload: any) {
    commit('SET_IS_LOADING_SHIPMENT_LOADMORE', true)
    this.$axios
      .get('/v1/shipment/active-orders', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          sort_column: payload?.sortColumn,
          sort_type: payload?.sortType,
          search_columns: payload?.searchColumns,
          search_key: payload?.searchKey,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys,
          filter_date_column: payload?.filterDateColumn,
          filter_date_start: payload?.filterDateStart,
          filter_date_end: payload?.filterDateEnd
        }
      })
      .then((response: any) => {
        if (response.data.success) {
          commit('ADD_ITEMS_ACTIVE_ORDERS', response.data)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_SHIPMENT_LOADMORE', false)
      })
  },

  getItemProposed ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/shipment', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys
        }
      })
      .then((response: any) => {
        commit('SET_ITEM_PROPOSED', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  getItemShipmentVendor ({ commit }, payload: any) {
    commit('SET_IS_LOADING_SHIPMENT_VENDOR', true)

    this.$axios
      .get('/v1/shipment-vendors', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys
        }
      })
      .then((response: any) => {
        commit('SET_ITEM_SHIPMENT_VENDOR', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_SHIPMENT_VENDOR', false)
      })
  },

  getItemDispatch ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/shipment', {
        params: {
          page: payload?.page,
          entries: payload?.entries,
          filter_columns: payload?.filterColumns,
          filter_keys: payload?.filterKeys
        }
      })
      .then((response: any) => {
        commit('SET_ITEM_DISPATCH', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async getItemDetail ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    try {
      const shipmentDetail = await this.$axios.get(
        payload.type === 'detail' 
          ? `/v1/shipment/${payload.id}/details`
          : payload.type === 'reorder'
            ? `/v1/shipment/${payload.id}`
            : `/v1/shipment/${payload.id}/show`
      )
      
      let combinedData;
      
      if (payload.type === 'reorder') {
        combinedData = shipmentDetail.data.data;
      } else {
        const trackDetail = await this.$axios.get(`/v1/shipment/${payload.id}/track`)
        combinedData = {
          ...shipmentDetail.data.data,
          tracks: trackDetail.data.data.tracks
        }
      }
      
      commit('SET_ITEM', combinedData)
      return true
    } catch (error: any) {
      exceptionHandler(error, this)
      return false
    } finally {
      commit('SET_IS_LOADING_DETAIL', false)
    }
  },

  async getItemDetailDashboardVendor ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    return await this.$axios
      .get(`/v1/shipment/${payload.id}`)
      .then((response: any) => {
        commit('SET_ITEM', response.data.data)

        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async getItemDetailDashboard ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    return await this.$axios
      .get(`/v1/shipment/${payload.id}`)
      .then((response: any) => {
        commit('SET_ITEM', response.data.data)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async getItemDetailRitase ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    return await this.$axios
      .get(`/v1/shipment/${payload.id}/ritase`, {
        params: {
          entries: '-1'
        }
      })
      .then((response: any) => {
        commit('SET_ITEM_RITASE', response.data.data)

        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async updateAssignShipment ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .put(`/v1/shipment/${payload.id}/assign`, {
        tracks: payload.tracks
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        dispatch('getItemDetail', { id: payload.id })
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  getDetailTrack ({ commit }, payload: any) {
    commit('SET_IS_LOADING_TRACK', true)

    this.$axios
      .get(`/v1/tracks/${payload.id}`)
      .then((response: any) => {
        commit('SET_TRACK_ITEM', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_TRACK', false)
      })
  },

  async removeTrack ({ commit }, payload: any) {
    commit('SET_IS_LOADING_TRACK_FORM', true)

    return await this.$axios
      .put(`/v1/tracks/${payload.id}/remove`)
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_TRACK_FORM', false)
      })
  },

  async bulkRemoveTrack ({ commit }, payload: { track_ids: string[] }) {
    commit('SET_IS_LOADING_TRACK_FORM', true)

    return await this.$axios
      .put(`/v1/tracks/bulk-remove`, { track_ids: payload.track_ids })
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)

        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_TRACK_FORM', false)
      })
  },

  getDialogDetailShipment ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DIALOG', true)
    this.$axios.get(`/v1/shipment/${payload.id}`).then((response: any) => {
      commit('SET_IS_LOADING_DIALOG', false)

      // commit('SET_DIALOG_ITEM', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
      commit('SET_IS_LOADING_DIALOG', false)
    })
  },

  async acceptItem ({ commit, state }, payload: any) {
    commit('SET_IS_LOADING_DIALOG', true)

    const data = {
      tracks: state.dialogItem?.vehicles.map((vehicle: Vehicle) => {
        const arr = []
        for (const index in vehicle?.selected_vehicle_detail_id ?? []) {
          arr.push({
            vehicle_id: vehicle?.id,
            vehicle_detail_id: vehicle?.selected_vehicle_detail_id?.[index],
            driver_id: vehicle.selected_driver_id?.[index]
          })
        }

        return arr
      }).flat(1),
      shipment_vendor_id: payload.id
    }
    return await this.$axios
      .put('/v1/shipment/' + payload.shipment_id + '/accept', data)
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        commit('SET_DIALOG_ITEM', null)
        commit('SET_RESPONSE_ACCEPT', response.data.data)

        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)

        return false
      }).finally(() => {
        commit('SET_IS_LOADING_DIALOG', false)
      })
  },

  async rejectItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DIALOG', true)

    return await this.$axios
      .put('/v1/shipment/' + payload.id + '/reject')
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)

        return false
      }).finally(() => {
        commit('SET_IS_LOADING_DIALOG', false)
      })
  },

  downloadDispatchNote ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DOWNLOAD', true)

    this.$axios.get(`/v1/shipment/${payload.shipmentId}/download`, {
      responseType: 'blob'
    })
      .then((response: any) => {
        const blob = new Blob([response.data], { type: 'application/pdf;charset=utf-8' })
        saveAs(blob, `dispatch-note-${payload.shipmentIdentity}.pdf`)
      })
      .catch((_: any) => {
        toastError('Failed to generate PDF', this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_DOWNLOAD', false)
      })
  },

  async updateVendor ({ commit }, payload: {
    id: string,
    vendor_id: string,
    new_vendor_id: string,
    new_vendor_weight: number,
    vehicles: any,
    note: string,
    set_expired_at: Date,
    shipment_vendor_id: string
  }) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.put(`/v1/shipment/${payload.id}/change-vendor`, { vendor_id: payload.vendor_id, new_vendor_id: payload.new_vendor_id, new_vendor_weight: payload.new_vendor_weight, vehicles: payload.vehicles, shipment_vendor_id: payload.shipment_vendor_id, note: payload.note, set_expired_at: payload.set_expired_at })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  },

  async changeVehicle ({ commit }, payload: {
    id: string,
    old_vehicle_detail_id: string,
    new_vehicle_detail_id: string,
    driver_id: string,
    date: string,
    note: string,
    files: any[]
  }) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()

    formData.append('old_vehicle_detail_id', payload.old_vehicle_detail_id)
    formData.append('new_vehicle_detail_id', payload.new_vehicle_detail_id)
    formData.append('driver_id', payload.driver_id)
    formData.append('date', payload.date)
    formData.append('note', payload.note)

    payload.files.forEach((photo, index) => {
      formData.append(`files[${index}]`, photo)
    })

    formData.append('_method', 'PUT')

    return await this.$axios.post(`/v1/shipment/${payload.id}/change-vehicles`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  },

  async manualDeliverWeight ({ commit }, payload: {
    track_id: string,
    ticketnumber: string,
    driver: string,
    trucknumber: string,
    intime: any,
    outtime: any,
    water_quality: string,
    ffa_quality: string,
    refinerynetweight: string,
    note: string,
    itemrecords: Array<{ donumber: string, totaldeliver: number }>,
  }) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()

    formData.append('track_id', payload.track_id)
    formData.append('ticketnumber', payload.ticketnumber)
    formData.append('driver', payload.driver)
    formData.append('trucknumber', payload.trucknumber)
    formData.append('intime', payload.intime)
    formData.append('outtime', payload.outtime)
    formData.append('water_quality', payload.water_quality)
    formData.append('refinerynetweight', payload.refinerynetweight)
    formData.append('ffa_quality', payload.ffa_quality)
    formData.append('note', payload.note)

    payload.itemrecords.forEach((item, index) => {
      formData.append(`itemrecords[${index}][donumber]`, item.donumber)
      formData.append(`itemrecords[${index}][totaldeliver]`, item.totaldeliver.toString())
    })

    formData.append('_method', 'POST')

    return await this.$axios.post(`/v1/weight-bridge/manual-deliver-weights/${payload.track_id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        commit('SET_ID_MANUAL_LOAD_WEIGHT', response.data.data.id)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  },

  async manualLoadWeight ({ commit }, payload: {
    track_id: string,
    ticketnumber: string,
    driver: string,
    trucknumber: string,
    outtime: any,
    quality: string,
    note: string,
    itemrecords: Array<{ donumber: string, deliveryweight: number, }>,
  }) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()

    formData.append('track_id', payload.track_id)
    formData.append('ticketnumber', payload.ticketnumber)
    formData.append('driver', payload.driver)
    formData.append('trucknumber', payload.trucknumber)
    formData.append('outtime', payload.outtime)
    formData.append('quality', payload.quality)
    formData.append('note', payload.note)

    payload.itemrecords.forEach((item, index) => {
      formData.append(`itemrecords[${index}][donumber]`, item.donumber)
      formData.append(`itemrecords[${index}][deliveryweight]`, item.deliveryweight.toString())
    })

    formData.append('_method', 'POST')

    return await this.$axios.post(`/v1/weight-bridge/manual-load-weights/${payload.track_id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        commit('SET_ID_MANUAL_LOAD_WEIGHT', response.data.data.id)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  },

  async cancelVendor ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)
    return await this.$axios
      .delete('/v1/shipment-vendors/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async getMarkerList ({ commit }, payload: { id: string }) {
    commit('SET_IS_LOADING_DIALOG', true)

    return await this.$axios
      .get(`/v1/shipment/${payload.id}/list-marker`)
      .then((response: any) => {
        commit('SET_IS_LOADING_DIALOG', false)
        return response.data.data
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_IS_LOADING_DIALOG', false)
        return null
      })
  },

  async exportMarkerList ({ commit }, payload: { id: string }) {
    commit('SET_IS_LOADING_DIALOG', true)

    return await this.$axios
      .get(`/v1/shipment/${payload.id}/export-list-marker`, {
        responseType: 'blob'
      })
      .then((response: any) => {
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        saveAs(blob, `marker-list-${payload.id}.xlsx`)
        commit('SET_IS_LOADING_DIALOG', false)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_IS_LOADING_DIALOG', false)
        return false
      })
  },

  async uploadWeightBridgeFiles ({ commit }, payload: {
    weight_bridge_id: number,
    files: File[]
  }) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()

    const files = Array.isArray(payload.files) ? payload.files : [payload.files].filter(Boolean)
    
    files.forEach((file, index) => {
      formData.append(`upload_file`, file)
    })

    return await this.$axios.post(`/v1/weight-bridge/manual-upload-file/${payload.weight_bridge_id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }
}

export default actions

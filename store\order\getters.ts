import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { OrderState } from './state'

export const getters: GetterTree<OrderState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  calculationOrder (state) {
    const checkedOrders = state.items.filter(order => order.is_selected)

    let totalVolume = 0
    let totalWeight = 0
    let totalLength = 0
    let totalWidth = 0
    let totalHeight = 0

    if (checkedOrders.length > 0) {
      checkedOrders?.forEach((order) => {
        totalLength += order.total_dimension_length!
        totalWidth += order.total_dimension_width!
        totalHeight += order.total_dimension_height!
        totalVolume += order.total_volume!
        totalWeight += order.total_weight!
      })
    }

    return {
      totalLength,
      totalWidth,
      totalHeight,
      totalVolume,
      totalWeight
    }
  },

  selectedOrder (state) {
    return state.selectedOrder
  },

  checkedOrders (state) {
    return state.items.filter(order => order.is_selected)
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  }
}

export default getters

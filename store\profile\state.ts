import { LogisticsServiceProvider, Vendor } from '~/types/user'

export interface ProfileLogisticShipmentCompanyState {
  logisticServiceProvider: LogisticsServiceProvider | null,
  vendor: Vendor | null,
  isLoading: boolean,
  isLoadingForm: boolean
}

export const state = () : ProfileLogisticShipmentCompanyState => ({
  logisticServiceProvider: null,
  vendor: null,
  isLoading: false,
  isLoadingForm: false
})

export default state

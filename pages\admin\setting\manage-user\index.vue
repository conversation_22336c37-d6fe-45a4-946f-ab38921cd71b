<template>
  <v-container fluid class="pa-0 px-5 px-sm-10 mb-10">
    <button-back title="Back" class="mb-6" />

    <v-container fluid class="pa-10 white rounded">
      <h4 class="mb-2">
        Manage User
      </h4>
      <p class="mb-10 body-1 text-secondary">
        Managemeng of user LSI
      </p>

      <div class="my-n4">
        <v-col class="pa-0 col-3">
          <v-text-field
            v-model="form.value"
            outlined
            :label="$t('Disable User in')"
            hide-details
            class="mr-6"
          />
        </v-col>
        <p class="body-1 text-secondary mt-2">
          User will disable <br> automatically if inactive
        </p>
      </div>

      <div class="mt-10">
        <v-btn
          x-large
          depressed
          color="primary"
          class="mr-6 subtitle-1 text-capitalize"
          @click="setDisable"
        >
          Save
        </v-btn>

        <v-btn
          x-large
          outlined
          color="primary"
          class="subtitle-1 text-capitalize"
          @click="$router.back()"
        >
          Cancel
        </v-btn>
      </div>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'SettingPage',

  components: {
  },

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  data: () => ({
    form: {
      value: ''
    }
  }),

  computed: {

  },

  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'Manage System'
    )
  },

  methods: {
    setDisable () {
      this.$store.dispatch('users/disableUser', {
        value: this.form.value
      })
    }
  }
})
</script>

<style scoped lang="scss"> </style>

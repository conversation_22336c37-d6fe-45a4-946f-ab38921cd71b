import { GetterTree } from 'vuex'
import { FirebaseState } from '~/store/firebase/state'

export const getters: GetterTree<FirebaseState, FirebaseState> = {
  dataNotification (state) {
    return state.dataNotification
  },

  dataLsp (state) {
    return state.dataLsp
  },

  dataSc (state) {
    return state.dataSc
  },

  dataVendor (state) {
    return state.dataVendor
  },

  messagingToken (state) {
    return state.messagingToken
  }
}

export default getters

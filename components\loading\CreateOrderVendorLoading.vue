<template>
  <v-row v-if="loaderType === 'card_shipment'">
    <v-col v-for="i in 3" :key="i">
      <v-sheet class="pa-6 v-sheet--outlined rounded">
        <v-skeleton-loader type="heading" />
        <v-skeleton-loader type="divider" class="my-6" />
        <v-skeleton-loader type="sentences" />
        <div class="d-flex mt-6">
          <v-skeleton-loader v-for="j in 2" :key="j" type="button" class="mr-6" />
        </div>
      </v-sheet>
    </v-col>
  </v-row>

  <v-row v-else-if="loaderType === 'detail_shipment'" class="ma-n5">
    <v-col class="pa-5 col-md-5 col-12">
      <v-sheet class="rounded pa-10 mb-10">
        <v-skeleton-loader type="text" max-width="50%" />
        <v-skeleton-loader type="heading" class="my-6" />

        <v-sheet class="v-sheet--outlined rounded pa-5 d-flex justify-start">
          <div>
            <v-skeleton-loader type="avatar" class="mr-5" />
          </div>

          <v-container class="pa-0">
            <v-skeleton-loader type="heading" />
            <v-skeleton-loader type="text" class="my-2" />
            <v-skeleton-loader type="chip" />
          </v-container>
        </v-sheet>
      </v-sheet>

      <v-skeleton-loader type="image" />
    </v-col>

    <v-col class="pa-5">
      <v-sheet class="rounded pa-10">
        <v-skeleton-loader type="heading" class="mb-10" />
        <v-skeleton-loader type="text" width="50%" class="mb-5" />
        <v-skeleton-loader type="image" class="ml-10" />
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'CreateOrderLoading',

  props: {
    loaderType: {
      type: String,
      default: ''
    }
  }
})
</script>

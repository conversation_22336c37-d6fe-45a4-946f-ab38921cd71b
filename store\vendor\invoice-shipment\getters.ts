import { GetterTree } from 'vuex'
import { RootState } from '~/store'
import { InvoiceShipmentState } from '~/store/vendor/invoice-shipment/state'

export const getters: GetterTree<InvoiceShipmentState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  vendorData (state) {
    return {
      vendorItems: state.vendorItems,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  detailData (state) {
    return {
      item: state.item
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingDialog (state) {
    return state.isLoadingDialog
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  },

  isLoadingPublish (state) {
    return state.isLoadingPublish
  }
}

export default getters

import { MutationTree } from 'vuex'
import { ShipmentCompanyState } from './state'
import { ShippingCompany } from '~/types/user'

export const mutations: MutationTree<ShipmentCompanyState> = {
  SET_ITEMS (state, items: Array<ShippingCompany>) {
    state.items = items
  },

  SET_ITEM (state, item: ShippingCompany) {
    state.item = item
  },

  SET_TOTAL_PAGE (state, totalPage: any) {
    state.totalPage = totalPage
  },

  SET_PAGE (state, page: any) {
    state.page = page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  }
}

export default mutations

import { ActionTree, GetterTree, MutationTree } from 'vuex'

// eslint-disable-next-line no-use-before-define
export type InitialState = ReturnType<typeof state>

export const state = () => ({
  title: '',
  dataTableMode: 0,
  cardMode: 0,
  data: 0
})

export const getters: GetterTree<InitialState, InitialState> = {
  title: state => state.title,
  dataTableMode: state => state.dataTableMode,
  entriesMode: (state) => {
    if (+state.dataTableMode === 1) {
      return 9
    } else {
      return 3
    }
  }
}

export const mutations: MutationTree<InitialState> = {
  SET_TITLE (state, title) {
    state.title = title
  },

  SET_DATATABLE_MODE (state, dataTableMode) {
    state.dataTableMode = dataTableMode
  }
}

export const actions: ActionTree<InitialState, InitialState> = {
  setDataTableMode ({ commit }, payload: any) {
    localStorage.setItem('dataTableMode', payload.dataTableMode ?? 0)

    commit('SET_DATATABLE_MODE', payload.dataTableMode)
  },

  getDataTableMode ({ commit }) {
    if (process.server) {
      return
    }

    const dataTableMode = localStorage.getItem('dataTableMode') ?? 0

    commit('SET_DATATABLE_MODE', dataTableMode)
  }
}

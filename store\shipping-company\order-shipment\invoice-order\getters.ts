import { GetterTree } from 'vuex'
import { RootState } from '../../../index'
import { ShipmentCompanyInvoiceOrderState } from './state'

export const getters: GetterTree<ShipmentCompanyInvoiceOrderState, RootState> =
  {
    data (state) {
      return {
        items: state.items,
        totalPage: state.totalPage,
        page: state.page
      }
    },

    detailData (state) {
      return state.item
    },

    isLoading (state) {
      return state.isLoading
    }
  }

export default getters

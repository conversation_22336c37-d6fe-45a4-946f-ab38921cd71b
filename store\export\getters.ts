import { GetterTree } from 'vuex'
import { ExportState } from './state'

export const getters: GetterTree<ExportState, ExportState> = {
  data (state) {
    return {
        itemExportResult: state.itemExportResult,
        page: state.page,
        totalPage: state.totalPage,
      }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingDelete (state) {
    return state.isLoadingDelete
  }
}

export default getters

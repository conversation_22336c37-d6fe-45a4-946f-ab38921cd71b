import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { ShipmentCompanyProductState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<ShipmentCompanyProductState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/product', {
        params: {
          page: payload.page ? payload.page : 1,
          search_columns: payload.searchColumns ? payload.searchColumns : 'name',
          search_key: payload.searchKey ? payload.searchKey : '',
          sort_column: payload.sortColumn ? payload.sortColumn : 'name',
          sort_type: payload.sortType ? payload.sortType : 'asc',
          filter_columns: payload.filterColumns ? payload.filterColumns : 'shipment_company_id',
          filter_keys: payload.filterKeys,
          entries: payload.entries ? payload.entries : 9
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
        commit('SET_TOTAL_PAGE', response.data.meta.last_page)
        commit('SET_PAGE', response.data.meta.current_page)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ state, commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('identity', payload.values?.identity)
    formData.append('name', payload.values?.name)
    formData.append('product_type', payload.values?.productType)
    formData.append('weight', payload.values?.weight)
    formData.append('volume', payload.values?.volume)
    formData.append('dimension_length', payload.values?.length)
    formData.append('dimension_width', payload.values?.width)
    formData.append('dimension_height', payload.values?.height)
    formData.append('unit', payload.values?.unit)
    formData.append('unit_type', payload.values?.unitType)
    formData.append('shipment_company_id', payload.idSc)
    formData.append('_method', 'POST')

    if (payload.values?.photo) {
      formData.append('photo', payload.values?.photo)
    }

    return await this.$axios
      .post('/v1/product', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async updateItem ({ state, commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('identity', payload.values?.identity)
    formData.append('photo_url', payload.values?.photoUrl)
    formData.append('name', payload.values?.name)
    formData.append('product_type', payload.values?.productType)
    formData.append('weight', payload.values?.weight)
    if (payload.values?.volume) {
      formData.append('volume', payload.values.volume)
    }
    if (payload.values?.length) {
      formData.append('dimension_length', payload.values.length)
    }
    if (payload.values?.width) {
      formData.append('dimension_width', payload.values.width)
    }
    if (payload.values?.height) {
      formData.append('dimension_height', payload.values.height)
    }
    formData.append('unit', payload.values?.unit)
    formData.append('unit_type', payload.values?.unitType)
    formData.append('shipment_company_id', payload?.idSc)
    formData.append('_method', 'PUT')

    if (payload.values?.photo && typeof payload.values?.photo !== 'string') {
      formData.append('photo', payload.values?.photo)
    }

    return await this.$axios
      .post('/v1/product/' + payload.values?.id, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ state, commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .delete('/v1/product/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  getItemsProductKey ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/product/import')
      .then((response: any) => {
        commit('SET_ITEMS_PRODUCT_KEY', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async importProducts ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('/v1/product/import', {
      products: payload.products,
      shipment_company_id: payload.shipmentCompanyId
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  }
}

export default actions

<template>
  <v-dialog
    v-model="dialog"
    max-width="540px"
    persistent
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-container fluid class="pa-0 mb-10 d-flex align-center justify-space-between">
        <h4>{{ isBlocked ? 'Activate' : 'Block' }} Driver</h4>

        <v-icon color="black" @click="$emit('on-click-close')">
          mdi-close
        </v-icon>
      </v-container>

      <p class="mb-10 body-1 text-center">
        Are you sure want to {{ isBlocked ? 'activate' : 'block' }} driver
        <span class="subtitle-1">
          {{ driverName }}
        </span>
        ?
      </p>

      <v-row class="ma-0 mx-n3 d-flex">
        <v-col class="pa-0 px-3" :class="isBlocked ? 'order-1' : 'order-0'">
          <v-btn
            block
            x-large
            color="primary"
            :depressed="!isBlocked"
            :outlined="isBlocked"
            class="subtitle-1 text-capitalize"
            @click="$emit('on-click-close')"
          >
            Cancel
          </v-btn>
        </v-col>
        <v-col class="pa-0 px-3" :class="isBlocked ? 'order-0' : 'order-1'">
          <v-btn
            block
            x-large
            color="primary"
            :loading="isLoading"
            :depressed="isBlocked"
            :outlined="!isBlocked"
            class="subtitle-1 text-capitalize"
            @click="isBlocked ? $emit('on-click-activate') : $emit('on-click-block')"
          >
            {{ isBlocked ? 'Activate' : 'Block' }}
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'BlockUnblockDriverDialog',

  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    driverName: {
      type: String,
      default: ''
    },
    dialog: {
      type: Boolean,
      default: false
    },
    isBlocked: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped lang="scss"> </style>

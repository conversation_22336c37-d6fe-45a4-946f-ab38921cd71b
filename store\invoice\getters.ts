import { GetterTree } from 'vuex'
import { InvoiceState } from './state'

export const getters: GetterTree<InvoiceState, InvoiceState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  detailData (state) {
    return {
      item: state.item
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingDialog (state) {
    return state.isLoadingDialog
  },

  isLoadingDetail (state) {
    return state.isLoadingDetail
  },

  isLoadingDetailForm (state) {
    return state.isLoadingDetailForm
  },

  isLoadingDownloadInvoice (state) {
    return state.isLoadingDownloadInvoice
  },

  blobInvoice (state) {
    return state.blobInvoice
  }
}

export default getters

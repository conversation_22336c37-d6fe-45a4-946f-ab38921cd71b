import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleFeatureState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<VehicleFeatureState, RootState> = {
  getItems ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/vehicle-features', {
        params: {
          entries: '-1'
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  createItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios
      .post('/v1/vehicle-features', {
        name: payload.name
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        commit('SET_SELECTED_FEATURES', {
          text: response.data.name,
          value: response.data.id
        })

        dispatch('getItems')
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  selectFeature ({ commit }, payload: any) {
    commit('SET_SELECTED_FEATURES', { text: payload.name, value: payload.id })
  }
}

export default actions

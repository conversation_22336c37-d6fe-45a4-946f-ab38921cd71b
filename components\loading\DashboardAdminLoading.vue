<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-row>
      <v-col class="col-12 col-md-7">
        <v-sheet class="mb-3 pa-7">
          <v-skeleton-loader type="heading" />
          <v-row class="pt-10">
            <v-col
              v-for="i in 3"
              :key="i"
              class="col-12 col-md-6 py-0 px-3"
            >
              <v-skeleton-loader type="heading" />
              <v-sheet height="125" class="pt-4 overflow-hidden">
                <div class="d-flex">
                  <div class="d-flex flex-row align-center" style="width: 100%">
                    <v-skeleton-loader type="image" height="75" width="100" />
                    <div class="d-flex flex-column" style="width: 100%">
                      <v-skeleton-loader type="list-item-two-line" width="100%" />
                    </div>
                  </div>
                </div>
              </v-sheet>
            </v-col>
          </v-row>
        </v-sheet>
      </v-col>
      <v-col class="col-12 col-md-5 rounded">
        <v-sheet class="pa-10" elevation="0" height="395">
          <div v-for="i in 2" :key="i">
            <v-skeleton-loader type="image" height="155" style="border-radius: 0 !important;" />
          </div>
        </v-sheet>
      </v-col>
      <v-col class="col-12">
        <v-sheet class="pa-10" min-height="220">
          <v-skeleton-loader type="heading" class="mb-10" />
          <v-row>
            <v-col
              v-for="i in 3"
              :key="i"
              class="pa-5 col-lg-4 col-sm-6 col-12"
            >
              <v-sheet height="70" class="overflow-hidden">
                <div class="d-flex">
                  <div class="d-flex flex-row align-center" style="width: 100%">
                    <v-skeleton-loader type="image" height="50" width="50" />
                    <div class="d-flex flex-column" style="width: 100%">
                      <v-skeleton-loader type="list-item-two-line" width="100%" />
                    </div>
                  </div>
                </div>
              </v-sheet>
            </v-col>
          </v-row>
        </v-sheet>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'DashboardAdminLoading'
})
</script>

<template>
  <LineChart
    ref="chartInstance"
    :chart-data="chartData"
    :chart-options="chartOptions"
    :plugins="[verticalLine]"
    style="height: 100%"
  />
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'PlayBack<PERSON><PERSON>',

  props: {
    modelValue: {
      type: Number,
      default: 0
    },
    labels: {
      type: Array,
      default: () => []
    },
    datasets: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      chartInstance: null as null | any,
      chartOptions: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        scales: {
          x: {
            ticks: {
              font: { size: 14 },
              color: '#667085'
            }
          },
          y: {
            min: 0,
            ticks: {
              font: { size: 14 },
              color: '#667085'
            }
          }
        },
        plugins: {
          legend: {
            align: 'end',
            labels: {
              font: { size: 14 },
              color: '#667085',
              boxHeight: 8,
              usePointStyle: true
            }
          },
          zoom: {
            zoom: {
              wheel: {
                enabled: true
              },
              pinch: {
                enabled: true
              },
              mode: 'x'
            }
          }
        },
        onClick: (event: any, elements: any) => {
          if (elements.length === 0) {
            return
          }

          const index = elements[0].index
          this.$emit('update:model-value', index)
        }
      },
      verticalLine: {
        id: 'verticalLine',
        afterDatasetsDraw: (chart: any) => {
          const { ctx, tooltip, chartArea: { top, bottom } } = chart
          const drawLine = (x: number, color: string) => {
            ctx.save()
            ctx.beginPath()
            ctx.setLineDash([3, 3])
            ctx.moveTo(x, top)
            ctx.lineTo(x, bottom)
            ctx.lineWidth = 1.5
            ctx.strokeStyle = color
            ctx.stroke()
            ctx.restore()
          }

          const drawPlaybackLine = () => {
            const x = chart.scales.x.getPixelForValue(this.modelValue)
            drawLine(x, '#EF3434')
          }

          const drawHoverLine = () => {
            if (tooltip._active && tooltip._active.length) {
              const hoverPoint = tooltip._active[0]
              const x = hoverPoint.element.x
              drawLine(x, '#777')
            }
          }

          drawPlaybackLine()
          drawHoverLine()
        }
      }
    }
  },

  computed: {
    chartData () {
      return {
        labels: this.labels,
        datasets: this.datasets.map((dataset: any) => ({
          label: dataset.label,
          borderWidth: 2,
          borderColor: '#EF3434',
          backgroundColor: '#EF343425',
          pointStyle: false,
          fill: true,
          data: [...dataset.data]
        }))
      }
    }
  },

  watch: {
    modelValue () {
      if (this.chartInstance && this.chartInstance.chart) {
        this.chartInstance.chart.update('none')
      }
    }
  },

  mounted () {
    this.chartInstance = this.$refs.chartInstance
    if (this.chartInstance && this.chartInstance.chart) {
      this.chartInstance.chart.update()
    }
  },

  methods: {
    //
  }
})
</script>

<style scoped>
.custom-dropdown .v-list {
  padding-bottom: 0
}
</style>

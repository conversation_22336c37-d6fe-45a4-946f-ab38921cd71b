<template>
  <lottie
    :loop="loop"
    :auto-play="autoPlay"
    :animation-data="animationData"
    :style="`width:${width}px; height: ${height}px`"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import lottie from 'vue-lottie-ts/packages/lottie.vue'

export default Vue.extend({
  name: 'SuccessAnimation',

  components: { lottie },

  props: {
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    },
    loop: {
      type: Boolean,
      default: false
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    animationData: {
      type: Object,
      default: null
    }
  }
})
</script>

<style scoped>

</style>

import { InvoiceOrder, Invoice } from '~/types/invoice'

export interface InvoiceState {
  isLoading: boolean,
  isLoadingForm: boolean,
  isLoadingDialog: boolean,
  isLoadingDetail: boolean,
  isLoadingDetailForm: boolean,
  isLoadingDownloadInvoice: boolean,
  blobInvoice: string | null,
  items: Invoice[],
  item: InvoiceOrder | null,
  totalPage: number,
  page: number
}

export const state = () : InvoiceState => ({
  isLoading: false,
  isLoadingForm: false,
  isLoadingDialog: false,
  isLoadingDetail: true,
  isLoadingDetailForm: false,
  isLoadingDownloadInvoice: false,
  blobInvoice: null,
  items: [],
  item: null,
  totalPage: 1,
  page: 1
})

export default state

<template>
  <div>
    <div v-if="label.length > 0" class="text-body">
      {{ label }}
    </div>
    <v-text-field
      :value="value"
      :label="hint"
      :hint="persistentHint"
      :persistent-hint="persistentHint != null"
      :type="type"
      :prepend-inner-icon="prependInnerIcon"
      :required="isRequired"
      :rules="rules"
      :append-icon="appendIcon"
      :flat="hiddenHintOutlined"
      :solo="hiddenHintOutlined"
      :loading="isLoading"
      :single-line="false"
      :full-width="hiddenHintOutlined"
      :disabled="disabled"
      hide-spin-buttons
      outlined
      @input="$emit('input', $event)"
      @click:append="$emit('click:append')"
      @keydown.enter="onEnter"
    />
  </div>
</template>

<script>
export default {
  name: 'CustomTextField',

  props: {
    appendIcon: {
      type: String,
      default: ''
    },
    rules: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'text'
    },
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    persistentHint: {
      type: String,
      default: ''
    },
    prependInnerIcon: {
      type: String,
      default: ''
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    hiddenHintOutlined: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    onEnter (event) {
      event.preventDefault()

      this.$emit('on-enter', event.target.value)
    }
  }
}
</script>

<style scoped></style>

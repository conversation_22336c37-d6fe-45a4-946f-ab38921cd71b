import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleTypeState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<VehicleTypeState, RootState> = {
  getItems ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/vehicle-types', {
        params: {
          entries: '-1'
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .post('/v1/vehicle-types', {
        name: payload.name
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        dispatch('getItems')
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  deleteItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_DELETE', true)

    this.$axios
      .delete('/v1/vehicle-types', {
        params: {
          ids: payload.ids
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        dispatch('getItems')
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_DELETE', false)
      })
  }
}

export default actions

import { ActionTree } from 'vuex'
import { ProfileShipmentCompanyState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<ProfileShipmentCompanyState, ProfileShipmentCompanyState> = {
  getData ({ commit }) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/profile/shipment-companies').then((response: any) => {
      commit('SET_SHIPMENT_COMPANY', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  saveData ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.name)
    formData.append('address', payload.address)
    formData.append('logistics_service_provider_domain', payload.domainLsp)
    formData.append('_method', 'PUT')

    if (payload.logo) {
      formData.append('logo', payload.logo)
    }

    this.$axios.post('/v1/profile/shipment-companies', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      this.$router.push('/shipping-company/dashboard')
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_FORM', false)
    })
  }
}

export default actions

<template>
  <v-card outlined class="pa-5 fill-height">
    <v-card-text class="pa-0">
      <v-row class="ma-0 mb-5 justify-center">
        <v-avatar rounded size="40" class="mr-5">
          <img
            v-if="lsp.logo_url"
            :src="lsp.logo_url"
            alt="logo"
          >
          <v-icon v-else size="40">
            mdi-domain
          </v-icon>
        </v-avatar>

        <v-col class="pa-0 d-flex align-center">
          <h4 class="text-wrap flex black--text">
            {{ lsp.name }}<span v-if="isHasNew" class="pl-2 red--text">
              {{ $moment.utc(lsp.collaborated_at[0]).isAfter($moment().subtract(30, 'minutes')) ? 'New' : '' }}
            </span>
          </h4>
          <div
            class="ml-4"
            style="
              background-color: $background-primary-color-lite;
              border-radius: 5px;
            "
          >
            <div
              v-if="lsp.status"
              class="subtitle-2 px-3 py-2"
              :style="styleContainerCollaboration"
            >
              {{ lsp.status }}
            </div>
          </div>
        </v-col>
      </v-row>

      <v-row class="ma-0">
        <v-col class="pa-0">
          <div class="caption">
            {{ $t('general.location') }}
          </div>
          <div class="text-body wrap mt-1">
            {{ lsp.address }}
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { LogisticsServiceProvider } from '~/types/user'

export default Vue.extend({
  name: 'LspCardItem',

  props: {
    lsp: {
      type: Object as () => LogisticsServiceProvider,
      required: true
    }
  },

  data: () => ({
    isShowDeleteDialog: false,
    tempStatus: '' as string | null
  }),

  computed: {
    styleContainerCollaboration () {
      if (this.tempStatus === 'COLLABORATE') {
        return 'color: #2FA841; background-color: #EAF6EC; border-radius: 5px 5px'
      } else if (this.tempStatus === 'BLOCK') {
        return 'color: #EF3434; background-color: #FDE0E0; border-radius: 5px 5px'
      }

      return ''
    },
    isHasNew () {
      const user = this.$auth.user?.data as any

      if (user.role === 'ADMIN') {
        return false
      } else {
        return true
      }
    }
  },

  mounted () {
    this.tempStatus = this.lsp?.status
  },

  methods: {
    onClickSaveEdit (value: any) {
      this.$emit('on-click-save-edit', value)
    }
  }
})
</script>

<style scoped lang="scss"></style>

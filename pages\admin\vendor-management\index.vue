<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10 d-flex flex-column align-end">
    <v-container class="pa-0" fluid>
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-search-icon-click="getData({searchKey: $event})"
        @on-filter-change="getData({filter: $event, page: $route.query?.page})"
      >
        <template #button>
          <user-role-form-item
            label="Transporter"
            :is-has-domain-field="false"
            :is-loading-form="isLoadingForm"
            :dialog="dialogCreateVendor"
            :clear-form="clearForm"
            @on-click-save="createItem"
            @on-close-dialog="
              dialogCreateVendor = false
              clearForm = true
            "
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-if="$vuetify.breakpoint.xs"
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                block
                class="mb-4 mt-5"
                v-on="on"
                @click="
                  dialogCreateVendor = true
                  clearForm = false
                "
              >
                {{ $t('adminVendor.add_vendor') }}
              </v-btn>
              <v-btn
                v-else
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                v-on="on"
                @click="
                  dialogCreateVendor = true
                  clearForm = false
                "
              >
                add Transporter
              </v-btn>
            </template>
          </user-role-form-item>
        </template>
      </header-datatable>
    </v-container>

    <vendor-company-loading v-if="isLoading" />

    <v-container v-else fluid class="pa-0 mb-10">
      <v-row v-if="(data.items.length !== 0)" class="ma-n5">
        <v-col
          v-for="(item, i) in data.items"
          :key="item.id"
          md="4"
          sm="6"
          class="pa-5"
        >
          <user-role-card-item
            :item="item"
            :is-loading-form="isLoadingForm"
            :is-location-visible="false"
            :index="i"
            :dialog-update="dialogUpdateVendor[i]"
            :dialog-delete="dialogDeleteVendor[i]"
            detail-route="/admin/vendor-management"
            @on-click-save-edit="editItem"
            @on-click-save-delete="deleteItem"
            @on-open-update-dialog="$set(dialogUpdateVendor, i, true)"
            @on-close-update-dialog="$set(dialogUpdateVendor, i, false)"
            @on-open-delete-dialog="$set(dialogDeleteVendor, i, true)"
            @on-close-delete-dialog="$set(dialogDeleteVendor, i, false)"
          />
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col class="justify-center align-center fill-height">
          <empty-placeholder
            hero="empty-shipment.svg"
            :message-title="$t('adminVendor.empty_message_title')"
            :message-description="$t('adminVendor.empty_message_description')"
          />
        </v-col>
      </v-row>
    </v-container>

    <pagination-component
      :page="data.page"
      :total-page="data.totalPage"
      page-id="page"
      @on-change-page="getData({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import VendorCompanyLoading from '~/components/loading/VendorCompanyLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'VendorManagementPage',
  components: {
    VendorCompanyLoading,
    EmptyPlaceholder,
    PaginationComponent
  },
  layout: 'admin/body',
  middleware: ['auth', 'is-admin'],
  data: () => ({
    searchKey: '',
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateVendor: false,
    dialogUpdateVendor: [] as Array<Boolean>,
    dialogDeleteVendor: [] as Array<Boolean>,
    clearForm: false
  }),
  computed: {
    data () {
      return this.$store.getters['admin/vendor/data']
    },
    isLoading () {
      return this.$store.getters['admin/vendor/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters['admin/vendor/isLoadingForm']
    }
  },
  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Transporter Management')

    this.getData({
      page: this.$route.query?.page as string
    })
  },
  methods: {
    getData ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('admin/vendor/getItems', {
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        name: searchKey,
        page
      })
    },
    async createItem (value: any) {
      const response = await this.$store.dispatch(
        'admin/vendor/createItem',
        value
      )
      if (response) {
        this.dialogCreateVendor = false
        this.clearForm = true
      }
    },
    async editItem (value: any, i: any) {
      const response = await this.$store.dispatch(
        'admin/vendor/editItem',
        value
      )
      if (response) {
        this.$set(this.dialogUpdateVendor, i, false)
      }
    },
    async deleteItem (id: any, i: any) {
      await this.$store.dispatch('admin/vendor/deleteItem', {
        id
      })
      this.$set(this.dialogDeleteVendor, i, false)
    }
  }
})
</script>

<style scoped> </style>

<template>
  <v-dialog v-model="dialog" persistent max-width="600px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-10">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>{{ $t('vendorInvoiceOrder.send_invoice') }}</h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>
        <v-card
          class="inner-card"
        >
          <v-col>
            <v-card-title style="font-weight: bold;">
              {{ $t('vendorInvoiceOrder.preview_invoice') }}
            </v-card-title>
            <v-card-title>
              <h4 style="color: red">
                {{ dataInvoice?.invoice?.shipment?.logistics_service_provider?.name }}
              </h4>
            </v-card-title>
          </v-col>
          <v-col>
            <v-divider />
            <v-container
              v-for="invoiceDetail in dataInvoice?.invoice_details"
              :key="invoiceDetail.id"
              fluid
            >
              <div class="d-flex justify-space-between align-center">
                <div>
                  <h4 class="ma-05 mb-0">
                    {{ invoiceDetail.vehicle_detail?.vehicle?.name }}
                  </h4>
                  <p class="body-1 mb-0">
                    {{ invoiceDetail.vehicle_detail?.plate_number }}
                  </p>
                </div>
                <div class="ml-auto">
                  <h4 class="ma-05 mb-0">
                    {{ invoiceDetail?.cost | toCurrency }}
                  </h4>
                </div>
              </div>
            </v-container>
            <v-col>
              <p style="font-weight: bold;">
                {{ $t('vendorInvoiceOrder.additional_fee') }}
              </p>
              <div v-for="(item, index) in additionalCosts" :key="index" class="mb-2">
                <h4 class="mb-3">
                  {{ item.description }} <span class="cost">{{ item.cost | toCurrency }}</span>
                </h4>
              </div>
              <v-divider />
            </v-col>
            <v-col style="display:flex; justify-content: space-between;">
              <h4 class="subtitle-1 text-bold mb-1 pa-1" style="color: red">
                {{ $t('vendorInvoiceOrder.total_invoice') }}
              </h4>
              <h4 class="subtitle-1 text-bold mb-1 pa-1 text-right" style="color: red">
                {{ totalCostDialog ?? 0 | toCurrency }}
              </h4>
            </v-col>
          </v-col>
        </v-card>

        <p class="mb-4 subtitle-1 mt-5">
          {{ $t('lspInvoiceShipment.dialog_send_invoice') }} <span style="font-weight: bold;">{{ dataInvoice?.invoice?.shipment?.identity }}</span> {{ $t('lspInvoiceShipment.dialog_send_invoice_correctly') }}
        </p>

        <div class="d-flex mt-2">
          <v-btn
            depressed
            class="mr-2"
            color="primary"
            @click="
              selected = null;
              $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', null);
              $emit('on-close-dialog')"
          >
            {{ $t('vendorInvoiceOrder.check_again') }}
          </v-btn>
          <v-btn
            outlined
            depressed
            color="primary"
            elevation="0"
            class="text-capitalize mr-6"
            :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
            @click="createInvoice"
          >
            {{ $t('vendorInvoiceOrder.send_invoice') }}
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { InvoiceOrder } from '~/types/invoice'

export default Vue.extend({
  name: 'SelectSummaryVendorSendInvoiceDialog',
  middleware: ['auth', 'is-vendor'],

  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    totalCostDialog: {
      type: Number || null,
      default: null
    }
  },
  data: () => ({
    form: {
      additional_costs: [] as any[],
      costs: {} as any
    },
    totalAmount: 0
  }),

  computed: {
    dataInvoice (): InvoiceOrder | null {
      return this.$store.getters['invoice/detailData'].item
    },
    dataInvoiceShipment (): InvoiceOrder | null {
      return this.$store.getters['logistic-service-provider/invoice-shipment/detailData'].item
    },
    additionalCosts (): { description: any; cost: any }[] {
      return this.$store.getters['invoice/additional-fee/data'].items.map((item: { description: any; cost: any }) => {
        return { description: item.description, cost: item.cost }
      })
    }
  },

  methods: {
    createInvoice () {
      this.$store.dispatch('invoice/createItem', {
        id: this.$route.params.id
      })
    }
  }
})

</script>
<style scoped>

.shipment-item {
  margin-bottom: 10px;
}

.cost {
  float: right;
  text-align: right;
}

.inner-card {
  background-color: rgb(233, 227, 227);
}

</style>

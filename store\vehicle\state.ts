import { Vehicle } from '~/types/vehicle'

export interface VehicleState {
  vendorWeight: number
  isLoading: boolean
  isLoadingList: boolean
  isLoadingForm: boolean
  isLoadingDelete: boolean
  items: Vehicle[]
  listVehicles: Vehicle[]
  selectedVehicle: Vehicle | null
  totalPage: number
  page: number
  searchKey: string
  exceedMessages: any
}

export const state = (): VehicleState => ({
  vendorWeight: 0,
  isLoading: false,
  isLoadingList: false,
  isLoadingForm: false,
  isLoadingDelete: false,
  items: [],
  listVehicles: [],
  selectedVehicle: null,
  totalPage: 1,
  page: 1,
  searchKey: '',
  exceedMessages: ''
})

export default state

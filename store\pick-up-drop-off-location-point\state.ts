import { PickupDropOffLocationPoint } from '~/types/product'
import { ImportKey } from '~/types/import-key'

export interface PickupDropOffLocationPointState {
  isLoading: boolean
  isLoadingForm: boolean
  items: PickupDropOffLocationPoint[]
  itemsLocationKey: ImportKey[]
  totalPage: number
  page: number
}

export const state = (): PickupDropOffLocationPointState => ({
  isLoading: false,
  isLoadingForm: false,
  items: [],
  itemsLocationKey: [],
  totalPage: 1,
  page: 1
})

export default state

<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <div class="white rounded pa-10">
      <div class="mb-10">
        <h3>Result</h3>
      </div>

      <div class="mt-8" v-if="isLoading">
         <v-skeleton-loader class="pb-5" type="text" />
         <v-skeleton-loader class="pb-5" type="text" />
         <v-skeleton-loader class="pb-5" type="text" />
      </div>

      <div v-else>
        <v-data-table
          :headers="dataExportHeaders"
          :items="data?.itemExportResult"
           hide-default-footer
        >
          <template #item.no="{ index }">
            {{ index + 1 }}
          </template>
          <template #item.name="{ item }">
            {{ item.name }}
          </template>
          <template #item.created="{ item }">
            {{ $moment(item.created_at).format('DD/MM/yyyy') }} <br>
            <p class="text-secondary">{{ $moment(item.created_at).format('HH:mm') }}</p>
          </template>
          <template #item.download="{ item }">
            <div v-if="item.status === 'ON PROCESS'" class="d-flex align-center" style="color: #EF3434;">
              <v-progress-circular
                indeterminate
                color="#EF3434"
                size="20"
                width="2"
                class="mr-2 slower-spin"
              ></v-progress-circular>
              On Process
            </div>

            <v-btn
              v-else-if="item.status === 'COMPLETED' && item.file_url"
              elevation="0"
              color="primary"
              :href="item.file_url"
              download
            >
              <v-icon left>mdi-download</v-icon>
              Download
            </v-btn>
            
            <v-btn
              v-else
              elevation="0"
              color="error"
              disabled
            >
              Failed
            </v-btn>
          </template>
          <template #item.delete="{ item }">
            <v-btn
              icon
              color="error"
              :loading="deletingId === item.id"
              @click="deleteItem(item.id)"
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </template>
          </v-data-table>

          <div v-if="data?.itemExportResult.length > 0" class="mt-6 mb-3">
            <pagination-component
              :page="data.page"
              :total-page="data.totalPage"
              page-id="page"
              class="float-end"
              @on-change-page="getExportData({
                page: $event
              })"
            />
          </div>
      </div>
    </div>
  </v-container>
</template>

<script>
import PaginationComponent from '~/components/PaginationComponent.vue'

export default {
  name: 'ExportFiles',

  components: {
    PaginationComponent,
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    dataExportHeaders: [
      { text: 'No', value: 'no', sortable: false },
      { text: 'File Name', value: 'name', sortable: false },
      { text: 'Created', value: 'created', sortable: false },
      { text: 'Action', value: 'download', sortable: false },
      { text: '', value: 'delete', sortable: false },
    ],
    deletingId: null,
  }),

  computed: {
    data() {
      return this.$store.getters['export/data']
    },
    isLoading() {
      return this.$store.getters['export/isLoading']
    },
    isLoadingDelete() {
      return this.$store.getters['export/isLoadingDelete']
    },
  },

  created () {
    this.$store.commit('layout/SET_TITLE', `Export Files`)
  },

  mounted() {
    this.getExportData({ 
      page: this.$route.query?.page
    })
  },

  methods: {
    getExportData({ page = '' }) {
      this.$store.dispatch('export/getExport', {
      page,
      entries: 10,
    })
    },

    async deleteItem(id) {
      this.deletingId = id
      try {
        await this.$store.dispatch('export/deleteItem', { id })
        this.getExportData({page: this.$route.query?.page})
      } finally {
        this.deletingId = null
      }
    },
  }
}
</script>

<style scoped>
.v-list-item {
  padding: 16px;
}
.v-list-item__title {
  font-weight: 500;
  margin-bottom: 4px;
}
.v-list-item__subtitle {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.6);
}
.slower-spin :deep(.v-progress-circular__overlay) {
  animation-duration: 10s !important; /* Default is 1.4s, higher number = slower spin */
}
</style>


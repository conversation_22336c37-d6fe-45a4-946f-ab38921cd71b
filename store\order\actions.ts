import { ActionTree } from 'vuex'
import { RootState } from '../index'
import { OrderState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHand<PERSON> } from '~/utils/functions'

export const actions: ActionTree<OrderState, RootState> = {
  getOrders ({ commit }, payload) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/order', {
        params: {
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          search_columns:
            payload.searchColumns == null ? '' : payload.searchColumns,
          sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
          sort_type: payload.sortType == null ? 'ASC' : payload.sortType,
          filter_columns: payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          page: payload.page == null ? '' : payload.page,
          entries: payload.entries == null ? -1 : payload.entries
        }
      })
      .then((response: any) => {
        if (response.data.meta) {
          commit('SET_RESULT', response.data)
        } else {
          commit('SET_ITEMS', response.data.data)
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async getDetailOrder ({ commit }, payload) {
    commit('SET_IS_LOADING_DETAIL', true)

    await this.$axios
      .get(`/v1/order/${payload.id}`)
      .then((response: any) => {
        commit('SET_SELECTED_ORDER', response.data.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  sendShipmentOrder ({ commit, dispatch }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios
      .post('/v1/shipment', payload)
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        this.$router.push(
          '/logistic-service-provider/order-shipment/history-order/detail-history-order/' +
            response.data.data.id
        )

        dispatch('vehicles/clearVehicles')

        commit('SET_SELECTED_ORDER', null)

        commit('REMOVE_SELECTED_ORDER_ITEM_FROM_LIST', payload.id)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  sendRitaseShipmentOrder ({ commit, dispatch }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    this.$axios
      .put(`/v1/shipment/${payload.id}/reopen`, payload)
      .then((response: any) => {
        toastSuccess(response.data.message, this)

        this.$router.push(
          '/logistic-service-provider/order-shipment/history-order/detail-history-order/' +
            response.data.data.id
        )

        dispatch('vehicles/clearVehicles')

        commit('SET_SELECTED_ORDER', null)

        commit('REMOVE_SELECTED_ORDER_ITEM_FROM_LIST', payload.id)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async cancelOrder ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)

    return await this.$axios
      .put(`/v1/order/${payload.id}/cancel`)
      .then((response: any) => {
        commit('SET_IS_LOADING_DETAIL', false)

        toastSuccess(response.data.message, this)

        commit('SET_SELECTED_ORDER', null)

        commit('REMOVE_SELECTED_ORDER_ITEM_FROM_LIST', payload.id)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_DETAIL', false)
      })
  },

  async bulkImportOrders ({ commit }, payload: {
    shipment_company_id: string,
    data: {
      identity_order: string,
      type: string,
      identity_location: string,
      identity_product: string,
      quantity: string,
      estimation: string
    }[]
  }) {
    commit('SET_IS_LOADING_FORM', true)

    const reformattedFormValues = payload.data.map(item => ({
      identity_order: item.identity_order,
      type: item.type,
      identity_location: item.identity_location,
      identity_product: item.identity_product,
      quantity: Number(item.quantity),
      estimation: item.estimation
    }))

    return await this.$axios.post('/v1/order/bulk-import', {
      shipment_company_id: payload.shipment_company_id,
      data: reformattedFormValues
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      return true
    }).catch((error: any) => {
      exceptionHandler(error, this)
      return false
    }).finally(() => { commit('SET_IS_LOADING_FORM', false) })
  }
}

export default actions

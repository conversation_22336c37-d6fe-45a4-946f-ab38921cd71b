import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { VehicleState } from './state'
import {Vehicle} from "~/types/vehicle";

export const getters: GetterTree<VehicleState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  vendorWeight (state) {
    return state.vendorWeight
  },

  listVehicles (state): Vehicle[] {
    return state.listVehicles
  },

  selectedVehicle (state) {
    return state.selectedVehicle
  },

  checkedVehicles (state) {
    return state.listVehicles.filter(v => v.quantity != null && v.quantity > 0)
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingList (state) {
    return state.isLoadingList
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingDelete (state) {
    return state.isLoadingDelete
  },

  searchKey (state) {
    return state.searchKey
  }
}

export default getters

    importScripts(
      'https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js'
    )
    importScripts(
      'https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js'
    )
    firebase.initializeApp({"apiKey":"AIzaSyDzoAVviLhZa4kwgjXrmDltChuBlGu2Js0","authDomain":"logistic-service-integrator.firebaseapp.com","databaseURL":"https:\u002F\u002Flogistic-service-integrator-default-rtdb.asia-southeast1.firebasedatabase.app","projectId":"logistic-service-integrator","storageBucket":"logistic-service-integrator.appspot.com","messagingSenderId":"814493655222","appId":"1:814493655222:web:50eff23c9a5c489f77c617","measurementId":"G-3BEFCGNNQ8"})

    // Retrieve an instance of Firebase Messaging so that it can handle background
    // messages.
    const messaging = firebase.messaging()

    
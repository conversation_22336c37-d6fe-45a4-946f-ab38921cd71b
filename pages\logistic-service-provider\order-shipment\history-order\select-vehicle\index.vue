<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 pb-10"
  >
    <button-back :title="String($t('lspCreateShipment.back_to_order'))" class="mb-10" />

    <select-vendor-vehicle :calculation-order="getTotalWeightRitase()" :total-weight-order="shipmentDetail?.total_weight" :is-loading-shipment-detail="isLoadingShipmentDetail" :order-number="shipmentDetail?.orders[0]?.identity" />
    <v-dialog v-if="getTotalWeightRitase() === 0" v-model="dialogVisible" max-width="540px" persistent>
      <v-card class="pa-md-10 pa-5">
        <div>
          <v-row class=" d-flex justify-center">
            <h4 class="pa-3">
              You have send all of weight on this order
            </h4>
          </v-row>
          <v-row class="d-flex justify-center">
            <v-btn
              elevation="0"
              color="primary"
              class="pa-3 text-capitalize col-3 mt-5"
              x-large
              @click="onClickCancelRitase"
            >
              Ok
            </v-btn>
          </v-row>
        </div>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'LogisticServiceProviderCreateOrderPage',

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  props: {
    path: {
      type: String || null,
      default: null
    }
  },

  data: () => ({
    dialogVisible: true
  }),

  computed: {
    shipmentDetail () {
      return this.$store.getters['shipment/detailShipment'] as any
    },
    isLoadingShipmentDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    }
  },

  mounted () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')
    this.getDetailShipment().then(() => {
      this.getTotalWeightRitase()
    })
  },

  methods: {
    async getDetailShipment () {
      await this.$store.dispatch('shipment/getItemDetail', {
        id: this.$route.query.shipmentRitaseId + '?target=reorder',
        type: 'reorder'
      })
    },

    getTotalWeightRitase () {
      // eslint-disable-next-line no-unused-expressions
      const totalWeightOrder = this.shipmentDetail?.orders[0]?.suborders[0]?.total_weight
      const pickupItems = (this.shipmentDetail?.orders[0]?.weight_bridges?.filter((item: { type: string }) => item.type === 'PICKUP'))

      const totalWeightWb = pickupItems?.reduce((sum: number, item: { weight: string }) => sum + parseFloat(item.weight), 0)

      return totalWeightOrder - totalWeightWb
    },

    onClickCancelRitase () {
      if (this.path) {
        this.$router.push(this.localePath(this.path))
      } else {
        this.$router.back()
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

//* {
//  border: 1px solid;
//}
</style>

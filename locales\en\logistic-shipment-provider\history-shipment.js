module.exports = {
  history_order_shipment: 'History Order Shipment',
  search: 'Search',
  invoice_to_sc: 'Invoice To Product Owner',
  review_order_shipment: 'Review Order Shipment',
  detail_vendor: 'Detail Vendor',
  estimated_distance: 'Estimated Distance',
  receiver: 'Receiver',
  status: 'Status',
  photo: 'Photo',
  features: 'Features',
  detail_invoice: 'Detail Invoice',
  select_track: 'Select Track',
  from: 'From',
  to: 'To',
  fuel: 'Fuel',
  invoice_from_vendor: 'Invoice From Vendor',
  invoice_number: 'Invoice Number',
  select_order: 'Select Order',
  identity_product: 'Identity Product',
  product: 'Product',
  quantity: 'Quantity',
  weight: 'Weight',
  estimation: 'Estimation',
  operational_time: 'Operational Time',
  location: 'Location',
  select_vehicle: 'Select Vehicle',
  sub_order_your_choice: 'Sub Order Your Choice',
  total_weight: 'Total Weight',
  total_length: 'Total Length',
  total_width: 'Total Width',
  total_height: 'Total Height',
  shipping_cost: 'Shipping Cost',
  add_additional_fee: 'Add Additional Fee',
  no_additional_fee: 'No Additional Fee',
  the_amount_that_must_be_paid: 'The Amount That Must Be Paid',
  the_total_amount_that_sc_must_be_paid: 'The Total Amount That Product Owner Must be Paid',
  the_total_amount_that_lsp_must_be_paid: 'The Total Amount That Logistic Provider Must be Paid',
  back_to_history: 'Back to history',
  back_to_invoice: 'Back to invoice',
  download_invoice: 'Download Invoice',
  order_number: 'Order Number',
  back_to_list: 'Back to list',
  created_invoice_lsp_text: 'Your Created Invoice for Logistic Provider',
  created_invoice_sc_text: 'Your Created Invoice for Product Owner',
  distance: 'Distance',
  invoice_empty_title: 'You don\'t have a invoice.',
  invoice_empty_desc: 'Now, you don\'t have an invoice history from an order shipment.',
  noted: 'Noted',
}

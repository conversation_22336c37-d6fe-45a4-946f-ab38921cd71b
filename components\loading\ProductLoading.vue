<template>
  <v-row class="ma-n8">
    <v-col v-for="i in 2" :key="i" class="pa-5 col-lg-6 col-sm-6 col-12">
      <v-sheet height="270" class="pa-5 overflow-hidden">
        <v-container class="d-flex justify-space-between pa-0">
          <v-skeleton-loader type="heading" width="200" />
          <v-skeleton-loader type="image" width="25" height="25" />
        </v-container>
        <v-skeleton-loader type="text" class="mt-10" />
        <v-skeleton-loader type="text" class="mt-5" />
        <v-divider class="mb-5 mt-8" />
        <v-skeleton-loader type="text" class="mt-8" />
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'ProductLoading'
})
</script>

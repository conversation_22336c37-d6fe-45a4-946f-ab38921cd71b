import { test, expect } from '@playwright/test'
import { checkVisibilityButton } from '~/tests/utils'

test('Login LSP Action Success', async ({ page }) => {
  await page.goto('http://localhost:3000/login')

  await expect(page.getByTestId('image-side').first()).toBeVisible()

  await page
    .getByTestId('input-email')
    .getByRole('textbox')
    .fill('<EMAIL>')

  await page
    .getByTestId('input-password')
    .getByRole('textbox')
    .fill('password')

  const visibilityButton = await page.getByTestId('input-password').getByRole('button', { name: 'append icon' })
  await checkVisibilityButton(visibilityButton)

  await page.getByRole('button', { name: 'Login' }).click()

  await expect(page.locator('h3').first()).toHaveText('Dashboard')
})

test('Login as Admin Action Success', async ({ page }) => {
  await page.goto('http://localhost:3000/login')
  await page.getByText('Login as Admin').click()

  await page
    .getByTestId('input-email')
    .getByLabel('Enter your email address')
    .fill('<EMAIL>')

  await page
    .getByTestId('input-password')
    .getByLabel('Enter your password')
    .fill('password')

  const visibilityButton = await page.getByTestId('input-password').getByRole('button', { name: 'append icon' })
  await checkVisibilityButton(visibilityButton)

  await page.getByRole('button', { name: 'Login' }).click()

  await expect(page.locator('h3').first()).toHaveText(
    'Logistic Service Aggregator/Provider'
  )
})

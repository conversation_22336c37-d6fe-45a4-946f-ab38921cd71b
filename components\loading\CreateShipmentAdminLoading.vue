<template>
  <div>
    <v-sheet height="600">
      <div v-for="i in 3" :key="i">
        <v-skeleton-loader type="image" style="border-radius: 0 !important;" />
      </div>
    </v-sheet>
    <v-row class="justify-space-between ma-0">
      <v-col class="col-sm-12 col-md-4 pa-0">
        <v-sheet height="600" class="mt-5 mb-5 pa-10 mr-2">
          <v-skeleton-loader type="heading" width="100%" class="mb-5" />
          <v-divider />
          <v-skeleton-loader type="heading" width="75%" class="mt-5 mb-5" />
          <v-skeleton-loader type="image" height="55" class="mb-5" />
          <div v-for="i in 3" :key="i">
            <v-skeleton-loader type="image" height="90" class="mb-5" />
          </div>
        </v-sheet>
        <v-sheet height="435" class=" pa-10 mr-2">
          <v-skeleton-loader type="heading" width="100%" class="mb-5" />
          <v-divider />
          <v-skeleton-loader type="text" width="50%" class="mt-5 mb-5" />
          <v-container class="d-flex justify-space-between pa-0">
            <div v-for="i in 2" :key="i">
              <div class="d-flex flex-column">
                <v-skeleton-loader type="text" width="100" class="mb-2" />
                <v-skeleton-loader type="image" width="100" height="40" style="border-radius: 10px !important;" />
              </div>
            </div>
          </v-container>
          <v-skeleton-loader type="text" width="75" class="mt-10" />
          <v-skeleton-loader type="text" class="mt-5 mb-5" />
          <v-skeleton-loader type="text" />
          <v-divider class="mt-3 mb-5" />
          <v-skeleton-loader type="image" height="30" />
        </v-sheet>
      </v-col>
      <v-col class="col-sm-12 col-md-8 pa-0">
        <v-sheet height="1000" class="d-flex flex-column mt-5 mb-5 overflow-hidden pa-10">
          <v-container class="d-flex justify-space-between mb-5 pa-0">
            <v-skeleton-loader type="text" width="300" />
            <v-skeleton-loader type="text" width="150" />
          </v-container>
          <div v-for="i in 2" :key="i">
            <v-skeleton-loader type="heading" class="mb-5 mt-10" />
            <v-container class="d-flex flex-column justify-center pl-16 pr-16">
              <v-skeleton-loader type="image" width="100%" height="150" style="border-radius: 0 !important;" />
              <v-skeleton-loader type="image" width="100%" height="150" style="border-radius: 0 !important;" />
            </v-container>
          </div>
        </v-sheet>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'CreateShipmentAdminLoading'
})
</script>

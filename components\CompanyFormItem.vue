<template>
  <v-dialog
    v-model="dialog"
    persistent
    max-width="600px"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>
    <v-card class="pa-md-10 pa-5">
      <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
        <h4>{{ $t('companyFormItem.edit') + " "}} {{ label }}</h4>
        <v-icon color="black" @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </v-card-title>
      <v-container class="pa-4">
        <v-row>
          <v-col v-if="isHasDomainsAutoComplete" class="pa-0" cols="12">
            <custom-autocomplete
              v-model="form.logisticsServiceProviderDomain"
              hint="Logistic Provider Domain"
              :items="logisticsServiceProviderDomains"
              is-required
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col class="pa-0" cols="12">
            <custom-text-field
              v-model="form.name"
              :hint="$t('companyFormItem.name_company')"
              is-required
            />
          </v-col>
          <v-col class="pa-0" cols="12">
            <custom-text-field
              v-model="form.address"
              :hint="$t('companyFormItem.address_company')"
              is-required
            />
          </v-col>
          <v-col v-if="isHasDomainCompany" class="pa-0" cols="12">
            <custom-text-field
              v-model="form.domain"
              :hint="$t('companyFormItem.enter_domain_company')"
              prepend-inner-icon="mdi-domain"
              is-required
            />
          </v-col>
        </v-row>
      </v-container>
      <v-card-actions class="pa-0 mt-2">
        <v-btn
          height="52"
          color="primary"
          depressed
          x-large
          :loading="isLoadingForm"
          @click="onClickSave"
        >
          {{ $t('companyFormItem.save')}}
        </v-btn>
        <v-col cols="4">
          <v-btn
            height="52"
            color="primary"
            depressed
            outlined
            x-large
            @click="$emit('on-close-dialog')"
          >
            {{ $t('companyFormItem.cancel') }}
          </v-btn>
        </v-col>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'CompanyFormItem',

  props: {
    user: {
      type: Object,
      default: () => ({

      })
    },
    label: {
      type: String,
      required: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    isHasDomainsAutoComplete: {
      type: Boolean,
      default: false
    },
    logisticsServiceProviderDomains: {
      type: Array,
      default: () => []
    },
    isHasDomainCompany: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isShowFormDialog: false,
    form: {
      id: '',
      name: '',
      address: '',
      domain: '',
      logisticsServiceProviderDomain: ''
    }
  }),

  mounted () {
    this.$emit('on-mounted')
  },

  created () {
    if (this.user) {
      this.form = this.user
    } else {
      this.form = {
        id: '',
        name: '',
        address: '',
        domain: '',
        logisticsServiceProviderDomain: ''
      }
    }
  },

  methods: {
    onClickSave () {
      this.$emit('on-click-save', this.form)
    }
  }
})
</script>

<style scoped>

</style>

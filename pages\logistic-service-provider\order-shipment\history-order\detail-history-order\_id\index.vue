<template>
  <v-container fluid class="rounded pa-0 px-10 mb-10">
    <div class="mb-sm-10 mb-5 d-flex align-center">
      <v-btn
        text
        plain
        class="text-capitalize pa-0"
        @click="$router.back()"
      >
        <v-icon
          class="mr-3 custom-icon"
        >
          mdi-chevron-left
        </v-icon>
        {{ $t('lspHistoryShipment.back_to_list') }}
      </v-btn>
    </div>

    <detail-history-order-loading v-if="isLoadingShipmentDetail" />

    <detail-history-order-shipment
      v-else
      from="LOGISTIC_SHIPMENT_PROVIDER"
      :vehicles="vehicles"
      :is-loading-vehicles="isLoadingVehicles"
      :driver="drivers"
      :is-loading-drivers="isLoadingDrivers"
      :vehicle-detail="vehicleDetail"
      :is-loading-vehicle-details="isLoadingVehicleDetails"
      :is-change-vendor="true"
      :center-position="centerLatLng"
      :zoom="zoomMap"
      :polyline="polyline ?? {mapBox: [], fms: []}"
      :markers="markers"
      :shipment-detail="shipmentDetail"
      :selected-routes="selectedRoutes"
      :shipment-id="shipmentDetail?.id"
      :is-loading-shipment-detail="isLoadingShipmentDetail"
      detail-invoice-url="/logistic-service-provider/order-shipment/history-order/detail-invoice-order/"
      @on-select-track="updateRoutes($event)"
      @on-success-change-destination="getDetailShipment"
      @on-remove-track="getDetailShipment"
      @update-change-vehicle="handleUpdateChangeVehicle"
    >
      <template #log-change-vehicle>
        <v-container v-if="shipmentDetail?.shipment_vehicle_log.length > 0"
                     class="white rounded flex-column ma-0 pa-0 mt-8"
        >
          <h4 class="mb-4">
            Log Changes Vehicle
          </h4>
            <v-col v-for="(logChangeVehicle, i) in shipmentDetail.shipment_vehicle_log" :key="i"
                   class="d-flex justify-space-between mb-8 ma-0 pa-0"
                   style="border-bottom: 2px solid #1b1f24;"
            >
              <v-col class="pa-0">
                <p class="ma-0 pa-0">From</p>
                <p class="subtitle-1 text--bold ma-0 pa-0">
                  {{ logChangeVehicle?.old_vehicle_detail?.vehicle?.name }}
                </p>
                <p class="ma-0 pa-0">{{ logChangeVehicle?.old_vehicle_detail?.plate_number }}</p>
                <p class="ma-0 pa-0">{{ logChangeVehicle?.old_driver?.user?.name }}</p>
                <p class="subtitle-1 text--bold ma-0 pa-0">
                  Lampiran
                </p>

                <div v-if="logChangeVehicle?.shipment_vehicle_photo[0]?.file.split('.')[1] === 'pdf'">
                  <a :href="logChangeVehicle?.shipment_vehicle_photo[0]?.files_url" target="_blank">Open File</a>
                </div>
                <image-component
                  v-else
                  :image="logChangeVehicle?.shipment_vehicle_photo[0]?.files_url"
                  class="zoom-image zoom-image:hover"
                  max-width="100"
                  min-width="100"
                />

                <p class="subtitle-1 text--bold mt-3 ma-0 pa-0">
                  Waktu
                </p>
                <p class="ma-0 pa-0">{{ $moment(logChangeVehicle?.created_at).format('DD MMMM YYYY HH:mm') }}</p>
              </v-col>
              <v-col class="ml-5 pa-0">
                <p class="ma-0 pa-0">To</p>
                <p class="subtitle-1 text--bold ma-0 pa-0">
                  {{ logChangeVehicle?.new_vehicle_detail?.vehicle?.name }}
                </p>
                <p class="ma-0 pa-0">{{ logChangeVehicle?.new_vehicle_detail?.plate_number }}</p>
                <p class="ma-0 pa-0">{{ logChangeVehicle?.new_driver?.user?.name }}</p>
                <p class="subtitle-1 text--bold ma-0 pa-0">
                  Note
                </p>
                <p>{{ logChangeVehicle?.note }}</p>
              </v-col>
            </v-col>
        </v-container>
      </template>
    </detail-history-order-shipment>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Shipment } from '~/types/shipment'
import DetailHistoryOrderLoading from '~/components/loading/DetailHistoryOrderLoading.vue'
import DetailHistoryOrderShipment from '~/components/DetailHistoryOrderShipment.vue'
import { generateCenterLatLng, generatePolyline, zoom } from '~/utils/functions'
import { VehicleDetail } from '~/types/vehicle'
import { Driver } from '~/types/driver'

interface Polyline {
  mapBox: object[]
  fms: object[]
}

export default Vue.extend({
  name: 'DetailHistoryOrderPage',

  components: {
    DetailHistoryOrderLoading,
    DetailHistoryOrderShipment
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    dialog: false,
    selectedTrackId: '',
    selectedRoutes: [] as any,
    polyline: null as Polyline | null,
    markers: [] as { lat: number, lng: number, type: string, name: string }[],
    centerLatLng: {} as { lat: number, lng: number },
    zoomMap: 0,
    trackIndex: 0,
    dialogChangeVehicle: false,
    selectedVehicleItem: null
  }),

  computed: {
    shipmentDetail () {
      return this.$store.getters['shipment/detailShipment'] as Shipment | null
    },
    isLoadingShipmentDetail () {
      return this.$store.getters['shipment/isLoadingDetail']
    },
    vehicles () {
      return this.$store.getters['vehicle/data'].items
    },
    isLoadingVehicles () {
      return this.$store.getters['vehicle/isLoading']
    },
    vehicleDetail () {
      return this.$store.getters['vehicle/details/data'].items as VehicleDetail[]
    },
    isLoadingVehicleDetails () {
      return this.$store.getters['vehicle/details/isLoading']
    },
    drivers () {
      return this.$store.getters['vehicle/drivers/data'].items as Driver[]
    },
    isLoadingDrivers () {
      return this.$store.getters['vehicle/drivers/isLoading']
    }
  },

  watch: {
    shipmentDetail () {
      this.updateRoutes(this.trackIndex)
    },
    dialogChangeVehicle(newVal) {
      if (newVal) {
        this.getDataVehicles()
        this.getVehicleDetailData()
        this.getDriverData({})
      }
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'History Order Shipment')
  },

  mounted () {
    this.getDetailShipment()
    this.$store.dispatch('vehicle/clearVehicles')
    // this.getDataVehicles()
    // this.getVehicleDetailData()
    // this.getDriverData({})
  },

  methods: {
    async getDetailShipment () {
      const res = await this.$store.dispatch('shipment/getItemDetail', {
        id: this.$route.params.id,
        type: 'detail'
      })
      if (res) {
        window.scrollTo(0, 0)
      }
    },
    updateRoutes (index: number) {
      if (this.shipmentDetail) {
        this.selectedTrackId = this.shipmentDetail?.tracks[index]?.id
        this.selectedRoutes = this.shipmentDetail?.orders[index]?.suborders

        this.setupDirections()

        this.polyline = generatePolyline(this.shipmentDetail?.tracks)
        this.centerLatLng = generateCenterLatLng(this.markers)
        this.zoomMap = zoom(this.markers)
      }
    },
    setupDirections () {
      const polyline = [] as any
      const markers = [] as any

      this.shipmentDetail?.tracks?.forEach((track, index) => {
        polyline.push([])

        track.directions?.forEach((direction) => {
          polyline[index].push([direction.latitude, direction.longitude])
        })

        track.routes?.forEach((route) => {
          if (!route.pickup_drop_off_location_point) {
            return
          }

          const lat = parseFloat(route.pickup_drop_off_location_point.latitude)
          const lng = parseFloat(route.pickup_drop_off_location_point.longitude)
          const type = route.type
          const name = route.pickup_drop_off_location_point.name

          markers.push({
            lat,
            lng,
            type,
            name
          })
        })
      })

      this.markers = markers
      this.polyline = polyline
    },

    async getDataVehicles () {
      await this.$store.dispatch('vehicle/getItems', {
        entries: '-1'
      })
    },

    async getVehicleDetailData () {
      await this.$store.dispatch('vehicle/details/getItems', {
        mode: 'simplified'
      })
    },

    async getDriverData ({
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc'
      }
    }) {
      await this.$store.dispatch('vehicle/drivers/getItems', {
        mode: 'simplified',
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        entries: -1
      })
    },

    handleUpdateChangeVehicle(params: { dialog: boolean; item: any }) {
      this.dialogChangeVehicle = params.dialog
      this.selectedVehicleItem = params.item
    }
  }
})
</script>

<style scoped lang="scss">
.f-resize {
  font-size: 16px !important;
}
.chip-info {
  background: #E6F4F8 !important;
}
@media (min-width: 280px) and (max-width: 576px) {
  .f-resize {
    font-size: 12px !important;
  }
  .f-resize-16 {
    font-size: 16px !important;
  }
  .f-resize-14 {
    font-size: 14px !important;
  }
  .icon-resize {
    font-size: 16px !important;
  }
}
</style>

import { MutationTree } from 'vuex'
import { DashboardState } from './state'

export const mutations: MutationTree<DashboardState> = {
  SET_ITEM_ADMIN (state, itemAdmin: any) {
    state.itemAdmin = itemAdmin
  },

  SET_ITEM_LSA (state, itemLsa: any) {
    state.itemLsa = itemLsa
  },

  SET_ITEM_SHIPMENT (state, itemShipment: any) {
    state.itemShipment = itemShipment
  },

  SET_ITEM_VENDOR (state, itemVendor: any) {
    state.itemVendor = itemVendor
  },

  SET_IS_LOADING_ADMIN (state, isLoadingAdmin) {
    state.isLoadingAdmin = isLoadingAdmin
  },

  SET_IS_LOADING_SHIPMENT_COMPANY (state, isLoadingShipmentCompany) {
    state.isLoadingShipmentCompany = isLoadingShipmentCompany
  },

  SET_IS_LOADING_LOGISTIC_SHIPMENT_PROVIDER (state, isLoadingLogisticServiceProvider) {
    state.isLoadingLogisticServiceProvider = isLoadingLogisticServiceProvider
  },

  SET_IS_LOADING_VENDOR (state, isLoadingVendor) {
    state.isLoadingVendor = isLoadingVendor
  },

  SET_IS_LOADING_LIVE_TRACKING (state, isLoadingLiveTracking) {
    state.isLoadingLiveTracking = isLoadingLiveTracking
  },

  SET_LIVE_TRACKING (state, liveTracking) {
    state.liveTracking = liveTracking
  },

  SET_TRUCK_STATUS (state, truckStatus) {
    state.truckStatus = truckStatus
  },

  SET_IS_LOADING_TRUCK_STATUS (state, isLoadingTruckStatus) {
    state.isLoadingTruckStatus = isLoadingTruckStatus
  },

  SET_IS_LOADING_GEOFENCES (state, isLoadingGeofences) {
    state.isLoadingGeofences = isLoadingGeofences
  },

  SET_ITEM_INCOMING_TRUCK (state, itemIncomingTruck: any) {
    state.itemIncomingTruck = itemIncomingTruck?.data
    state.page = itemIncomingTruck?.meta?.current_page
    state.totalPages = itemIncomingTruck?.meta?.last_page
    state.totalData = itemIncomingTruck?.meta?.total
  },

  SET_IS_LOADING_INCOMING_TRUCK (state, isLoadingIncomingTRuck) {
    state.isLoadingIncomingTRuck = isLoadingIncomingTRuck
  },

  SET_PERFORMANCE_VENDOR (state, itemPerformanceVendor) {
    state.itemPerformanceVendor = itemPerformanceVendor?.data
    state.page = itemPerformanceVendor?.meta?.current_page
    state.totalPages = itemPerformanceVendor?.meta?.last_page
  },

  SET_PERFORMANCE_VENDOR_RATE (state, itemPerformanceVendorRate) {
    state.itemPerformanceVendorRate = itemPerformanceVendorRate?.data
    state.pageVendorRate = itemPerformanceVendorRate?.meta?.current_page
    state.totalPagesVendorRate = itemPerformanceVendorRate?.meta?.last_page
  },

  SET_ITEM_VENDOR_VEHICLE (state, response: any) {
    state.itemVendorVehicles = response.data
    state.totalPageVehicle = response.meta.last_page
    state.pageVehicle = response.meta.current_page
  },

  SET_IS_LOADING_PERFORMANCE_VENDOR (state, isLoadingPerformanceVendor) {
    state.isLoadingPerformanceVendor = isLoadingPerformanceVendor
  },

  SET_IS_LOADING_PERFORMANCE_VENDOR_RATE (state, isLoadingPerformanceVendorRate) {
    state.isLoadingPerformanceVendorRate = isLoadingPerformanceVendorRate
  },

  SET_IS_LOADING_SHIPPING_STATUS_VEHICLE (state, isLoadingShippingStatusVehicle) {
    state.isLoadingShippingStatusVehicle = isLoadingShippingStatusVehicle
  },

  SET_ITEM_EXPORT_EXCEL (state, itemExportExcel) {
    state.itemExportExcel = itemExportExcel
  },

  SET_ITEMS_GEOFENCES (state, itemsGeofences) {
    state.itemsGeofences = itemsGeofences
    state.filteredItemsGeofences = itemsGeofences
  },

  SET_FILTER_GEOFENCE (state, searchKey: string) {
    state.filteredItemsGeofences = state.itemsGeofences.filter((e) => e.name?.toLowerCase().includes(searchKey.toLowerCase()))
  },

  SET_IS_LOADING_EXPORT_EXCEL (state, isLoadingExportExcel) {
    state.isLoadingExportExcel = isLoadingExportExcel
  },

  SET_IS_LOADING_EXPORT_REPORT_EXCEL (state, isLoadingExportReportExcel) {
    state.isLoadingExportReportExcel = isLoadingExportReportExcel
  },

  SET_IS_LOADING_EXPORT_ALERT_GEOFENCING (state, isLoadingExportAlertGeofencing) {
    state.isLoadingExportAlertGeofencing = isLoadingExportAlertGeofencing
  },

  SET_IS_LOADING_ALL_LIVE_TRACKING (state, isLoadingAllLiveTracking) {
    state.isLoadingAllLiveTracking = isLoadingAllLiveTracking
  },

  SET_ITEMS_ALL_LIVE_TRACKING (state, itemsAllLiveTracking) {
    state.itemsAllLiveTracking = itemsAllLiveTracking
  },

  SET_IS_LOADING_REPORT_TRANSPORTER (state, isLoadingReportTransporter) {
    state.isLoadingReportTransporter = isLoadingReportTransporter
  },

  SET_ITEMS_REPORT_TRANSPORTER (state, itemsReportTransporter) {
    state.itemsReportTransporter = itemsReportTransporter
  },

  SET_ITEMS_ALERT_GEOFENCING (state , response: any) {
    state.itemsAlertGeofencing = response.data
    state.totalPageAlertGeofencing = response.meta.last_page
    state.pageAlertGeofencing = response.meta.current_page
  },

  SET_IS_LOADING_ALERT_GEOFENCING (state, isLoadingAlertGeofencing: boolean) {
    state.isLoadingAlertGeofencing = isLoadingAlertGeofencing
  },

  SET_IS_LOADING_PERFORMANCE_DETAIL_ORDER (state, isLoadingPerformanceDetailOrder) {
    state.isLoadingPerformanceDetailOrder = isLoadingPerformanceDetailOrder
  },

  SET_ITEMS_PERFORMANCE_DETAIL_ORDER (state, response: any) {
    state.itemPerformanceDetailOrder = response.data
    state.totalPagePerformanceDetailOrder = response.meta.last_page
    state.pagePerformanceDetailOrder = response.meta.current_page
  },

  RESET_INCOMING_TRUCK(state) {
    state.itemIncomingTruck = []
    state.page = 1
    state.totalPages = 0
    state.totalData = 0
    state.isLoadingIncomingTRuck = false
  },

  RESET_REPORT_TRANSPORTER(state) {
    state.itemsReportTransporter = []
    state.isLoadingReportTransporter = false
  }

}

export default mutations

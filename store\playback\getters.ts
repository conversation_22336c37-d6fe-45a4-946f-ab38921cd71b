import { GetterTree } from 'vuex'
import { RootState } from '../index'
import { PlaybackState } from './state'

export const getters: GetterTree<PlaybackState, RootState> = {
  data (state) {
    const items = state.items.filter((p: any) => p.latitude !== '-' && p.longitude !== '-')
    return items
  },

  isLoading (state) {
    return state.isLoading
  },

  poiData (state) {
    const items = state.poiItems
    return items
  },

  isLoadingPoi (state) {
    return state.isLoadingPoi
  }
}

export default getters

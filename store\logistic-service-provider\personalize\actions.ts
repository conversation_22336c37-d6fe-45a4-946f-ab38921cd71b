import { ActionTree } from 'vuex'
import { LogisticServiceProviderPersonalizeState } from './state'
// import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<LogisticServiceProviderPersonalizeState, LogisticServiceProviderPersonalizeState> = {
  async getPersonalize ({ commit }, payload) {
    commit('SET_IS_LOADING', true)
    await this.$axios.get('v1/personalization', {
      params: {
        domain: payload.domain
      }
    }).then((response: any) => {
      commit('SET_PERSONALIZE', response.data.data)
    }).catch((_: any) => {

    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  }
}

export default actions

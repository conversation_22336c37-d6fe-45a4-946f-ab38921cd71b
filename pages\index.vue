<template>
  <v-row justify="center" align="center" />
</template>

<script>
import Vue from 'vue'

export default Vue.extend({
  name: 'IndexPage',

  head: {
    title: 'Logistic Service Integrator',
    meta: [
      { hid: 'description', name: 'description', content: 'Efficiency your Shipment Every Time With Logistic Service Integrator.' },
      { hid: 'og:title', property: 'og:title', content: 'Logistic Service Integrator' },
      { hid: 'og:description', property: 'og:description', content: 'Efficiency your Shipment Every Time With Logistic Service Integrator.' },
      { hid: 'og:image', property: 'og:image', content: 'https://lsi-dev.transtrack.id/meta-image.jpg' }
    ]
  },

  created () {
    this.$router.push('/login')
  }
})
</script>

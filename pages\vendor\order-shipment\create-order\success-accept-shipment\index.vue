<template>
  <v-container
    v-if="dataResponseAccept"
    fluid
    class="pa-5 pa-sm-10 d-flex fill-height"
  >
    <div class="ma-auto d-flex flex-column align-center" style="width: 600px">
      <animation-component
        :animation-data="require('~/assets/animations/success.json')"
        class="mb-10"
      />

      <h4 class="mb-6 text-center">
        {{ $t('vendorSuccessAcceptShipment.title') }}
        <span class="text-primary">{{ dataResponseAccept?.orders[0]?.identity }}</span>
      </h4>

      <p class="body-1 text-secondary mb-10 text-center">
        {{ $t('vendorSuccessAcceptShipment.description') }}
      </p>

      <v-row class="ma-0 ma-sm-n3" style="width: 100%">
        <v-col class="col-12 col-sm-6 pa-0 pa-sm-3 mb-5 mb-sm-0">
          <v-btn
            block
            x-large
            depressed
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="$router.push(localePath('/vendor/order-shipment/create-order'))"
          >
            {{ $t('vendorSuccessAcceptShipment.button_back') }}
          </v-btn>
        </v-col>

        <v-col class="col-12 col-sm-6 pa-0 pa-sm-3">
          <v-btn
            block
            x-large
            outlined
            color="primary"
            :loading="isLoadingDownload"
            class="subtitle-1 text-capitalize"
            @click="downloadDispatchNote"
          >
            <v-icon class="mr-2">
              mdi-file-download-outline
            </v-icon>
            {{ $t('vendorSuccessAcceptShipment.button_download') }}
          </v-btn>
        </v-col>
      </v-row>

      <v-btn
        x-large
        text
        :block="!$vuetify.breakpoint.smAndUp"
        color="primary"
        class="subtitle-1 text-capitalize mt-5"
        @click="
          $router.push(
            localePath(
              `/vendor/order-shipment/history-order/detail-history-order/${dataResponseAccept.id}`
            )
          )
        "
      >
        {{ $t('vendorSuccessAcceptShipment.button_detail') }}
      </v-btn>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import AnimationComponent from '~/components/AnimationComponent.vue'

export default Vue.extend({
  name: 'SuccessAcceptShipmentPage',

  components: { AnimationComponent },

  computed: {
    dataResponseAccept () {
      return this.$store.getters['shipment/dataResponseAccept']
    },

    isLoadingDownload (): boolean {
      return this.$store.getters['shipment/isLoadingDownload']
    }
  },

  watch: {
    dataResponseAccept: {
      handler (currentValue) {
        if (!currentValue) {
          this.$router.push('/vendor/order-shipment/create-order')
        }
      },
      immediate: true
    }
  },

  methods: {
    downloadDispatchNote () {
      this.$store.dispatch('shipment/downloadDispatchNote', {
        shipmentId: this.dataResponseAccept?.id,
        shipmentIdentity: this.dataResponseAccept?.identity
      })
    }
  }
})
</script>

<style scoped>

</style>

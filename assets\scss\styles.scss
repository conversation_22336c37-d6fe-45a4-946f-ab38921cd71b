@import "assets/scss/variables.scss";

.text-highlight-plate {
  background-color: $highlight-color;
}

.text-heading-5 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.text-heading-6 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.text-body {
  color: $text-body-color;
  font-size: $font-size-label;
  margin-bottom: $padding-sm-y;
}

.text-caption {
  color: $text-subtitle-color;
  font-size: $font-size-caption;
  margin-bottom: $padding-sm-y;
}

.text-info-body {
  color: $text-info-color !important;
  font-size: $font-size-label;
}

.item-drawer {
  margin-left: $padding-lg-y;
  margin-right: $padding-lg-y;
  border-radius: $border-radius;
}

// Layout

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.spacer-y-lg {
  margin-bottom: $padding-lg-y;
  margin-top: $padding-lg-y;
}

.spacer-y-2lg {
  margin-bottom: $padding-lg-y * 2;
  margin-top: $padding-lg-y * 2;
}


// Color
.bg-color {
  background-color: $background-color;
}

.bg-color-white {
  background-color: $white-color;
}

.bg-info-color {
  background-color: $background-info-color;
}

.bg-success-color {
  background-color: $background-success-color;
}

.bg-warning-color {
  background-color: $background-warning-color;
}

.text-primary {
  color: $text-primary-color;
}

.text-secondary {
  color: $text-subtitle-color;
}

.text-info {
  color: $text-info-color !important;
}

.text-success {
  color: $text-success-color;
}

.text-orange {
  color: $text-orange-color;
}

.brand-color {
  background-color: $brand-color;
}

.vtimepicker {
  height: 56px !important;
  border-radius: 4px !important;
  border-color:  rgba(0, 0, 0, 0.38) !important;
}

.vue__time-picker .dropdown ul li.active {
  background: red !important;
}

.vue__time-picker input.display-time {
  border: #0D0000;
}

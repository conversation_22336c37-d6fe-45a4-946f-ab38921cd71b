import { GetterTree } from 'vuex'
import { RootState } from '../../index'
import { VehicleFeatureState } from './state'

export const getters: GetterTree<VehicleFeatureState, RootState> = {
  data (state) {
    return {
      items: state.items,
      totalPage: state.totalPage,
      page: state.page
    }
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingFormAndList (state) {
    return state.isLoading && state.isLoadingForm
  },

  selectedFeatures (state) {
    return state.selectedFeatures
  }
}

export default getters

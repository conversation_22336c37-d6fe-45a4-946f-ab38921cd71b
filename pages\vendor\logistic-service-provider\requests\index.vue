<template>
  <v-container fluid class="pa-0 px-10 pb-10 d-flex flex-column align-end">
    <v-container fluid class="mb-5 pa-0 d-flex justify-space-between align-start flex-sm-row flex-column">
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-filter-change="getDataLSPs({ filter: $event, page: $route.query?.page })"
        @on-search-icon-click="getDataLSPs({ searchKey: $event })"
      />
    </v-container>
    <v-container fluid class="pa-0 mb-10">
      <v-row v-if="isLoadingLSP" class="ma-n5">
        <v-col v-for="i in 3" :key="i" class="pa-5 col-lg-4 col-sm-6 col-12">
          <v-sheet class="pa-5 overflow-hidden" height="260">
            <div class="d-flex">
              <div class="d-flex flex-row align-center" style="width: 100%">
                <v-skeleton-loader type="image" height="50" width="50" />
                <div class="d-flex flex-column" style="width: 100%">
                  <v-skeleton-loader type="list-item-two-line" width="100%"/>
                </div>
              </div>
            </div>
            <v-skeleton-loader type="text" width="100" class="mt-5" />
            <v-skeleton-loader type="text" />
            <v-skeleton-loader type="image" width="210" height="50" class="mt-5" />
          </v-sheet>
        </v-col>
      </v-row>
      <v-row v-else class="ma-n5">
        <v-col
          v-for="(lsp, i) in dataLSPs.items"
          :key="lsp.id"
          class="pa-5 col-lg-4 col-sm-6 col-12"
        >
          <request-lsa-card-item
            :logistic-service-provider="lsp"
            :is-loading-form="isLoadingFormLSP"
            :index="parseFloat(i)"
            :dialog="dialogPropose[i]"
            @on-click-send="proposeCollaboration({ id: lsp.id, i })"
            @on-open-dialog="$set(dialogPropose, i, true)"
            @on-close-dialog="$set(dialogPropose, i, false)"
          />
        </v-col>
      </v-row>
    </v-container>

    <pagination-component
      :page="dataLSPs.page"
      :total-page="dataLSPs.totalPage"
      page-id="page"
      @on-change-page="getDataLSPs({
        page: $event,
        filter: {
          sortColumn: $route.query?.sort_column,
          sortType: $route.query?.sort_type
        }
      })"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import RequestLsaCardItem from '~/components/vendor/RequestLsaCardItem.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'RequestLSAPage',

  components: { RequestLsaCardItem, PaginationComponent },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      },
      address: {
        label: 'Address',
        value: 'address'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogPropose: [] as Array<Boolean>
  }),

  computed: {
    dataLSPs (): any {
      return this.$store.getters['vendor/logistic-service-provider/data']
    },
    isLoadingLSP (): any {
      return this.$store.getters['vendor/logistic-service-provider/isLoading']
    },
    isLoadingFormLSP (): any {
      return this.$store.getters['vendor/logistic-service-provider/isLoadingForm']
    }
  },

  mounted () {
    this.getDataLSPs({
      page: this.$route.query?.page as string
    })
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Cooperation Logistic Provider')
  },

  methods: {
    getDataLSPs ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('vendor/logistic-service-provider/getItems', {
        searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        filterColumns: 'vendors',
        filterKeys: '<_null>',
        page
      })
    },
    async proposeCollaboration ({
      id = '',
      i = 0
    }) {
      const response = await this.$store.dispatch('vendor/logistic-service-provider/proposeCollaboration', {
        idLSP: id
      })
      if (response) {
        this.$set(this.dialogPropose, i, false)

        this.getDataLSPs({})
      }
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #EF3434 !important;
  color: white !important;
}
</style>

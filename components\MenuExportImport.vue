<template>
  <v-menu
    offset-y
    :close-on-content-click="false"
    transition="slide-y-transition"
  >
    <template #activator="{ on, attrs}">
      <v-btn
        v-bind="attrs"
        x-large
        depressed
        color="white"
        class="subtitle-1 text-capitalize mr-5 custom-btn"
        v-on="on"
      >
        <v-icon size="24" class="mr-2 custom-icon">
          mdi-file
        </v-icon>
        Data
      </v-btn>
    </template>

    <v-container class="py-4 px-3 d-flex flex-column white">
<!--      <v-btn-->
<!--        x-large-->
<!--        depressed-->
<!--        color="white"-->
<!--        class="mb-2 subtitle-1 text-capitalize"-->
<!--      >-->
<!--        <v-icon size="24" class="mr-2">-->
<!--          mdi-file-download-->
<!--        </v-icon>-->
<!--        Export-->
<!--      </v-btn>-->
      <v-btn
        x-large
        depressed
        color="white"
        class="subtitle-1 text-capitalize"
        @click="
          queryImportType
            ? $router.push({ path: routeImport, query: { type: queryImportType }})
            : $router.push({ path: routeImport })
        "
      >
        <v-icon size="24" class="mr-2">
          mdi-file-upload
        </v-icon>
        Import
      </v-btn>
    </v-container>
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'MenuExportImport',

  props: {
    routeImport: {
      type: String,
      default: ''
    },
    queryImportType: {
      type: String,
      default: ''
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: .28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: var(--v-primary-base) !important;
  color: white !important;
}
</style>

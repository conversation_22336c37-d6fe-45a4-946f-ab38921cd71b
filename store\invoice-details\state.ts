import { InvoiceDetail } from '~/types/invoice'

export interface InvoiceDetailsState {
  isLoading: boolean,
  isLoadingDetail: boolean,
  isLoadingDownloadInvoice: boolean,
  blobInvoice: string | null,
  items: InvoiceDetail[],
  item: InvoiceDetail | null,
  totalPage: number,
  page: number
}

export const state = () : InvoiceDetailsState => ({
  isLoading: false,
  isLoadingDetail: false,
  isLoadingDownloadInvoice: false,
  blobInvoice: null,
  items: [],
  item: null,
  totalPage: 1,
  page: 1
})

export default state

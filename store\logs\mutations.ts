import { MutationTree } from 'vuex'
import { LogsState } from './state'

export const mutations: MutationTree<LogsState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_REFRESH_WB (state, isLoadingRefreshWB) {
    state.isLoadingRefreshWB = isLoadingRefreshWB
  },

  SET_ITEM_LOADING(state, { id, isLoading }: { id: string, isLoading: boolean }) {
    state.loadingItems = {
      ...state.loadingItems,
      [id]: isLoading
    }
  },

  RESET_ITEMS(state) {
    state.items = []
    state.isLoading = false
    state.totalPage = 0
    state.page = 0
  }
}

export default mutations

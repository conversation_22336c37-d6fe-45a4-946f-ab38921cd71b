import { ActionTree } from 'vuex'
import { PasswordState } from './state'
import { RootState } from '~/store'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<PasswordState, RootState> = {

  async sendEmail ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.post('/v1/users/forgot-password', {
      email: payload.email
    })
      .then((response: any) => {
        commit('SET_ITEM', response.data.data)
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async changePassword ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.post('/v1/users/change-passwords', {
      password: payload.password,
      password_confirmation: payload.password_confirmation,
      token: payload.token
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        if (error.response.data.message === 'token doesnt exist') {
          this.$router.push('/forgot-password/link-expired')
        } else {
          exceptionHandler(error, this)
        }
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

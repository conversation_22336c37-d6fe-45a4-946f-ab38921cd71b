<template>
  <v-card outlined class="pa-5">
    <div class="mb-6 d-flex align-start justify-space-between">
      <div class="d-flex">
        <v-img
          :src="require(`~/assets/images/placeholder-company-logo.svg`)"
          aspect-ratio="1"
          min-width="50"
          max-width="50"
          class="mr-5"
          contain
        />

        <v-card-title class="pa-0 d-flex flex-column align-start">
          <h4 class="mb-2">
            {{ logisticServiceProvider.name }}
          </h4>
        </v-card-title>
      </div>
    </div>

    <v-row class="ma-0">
      <v-col class="pa-0 mr-4">
        <v-card-text class="pa-0">
          <p class="caption font-weight-medium mb-2">
            {{ $t('requestLsaCardItem.location') }}
          </p>
          <p class="subtitle-1 ma-0">
            {{ logisticServiceProvider.address }}
          </p>
        </v-card-text>
      </v-col>
    </v-row>

    <div class="py-5">
      <v-dialog
        v-model="dialog"
        max-width="500px"
      >
        <template #activator="{ on, attrs }">
          <v-btn
            color="primary"
            outlined
            x-large
            class="text-capitalize font-weight-medium"
            v-bind="attrs"
            v-on="on"
            @click="$emit('on-open-dialog')"
          >
            {{ $t('requestLsaCardItem.send_cooperation') }}
          </v-btn>
        </template>
        <v-card class="pa-10">
          <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
            <h4>
              {{ $t('requestLsaCardItem.send_cooperation') }}
            </h4>

            <v-icon color="black" @click="$emit('on-close-dialog')">
              mdi-close
            </v-icon>
          </v-card-title>

          <v-card-text class="text-center black--text px-0">
            {{ $t('requestLsaCardItem.confirm_request_collaboration') }}  <span class="font-weight-bold">{{ logisticServiceProvider.name }}?</span>
          </v-card-text>

          <v-card-actions class="mt-10 pa-0 d-flex justify-start">
            <v-row class="ma-0">
              <v-col class="mr-5 pa-0">
                <v-btn
                  elevation="0"
                  color="primary"
                  class="text-capitalize"
                  x-large
                  block
                  :loading="isLoadingForm"
                  @click="onClickSend"
                >
                  {{ $t('requestLsaCardItem.send_cooperation') }}
                </v-btn>
              </v-col>
              <v-col class="pa-0">
                <v-btn
                  elevation="0"
                  outlined
                  color="primary"
                  class="text-capitalize ma-0"
                  x-large
                  block
                  @click="$emit('on-close-dialog')"
                >
                  {{ $t('requestLsaCardItem.cancel') }}
                </v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { LogisticsServiceProvider } from '~/types/user'

export default Vue.extend({
  name: 'RequestLsaCardItem',

  props: {
    logisticServiceProvider: {
      type: Object as () => LogisticsServiceProvider,
      required: true
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    }
  },

  data: () => ({
    sendCooperationDialog: false
  }),

  methods: {
    onClickSend () {
      this.$emit('on-click-send')
    }
  }

})
</script>

<style scoped lang="scss"> </style>

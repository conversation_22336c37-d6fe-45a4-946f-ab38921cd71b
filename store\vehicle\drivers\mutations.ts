import { MutationTree } from 'vuex'
import { VehicleDriversState } from './state'

export const mutations: MutationTree<VehicleDriversState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },
  SET_RESULT_FILTER (state, response: any) {
    state.itemFilters = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  }
}

export default mutations

import { MutationTree } from 'vuex'
import { InvoiceDetailsState } from './state'

export const mutations: MutationTree<InvoiceDetailsState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_ITEM (state, item: any) {
    state.item = item
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_DETAIL (state, isLoadingDetail) {
    state.isLoadingDetail = isLoadingDetail
  },

  SET_IS_LOADING_DOWNLOAD_INVOICE (state, isLoadingDownloadInvoice) {
    state.isLoadingDownloadInvoice = isLoadingDownloadInvoice
  },

  SET_BLOB_INVOICE (state, blobInvoice) {
    state.blobInvoice = blobInvoice
  }
}

export default mutations

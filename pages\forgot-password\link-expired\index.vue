<template>
  <v-container
    fluid
    class="pa-5 pa-sm-10 d-flex fill-height"
  >
    <div class="ma-auto d-flex flex-column align-center" style="width: 400px">
      <v-row align="center" justify="center">
        <v-img
          :src="require(`~/assets/images/logo-lsi.png`)"
          max-width="60"
          contain
        >
          <template #placeholder>
            <v-img
              min-width="80"
              max-width="80"
              aspect-ratio="1"
              contain
              :src="require(`~/assets/images/placeholder-company-logo.svg`)"
            />
          </template>
        </v-img>
        <div class="ml-4 mr-4" />
        <div class="text-heading-6 spacer-y-lg" style="max-width: 9em">
          Logistic Service Integrator
        </div>
      </v-row>
      <v-img
        :src="require(`~/assets/images/reset-password.png`)"
        min-width="400"
        max-width="400"
        aspect-ratio="1"
        contain
      />
      <h3>
        Forgot Password Link Expired
      </h3>
      <p class="body-1 text-secondary mb-10 text-center mt-2">
        Your Forgot Password link is expired, You can do request forgot password to get new link.
      </p>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'LinkExpiredPage'

})
</script>

<style scoped>

</style>

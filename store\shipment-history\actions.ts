import { ActionTree } from 'vuex'
import { RootState } from '../index'
import { ShipmentHistoryState } from './state'
import { exceptionHand<PERSON> } from '~/utils/functions'

export const actions: ActionTree<ShipmentHistoryState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/shipment-history', {
        params: {
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          search_columns:
            payload.searchColumns == null ? '' : payload.searchColumns,
          sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
          sort_type: payload.sortType == null ? '' : payload.sortType,
          filter_columns: payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          page: payload.page == null ? '' : payload.page,
          entries: payload.page == null ? '15' : payload.entries
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

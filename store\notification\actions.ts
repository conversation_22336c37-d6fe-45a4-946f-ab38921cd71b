import { ActionTree } from 'vuex'
import { NotificationState } from './state'
import { exception<PERSON><PERSON><PERSON> } from '~/utils/functions'

export const actions: ActionTree<NotificationState, NotificationState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/notifications', {
      params: {
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns: payload.searchColumns == null ? '' : payload.searchColumns,
        sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
        sort_type: payload.sortType == null ? '' : payload.sortType,
        filter_columns: payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page == null ? '' : payload.page,
        entries: payload.page == null ? '15' : payload.entries
      }
    })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHand<PERSON>(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

<template>
  <v-dialog
    v-model="dialog"
    persistent
    max-width="540px"
  >
    <template
      #activator="{on, attrs}"
    >
      <div v-if="role !== 'LOGISTIC_SERVICE_PROVIDER'">
        <v-btn
          v-if="isReadySendShipment"
          v-bind="attrs"
          elevation="0"
          color="primary"
          :disabled="isDisabled"
          class="d-flex align-center text-capitalize"
          x-large
          v-on="on"
          @click="$emit('on-open-dialog')"
        >
          <v-icon class="mr-3">
            mdi-send
          </v-icon>
          <p class="ma-0 subtitle-1">
            {{ $t('sendOrderShipmentDialog.button_send_activator') }}
          </p>
        </v-btn>

        <v-btn
          v-else
          elevation="0"
          color="primary"
          class="d-flex align-center text-capitalize"
          x-large
          disabled
        >
          <v-icon class="mr-3">
            mdi-send
          </v-icon>
          <p class="ma-0 subtitle-1">
            {{ $t('sendOrderShipmentDialog.button_send_activator') }}
          </p>
        </v-btn>
      </div>
      <div v-else>
        <v-btn
          v-if="isReadySendShipment"
          v-bind="attrs"
          elevation="0"
          color="primary"
          class="d-flex align-center text-capitalize"
          x-large
          v-on="on"
          @click="$emit('on-open-dialog')"
        >
          <v-icon class="mr-3">
            mdi-send
          </v-icon>
          <p class="ma-0 subtitle-1">
            {{ $t('sendOrderShipmentDialog.button_create_activator') }}
          </p>
        </v-btn>

        <v-btn
          v-else
          elevation="0"
          color="primary"
          class="d-flex align-center text-capitalize"
          x-large
          disabled
        >
          <v-icon class="mr-3">
            mdi-send
          </v-icon>
          <p class="ma-0 subtitle-1">
            {{ $t('sendOrderShipmentDialog.button_create_activator') }}
          </p>
        </v-btn>
      </div>
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-form ref="form">
        <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
          <h4 v-if="role !== 'LOGISTIC_SERVICE_PROVIDER'">
            {{ $t('sendOrderShipmentDialog.dialog_send_title') }}
          </h4>
          <h4 v-else>
            {{ $t('sendOrderShipmentDialog.dialog_create_title') }}
          </h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <p class="body-1 mb-4 black--text">
          {{ $t('sendOrderShipmentDialog.text_note') }}
        </p>

        <v-textarea
          v-model="form.note"
          outlined
          :label="$t('sendOrderShipmentDialog.label_note')"
          hide-details
          class="mb-5"
          no-resize
        />

        <p v-if="role !== 'LOGISTIC_SERVICE_PROVIDER'" class="body-1 mb-4 black--text">
          {{ $t('sendOrderShipmentDialog.text_send') }}
        </p>
        <p v-else class="body-1 mb-4 black--text">
          {{ $t('sendOrderShipmentDialog.text_create') }}
        </p>

        <v-card-actions class="pa-0">
          <v-row class="ma-0">
            <v-col class="mr-sm-5 mb-sm-0 mb-5 pa-0 col-sm-6 col-12">
              <v-btn
                v-if="role !== 'LOGISTIC_SERVICE_PROVIDER'"
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                :loading="isLoadingForm"
                @click="onClickSend"
              >
                {{ $t('sendOrderShipmentDialog.button_send') }}
              </v-btn>
              <v-btn
                v-else
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                :loading="isLoadingForm"
                @click="onClickSend"
              >
                {{ $t('sendOrderShipmentDialog.button_create') }}
              </v-btn>
            </v-col>
            <v-col class="pa-0">
              <v-btn
                elevation="0"
                outlined
                color="primary"
                class="text-capitalize ma-0"
                x-large
                block
                @click="$emit('on-close-dialog')"
              >
                {{ $t('sendOrderShipmentDialog.button_cancel') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'SendOrderShipmentDialog',

  props: {
    pickUpOrderExist: {
      type: Boolean,
      default: false
    },
    dropOffOrderExist: {
      type: Boolean,
      default: false
    },
    isReadySendShipment: {
      type: Boolean,
      default: false
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    },
    clearForm: {
      type: Boolean,
      default: false
    },
    isLoadingOrder: {
      type: Boolean,
      default: false
    },
    isLoadingSubOrder: {
      type: Boolean,
      default: false
    },
    isLoadingProduct: {
      type: Boolean,
      default: false
    },
    isLoadingLocation: {
      type: Boolean,
      default: false
    },
    isLoadingDetailOrder: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    form: {
      note: ''
    }
  }),

  computed: {
    isDisabled (): boolean {
      return this.isLoadingOrder ||
        this.isLoadingSubOrder ||
        this.isLoadingProduct ||
        this.isLoadingLocation ||
        this.isLoadingDetailOrder
    },
    data (): any {
      if (this.role === 'LOGISTIC_SERVICE_PROVIDER') {
        return this.$store.getters['profile/logisticServiceProvider']
      }
      return null
    },
    role () {
      const user = this.$auth.user as any

      return user?.data?.role
    }

  },

  watch: {
    clearForm () {
      if (this.clearForm) {
        const form = this.$refs.form as HTMLFormElement
        form.reset()
      }
    }
  },

  methods: {
    onClickSend () {
      this.$emit('on-click-send', this.form)
    }
  }
})
</script>

<style lang="scss" scoped> </style>

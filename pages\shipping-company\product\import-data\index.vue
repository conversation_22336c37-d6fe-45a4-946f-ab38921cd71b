<template>
  <v-container fluid class="pa-0 px-5 px-sm-10 mb-10">
    <v-btn
      plain
      class="text-capitalize subtitle-1 mb-6"
      @click="$router.back()"
    >
      <v-icon class="mr-2">
        mdi-chevron-left
      </v-icon>
      Back to List
    </v-btn>

    <import-data-component
      type="PRODUCT"
      :import-keys="productKeys"
      @on-change-form-values="formValues = $event"
    >
      <template #footer-bulk-import>
        <v-row class="ma-0 mx-n3">
          <v-col class="col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              depressed
              color="primary"
              :loading="isLoadingForm"
              :disabled="formValues?.length === 0"
              class="subtitle-1 text-capitalize"
              @click="onClickImport"
            >
              Import Data
            </v-btn>
          </v-col>
          <v-col class="col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              outlined
              color="primary"
              class="subtitle-1 text-capitalize"
              @click="$router.back()"
            >
              Cancel
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </import-data-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import ImportDataComponent from '~/components/ImportDataComponent.vue'
import { ImportKey } from '~/types/import-key'

export default Vue.extend({
  name: 'ImportDataPage',

  components: {
    ImportDataComponent
  },

  layout: 'shipping-company/body',

  middleware: ['auth', 'is-shipping-company'],

  data: () => ({
    formValues: [] as object[]
  }),

  computed: {
    shipmentCompanyId (): string {
      return this.$auth.$state.user.data.shipment_company_id
    },

    isLoading (): boolean {
      return this.$store.getters['shipping-company/product/isLoading']
    },

    isLoadingForm (): boolean {
      return this.$store.getters['shipping-company/product/isLoadingForm']
    },

    productKeys (): ImportKey[] {
      return this.$store.getters['shipping-company/product/dataItemsProductKey']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Import Data')
  },

  mounted () {
    this.getProductKeys()
  },

  methods: {
    getProductKeys () {
      this.$store.dispatch('shipping-company/product/getItemsProductKey')
    },

    async onClickImport () {
      const response = await this.$store.dispatch('shipping-company/product/importProducts', {
        products: this.formValues,
        shipmentCompanyId: this.shipmentCompanyId
      })

      if (response) {
        this.$router.back()
      }
    }
  }
})
</script>

<style scoped> </style>

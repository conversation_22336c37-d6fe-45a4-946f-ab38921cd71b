module.exports = {
  history_order_shipment: 'Riwayat Order Shipment',
  search: 'Cari',
  invoice_to_sc: '<PERSON>ihan ke Product Owner',
  review_order_shipment: 'Tinjauan Order Shipment',
  detail_vendor: 'Detail Vendor',
  estimated_distance: 'Estimasi Jarak',
  receiver: '<PERSON><PERSON><PERSON>',
  status: 'Status',
  photo: 'Foto',
  features: 'Fitur',
  detail_invoice: 'Detail Tagihan',
  select_track: 'Pilih Rute',
  from: 'Dari',
  to: 'Kepada',
  fuel: 'Nominal',
  invoice_from_vendor: 'Tagihan dari Vendor',
  invoice_number: 'Nomor Tagihan',
  select_order: 'Pilih Order',
  identity_product: 'Identitas Produk',
  product: 'Produk',
  quantity: 'Kuantitas',
  weight: 'Berat',
  estimation: 'Estimasi',
  operational_time: 'Waktu Operational',
  location: 'Lokasi',
  select_vehicle: 'Pilih Kendaraan',
  sub_order_your_choice: 'Suborder yang anda pilih',
  total_weight: 'Total Berat',
  total_length: 'Total Panjang',
  total_width: 'Total Lebar',
  total_height: 'Total Tinggi',
  shipping_cost: 'Biaya <PERSON>giriman',
  add_additional_fee: 'Tambahkan Biaya Tambahan',
  no_additional_fee: 'Tidak ada tambahan biaya',
  the_amount_that_must_be_paid: 'Jumlah yang harus dibayar',
  the_total_amount_that_sc_must_be_paid: 'Jumlah total yang harus dibayar Product Owner',
  the_total_amount_that_lsp_must_be_paid: 'Jumlah total Logistic Provider yang harus dibayar',
  back_to_history: 'Kembali ke riwayat',
  back_to_invoice: 'Kembali ke tagihan',
  download_invoice: 'Unduh Tagihan',
  order_number: 'Nomor Order',
  back_to_list: 'Kembali ke daftar',
  created_invoice_lsp_text: 'Tagihan yang anda buat untuk Logistic Provider',
  created_invoice_sc_text: 'Tagihan yang anda buat untuk Product Owner',
  distance: 'Jarak',
  invoice_empty_title: 'Anda belum memiliki Tagihan',
  invoice_empty_desc: 'Sekarang anda belum memiliki riwayat tagihan pada order shipment',
  noted: 'Noted',
}

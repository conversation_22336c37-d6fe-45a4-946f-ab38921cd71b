<template>
  <v-dialog v-model="dialog" persistent max-width="640px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Add Order Number</h4>

          <v-icon color="black" @click="$emit('on-close-dialog'), skipOrder()">
            mdi-close
          </v-icon>
        </v-card-title>

        <v-col class="pa-0" cols="12">
          <custom-text-field
            v-model="identityNumber"
            depressed
            class="mr-2"
            :hint="'Input Order Number'"
            color="primary"
          >
            {{ $t('lspCreateShipment.save_customer') }}
          </custom-text-field>
        </v-col>
      </v-form>
      <div class="d-flex mt-2">
        <v-btn
          color="primary"
          class="mr-2 col-2"
          depressed
          :disabled="identityNumber === null"
          @click="saveNumberDialog"
        >
          {{ $t('userFormItem.button_save') }}
        </v-btn>
        <v-btn
          outlined
          depressed
          color="primary"
          @click="skipOrder"
        >
          {{ $t('lspCreateShipment.skip') }}
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'SelectShipmentCompanyDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      identityNumber: null

    }
  },

  computed: {
    isLoadingCustomers (): Boolean {
      return this.$store.getters['shipping-company/isLoading']
    }
  },

  methods: {
    async saveNumberDialog () {
      const response = await this.$store.dispatch('shipping-company/order-shipment/create-order/createOrder', {
        identityNumber: this.identityNumber,
        idSc: this.id
      })
      if (response) {
        this.$emit('on-close-dialog')
      }
    },
    skipOrder () {
      if (!this.identityNumber) {
        this.$store.dispatch('shipping-company/order-shipment/create-order/createOrder', {
          identityNumber: this.identityNumber,
          idSc: this.id
        })
      } else {
        this.$store.dispatch('order/getOrders', {
          filterColumns: 'shipment_company_id,status',
          filterKeys: `${this.id},DRAFT`,
          entries: -1
        })
      }
      this.$emit('on-close-dialog')
    }
  }
})
</script>

<style scoped>

</style>

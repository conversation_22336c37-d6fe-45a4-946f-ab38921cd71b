<template>
  <v-row align="center" justify="center">
    <v-img
      :src="image"
      max-width="80"
      contain
    >
      <template #placeholder>
        <v-img
          min-width="80"
          max-width="80"
          aspect-ratio="1"
          contain
          :src="require(`~/assets/images/placeholder-company-logo.svg`)"
        />
      </template>
    </v-img>
    <div class="ml-4 mr-4" />
    <div class="text-heading-6 spacer-y-lg" style="max-width: 10em">
      {{ text }}
    </div>
  </v-row>
</template>

<script>
export default {
  name: 'CompanyLogo',

  props: {
    image: {
      type: String,
      default: '~/assets/images/placeholder-company-logo.svg'
    },

    text: {
      type: String,
      default: 'Logistic Service Integrator'
    }
  }
}
</script>

<style scoped>

</style>

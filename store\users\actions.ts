import { ActionTree } from 'vuex'
import { RootState } from '..'
import { UserState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<UserState, RootState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    if (payload.filterColumns === undefined) {
      payload.filterColumns = ''
    } else if (payload.filterColumns.length > 0) {
      payload.filterColumns += ','
    }

    switch (payload.role) {
      case 'LOGISTIC_SERVICE_PROVIDER':
        payload.filterColumns += 'logistics_service_provider_id'
        break
      case 'SHIPMENT_COMPANY':
        payload.filterColumns += 'shipment_company_id'
        break
      case 'VENDOR':
        payload.filterColumns += 'vendor_id'
        break
      case 'DRIVER':
        payload.filterColumns += 'driver_id'
        break
      default:
        break
    }

    this.$axios
      .get('/v1/users', {
        params: {
          search_key: payload.searchKey == null ? '' : payload.searchKey,
          search_columns:
            payload.searchColumns == null
              ? 'name,email,phone_number'
              : payload.searchColumns,
          sort_column: payload.sortColumn == null ? 'name' : payload.sortColumn,
          sort_type: payload.sortType == null ? 'ASC' : payload.sortType,
          filter_columns: payload.filterColumns,
          filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
          page: payload.page == null ? '' : payload.page,
          entries: payload?.entries ? payload.entries : 9
        }
      })
      .then((response: any) => {
        commit('SET_RESULT', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    formData.append('name', payload.value.name)
    formData.append('phone_number', payload.value.phone)
    formData.append('phone_country_code', payload.value.phoneCountryCode)
    formData.append('email', payload.value.email)
    formData.append('password', payload.value.password)
    formData.append('password_confirmation', payload.value.passwordConfirmation)
    formData.append('role', payload.role)

    if (payload.value.avatar) {
      formData.append('avatar', payload.value.avatar)
    }

    switch (payload.role) {
      case 'LOGISTIC_SERVICE_PROVIDER':
        formData.append('logistics_service_provider_id', payload.selectedId)
        break
      case 'SHIPMENT_COMPANY':
        formData.append('shipment_company_id', payload.selectedId)
        break
      case 'VENDOR':
        formData.append('vendor_id', payload.selectedId)
        break
      case 'DRIVER':
        formData.append('driver_id', payload.selectedId)
        break
      default:
        break
    }

    return await this.$axios
      .post('/v1/users', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async editItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    if (payload.nationalIdentity) {
      formData.append('national_identity', payload.nationalIdentity)
    }
    formData.append('name', payload.value.name)
    formData.append('phone_number', payload.value.phone)
    formData.append('phone_country_code', payload.value.phoneCountryCode)
    formData.append('email', payload.value.email)
    formData.append('password', payload.value.password)
    formData.append('password_confirmation', payload.value.passwordConfirmation)
    formData.append('_method', 'PUT')

    if (payload.value.avatar && typeof payload.value.avatar !== 'string') {
      formData.append('avatar', payload.value.avatar)
    }

    return await this.$axios
      .post('/v1/users/' + payload.id, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async editProfile ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()
    if (payload.nationalIdentity) {
      formData.append('national_identity', payload.nationalIdentity)
    }

    // Handle both regular edit and email change scenarios
    if (payload.value) {
      // Regular edit scenario
      formData.append('name', payload.value.name)
      formData.append('phone_number', payload.value.phone)
      formData.append('phone_country_code', payload.value.phoneCountryCode)
      if (payload.value.email) {
        formData.append('email', payload.value.email)
      }

      if (payload.value.password !== '') {
        formData.append('password', payload.value.password)

        if (payload.value.passwordConfirmation !== '') {
          formData.append('password_confirm', payload.value.passwordConfirmation)
        }

        if (payload.value.passwordOld !== '') {
          formData.append('password_old', payload.value.passwordOld)
        }
      }

      if (payload.value.avatar && typeof payload.value.avatar !== 'string') {
        formData.append('avatar', payload.value.avatar)
      }
    } else {
      // Email change scenario
      formData.append('name', payload.name)
      formData.append('phone_number', payload.phone)
      formData.append('phone_country_code', payload.phoneCountryCode)
      formData.append('email', payload.email)
    }

    if (payload.value.otp) {
      formData.append('otp', payload.value.otp)
    }

    if (payload.value.newEmail) {
      formData.append('new_email', payload.value.newEmail)
    }

    // Add role if provided
    if (payload.role) {
      formData.append('role', payload.role)
    }

    formData.append('_method', 'PUT')

    // Handle role-specific IDs
    if (payload.selectedId) {
      switch (payload.role) {
        case 'LOGISTIC_SERVICE_PROVIDER':
          formData.append('logistics_service_provider_id', payload.selectedId)
          break
        case 'SHIPMENT_COMPANY':
          formData.append('shipment_company_id', payload.selectedId)
          break
        case 'VENDOR':
          formData.append('vendor_id', payload.selectedId)
          break
        case 'DRIVER':
          formData.append('driver_id', payload.selectedId)
          break
        default:
          break
      }
    }

    return await this.$axios
      .post('/v1/auth/profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        if (payload.isUpdateProfile) {
          dispatch('profile/account-setting/getItem', {}, { root: true })
        }
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .delete('/v1/users/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async removeAvatar ({ commit }, payload: any) {
    commit('SET_IS_LOADING_FORM_CLEAR_IMAGE', true)

    await this.$axios.delete('/v1/users/' + payload + '/avatar')
      .then((response: any) => {
        toastSuccess(response.data.message, this)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM_CLEAR_IMAGE', false)
      })
  },

  async disableUser ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.put('/v1/settings/set-disable-user', { value: payload.value })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
      }).catch((error: any) => {
        exceptionHandler(error, this)
      }).finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async enableUser ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.put('/v1/users/enable-user/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async getLogs ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    await this.$axios.get('/v1/logs', {
      params: {
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns:
        payload.searchColumns == null
          ? 'user.name'
          : payload.searchColumns,
        sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
        sort_type: payload.sortType == null ? 'DESC' : payload.sortType,
        filter_date_column: payload.filterDateColumn ?? '',
        filter_date_start: payload.filterDateStart ?? '',
        filter_date_end: payload.filterDateEnd ?? '',
        filter_columns: payload.filterColumns ?? '',
        filter_keys: payload?.filterKeys ?? '',
        page: payload.page == null ? '' : payload.page,
        entries: payload?.entries ?? ''
      }
    })
      .then((response: any) => {
        commit('SET_RESULT_LOGS', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async getPermissionUsers ({ commit }) {
    commit('SET_IS_LOADING', true)

    await this.$axios.get('/v1/users/list-permission', {
      params: {
        entries: -1
      }
    })
      .then((response: any) => {
        commit('SET_RESULT_PERMISSION', response.data)
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async updatePermission ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.put('/v1/users/update-permission/' + payload.id, {
      permission_id: payload.permission_id
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async endSession ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    return await this.$axios.post('/v1/users/' + payload.id + '/end-session')
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async requestOtp ({ commit }) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.post('/v1/verify/request')
      .then((response: any) => {
        toastSuccess(response.data.message || 'OTP sent successfully', this)
        commit('SET_IS_LOADING_FORM', false)

        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        commit('SET_IS_LOADING_FORM', false)
        return false
      })
  },


}

export default actions

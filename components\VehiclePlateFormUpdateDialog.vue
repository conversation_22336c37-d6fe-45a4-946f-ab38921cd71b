<template>
  <v-dialog v-model="dialog" persistent max-width="640px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>{{ $t('vehiclePlateFormUpdateDialog.update') }}</h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <v-tabs v-model="tab" grow class="mb-10">
          <v-tab class="text-capitalize">
            <p class="subtitle-1 ma-0">
              {{ $t('vehiclePlateFormUpdateDialog.tab_move') }}
            </p>
          </v-tab>
          <v-tab class="text-capitalize">
            <p class="subtitle-1 ma-0">
              {{ $t('vehiclePlateFormUpdateDialog.tab_update') }}
            </p>
          </v-tab>
        </v-tabs>

        <v-tabs-items v-model="tab">
          <v-tab-item>
            <vehicle-plate-form
              form-type="MOVE"
              :tab="tab"
              :plate-numbers="plateNumbers"
              :list-vehicle="listVehicle"
              :selected-vehicle-id="selectedVehicleId"
              @on-click-move="updatePlateNumber($event)"
              @on-click-cancel="$emit('on-close-dialog')"
            />
          </v-tab-item>

          <v-tab-item>
            <vehicle-plate-form
              form-type="UPDATE"
              :tab="tab"
              :plate-numbers="plateNumbers"
              :list-vehicle="listVehicle"
              :selected-vehicle-id="selectedVehicleId"
              @on-click-update="updatePlateNumber($event)"
              @on-click-cancel="$emit('on-close-dialog')"
            />
          </v-tab-item>
        </v-tabs-items>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Vehicle, VehicleDetail } from '~/types/vehicle'

export default Vue.extend({
  name: 'VehiclePlateFormUpdateDialog',

  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    plateNumbers: {
      type: Array as () => VehicleDetail[],
      default: () => ([])
    },
    selectedVehicleId: {
      type: String,
      default: ''
    },
    vendorId: {
      type: String,
      default: ''
    },
    listVehicle: {
      type: Array as () => Vehicle[],
      default: () => ([])
    },
  },

  data: () => ({
    tab: 0 as number,
    formUpdate: {
      plateNumberId: '' as string,
      plateNumber: '' as string,
      driverId: '' as string,
      vehicleId: '' as string
    }
  }),

  computed: {
    entriesMode (): any {
      return this.$store.getters['layout/entriesMode']
    }
  },

  methods: {
    async updatePlateNumber (formValues: any) {
      const response = await this.$store.dispatch('vehicle/details/editItem', {
        vendorId: this.vendorId,
        formValues,
        entries: this.entriesMode
      })

      if (response) {
        this.$emit('on-close-dialog')
        this.$emit('on-success-update')
      }
    }
  }
})
</script>

<style scoped lang="scss"> </style>

<template>
  <div class="mx-5 d-flex flex-column">
    <p class="mb-5 text-secondary">
      Menu
    </p>

    <v-list nav class="pa-0">
      <v-list-item-group color="primary">
        <v-list-item link :to="localePath('/admin/dashboard')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              {{ $t('adminDashboard.menu_dashboard') }}
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item
          link
          :to="localePath('/admin/logistic-service-provider-management')"
          class="mb-2 pa-0"
        >
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Logistic Provider
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item
          v-if="userEmail === '<EMAIL>'"
          link
          :to="localePath('/admin/shipping-company-management')"
          class="mb-2 pa-0"
        >
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Product Owner
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item link :to="localePath('/admin/vendor-management')" class="mb-2 pa-0">
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Transporter
            </p>
          </v-list-item-title>
        </v-list-item>
        <p class="mb-5 text-secondary">
          Settings
        </p>
        <v-list-item
          link
          :to="localePath('/admin/setting')"
          class="mb-2 pa-0"
        >
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              Setting
            </p>
          </v-list-item-title>
        </v-list-item>
        <v-list-item
          link
          :to="localePath('/admin/user-activity-log')"
          class="mb-2 pa-0"
        >
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              User Activity Log
            </p>
          </v-list-item-title>
        </v-list-item>

        <v-list-item
          link
          :to="localePath('/admin/wb-activity-log')"
          class="mb-2 pa-0"
        >
          <v-list-item-title
            class="pl-5 font-weight-medium text-decoration-none"
          >
            <p class="ma-0">
              WB Activity Log
            </p>
          </v-list-item-title>
        </v-list-item>

        <!--        <v-list-item link to="#" class="mb-5 pa-0" @click="logout">-->
        <!--          <v-list-item-title-->
        <!--            class="pl-5 font-weight-medium text-decoration-none"-->
        <!--          >-->
        <!--            <p class="ma-0">-->
        <!--              Logout-->
        <!--            </p>-->
        <!--          </v-list-item-title>-->
        <!--        </v-list-item>-->
      </v-list-item-group>
    </v-list>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'NavigationList',

  computed: {
    userEmail (): string {
      return this.$auth.$state.user.data.email
    }
  },

  methods: {
    logout () {
      this.$auth.logout()
    }
  }
})
</script>

<style lang="scss" scoped>
p {
  font-weight: 600;
}
</style>

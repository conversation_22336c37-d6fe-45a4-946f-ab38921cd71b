export interface LogisticServiceProviderPersonalizeState {
  personalize: {
    name: string,
    logo: string,
    logo_url: string,
    primary_color: string,
    secondary_color: string,
    register_banner: string,
    register_banner_url: string,
  },
  isLoading: boolean
}

export const state = () : LogisticServiceProviderPersonalizeState => ({
  personalize: {
    name: '',
    logo: '',
    logo_url: '',
    primary_color: '#EF3434',
    secondary_color: '',
    register_banner: '',
    register_banner_url: ''
  },
  isLoading: true
})

export default state

<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="mb-10 pa-0">
      <div class="mb-sm-10 mb-5 d-flex align-center">
        <v-btn
          text
          plain
          class="text-capitalize pa-0"
          @click="$router.back()"
        >
          <v-icon
            class="mr-3 custom-icon"
          >
            mdi-chevron-left
          </v-icon>
          {{ $t('vendorInvoiceOrder.back_to_list_invoice') }}
        </v-btn>
      </div>

      <detail-invoice-order-loading v-if="isLoadingDetail" />

      <detail-invoice-vendor
        v-else
        :data-invoice="dataInvoice"
        :total-cost="totalAmount"
        :is-has-action="true"
      >
        <template #default="{ invoiceDetail }">
          <v-text-field
            :value="form.costs[invoiceDetail.id] ?? invoiceDetail.cost"
            outlined
            prefix="Rp."
            :label="$t('vendorInvoiceOrder.shipment_fee')"
            :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
            :loading="isLoadingDetailForm"
            @input="changeCostVehicle(invoiceDetail.id, $event)"
            @blur="updateFee(
              invoiceDetail.id,
              invoiceDetail.invoice_id,
              invoiceDetail.order_id,
              invoiceDetail.vehicle_detail_id,
              invoiceDetail.total_odometer,
              form.costs[invoiceDetail.id]
            )"
          />
        </template>

        <template #additional-fee>
          <v-row
            v-for="(additionalCost, i) in form.additional_costs"
            :key="i"
            class="ma-0 mb-6 d-flex"
          >
            <v-col class="pa-0 col-auto">
              <v-text-field
                v-model="form.additional_costs[i].description"
                outlined
                :label="$t('vendorInvoiceOrder.description')"
                hide-details
                class="mr-6"
                :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
              />
            </v-col>
            <v-col class="pa-0 col-auto">
              <v-text-field
                v-model="form.additional_costs[i].cost"
                outlined
                prefix="Rp."
                :label="$t('vendorInvoiceOrder.amount')"
                type="number"
                hide-details
                class="mr-6"
                :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
              />
            </v-col>
            <v-col class="pa-0 col-auto">
              <v-btn
                x-large
                color="primary"
                elevation="0"
                class="text-capitalize mr-4"
                :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
                :loading="form.additional_costs[i].isLoading"
                @click=" submitAdditionalCost(
                  i,
                  form.additional_costs[i].id,
                  form.additional_costs[i].description,
                  form.additional_costs[i].cost,
                )"
              >
                <p class="subtitle-1 ma-0">
                  Save Cost
                </p>
              </v-btn>
            </v-col>
            <v-col class="pa-0 col-auto">
              <v-btn
                x-large
                outlined
                color="primary"
                elevation="0"
                class="text-capitalize mr-4"
                :loading="form.additional_costs[i].isLoadingDelete"
                :disabled="additionalCost.id === null || dataInvoice?.invoice?.status === 'PUBLISHED'"
                @click="deleteAdditionalCost(i, form.additional_costs[i].id)"
              >
                <p class="subtitle-1 ma-0">
                  Delete Cost
                </p>
              </v-btn>
            </v-col>
            <!--            <v-col class="pa-0 col-auto">-->
            <!--              <v-btn-->
            <!--                outlined-->
            <!--                fab-->
            <!--                :disabled="additionalCost.id !== null || dataInvoice?.invoice?.status === 'PUBLISHED'"-->
            <!--                class="rounded"-->
            <!--                style="border: 1px solid #CFCCCC"-->
            <!--                @click="removeAdditionalCost(i)"-->
            <!--              >-->
            <!--                <v-icon>mdi-close</v-icon>-->
            <!--              </v-btn>-->
            <!--            </v-col>-->
          </v-row>

          <v-btn
            x-large
            outlined
            class="text-capitalize"
            style="border: 1px solid #CFCCCC"
            :disabled="dataInvoice?.invoice?.status === 'PUBLISHED'"
            @click="addAdditionalCost"
          >
            <v-icon class="mr-3">
              mdi-plus
            </v-icon>
            <p class="ma-0 body-1">
              {{ $t('vendorInvoiceOrder.add_additional_fee') }}
            </p>
          </v-btn>
        </template>
      </detail-invoice-vendor>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailInvoiceOrderLoading from '~/components/loading/DetailInvoiceOrderLoading.vue'
import DetailInvoiceVendor from '~/components/DetailInvoiceVendor.vue'
import { InvoiceOrder } from '~/types/invoice'

export default Vue.extend({

  name: 'CreateInvoiceOrderPage',

  components: {
    DetailInvoiceOrderLoading,
    DetailInvoiceVendor
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],
  data: () => ({
    tabs: null,
    expanded: [],
    form: {
      additional_costs: [] as any[],
      costs: {} as any
    },
    totalAmount: 0,

    rules: {
      required: (value: string) => !!value || 'Required.'
    }
  }),

  computed: {
    dataInvoice () {
      return this.$store.getters['invoice/detailData'].item as InvoiceOrder | null
    },

    additionalCosts () {
      return this.$store.getters['invoice/additional-fee/data'].items
    },

    isLoadingDetail () {
      return this.$store.getters['invoice/isLoadingDetail']
    },

    isLoadingDetailForm () {
      return this.$store.getters['invoice/isLoadingDetailForm']
    }
  },

  watch: {
    additionalCosts () {
      this.form.additional_costs = this.additionalCosts.map((item:any) => ({
        id: item.id,
        description: item.description,
        cost: item.cost,
        isLoading: false,
        isLoadingDelete: false
      }))
    },

    dataInvoice () {
      this.form.costs = this.dataInvoice?.invoice_details?.reduce((acc:any, item:any) => {
        acc[item.id] = item.cost
        return acc
      }, {}) ?? {}

      this.setTotalAmount()
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Fill Invoice to Logistic Provider')
  },

  mounted () {
    this.$store.dispatch('invoice/getItemDetail', {
      id: this.$route.params.id
    })

    this.getAdditionalCost({})
  },

  methods: {
    addAdditionalCost () {
      this.form.additional_costs.push({
        id: null,
        description: null,
        cost: null,
        isLoading: false,
        isLoadingDelete: false
      })
    },

    changeCostVehicle (id: string, value: string) {
      if (value === '') {
        this.form.costs[id] = '0'
      } else {
        this.form.costs[id] = value
      }

      this.setTotalAmount()
    },

    removeAdditionalCost (i: number) {
      this.form.additional_costs.splice(i, 1)
    },

    updateFee (id: String, invoiceId : String, orderId: String, vehicleDetailId: String, totalOdometer: String, cost: String) {
      this.$store.dispatch('invoice/updateItem', {
        id,
        cost,
        invoice_id: invoiceId,
        order_id: orderId,
        vehicle_detail_id: vehicleDetailId,
        total_odometer: totalOdometer
      })
    },
    async getAdditionalCost ({ page = 1 }) {
      await this.$store.dispatch('invoice/additional-fee/getItems', {
        filterColumns: 'invoice_id',
        filterKeys: this.$route.params.id,
        page
      })
      this.setTotalAmount()
    },

    async submitAdditionalCost (index: number | keyof any[], invoiceFeeId: String | undefined, description: String, cost: String) {
      let response = false

      this.$set(this.form.additional_costs[index], 'isLoading', true)

      if (invoiceFeeId === null) {
        response = await this.$store.dispatch('invoice/additional-fee/createItem', {
          invoice_id: this.$route.params.id,
          description,
          cost
        })
      } else {
        response = await this.$store.dispatch('invoice/additional-fee/updateItem', {
          id: invoiceFeeId,
          invoice_id: this.$route.params.id,
          description,
          cost
        })
      }

      if (response) {
        await this.getAdditionalCost({})
      }
      this.$set(this.form.additional_costs[index], 'isLoading', false)
    },

    async deleteAdditionalCost (index: number | keyof any[], id: String) {
      this.$set(this.form.additional_costs[index], 'isLoadingDelete', true)

      const response = await this.$store.dispatch('invoice/additional-fee/deleteItem', {
        id
      })

      if (response) {
        await this.getAdditionalCost({})
      }
    },

    setTotalAmount () {
      let amountCost = 0

      for (const key in this.form.costs) {
        if (Object.prototype.hasOwnProperty.call(this.form.costs, key)) {
          const cost = this.form.costs[key]
          amountCost += Number(`${cost}`)
        }
      }

      for (const items of this.form.additional_costs) {
        if (items.cost === null) {
          amountCost += 0
          continue
        }
        amountCost += Number(`${items.cost}`)
      }

      this.totalAmount = amountCost
    }

  }
})
</script>

<style lang="scss" scoped>

.custom-icon {
  transition: 0s !important;
}

.w-custom {
  max-width: 250px;
}

.border-grey {
  border-radius: 4px !important;
  border: 1px solid #CFCCCC !important;
}

</style>

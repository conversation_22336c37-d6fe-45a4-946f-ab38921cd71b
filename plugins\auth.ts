import { AxiosError } from 'axios'

export default function (context: any) {
  context.$axios.onError((err: AxiosError) => {
    const url = err.response?.config.url
    const webPath = context.route.path

    if (url !== '/v1/fms/sync' && url !== '/v1/auth/login') {
      if ((err.response?.status === 401 || err.response?.statusText === 'Unauthorized') && webPath !== '/login') {
        context.store.dispatch('firebase/detachListener')
        context.$auth.setUser(null)
        context.$auth.strategy.token.reset()
        context.redirect('/')
      }
    }
  })
}

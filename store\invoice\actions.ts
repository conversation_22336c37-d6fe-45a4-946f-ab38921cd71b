import { ActionTree } from 'vuex'
import { saveAs } from 'file-saver'
import { InvoiceState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastError, toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<InvoiceState, InvoiceState> = {
  getItems ({ commit }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios.get('/v1/invoices', {
      params: {
        search_key: payload.searchKey == null ? '' : payload.searchKey,
        search_columns: payload.searchColumns == null ? 'shipment.identity' : payload.searchColumns,
        sort_column: payload.sortColumn == null ? '' : payload.sortColumn,
        sort_type: payload.sortType == null ? '' : payload.sortType,
        filter_columns: payload.filterColumns,
        filter_keys: payload.filterKeys == null ? '' : payload.filterKeys,
        page: payload.page == null ? '' : payload.page,
        entries: payload?.entries ? payload.entries : 9
      }
    }).then((response: any) => {
      commit('SET_ITEMS', response.data.data)

      commit('SET_TOTAL_PAGE', response.data.meta.last_page)

      commit('SET_PAGE', response.data.meta.current_page)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING', false)
    })
  },

  async getItemDetail ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)
    await this.$axios.get(`v1/invoices/${payload.id}`).then((response: any) => {
      commit('SET_ITEM', response.data.data)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_DETAIL', false)
    })
  },

  createItem ({ state, commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_DETAIL', true)
    this.$axios.put(`v1/invoices/${payload.id}/publish`, {
      id: payload.id
    }).then((response: any) => {
      toastSuccess(response.data.message, this)
      dispatch('getItemDetail', { id: payload.id, page: state.page })
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_DETAIL', false)
    })
  },

  updateItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_DETAIL_FORM', true)

    const data = {
      cost: payload.cost,
      invoice_id: payload.invoice_id,
      order_id: payload.order_id,
      vehicle_detail_id: payload.vehicle_detail_id,
      total_odometer: payload.total_odometer
    }

    this.$axios
      .put('/v1/invoice-details/' + payload.id, data)
      .then((response: any) => {
        commit('SET_IS_LOADING_DETAIL_FORM', false)
        toastSuccess(response.data.message, this)
        dispatch('getItemDetail', { id: payload.invoice_id })
      }).catch((error: any) => {
        exceptionHandler(error, this)
      }).finally(() => {
        commit('SET_IS_LOADING_DETAIL_FORM', false)
      })
  },

  async downloadInvoices ({ commit }, payload: any) {
    commit('SET_IS_LOADING_DOWNLOAD_INVOICE', true)

    await this.$axios.get(`v1/invoices/${payload.id}/print`, {
      responseType: 'blob'
    }).then((response: any) => {
      commit('SET_BLOB_INVOICE', response.data)

      const blob = new Blob([response.data], { type: 'application/pdf;charset=utf-8' })
      saveAs(blob, `invoice-${payload.number}.pdf`)
    }).catch((_: any) => {
      toastError('Failed to generate PDF', this)
    }).finally(() => {
      commit('SET_IS_LOADING_DOWNLOAD_INVOICE', false)
    })
  }
}

export default actions

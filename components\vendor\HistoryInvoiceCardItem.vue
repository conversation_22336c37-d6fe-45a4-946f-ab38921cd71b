<template>
  <v-card outlined class="pa-5">
    <div class="mb-6 d-flex align-start justify-space-between">
      <div class="d-flex">
        <image-component :image="lsaImage" max-width="50" max-height="50" class="mr-5" />

        <v-card-title class="pa-0 d-flex flex-column align-start">
          <v-row>
            <v-col>
              <h4 class="mb-2 text-truncate w-180">
                {{ lsaName }}
              </h4>
            </v-col>
            <v-col class="pl-2 red--text">
              <h4>
                <span>{{ getNewStatus() }}</span>
              </h4>
            </v-col>
          </v-row>

          <p class="body-1 ma-0">
            {{ date }}
          </p>
        </v-card-title>
      </div>
    </div>

    <v-row class="ma-0">
      <v-col class="pa-0 mr-4">
        <v-card-text class="pa-0">
          <p class="caption mb-2">
            {{ $t('historyInvoiceCardItem.invoice_number') }}
          </p>
          <p class="subtitle-1 ma-0">
            {{ identity }}
          </p>
        </v-card-text>
      </v-col>

      <v-col class="pa-0">
        <v-card-actions class="pa-0 float-end">
          <v-btn
            x-large
            plain
            text
            class="text-capitalize"
            @click="$router.push(localePath(detailRoute + '/' + id))"
          >
            <p class="subtitle-1 ma-0 mr-2">
              Detail Invoice
            </p>
            <v-icon>
              mdi-chevron-right
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-row>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { getNewStatus } from '~/utils/functions'

export default Vue.extend({
  name: 'HistoryInvoiceCardItem',

  props: {
    detailRoute: {
      type: String,
      required: true
    },
    id: {
      type: String,
      default: ''
    },
    identity: {
      type: String,
      default: ''
    },
    lsaName: {
      type: String,
      default: ''
    },
    lsaImage: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    },
    readVendor: {
      type: String,
      default: ''
    }
  },

  methods: {
    getNewStatus ():string {
      return getNewStatus(this.readVendor)
    }
  }
})
</script>

<style scoped>
.w-180 {
  max-width: 180px;
}
</style>

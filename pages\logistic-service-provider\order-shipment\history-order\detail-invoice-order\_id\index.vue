<template>
  <v-container v-resize="getWindowWidth" fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="mb-10 pa-0">
      <div class="mb-sm-10 mb-5 d-flex align-center">
        <v-btn
          text
          plain
          class="text-capitalize pa-0"
          @click="$router.back()"
        >
          <v-icon class="mr-3 custom-icon">
            mdi-chevron-left
          </v-icon>
          {{ $t('lspHistoryShipment.back_to_history') }}
        </v-btn>
      </div>

      <detail-invoice-order-loading v-if="isLoadingDetailInvoiceOrder" />

      <detail-invoice-vendor
        v-else
        :data-invoice="dataInvoice"
        :total-cost="totalFeeCost + totalAmount"
      >
        <template #download-invoice>
          <v-btn
            x-large
            outlined
            :loading="isLoadingDownloadInvoice"
            class="text-capitalize"
            style="border: 1px solid #CFCCCC;"
            @click="downloadInvoice"
          >
            <v-icon class="mr-3">
              mdi-file-download
            </v-icon>
            <p class="subtitle-1 ma-0">
              {{ $t('lspHistoryShipment.download_invoice') }}
            </p>
          </v-btn>
        </template>

        <template #default="{ invoiceDetail }">
          <h4>{{ invoiceDetail.cost | toCurrency }}</h4>
        </template>

        <template #additional-fee>
          <v-container v-if="dataInvoice?.invoice?.fees.length === 0">
            <p class="body-1 ma-0">
              {{ $t('lspHistoryShipment.no_additional_fee') }}
            </p>
          </v-container>
          <div v-else>
            <v-container
              v-for="fee in dataInvoice?.invoice?.fees"
              :key="fee.id"
              fluid
              class="mb-5 pa-0 d-flex align-center justify-space-between"
            >
              <p class="body-1 ma-0">
                {{ fee.description }}
              </p>
              <h4>{{ parseInt(fee.cost) | toCurrency }}</h4>
            </v-container>
          </div>
        </template>
      </detail-invoice-vendor>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import DetailInvoiceOrderLoading from '~/components/loading/DetailInvoiceOrderLoading.vue'
import DetailInvoiceVendor from '~/components/DetailInvoiceVendor.vue'
import { InvoiceOrder } from '~/types/invoice'

export default Vue.extend({
  name: 'DetailHistoryInvoiceOrderPage',

  components: {
    DetailInvoiceOrderLoading,
    DetailInvoiceVendor
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    tabs: null,
    windowWidth: 0,
    expanded: [],
    cost: '',
    feeCost: ''
  }),

  computed: {
    dataInvoice () {
      return this.$store.getters[
        'logistic-service-provider/invoice-shipment/detailData'
      ].item as InvoiceOrder
    },

    totalAmount (): Number {
      let amountCost = 0
      const dataInvoice = this.dataInvoice as InvoiceOrder

      dataInvoice?.invoice_details?.forEach((item) => {
        amountCost += parseInt(item.cost ?? '0')
      })

      return amountCost
    },

    totalFeeCost (): Number {
      let amountCost = 0
      const dataInvoice = this.dataInvoice as InvoiceOrder

      dataInvoice?.invoice?.fees?.forEach((fee) => {
        amountCost += parseInt(fee.cost ?? '0')
      })

      return amountCost
    },

    isLoadingDetailInvoiceOrder () {
      return this.$store.getters['logistic-service-provider/invoice-shipment/isLoadingDetail']
    },

    isLoadingDownloadInvoice () {
      return this.$store.getters['invoice/isLoadingDownloadInvoice']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Invoice Order Shipment')
  },

  mounted () {
    this.$store.dispatch(
      'logistic-service-provider/invoice-shipment/getItemDetail',
      {
        id: this.$route.params.id
      }
    )
  },

  methods: {
    getWindowWidth () {
      this.windowWidth = window.innerWidth
    },

    async downloadInvoice () {
      await this.$store.dispatch('invoice/downloadInvoices', {
        id: this.$route.params.id,
        number: this.dataInvoice?.invoice?.shipment?.identity
      })
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-icon {
  transition: 0s !important;
}

.w-custom {
  max-width: 250px;
}

.border-grey {
  border-radius: 4px !important;
  border: 1px solid #cfcccc !important;
}
</style>

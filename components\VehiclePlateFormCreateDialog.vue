<template>
  <v-dialog v-model="dialog" persistent max-width="640px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4> {{ $t('vehiclePlateFormAddDialog.add_plate_number') }}</h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <vehicle-plate-form
          form-type="CREATE"
          :selected-vehicle="selectedVehicle"
          :dialog="dialog"
          @on-click-add="createPlateNumber($event)"
          @on-click-cancel="$emit('on-close-dialog')"
        />
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import VehiclePlateForm from '~/components/VehiclePlateForm.vue'
import { Vehicle } from '~/types/vehicle'

export default Vue.extend({
  name: 'VehiclePlateFormAddDialog',

  components: { VehiclePlateForm },

  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    vendorId: {
      type: String,
      default: ''
    },
    selectedVehicle: {
      type: Object as () => Vehicle | null,
      required: true
    }
  },

  data: () => ({
    form: {
      vehicleId: '' as string,
      vehicleDetails: [{
        plateNumber: '',
        fms: '',
        driver: ''
      }] as any[]
    }
  }),

  computed: {
    entriesMode (): any {
      return this.$store.getters['layout/entriesMode']
    }
  },

  watch: {
    dialog (isOpen: boolean) {
      if (!isOpen) { return null }

      this.$store.dispatch('vehicle/getItems', {
        filterColumns: 'vendor_id',
        filterKeys: this.$auth.$state.user.data.vendor_id,
        entries: -1
      })
    }
  },

  methods: {
    async createPlateNumber (formValues: any) {
      const response = await this.$store.dispatch('vehicle/details/createItem', {
        formValues,
        vendorId: this.vendorId,
        entries: this.entriesMode
      })

      if (response) {
        this.$emit('on-close-dialog')
        this.$emit('on-success-create')
      }
    }
  }
})
</script>

<style scoped lang="scss"></style>

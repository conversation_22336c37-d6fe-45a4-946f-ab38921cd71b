<template>
  <v-pagination
    :value="page"
    :length="totalPage"
    total-visible="7"
    class="custom-pagination"
    @input="onChangePage($event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'PaginationComponent',

  props: {
    page: {
      type: Number,
      required: true
    },

    totalPage: {
      type: Number,
      required: true
    },

    pageId: {
      type: String,
      required: true
    }
  },

  data: () => ({
    routeQuery: null as any
  }),

  watch: {
    '$route.query': {
      handler (currentQuery) {
        this.routeQuery = { ...currentQuery }
      }
    }
  },

  created () {
    this.routeQuery = { ...this.$route.query }
  },

  methods: {
    async onChangePage (page: string) {
      try {
        if (this.routeQuery[this.pageId] !== page.toString()) {
        this.routeQuery[this.pageId] = page.toString()

        await this.$router.replace({
          query: { ...this.routeQuery }
        })

        this.$emit('on-change-page', this.routeQuery[this.pageId])
      }
      } catch (error) {
        console.error(error)
      }
    }
  }
})
</script>

<style scoped lang="scss">
.custom-pagination ::v-deep(li) span {
  margin: 0 8px 0 0;
}

.custom-pagination ::v-deep(li):last-child button {
  margin: 0;
}

.custom-pagination ::v-deep(li):not(:last-child) button {
  margin: 0 8px 0 0;
}
</style>

import { ShippingCompany } from '~/types/user'
import { Shipment } from '~/types/shipment'
import { InvoiceDetail } from '~/types/invoice'

export interface Pivot {
  suborder_id: string
  product_id: string
  quantity: number
}

export interface Product {
  id: string
  photo: string | null
  identity: string
  name: string
  product_type: string
  weight: string
  volume: string
  dimension_length: string
  dimension_width: string
  dimension_height: string
  unit: string
  unit_type: string
  shipment_company_id: string
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  photo_url: string | null
  pivot: Pivot
}

export interface PickupDropOffLocationPoint {
  id: string
  identity: string
  name: string
  longitude: string
  latitude: string
  start_operation_hour: string
  end_operation_hour: string
  address: string
  type: string
  shipment_company_id: string
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
}

export interface SubOrder {
  id: string
  identity: string
  estimation_date: string
  type: string
  order_id: string
  pickup_dropoff_location_point_id: string
  deleted_at: Date | null
  created_at: Date
  updated_at: Date
  products: Product[]
  pickup_drop_off_location_point: PickupDropOffLocationPoint
}

export interface Order {
  id: string
  photo: string | null
  identity: string
  status: string
  note: string | null
  shipment_company_id: string
  session_last_suborder_identity: number
  shipment_id: string
  deleted_at: Date | null
  created_at: Date
  published_at: Date | null
  updated_at: Date
  shipment_company?: ShippingCompany

  total_dimension_height?: number
  total_dimension_length?: number
  total_dimension_width?: number
  total_volume?: number
  total_weight?: number
  remaining_weight?: number

  suborders: SubOrder[] | undefined | null

  pickup_suborders: SubOrder[] | undefined | null

  is_selected: boolean

  shipments?: Shipment[]

  invoice_detail?: InvoiceDetail
  completion_status: string
  weight_bridges: any
}

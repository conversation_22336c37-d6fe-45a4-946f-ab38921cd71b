<template>
  <v-card outlined class="pa-5">
    <v-container class="d-flex align-center pa-0 mb-5">
      <v-avatar class="mr-5">
        <v-icon size="40">
          mdi-domain
        </v-icon>
      </v-avatar>

      <div>
        <h4 class="mb-2">
          {{ shippingCompany.name }}
        </h4>

        <v-menu offset-y>
          <template #activator="{ on, attrs }">
            <v-chip
              v-if="shippingCompany.status === 'COLLABORATE'"
              label
              v-bind="attrs"
              class="chip-success"
              v-on="on"
            >
              <p class="subtitle-2 ma-0 text-success">
                Collaborate
              </p>
            </v-chip>

            <v-chip
              v-else-if="shippingCompany.status === 'BLOCK'"
              label
              v-bind="attrs"
              class="chip-primary"
              v-on="on"
            >
              <p class="subtitle-2 ma-0 text-primary">
                Block
              </p>
            </v-chip>
          </template>
          <v-list>
            <v-list-item @click="onClickCollaborate">
              <v-list-item-title>
                Collaborate
              </v-list-item-title>
            </v-list-item>

            <v-list-item @click="onClickBlock">
              <v-list-item-title>
                Block
              </v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </v-container>

    <v-container class="pa-0 mb-5 d-flex align-center justify-space-between" fluid>
      <div class="d-flex align-center">
        <v-icon size="16" color="black" class="mr-3">
          mdi-map-marker
        </v-icon>

        <p class="body-1 ma-0">
          {{ shippingCompany.pickup_dropoff_location_points_count }} Location
        </p>
      </div>

      <v-btn
        plain
        class="text-capitalize"
        @click="$router.push(`/admin/logistic-service-provider-management/${$route.params.detail}/${shippingCompany.id}`)"
      >
        <p class="subtitle-1 ma-0 mr-2">
          {{ $t('scCardItem.see_details') }}
        </p>
        <v-icon>
          mdi-chevron-right
        </v-icon>
      </v-btn>
    </v-container>

    <v-container class="pa-0">
      <p class="caption mb-2">
        {{ $t('scCardItem.location') }}
      </p>
      <p class="body-1 ma-0">
        {{ shippingCompany.address }}
      </p>
    </v-container>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { ShippingCompany } from '~/types/user'

export default Vue.extend({
  name: 'ScCardItem',

  props: {
    shippingCompany: {
      type: Object as () => ShippingCompany,
      required: true
    }
  },

  methods: {
    onClickCollaborate () {
      return this.$emit('on-click-collaborate', this.shippingCompany)
    },
    onClickBlock () {
      return this.$emit('on-click-block', this.shippingCompany)
    }
  }
})
</script>

<style scoped lang="scss">
.chip-success {
  background-color: #EAF6EC !important;
}

.chip-primary {
  background-color: #FDE0E0 !important;
}
</style>

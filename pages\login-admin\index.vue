<template>
  <v-container fill-height fluid>
    <v-row justify="space-between">
      <v-col class="pa-10">
        <v-row align="center" justify="center">
          <v-img
            :src="require(`~/assets/images/logo-lsi.png`)"
            max-width="80"
            contain
          >
            <template #placeholder>
              <v-img
                min-width="80"
                max-width="80"
                aspect-ratio="1"
                contain
                :src="require(`~/assets/images/placeholder-company-logo.svg`)"
              />
            </template>
          </v-img>
          <div class="ml-4 mr-4" />
          <div class="text-heading-6 spacer-y-lg" style="max-width: 10em">
            Logistic Service Integrator
          </div>
        </v-row>
        <div class="text-heading-5 mt-10">
          Login
        </div>
        <div class="text-body">
          {{$t('loginAdmin.welcome_to')}} LSI, {{$t('loginAdmin.please_input')}}
        </div>
        <div class="spacer-y-2lg" />
        <v-form>
          <custom-text-field
            v-model="email"
            label="Email"
            type="email"
            :hint="$t('loginAdmin.enter_email')"
            prepend-inner-icon="mdi-email"
            :rules="[rulesRequired, rulesEmail]"
            is-required
            data-testid="input-email"
            @on-enter="login"
          />

          <custom-text-field
            v-model="password"
            label="Password"
            type="password"
            :hint="$t('loginAdmin.enter_password')"
            :type="isShowPassword ? 'text' : 'password'"
            prepend-inner-icon="mdi-lock"
            :rules="[rulesRequired]"
            :append-icon="isShowPassword ? 'mdi-eye' : 'mdi-eye-off'"
            is-required
            data-testid="input-password"
            @click:append="isShowPassword = !isShowPassword"
            @on-enter="login"
          />
          <div class="spacer-y-lg" />
          <v-btn
            height="52"
            color="primary"
            block
            depressed
            :loading="isLoading"
            @click="login"
          >
            {{ $t('loginAdmin.login') }}
          </v-btn>
          <div class="spacer-y-lg" />
          <v-btn
            height="52"
            color="primary"
            outlined
            block
            depressed
            @click="$router.push('/register-vendor')"
          >
            {{ $t('loginAdmin.register') }}
          </v-btn>
          <div class="spacer-y-lg" />
          <!--          <v-container>-->
          <!--            <v-row align="center" justify="space-between">-->
          <!--              <a class="text-body"> Login as Admin </a>-->
          <!--              <a class="text-info-body"> Forgot Password? </a>-->
          <!--            </v-row>-->
          <!--          </v-container>-->
          <div class="mt-16" />
          <v-row align="center" justify="center">
            <v-img
              :src="require(`~/assets/icons/powered.svg`)"
              max-width="150"
            />
          </v-row>
        </v-form>
      </v-col>

      <v-container v-if="$device.isDesktop" class="pa-0 col-6">
        <div class="py-5 pr-5 fill-height" data-testid="image-side">
          <v-img
            :src="require('~/assets/images/banner-login-vendor.png')"
            class="fill-height"
          />
        </div>
      </v-container>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '@/components/CustomTextField.vue'
import { rules } from '~/utils/functions'
import { Personalize } from '~/types/user'

export default Vue.extend({
  name: 'LoginPage',

  components: {
    CustomTextField
  },

  middleware: 'is-logged-in',

  data: () => ({
    email: '',
    password: '',
    isShowPassword: false
  }),

  computed: {
    isLoading () {
      return this.$store.getters['login/isLoading']
    }
  },

  methods: {
    login () {
      // get domain
      const domain = this.$store.getters.domain

      this.$store.dispatch('login/submitLogin', {
        email: this.email,
        password: this.password,
        domain
      })
    },
    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    }
  }
})
</script>

<style scoped>
.side-image {
  background-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.75) 0%,
    transparent 35%
  );
}
</style>

import { ActionTree } from 'vuex'
import { LogisticServiceProviderState } from './state'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<LogisticServiceProviderState, LogisticServiceProviderState> = {
  getRegisterLink ({ commit }) {
    commit('SET_IS_LOADING_REGISTER_LINK', true)
    this.$axios.get('v1/profile/logistics-service-providers/register-link').then((response: any) => {
      commit('SET_REGISTER_LINK', response.data.data.url)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_REGISTER_LINK', false)
    })
  },

  async removeLogo ({ commit, dispatch }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios.delete(`/v1/logistics-service-providers/${payload}/logo`)
      .then(() => {
        dispatch('profile/getFormLSP', null, { root: true })
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }
}

export default actions

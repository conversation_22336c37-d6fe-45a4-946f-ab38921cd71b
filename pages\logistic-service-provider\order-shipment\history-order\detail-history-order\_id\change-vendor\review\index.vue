<template>
  <v-container
    fluid
    class="pa-0 px-md-10 px-5 pb-10"
  >
    <v-row>
      <v-col>
        <v-btn
          text
          outlined
          @click="goBack"
        >
          <v-icon text>
            mdi-chevron-left
          </v-icon> Back to Order
        </v-btn>

        <v-responsive :aspect-ratio="setAspectRatio" class="mt-5 mb-lg-0 mb-10 pa-0 rounded" style="z-index: 0">
          <custom-map
            :latitude="centerLatLng.lat"
            :longitude="centerLatLng.lng"
            :zoom="zoomMap"
          >
            <template #marker>
              <l-marker
                v-for="(marker, i) in markers"
                :key="i"
                :lat-lng="[marker.lat, marker.lng]"
              >
                <l-icon
                  v-if="marker.type === 'PICKUP'"
                  :icon-anchor="[20, 30]"
                >
                  <v-icon
                    size="40"
                    color="info"
                  >
                    mdi-map-marker
                  </v-icon>
                </l-icon>

                <l-icon
                  v-else
                  :icon-anchor="[20, 30]"
                >
                  <v-icon
                    size="40"
                    color="success"
                  >
                    mdi-map-marker
                  </v-icon>
                </l-icon>

                <l-popup>
                  <div>
                    <p
                      class="subtitle-3 mb-1"
                      :style="marker.type === 'PICKUP' ? 'color: #0094BC' : 'color: #2FA841'"
                    >
                      {{ marker.type }}
                    </p>
                    <p class="caption ma-0 text--primary">
                      {{ marker.name }}
                    </p>
                  </div>
                </l-popup>
              </l-marker>
            </template>
          </custom-map>
        </v-responsive>

        <v-col class="d-md-flex pa-0 mt-5">
          <div class="col-sm-12 col-md-4 pa-0">
            <div class="white rounded pa-md-10 pa-5 mr-2 ">
              <h4 class="mb-5">
                {{ $t('lspCreateShipment.calculation') }}
              </h4>

              <v-divider />

              <h5 class="mt-5">
                Sub order your choice :
              </h5>

              <v-col class="d-flex justify-space-between">
                <!-- <div>
                  <h5 class="font-weight-light">
                    Volume :
                  </h5>
                  <h2>
                    {{ +(detailShipment?.total_volume ?? 0).toFixed(2) }} CBM
                  </h2>
                </div> -->
                <div>
                  <h5 class="font-weight-light">
                    {{ $t('lspCreateShipment.total_weight') }} :
                  </h5>
                  <h2>
                    {{ +(detailShipment?.total_weight ?? 0).toFixed(2) }} KG
                  </h2>
                </div>
              </v-col>

              <!-- <h5 class="mt-5">
                Detail
              </h5>

              <v-col class="d-flex justify-space-between">
                <div class="d-flex flex-column">
                  <a class="font-weight-light caption black--text">
                    {{ $t('lspCreateShipment.total_length') }} :
                  </a>
                  <a class="font-weight-bold black--text">
                    {{ detailShipment?.total_dimension_length }} CM
                  </a>
                </div>
                <div class="d-flex flex-column">
                  <a class="font-weight-light caption black--text">
                    {{ $t('lspCreateShipment.total_width') }} :
                  </a>
                  <a class="font-weight-bold black--text">
                    {{ detailShipment?.total_dimension_width }} CM
                  </a>
                </div>
                <div class="d-flex flex-column">
                  <a class="font-weight-light caption black--text">
                    {{ $t('lspCreateShipment.total_height') }} :
                  </a>
                  <a class="font-weight-bold black--text">
                    {{ detailShipment?.total_dimension_height }} CM
                  </a>
                </div>
              </v-col> -->

              <v-divider />

              <h5 class="my-5">
                Detail Vendor
              </h5>
              <div v-for="(vehicle, index) of checkedVehicles" :key="index" class="pa-2 my-3">
                <div class="d-flex mb-3">
                  <h5>
                    Vendor :
                  </h5>
                  <h5 class="ml-3">
                    {{ vehicle?.vendor?.name }}
                  </h5>
                </div>
                <h5>
                  {{ $t('lspCreateShipment.name') }} :
                </h5>
                {{ vehicle.name }}
                <h5 class="mt-2">
                  {{ $t('lspCreateShipment.features') }} :
                </h5>
                <div class="pa-0">
                  <div
                    v-for="feature in vehicle.vehicle_features"
                    :key="feature.id"
                    class="pa-2 ma-1 my-1 d-inline-flex caption"
                    style="
                      border-color: #cfcccc;
                      border-style: solid;
                      border-radius: 4px;
                  "
                  >
                    {{ feature.name.toUpperCase() }}
                  </div>
                </div>
                <div class="pa-0">
                  <h5 class="mt-2">
                    {{ $t('lspCreateShipment.type') }}
                  </h5>

                  <p class="mb-0">
                    {{ vehicle.vehicle_type?.name }}
                  </p>
                  <h5 class="mt-2">
                    {{ $t('lspCreateShipment.quantity') }}
                  </h5>
                  {{ vehicle.quantity }}
                </div>
                <v-divider class="my-3" />
              </div>
              <v-dialog v-model="isShowSubmitDialog" max-width="600px">
                <v-card class="pa-md-10 pa-5">
                  <v-form ref="form">
                    <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
                      <h4>
                        {{ $t('sendOrderShipmentDialog.dialog_send_title') }}
                      </h4>

                      <v-icon color="black" @click="isShowSubmitDialog = false">
                        mdi-close
                      </v-icon>
                    </v-card-title>

                    <p class="body-1 mb-4 black--text">
                      Make Note for LSA?
                    </p>

                    <div v-for="(vehicle, index) of checkedVehicles" :key="index" class="pa-2 my-3">
                      <div class="d-flex mb-3">
                        <v-icon @click="toggleNoteField(index)">
                          mdi-plus
                        </v-icon>
                        <h5 class="ml-2">
                          Add {{ vehicle?.vendor?.name }} Note
                        </h5>
                      </div>
                      <v-textarea
                        v-if="vehicle.showNote"
                        v-model="note"
                        outlined
                        :label="$t('sendOrderShipmentDialog.label_note')"
                        hide-details
                        class="mb-5"
                        no-resize
                      />
                    </div>

                    <p class="red--text">
                      Expired Order {{ nextDay }} at 10.00
                    </p>
                    <div class="pa-0 d-flex">
                      <v-checkbox v-model="setExpired" class="ma-1" />
                      <p class="ma-2">
                        Set Expired manually?
                      </p>
                    </div>

                    <div v-if="setExpired" class="col-10">
                      <v-menu
                        ref="menu"
                        v-model="menuDateRange"
                        max-width="290"
                        :close-on-content-click="false"
                        transition="slide-y-transition"
                        offset-y
                      >
                        <template #activator="{ on, attrs }">
                          <v-text-field
                            v-model="dateRange"
                            outlined
                            clearable
                            label="Date and Time"
                            v-bind="attrs"
                            append-icon="mdi-calendar-range"
                            v-on="on"
                          >
                            Select Date Range
                          </v-text-field>
                        </template>

                        <v-date-picker
                          v-model="dateRange"
                          no-title
                          :allowed-dates="allowedDates"
                          color="primary"
                          @input="updateNextDay"
                        >
                          <v-btn text color="primary" @click="menuDateRange = false">
                            Save
                          </v-btn>
                          <v-btn text color="primary" @click="menuDateRange = false">
                            Cancel
                          </v-btn>
                        </v-date-picker>
                      </v-menu>
                    </div>

                    <p class="body-1 mb-4 black--text">
                      Are you sure you want to send order shipment?
                    </p>

                    <v-card-actions class="pa-0">
                      <v-row class="ma-0">
                        <v-col class="mr-sm-5 mb-sm-0 mb-5 pa-0 col-sm-6 col-12">
                          <v-btn
                            elevation="0"
                            color="primary"
                            class="text-capitalize"
                            x-large
                            block
                            @click="sendShipmentOrder"
                          >
                            Send Order Shipment
                          </v-btn>
                        </v-col>
                        <v-col class="pa-0">
                          <v-btn
                            elevation="0"
                            outlined
                            color="primary"
                            class="text-capitalize ma-0"
                            x-large
                            block
                            @click="isShowSubmitDialog = false"
                          >
                            {{ $t('sendOrderShipmentDialog.button_cancel') }}
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-card-actions>
                  </v-form>
                </v-card>
                <template #activator="{ on, attrs }">
                  <div class="d-flex flex-column">
                    <v-btn
                      class="mt-5 mr-2"
                      text
                      outlined
                      color="primary"
                      @click="goBack"
                    >
                      <v-icon text>
                        mdi-chevron-left
                      </v-icon> Back to Order
                    </v-btn><v-btn
                      class="mt-5"
                      color="primary"
                      v-bind="attrs"
                      :loading="isLoadingForm"
                      @click="isShowSubmitDialog = true"
                      v-on="on"
                    >
                      {{ $t('lspCreateShipment.send_order_shipment') }}
                    </v-btn>
                  </div>
                </template>
              </v-dialog>
            </div>
          </div>
          <v-container class="white rounded col-sm-12 col-md-8 d-flex ml-md-2 ml-sm-0 mt-5 mt-md-0 mt-sm-5 pa-md-10 pa-5 flex-column">
            <h4>
              <v-icon
                color="black"
                class="mr-5"
              >
                mdi-text-box-multiple
              </v-icon>Detail Order Shipment
            </h4>
            <v-divider class="my-5" />
            <h4 class="mb-5">
              {{ $t('lspCreateShipment.select_order') }}
            </h4>
            <div class="d-flex">
              <v-card
                v-for="(order, index) of detailShipment?.orders"
                :key="index"
                class="pa-3 ma-2"
                outlined
                elevation="0"
                :style="selectedOrder?.id === order.id? `border: 1px solid #bf2a2a`:null"
                @click="selectOrder(order)"
              >
                <div class="d-flex flex-column">
                  <div class="body-1">
                    {{ order?.shipment_company?.name }}
                  </div>
                  <div class="subtitle-1">
                    {{ order?.identity }}
                  </div>
                </div>
              </v-card>
            </div>
            <v-divider class="my-5" />
            <detail-order-shipment
              v-if="selectedOrder"
              :is-selected-order-exist="isSelectedOrderExist"
              :selected-order="selectedOrder"
              :is-loading="isLoadingDetailOrder"
            />
          </v-container>
        </v-col>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import { Shipment } from '~/types/shipment'
import { Vehicle } from '~/types/vehicle'
import { Order } from '~/types/product'
import {
  generateCenterLatLng,
  colorType,
  zoomBetweenTwoPoints
} from '~/utils/functions'

export default Vue.extend({
  name: 'ReviewOrderShipmentPage',

  components: {
    CustomMap
  },

  layout: 'logistic-service-provider/body',

  middleware: ['auth', 'is-logistic-service-provider'],

  data: () => ({
    nextDay: '',
    dateRange: '' as any,
    menuDateRange: false as boolean,
    setExpired: false,
    note: '',
    isShowSubmitDialog: false
  }),

  computed: {
    zoomMap (): number {
      if (!this.markers) { return 0 }

      const firstPoint = this.markers[0]
      const lastPoint = this.markers[this.markers.length - 1]

      return zoomBetweenTwoPoints(firstPoint.lat, firstPoint.lng, lastPoint.lat, lastPoint.lng)
    },
    markers (): { lat: number, lng: number, type: string, name: string }[] | null {
      return this.selectedOrder
        ? this.selectedOrder.suborders?.map(suborder => ({
          lat: Number(suborder.pickup_drop_off_location_point.latitude),
          lng: Number(suborder.pickup_drop_off_location_point.longitude),
          type: suborder.type,
          name: suborder.pickup_drop_off_location_point.name
        })) ?? null
        : null
    },
    centerLatLng (): { lat: number, lng: number } {
      return this.markers
        ? generateCenterLatLng(this.markers)
        : { lat: 0, lng: 0 }
    },
    setAspectRatio () {
      let aspectRatio = 0

      if (this.$vuetify.breakpoint.smAndUp) {
        aspectRatio = 5 / 2
      } else {
        aspectRatio = 1
      }

      return aspectRatio
    },

    isLoadingForm (): boolean {
      return this.$store.getters['order/isLoadingForm']
    },

    isLoadingFormShipment (): boolean {
      return this.$store.getters['shipment/isLoadingForm']
    },

    isLoadingDetailOrder (): boolean {
      return this.$store.getters['order/isLoadingDetail']
    },

    isSelectedOrderExist (): boolean {
      return Object.keys(this.selectedOrder).length !== 0
    },

    checkedVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/checkedVehicles'] as Array<Vehicle>
    },

    detailShipment (): Shipment | null {
      return this.$store.getters['shipment/detailShipment']
    },

    selectedOrder (): Order {
      return this.$store.getters['order/selectedOrder']
    }
  },

  watch: {
    dateRange (newVal) {
      if (!newVal) {
        this.updateNextDay()
      }
    }
  },

  async mounted () {
    this.$store.commit('layout/SET_TITLE', 'Order Shipment')

    if (!this.detailShipment) {
      this.$router.go(-2)
    } else {
      await this.selectOrder(this.detailShipment.orders[0])
    }

    this.updateNextDay()
  },

  methods: {
    async selectOrder (order: Order) {
      await this.$store.dispatch('order/getDetailOrder', { id: order.id })
    },

    goBack () {
      this.$router.back()
    },

    colorType (type: string) {
      return colorType(type)
    },

    async sendShipmentOrder () {
      const res = await this.$store.dispatch('shipment/updateVendor', {
        id: this.detailShipment?.id,
        vendor_id: this.$route.query.shipmentVendorIds,
        note: this.note,
        set_expired_at: this.dateRange,
        vehicles: this.checkedVehicles.map(vehicle => ({
          vehicle_id: vehicle.id,
          quantity: vehicle.quantity
        })),
        new_vendor_id: this.checkedVehicles[0].vendor.id,
        new_vendor_weight: this.checkedVehicles[0].vendor.weight,
        shipment_vendor_id: this.$route.query.id
      })

      if (res) {
        this.isShowSubmitDialog = false
        await this.$router.push(this.$route.path.split('/').slice(0, 6).join('/'))
      }
    },

    toggleNoteField (index: number) {
      this.checkedVehicles[index].showNote = !this.checkedVehicles[index].showNote
      this.$forceUpdate()
    },

    allowedDates (date: Date) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const tomorrow = new Date()
      tomorrow.setDate(today.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)

      const selectedDate = new Date(date)

      return selectedDate >= tomorrow
    },

    updateNextDay () {
      const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      if (this.dateRange) {
        const selectedDate = new Date(this.dateRange)
        this.nextDay = daysOfWeek[selectedDate.getDay()]
      } else {
        const today = new Date().getDay()
        this.nextDay = daysOfWeek[(today + 1) % 7]
      }
    }
  }
})

</script>

<style lang="scss" scoped> </style>

import { ActionTree } from 'vuex'
import { RootState } from '../../../../index'
import { ShipmentCompanyCreateOrderSubOrderState } from './state'
import { toastSuccess } from '~/utils/toasts'
import { exceptionHandler } from '~/utils/functions'

export const actions: ActionTree<
  ShipmentCompanyCreateOrderSubOrderState,
  RootState
> = {
  getItems ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get('/v1/suborder', {
        params: {
          filter_columns: 'order_id',
          filter_keys: typeof payload === 'string' ? payload : payload.orderId
        }
      })
      .then((response: any) => {
        commit('SET_ITEMS', response.data.data)
        dispatch(
          'shipping-company/order-shipment/create-order/getDetailOrder',
          typeof payload === 'string' ? payload : payload.orderId,
          { root: true }
        )
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },

  async createItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .post('/v1/suborder', {
        estimation_date: payload.dateTimeEstimation,
        type: payload.type,
        pickup_dropoff_location_point_id: payload.pickupDropOffLocation,
        products: payload.products,
        order_id: payload.orderId
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        dispatch('getItems', payload)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async updateItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios
      .put('/v1/suborder/' + payload.id, {
        estimation_date: payload.dateTimeEstimation,
        type: payload.type,
        pickup_dropoff_location_point_id: payload.pickupDropOffLocation,
        products: payload.products,
        order_id: payload.orderId
      })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        // dispatch('getItems', payload.orderId)
        dispatch(
          'shipping-company/order-shipment/create-order/getDetailOrder',
          typeof payload === 'string' ? payload : payload.orderId,
          { root: true }
        )
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async deleteItem ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING_FORM', true)

    await this.$axios
      .delete('/v1/suborder/' + payload.id)
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        // dispatch('getItems', payload.orderId)
        dispatch(
          'shipping-company/order-shipment/create-order/getDetailOrder',
          typeof payload === 'string' ? payload : payload.orderId,
          { root: true }
        )
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  },

  async changeDestination ({ commit }, payload: {
    subOrderId: string,
    orderNumber: string,
    locationId: string,
    note: string
  }) {
    commit('SET_IS_LOADING_FORM', true)

    return await this.$axios.put(`/v1/suborder/${payload.subOrderId}/change-destinations`, {
      order_number: payload.orderNumber,
      pickup_dropoff_location_point_id: payload.locationId,
      note: payload.note
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        return true
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
        return false
      })
      .finally(() => { commit('SET_IS_LOADING_FORM', false) })
  }
}

export default actions

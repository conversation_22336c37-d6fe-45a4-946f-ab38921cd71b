<template>
  <v-dialog
    :value="dialog"
    max-width="540px"
    persistent
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
        <h4 v-if="!product">
          {{ $t('scFormProductDialog.form_title_add') }}
        </h4>
        <h4 v-else>
          {{ $t('scFormProductDialog.form_title_edit') }}
        </h4>

        <v-icon color="black" @click="onClickClose">
          mdi-close
        </v-icon>
      </v-card-title>

      <form-product-items
        :product="product"
        :is-loading-form="isLoadingForm"
        :clear-form="clearForm"
        @on-click-close="onClickClose"
        @on-click-add="onClickAdd($event)"
        @on-click-save="onClickSave($event)"
      />
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Product } from '~/types/product'
import FormProductItems from '~/components/shipping-company/FormProductItems.vue'

export default Vue.extend({
  name: 'FormProductDialog',

  components: { FormProductItems },

  props: {
    product: {
      type: Object as () => Product,
      default: null
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    dialog: {
      type: Boolean,
      default: false
    },
    clearForm: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    onClickAdd (values: any) {
      this.$emit('on-click-add', values)
    },
    onClickSave (values: any) {
      this.$emit('on-click-save', values)
    },
    onClickClose () {
      this.$emit('on-click-close')
    }
  }
})

</script>

<style scoped lang="scss"> </style>

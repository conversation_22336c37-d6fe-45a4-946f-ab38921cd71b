<template>
  <v-card outlined class="pa-5">
    <div class="mb-5 d-flex align-start justify-space-between">
      <v-card-title class="pa-0">
        <v-icon color="black" size="32" class="mr-5">
          mdi-text-box-multiple
        </v-icon>

        <div>
          <p class="body-1 mb-2">
            {{ $t('scInvoiceCardItem.invoice_order') }}
          </p>
          <h4>{{ identity }} <span class="pl-2 red--text">{{ getNewStatus() }}</span></h4>
        </div>
      </v-card-title>
    </div>

    <v-row class="ma-0 d-flex justify-space-between align-center">
      <v-col class="pa-0">
        <v-card-text class="pa-0">
          <p class="body-1 ma-0">
            {{ date }}
          </p>
        </v-card-text>
      </v-col>

      <v-col class="pa-0 d-flex justify-end align-center">
        <v-card-actions class="pa-0">
          <v-btn
            plain
            class="text-capitalize"
            @click="$router.push(localePath(detailRoute + '/' + id))"
          >
            <p class="subtitle-1 ma-0 mr-2">
              {{ $t('scInvoiceCardItem.detail_invoice') }}
            </p>
            <v-icon>
              mdi-chevron-right
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-col>
    </v-row>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { getNewStatus } from '~/utils/functions'

export default Vue.extend({
  name: 'InvoiceCardItem',

  props: {
    detailRoute: {
      type: String,
      required: true
    },
    id: {
      type: String,
      default: ''
    },
    identity: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    },
    readAt: {
      type: String,
      default: ''
    }
  },
  methods: {
    getNewStatus ():string {
      return getNewStatus(this.readAt)
    }
  }
})
</script>

<style lang="scss" scoped> </style>

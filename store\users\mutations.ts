import { MutationTree } from 'vuex'
import { UserState } from './state'

export const mutations: MutationTree<UserState> = {
  SET_RESULT (state, response: any) {
    state.items = response.data
    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_RESULT_LOGS (state, response: any) {
    state.itemLogs = response.data
    state.totalPageLogs = response.meta.last_page
    state.pageLogs = response.meta.current_page
  },

  SET_RESULT_PERMISSION (state, response: any) {
    state.itemsPermission = response.data
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },
  SET_IS_LOADING_FORM_CLEAR_IMAGE (state, isLoadingFormClearImage) {
    state.isLoadingFormClearImage = isLoadingFormClearImage
  }
}

export default mutations

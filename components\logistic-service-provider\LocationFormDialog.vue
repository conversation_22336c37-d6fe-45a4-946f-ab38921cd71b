<template>
  <v-dialog
    v-model="dialog"
    width="540"
    persistent
    style="z-index: 9999"
  >
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-10">
      <v-form ref="form">
        <v-card-title class="mb-10 pa-0 d-flex justify-space-between">
          <h4 v-if="location">
            {{ $t('lspLocationFormDialog.edit_location') }}
          </h4>
          <h4 v-else>
            {{ $t('lspLocationFormDialog.add_location') }}
          </h4>
          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <p class="subtitle-1">
          Category
        </p>
        <v-row class="d-flex align-center mb-5">
          <v-col class="d-flex">
            <v-radio-group
              v-model="formValues.category"
              class="ma-0 pa-0"
              hide-details
              row
            >
              <v-radio
                label="MILL"
                value="MILL"
              />
              <v-radio
                label="REFINERY"
                value="REFINERY"
              />
            </v-radio-group>
          </v-col>
        </v-row>

        <v-row class="ma-0 mb-5">
          <v-col class="pa-0">
            <v-text-field
              v-model="formValues.identity"
              outlined
              :label="$t('lspLocationFormDialog.identity')"
              hint="Ex. BDG-01"
              persistent-hint
              class="mr-5"
            />
          </v-col>

          <v-col class="pa-0 col-7">
            <v-text-field
              v-model="formValues.name"
              outlined
              label="Location Name"
            />
          </v-col>
        </v-row>

        <v-combobox
          v-model="formValues.type"
          :items="itemType"
          :label="$t('lspLocationFormDialog.location_type')"
          outlined
        />

        <!-- <p class="subtitle-1 mb-2">
          {{ $t('lspLocationFormDialog.select_map') }}
        </p> -->

        <v-text-field
          v-model="valueGeoLocation"
          label="Latitude,Longitude"
          outlined
          :error="isInvalidLatLong"
          @change="setGeoLocationValue($event)"
          @keydown.enter="setGeoLocationValue($event)"
        />

        <!-- <v-autocomplete
          v-model="valueGeoLocation"
          :items="dataGeoLocation"
          :search-input.sync="searchGeoLocation"
          item-text="display_name"
          item-value="id"
          no-filter
          append-icon=""
          outlined
          :label="$t('lspLocationFormDialog.search_location')"
          :loading="isLoadingGeoLocation"
          hide-details
          hide-selected
          solo
          class="mb-2"
          return-object
          @change="setGeoLocationValue($event)"
          @keydown.enter="setGeoLocationValue($event)"
        >
          <template #no-data>
            <v-list-item>
              <v-list-item-title>
                {{ $t('lspLocationFormDialog.search_for') }}
                <strong>{{ $t('lspLocationFormDialog.location') }}</strong>
              </v-list-item-title>
            </v-list-item>
          </template>

          <template #progress>
            <v-list-item class="text-right align-self-start">
              <v-list-item-title>
                <v-progress-circular
                  indeterminate
                  color="primary"
                  size="20"
                  class="mr-4 mt-2"
                />
              </v-list-item-title>
            </v-list-item>
          </template>

          <template #item="{ item }">
            <v-list-item-content>
              <v-list-item-title v-text="item.display_name" />
            </v-list-item-content>
          </template>
        </v-autocomplete> -->

        <v-responsive :aspect-ratio="16/9" class="mb-10 pa-0 rounded">
          <custom-map
            :is-clickable="isClickableMap"
            :latitude="validatedLocation.latitude"
            :longitude="validatedLocation.longitude"
            @on-click-map="onClickMap"
          >
            <template #marker>
              <l-marker
                v-if="!isClickableMap"
                :lat-lng="[
                  validatedLocation.latitude,
                  validatedLocation.longitude
                ]"
              />
            </template>
          </custom-map>
        </v-responsive>

        <v-textarea
          v-model="formValues.address"
          outlined
          :label="$t('lspLocationFormDialog.address')"
          hide-details
          no-resize
          height="120"
          class="mb-5"
        />

        <p class="subtitle-1 mb-5">
          {{ $t('lspLocationFormDialog.operational_time') }}
        </p>

        <v-row class="ma-n2">
          <v-col class="pa-2">
            <vue-timepicker
              v-model="form.startOperationHour"
              fixed-dropdown-button
              close-on-complete
              auto-scroll
              drop-direction="up"
              input-width="100%"
              manual-input
              :placeholder="$t('lspLocationFormDialog.label_start')"
              input-class="vtimepicker"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
          <v-col class="pa-2">
            <vue-timepicker
              v-model="form.endOperationHour"
              fixed-dropdown-button
              close-on-complete
              auto-scroll
              drop-direction="up"
              input-width="100%"
              manual-input
              :placeholder="$t('lspLocationFormDialog.label_end')"
              input-class="vtimepicker"
            >
              <template #dropdownButton>
                <v-icon>mdi-clock-time-four-outline</v-icon>
              </template>
            </vue-timepicker>
          </v-col>
        </v-row>

        <v-card-actions class="pa-0 mt-10">
          <v-row class="ma-0 d-flex">
            <v-col class="mr-sm-5 mb-sm-0 mb-5 pa-0">
              <v-btn
                v-if="location"
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                :loading="isLoadingForm"
                @click="onClickSave"
              >
                {{ $t('lspLocationFormDialog.save_location') }}
              </v-btn>

              <v-btn
                v-else
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                block
                :loading="isLoadingForm"
                @click="onClickAdd"
              >
                {{ $t('lspLocationFormDialog.add_location') }}
              </v-btn>
            </v-col>

            <v-col class="pa-0">
              <v-btn
                elevation="0"
                outlined
                color="primary"
                class="text-capitalize ma-0"
                x-large
                block
                @click="$emit('on-close-dialog')"
              >
                {{ $t('lspLocationFormDialog.cancel') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import { PickupDropOffLocationPoint } from '~/types/product'
import { GeoLocation } from '~/types/geo-location'
import { defaultLat, defaultLng } from '~/utils/functions'

export default Vue.extend({
  name: 'LocationFormDialog',

  components: { CustomMap },

  props: {
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    location: {
      type: Object as () => PickupDropOffLocationPoint,
      default: () => undefined
    },
    dialog: {
      type: Boolean,
      default: false
    },
    clearForm: {
      type: Boolean,
      default: false
    },
    showComponent: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    category: null,
    timeMenu: false,
    isClickableMap: false,
    isDefaultLocation: false,
    lastLatitude: defaultLat,
    lastLongitude: defaultLng,
    valueGeoLocation: null as any,
    searchGeoLocation: null as string | null,
    tempSearchKey: null as string | null,
    isInvalidLatLong: false as boolean,
    itemType: [
      { text: 'Pickup', value: 'PICKUP' },
      { text: 'Dropoff', value: 'DROPOFF' },
      { text: 'Pickup - Dropoff', value: 'PICKUP DROPOFF' }
    ],
    form: {
      id: '',
      name: '',
      identity: '',
      category: '',
      startOperationHour: {
        HH: '' as string | null,
        mm: '' as string | null
      },
      endOperationHour: {
        HH: '' as string | null,
        mm: '' as string | null
      },
      address: '',
      longitude: 0,
      latitude: 0,
      type: {
        text: '',
        value: ''
      }
    }
  }),

  computed: {
    dataGeoLocation (): GeoLocation[] {
      return this.$store.getters['geo-location/data']
    },

    isLoadingGeoLocation (): Boolean {
      return this.$store.getters['geo-location/isLoading'] as Boolean
    },

    formValues (): any {
      const formValues = this.form

      if (this.location) {
        const location = this.location as any

        formValues.id = location.id
        formValues.name = location.name
        formValues.identity = location.identity
        formValues.category = location.category

        const startOperationHour = location.start_operation_hour.split(':')

        formValues.startOperationHour = {
          HH: startOperationHour.at(0),
          mm: startOperationHour.at(1)
        }

        const endOperationHour = location.end_operation_hour.split(':')

        formValues.endOperationHour = {
          HH: endOperationHour.at(0),
          mm: endOperationHour.at(1)
        }

        let indexOfTypeName = 0
        for (let i = 1; i < this.itemType.length; i++) {
          if (this.itemType[i].value === location.type) {
            indexOfTypeName = i
          }
        }
        formValues.type = {
          text: this.itemType[indexOfTypeName].text,
          value: location.type
        }

        formValues.latitude = location.latitude
        formValues.longitude = location.longitude
        formValues.address = location.address
      }

      return formValues
    },

    reformattedForm (): any {
      const form = this.formValues

      return {
        id: form.id,
        identity: form.identity,
        name: form.name,
        address: form.address,
        latitude: form.latitude,
        longitude: form.longitude,
        type: form.type,
        category: form.category,
        startOperationHour: `${form.startOperationHour.HH}:${form.startOperationHour.mm}`,
        endOperationHour: `${form.endOperationHour.HH}:${form.endOperationHour.mm}`
      }
    },

    validatedLocation (): any {
      const location = this.location as any
      let latitude = NaN
      let longitude = NaN
      const lastLatitude = this.lastLatitude
      const lastLongitude = this.lastLongitude
      const isDefaultLocation = this.isDefaultLocation

      if (this.valueGeoLocation) {
        const [lat, lng] = this.valueGeoLocation.split(',')
        latitude = parseFloat(lat)
        longitude = parseFloat(lng)
      } else if (!this.valueGeoLocation && isDefaultLocation) {
        latitude = parseFloat(location.latitude)
        longitude = parseFloat(location.longitude)
      } else {
        latitude = lastLatitude
        longitude = lastLongitude
      }

      if (isNaN(latitude) || isNaN(longitude)) {
        latitude = 0
        longitude = 0
      }

      return { latitude, longitude }
    }
  },

  watch: {
    searchGeoLocation (searchKey: string | null) {
      if (this.isLoadingGeoLocation) { return }
      if (searchKey === null) { return }
      if (searchKey.length < 3) { return }

      this.$store.dispatch('geo-location/searchByName', {
        searchKey
      })

      this.tempSearchKey = searchKey
    },

    isLoadingGeoLocation (isLoading: any) {
      if (isLoading) { return }

      if (this.tempSearchKey !== this.searchGeoLocation) {
        this.tempSearchKey = this.searchGeoLocation
        if (this.searchGeoLocation !== null) {
          this.$store.dispatch('geo-location/searchByName', {
            searchKey: this.searchGeoLocation
          })
        }
      }
    },

    clearForm () {
      if (this.clearForm) {
        const form = this.$refs.form as HTMLFormElement

        this.form.startOperationHour = { HH: null, mm: null }
        this.form.endOperationHour = { HH: null, mm: null }

        form.reset()
      }
    },

    valueGeoLocation () {
      if (this.valueGeoLocation == null) {
        this.isClickableMap = true
      }
    }
  },

  mounted () {
    if (this.location) {
      this.isDefaultLocation = true
    }
  },

  methods: {
    setGeoLocationValue (value: any) {
      this.isClickableMap = false

      const [lat, lng] = value.split(',')
      const latitude = parseFloat(lat)
      const longitude = parseFloat(lng)

      if (
        isNaN(latitude) || isNaN(longitude) ||
        latitude < -90 || latitude > 90 ||
        longitude < -180 || longitude > 180
      ) {
        this.isInvalidLatLong = true
        this.formValues.latitude = null
        this.formValues.longitude = null
        return
      } else {
        this.isInvalidLatLong = false
      }

      this.formValues.latitude = latitude
      this.formValues.longitude = longitude
      this.lastLatitude = latitude
      this.lastLongitude = longitude

      this.isDefaultLocation = false
      this.isClickableMap = false
    },

    onClickMap (position: any) {
      this.isClickableMap = true
      this.form.latitude = position.lat
      this.form.longitude = position.lng
    },

    onClickAdd () {
      this.$emit('on-click-add', this.reformattedForm)
    },

    onClickSave () {
      this.$emit('on-click-save', this.reformattedForm)
    }
  }
})
</script>

<style lang="scss" scoped> </style>

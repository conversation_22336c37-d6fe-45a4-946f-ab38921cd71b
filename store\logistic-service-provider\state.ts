import { ShippingCompany } from '~/types/user'

export interface LogisticServiceProviderState {
  isLoadingForm: boolean
  isLoadingRegisterLink: boolean
  registerLink: string
  selectedShipmentCompany: ShippingCompany | null
}

export const state = () : LogisticServiceProviderState => ({
  isLoadingForm: false,
  isLoadingRegisterLink: false,
  registerLink: '',
  selectedShipmentCompany: null
})

export default state

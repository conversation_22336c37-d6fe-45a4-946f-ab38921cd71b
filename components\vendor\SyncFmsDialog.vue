<template>
  <v-dialog v-model="dialog" max-width="540px" persistent>
    <template #activator="{ on, attrs }">
      <slot name="activator" v-bind="attrs" v-on="on" />
    </template>

    <!-- Initial Dialog for Sync/Unsync Selection -->
    <v-card v-if="!selectedAction" class="pa-md-12">
      <v-card-title class="pa-0 d-flex justify-space-between">
        <h4>Sync vehicle from FMS</h4>
        <v-icon color="black" @click="$emit('on-close-dialog')">
          mdi-close
        </v-icon>
      </v-card-title>
      <v-card-text class="mt-6 pa-3">
        <v-row 
          class="mb-6 bordered-row" 
          :class="{ 'selected-row': selectedAction === 'sync' }"
          style="cursor: pointer"
          @click="handleActionSelect('sync')"
        >
          <v-col cols="8">
            <div class="d-flex align-center h-100">
              <p class="mb-0">Easily sync vehicle data from your Fleet Management System (FMS) to ensure accurate and up-to-date information across platforms.</p>
            </div>
          </v-col>
          <v-col cols="4" class="d-flex justify-center">
            <v-img
              :src="require(`assets/images/cloud-technology.svg`)"
              max-width="120"
              contain
              class="ml-4"
            />
          </v-col>
        </v-row>
        <v-row 
          class="bordered-row"
          :class="{ 'selected-row': selectedAction === 'unsync' }"
          style="cursor: pointer"
          @click="handleActionSelect('unsync')"
        >
          <v-col cols="8">
            <div class="d-flex align-center h-100">
              <p class="mb-0">Remove synced vehicle data from the Fleet Management System (FMS) to maintain platform accuracy and manage data independently.</p>
            </div>
          </v-col>
          <v-col cols="4" class="d-flex justify-center">
            <v-img
              :src="require(`assets/images/loading-illustration.svg`)"
              max-width="120"
              contain
              class="ml-4"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Sync Form -->
    <v-card v-else-if="selectedAction === 'sync'" class="pa-md-10 pa-5">
      <v-form ref="form" v-model="isValid">
        <v-card-title class="pa-0 d-flex justify-space-between">
          <h4>Sync vehicle from FMS</h4>
          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <div class="d-flex flex-column align-center">
          <v-col v-if="isLoadingSubmit" class="align-center justify-center">
            <v-img
              :src="require(`assets/images/loading-illustration.svg`)"
              min-width="260"
              max-width="260"
              aspect-ratio="1"
              contain
              class="mb-2 ma-auto"
            />
            <h4 class="text-center">Please wait a minutes</h4>
            <p class="text-center">
              The system is fetching vehicle data from the server for less than a
              minutes
            </p>
          </v-col>
          <v-img
            v-else
            :src="require(`assets/images/cloud-technology.svg`)"
            min-width="260"
            max-width="260"
            aspect-ratio="1"
            contain
            class="mb-2 m-auto"
          />
          <v-col v-if="!isLoadingSubmit">
            <custom-text-field
              v-model="email"
              label="Email"
              :hint="$t('login.enter_email')"
              prepend-inner-icon="mdi-email"
              :rules="[rulesRequired, rulesEmail]"
              :hidden-hint-outlined="true"
              @on-enter="login"
            />

            <custom-text-field
              v-model="password"
              label="Password"
              :hint="$t('login.enter_password')"
              :type="isShowPassword ? 'text' : 'password'"
              prepend-inner-icon="mdi-lock"
              :rules="[rulesRequired]"
              :append-icon="isShowPassword ? 'mdi-eye' : 'mdi-eye-off'"
              :hidden-hint-outlined="true"
              @click:append="isShowPassword = !isShowPassword"
              @on-enter="login"
            />
            <div class="d-flex">
              <v-btn
                class="mr-3"
                height="52"
                color="primary"
                depressed
                :loading="isLoadingSubmit"
                :disabled="!isValid"
                @click="login"
              >
                Sync Vehicle
              </v-btn>
              <v-btn
                height="52"
                color="primary"
                outlined
                depressed
                @click="$emit('on-close-dialog')"
              >
                Cancel
              </v-btn>
            </div>
          </v-col>
        </div>
      </v-form>
    </v-card>

    <!-- Unsync Form -->
    <categorize-vehicle-dialog
      v-else-if="selectedAction === 'unsync'"
      type="SYNC"
      :dialog="showCategorizeDialog"
      :selected-action="selectedAction"
      @on-close-dialog="handleCloseCategorize"
      @on-success-add-vehicle-detail="handleSuccessAddVehicle"
    >
      <template #activator>
        <div />
      </template>
    </categorize-vehicle-dialog>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { rules } from '~/utils/functions'
import CategorizeVehicleDialog from '~/components/vendor/CategorizeVehicleDialog.vue'

export default Vue.extend({
  components: {
    CategorizeVehicleDialog
  },

  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isShowPassword: false,
    email: '',
    password: '',
    isValid: false,
    selectedAction: null as string | null,
    showCategorizeDialog: false
  }),

  computed: {
    isLoadingSubmit(): boolean {
      return this.$store.getters['vendor/sync-vehicles/isLoadingSubmitSync']
    },

    isLoadingSubmitSuccess(): boolean {
      return this.$store.getters['vendor/sync-vehicles/isLoadingSubmitSuccess']
    }
  },

  watch: {
    isLoadingSubmitSuccess(value: boolean) {
      if (value) {
        this.$emit('on-close-dialog-by-success')
      }
    },
    dialog(value: boolean) {
      if (!value) {
        this.selectedAction = null
        this.showCategorizeDialog = false
      }
    }
  },

  methods: {
    handleActionSelect(action: string) {
      this.selectedAction = action
      this.$emit('update:selectedAction', action);
      if (action === 'unsync') {
        this.showCategorizeDialog = true
      }
    },
    handleCloseCategorize() {
      this.showCategorizeDialog = false
      this.$emit('on-close-dialog')
    },
    handleSuccessAddVehicle() {
      this.showCategorizeDialog = false
      this.$emit('on-close-dialog')
      this.$emit('on-success-add-vehicle-detail')
    },
    login() {
      if (this.isValid) {
        this.$store.dispatch('vendor/sync-vehicles/submitSync', {
          email: this.email,
          password: this.password
        })
      }
    },
    rulesRequired(value: string) {
      return rules.required(value)
    },
    rulesEmail(value: string) {
      return rules.email(value)
    }
  }
})
</script>

<style scoped>
.bordered-row {
  border: 1px solid rgb(192, 190, 190);
  padding: 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.bordered-row:hover {
  border-color: #ff4081;
  background-color: rgba(255, 64, 129, 0.05);
}

.selected-row {
  border-color: #ff4081;
  background-color: rgba(255, 64, 129, 0.05);
}
</style>

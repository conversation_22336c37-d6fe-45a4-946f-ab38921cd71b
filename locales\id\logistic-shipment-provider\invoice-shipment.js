module.exports = {
  invoice_order_shipment: 'Tagihan Order Shipment',
  detail_invoice: 'Detail Tagihan',
  invoice_from_vendor: 'Tagihan dari Vendor',
  invoice_number: 'Nomor Tagihan',
  select_order: 'Pilih Order',
  identity_product: 'Identitas produk',
  product: 'Produk',
  quantity: '<PERSON><PERSON><PERSON>',
  weight: 'Berat',
  estimation: 'Est<PERSON><PERSON>',
  location: 'Lokasi',
  select_vehicle: 'Pilih Kendaraan',
  sub_order_your_choice: 'Suborder yang anda pilih',
  total_weight: 'Total Berat',
  total_length: 'Total Panjang',
  total_width: 'Total Lebar',
  total_height: 'Total Tinggi',
  shipping_cost: 'Biaya pengiriman',
  add_additional_fee: 'Tambahkan biaya tambahan',
  the_amount_that_must_be_paid: 'Jumlah yang harus dibayar',
  back_to_list_invoice: 'Kembali ke daftar tagihan',
  download_invoice: 'Unduh Tagihan',
  no_additional_fee: 'Tidak ada tambahan biaya',
  preview_invoice: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  check_again: '<PERSON><PERSON>',
  send_order: '<PERSON><PERSON>',
  send_invoice: '<PERSON><PERSON>',
  order_number: 'Nomor order',
  save_invoice: 'Simpan Tagihan',
  dialog_send_invoice: 'Apakah Anda yakin telah mengisi pesanan pengiriman',
  dialog_send_invoice_correctly: 'dengan benar?',
  invoice_empty_title: 'Anda belum memiliki Tagihan',
  invoice_empty_desc: 'Sekarang anda belum memiliki riwayat tagihan pada order shipment'
}

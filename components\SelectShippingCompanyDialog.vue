<template>
  <v-dialog v-model="dialog" persistent max-width="640px">
    <template #activator="{ on, attrs }">
      <slot name="activator" :on="on" :attrs="attrs" />
    </template>

    <v-card class="pa-md-10 pa-5">
      <v-form ref="form">
        <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
          <h4>Product</h4>

          <v-icon color="black" @click="$emit('on-close-dialog')">
            mdi-close
          </v-icon>
        </v-card-title>

        <p class="mb-5 subtitle-1" style="color: #6E6666 ">
          Before Create order, Please select Product first
        </p>
        <v-select
          v-model="selected"
          :loading="isLoadingCustomers"
          outlined
          :items="shipmentCompanyData.items.map((e) => {
            return {
              text: e.name,
              value: e
            }
          })"
          :label="$t('lspCreateShipment.select_product')"
          :hint="$t('lspCreateShipment.select_shipping_company')"
        />
        <div class="d-flex mt-2">
          <v-btn
            depressed
            class="mr-2"
            color="primary"
            :disabled="!selected"
            @click="
              $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', selected)
              $router.push(localePath('/logistic-service-provider/order-shipment/create-shipment/create-order'))
            "
          >
            Save Product
          </v-btn>
          <v-btn
            outlined
            depressed
            color="primary"
            @click="
              selected = null;
              $store.commit('logistic-service-provider/SET_SELECTED_SHIPMENT_COMPANY', null);
              $emit('on-close-dialog')"
          >
            {{ $t('lspCreateShipment.cancel') }}
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { ShippingCompany } from '~/types/user'

export default Vue.extend({
  name: 'SelectShipmentCompanyDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      selected: null

    }
  },

  computed: {
    shipmentCompanyData (): {items: ShippingCompany[], totalPage: number, page: number} {
      return this.$store.getters['shipping-company/data']
    },
    isLoadingCustomers (): Boolean {
      return this.$store.getters['shipping-company/isLoading']
    }
  },

  watch: {
    dialog (value: any) {
      if (value) {
        this.getCustomers()
      }
    }
  },

  methods: {
    getCustomers () {
      this.$store.dispatch('shipping-company/getItems', {
        filterColumns: 'logistics_service_provider_id',
        filterKeys: this.$auth.$state.user.data.logistics_service_provider_id,
        entries: '-1',
        fetchDetail: false
      })
    }
  }
})
</script>

<style scoped>

</style>

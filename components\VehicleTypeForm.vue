<template>
  <v-container fluid class="pa-0">
    <v-container
      fluid
      class="mb-10 pa-0 d-flex justify-space-between align-center"
    >
      <h3>{{ $t('vehicleTypeForm.manage_vehicle_type') }}</h3>

      <v-icon
        color="secondary"
        @click="$emit('on-click-close')"
      >
        mdi-close
      </v-icon>
    </v-container>

    <slot name="tab-activator" />

    <v-text-field
      v-model="vehicleTypeName"
      outlined
      hide-details
      :label="$t('vehicleTypeForm.label_type_name')"
      class="custom-text-field mb-6"
      @keydown.enter="createVehicleType"
    >
      <template #append>
        <v-btn
          x-large
          elevation="0"
          color="primary"
          class="text-capitalize"
          :loading="isLoadingForm"
          @click="createVehicleType"
        >
          <p class="subtitle-1 ma-0">
            {{ $t('vehicleTypeForm.button_add') }}
          </p>
        </v-btn>
      </template>
    </v-text-field>
    <v-container v-if="isLoading" fluid class="pa-0">
      <v-container fluid class="mb-3 pa-0 d-flex justify-space-between">
        <v-skeleton-loader type="heading" width="100" />
        <v-skeleton-loader type="heading" width="100" />
      </v-container>

      <v-card class="pa-3 pb-1" style="border: 1px solid #00000061">
        <v-skeleton-loader v-for="i in 3" :key="i" type="heading" width="300" class="mb-2" />
      </v-card>
    </v-container>

    <v-container v-else fluid class="pa-0">
      <v-container fluid class="pa-0 mb-3 d-flex justify-space-between align-center">
        <v-checkbox
          v-model="selectAll"
          color="primary"
          :label="$t('vehicleTypeForm.label_select_all')"
          hide-details
          class="ma-0"
        />

        <v-btn
          text
          color="primary"
          class="text-capitalize"
          :loading="isLoadingDelete"
          @click="deleteVehicleTypes"
        >
          <v-icon class="mr-2">
            mdi-delete
          </v-icon>
          <p class="subtitle-1 ma-0">
            {{ $t('vehicleTypeForm.button_delete') }}
          </p>
        </v-btn>
      </v-container>

      <v-card class="pa-3 pb-1" style="border: 1px solid #00000061">
        <v-checkbox
          v-for="vehicleType in dataVehicleTypes"
          :key="vehicleType.id"
          v-model="selectedVehicleType"
          :value="vehicleType.id"
          hide-details
          :label="vehicleType.name"
          :disabled="!vehicleType.is_owned"
          class="ma-0 mb-2"
        />
      </v-card>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { VehicleType } from '~/types/vehicle'

export default Vue.extend({
  name: 'VehicleTypeFormAddDialog',

  data: () => ({
    vehicleTypeName: '' as string,
    selectAll: false as boolean,
    selectedVehicleType: [] as string[]
  }),

  computed: {
    dataVehicleTypes (): VehicleType[] {
      return this.$store.getters['vehicle/types/data'].items
    },

    isLoading (): Boolean {
      return this.$store.getters['vehicle/types/isLoading']
    },

    isLoadingForm (): Boolean {
      return this.$store.getters['vehicle/types/isLoadingForm']
    },

    isLoadingDelete (): Boolean {
      return this.$store.getters['vehicle/types/isLoadingDelete']
    }
  },

  watch: {
    selectAll () {
      if (this.selectAll) {
        this.selectedVehicleType = this.dataVehicleTypes.map((vehicleType) => {
          return vehicleType.id
        })
      } else {
        this.selectedVehicleType = []
      }
    }
  },

  mounted () { },

  methods: {
    async createVehicleType () {
      await this.$store.dispatch('vehicle/types/createItem', {
        name: this.vehicleTypeName
      })

      this.vehicleTypeName = ''
    },

    async deleteVehicleTypes () {
      await this.$store.dispatch('vehicle/types/deleteItem', {
        ids: this.selectedVehicleType
      })

      this.selectedVehicleType = this.selectedVehicleType.filter(item => !this.selectedVehicleType.includes(item))
    }
  }
})
</script>

<style scoped>
.custom-text-field >>> .v-input__slot {
  padding-right: 4px !important;
}

.custom-text-field >>> .v-input__append-inner {
  padding: 0 !important;
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

.v-skeleton-loader >>> .v-skeleton-loader__heading {
  width: 100px;
}
</style>

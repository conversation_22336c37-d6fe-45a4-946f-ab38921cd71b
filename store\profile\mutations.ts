import { MutationTree } from 'vuex'
import { ProfileLogisticShipmentCompanyState } from './state'
import { LogisticsServiceProvider, Vendor } from '~/types/user'

export const mutations: MutationTree<ProfileLogisticShipmentCompanyState> = {
  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },
  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },
  SET_LOGISTIC_SERVICE_PROVIDER (state, logisticServiceProvider: LogisticsServiceProvider) {
    state.logisticServiceProvider = logisticServiceProvider
  },
  SET_VENDOR (state, vendor: Vendor) {
    state.vendor = vendor
  }
}

export default mutations

import { ActionTree } from 'vuex'
import { LoginState } from './state'
import { exception<PERSON><PERSON><PERSON>, isPasswordExpired } from '~/utils/functions'
import { toastError } from '~/utils/toasts'

export const actions: ActionTree<LoginState, LoginState> = {
  submitLogin ({ commit, dispatch }, payload: any) {
    commit('SET_IS_LOADING', true)

    // Include recaptcha token in the payload
    this.$auth.loginWith('local', { data: payload })
      .then((response: any) => {
        const user = response.data.data
        this.$auth.setUser(response.data)

        this.$auth.setUserToken(user.token)

        if (user.role === 'ADMIN') {
          this.$router.push(this.localePath('/admin/dashboard'))
        } else if (user.role === 'SHIPMENT_COMPANY') {
          if (!user.shipment_company?.logo || !user.shipment_company.address) {
            this.$router.push(this.localePath('/shipping-company/form-profile'))
          } else {
            this.$router.push(this.localePath('/shipping-company/dashboard'))
          }
        } else if (user.role === 'VENDOR') {
          if (response.data.data.last_updated_password === null || isPasswordExpired(response.data.data.last_login)) {
            this.$router.push(this.localePath('/create-new-password'))
          } else {
            this.$router.push(this.localePath('/vendor/order-shipment/create-order'))
          }
        } else if (user.role === 'DRIVER') {
          toastError('You\'re not able to login with this role.', this)
        } else if (user.role === 'LOGISTIC_SERVICE_PROVIDER') {
          if (response.data.data.last_updated_password === null || isPasswordExpired(response.data.data.last_login) || isPasswordExpired(response.data.data.last_updated_password)) {
            this.$router.push(this.localePath('/create-new-password'))
          } else {
            this.$router.push(this.localePath('/logistic-service-provider/order-shipment/create-shipment'))
          }
        }

        Notification.requestPermission().then((permission) => {
          if (permission === 'granted') {
            dispatch('fcm/updateFcm', { fcmToken: payload.fcmToken }, { root: true })
          }
        })
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  }
}

export default actions

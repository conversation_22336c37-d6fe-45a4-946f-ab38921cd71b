<template>
  <v-container fluid class="pa-0 col-12 col-sm-10 d-flex flex-column align-center justify-space-between">
    <v-row align="center" justify="center" class="mb-8">
      <v-img
        :src="require(`~/assets/images/logo-lsi.png`)"
        max-width="80"
        contain
      >
        <template #placeholder>
          <v-img
            min-width="80"
            max-width="80"
            aspect-ratio="1"
            contain
            :src="require(`~/assets/images/placeholder-company-logo.svg`)"
          />
        </template>
      </v-img>
      <div class="ml-4 mr-4" />
      <div class="text-heading-6 spacer-y-lg" style="max-width: 9em">
        Logistic Service Integrator
      </div>
    </v-row>

    <h3 class="mt-4 text-center">
      Create New Password
    </h3>

    <p class="body-1 text-secondary mt-2 text-center">
      Please create your new password and make sure <br> you don't forget the password again.
    </p>

    <v-col cols="5">
      <v-form ref="form" v-model="isValid">
        <custom-text-field
          v-if="!isForgotPassword"
          v-model="form.passwordOld"
          prepend-inner-icon="mdi-lock-outline"
          hint="Current Password"
          class="mt-4"
          :type="isShowPasswordOld ? 'text' : 'password'"
          :append-icon="isShowPasswordOld ? 'mdi-eye' : 'mdi-eye-off'"
          :rules="!isForgotPassword ? [rulesRequired] : []"
          @click:append="isShowPasswordOld = !isShowPasswordOld"
        />

        <custom-text-field
          v-model="form.password"
          prepend-inner-icon="mdi-lock"
          hint="New Password"
          :class="{'mt-5': !isForgotPassword, 'mt-4': isForgotPassword}"
          persistent-hint="Have at least min 8 char, 1 uppercase letter, 1 number and 1 special character"
          :type="isShowPassword ? 'text' : 'password'"
          :append-icon="isShowPassword ? 'mdi-eye' : 'mdi-eye-off'"
          :rules="[rulesRequired, rulesPasswordMinLength]"
          @click:append="isShowPassword = !isShowPassword"
        />

        <custom-text-field
          v-model="form.passwordConfirmation"
          prepend-inner-icon="mdi-lock"
          class="mt-5"
          :hint="$t('scRegistration.label_confirm_password')"
          :type="isShowPasswordConfirmation ? 'text' : 'password'"
          :append-icon="isShowPasswordConfirmation ? 'mdi-eye' : 'mdi-eye-off'"
          :rules="[rulesRequired, rulesPasswordConfirmation]"
          @click:append="isShowPasswordConfirmation = !isShowPasswordConfirmation"
        />
      </v-form>
    </v-col>
    <div class="d-flex justify-center">
      <v-btn
        height="50"
        color="primary"
        block
        class="text-capitalize"
        :disabled="!isValid"
        :loading="isLoading"
        @click="onClickSend"
      >
        Create New Password
      </v-btn>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '~/components/CustomTextField.vue'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'CreateNewPasswordPage',

  components: { CustomTextField },

  props: {
    isForgotPassword: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isShowPasswordOld: false,
    isShowPassword: false,
    isShowPasswordConfirmation: false,
    isValid: false as boolean,
    form: {
      passwordOld: '',
      password: '',
      passwordConfirmation: ''
    }
  }),

  computed: {
    isLoading () {
      return this.$store.getters['password/isLoading']
    }
  },

  methods: {
    onClickSend () {
      this.$emit('on-click-send', this.form)
    },
    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesPasswordMinLength (value: string) {
      return rules.passwordMinLength(value)
    },
    rulesPasswordConfirmation (value: string) {
      return rules.passwordConfirm(value, this.form.password)
    }
  }
})
</script>

<style lang="scss" scoped></style>

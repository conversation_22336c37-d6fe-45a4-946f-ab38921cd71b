<template>
  <v-dialog
    v-model="dialog"
    width="540"
    persistent
  >
    <template #activator="{on, attrs}">
      <slot :on="on" :attrs="attrs" name="activator" />
    </template>

    <v-card>
      <v-card-title class="pa-10 d-flex align-center justify-space-between">
        <h3>Change Order</h3>
        <v-icon
          size="24"
          @click="$emit('on-click-close')"
        >
          mdi-close
        </v-icon>
      </v-card-title>

      <v-card-text class="pa-0 px-10 black--text">
        <v-form v-model="isValid">
          <div class="mb-6">
            <h5 class="mb-1">
              {{ order?.shippingCompanyName }}
            </h5>
            <p class="body-1 ma-0">
              {{ order?.identity }}
            </p>
          </div>

          <v-text-field
            v-model="form.orderNumber"
            outlined
            clearable
            label="New Order Number"
            :rules="[ruleRequired]"
          />

          <v-autocomplete
            v-model="form.locationId"
            outlined
            clearable
            :items="dropOffLocations?.map(location => ({
              text: location.name,
              value: location.id,
              lat: location.latitude,
              lng: location.longitude
            }))"
            label="Location"
            :rules="[ruleRequired]"
            @change="onChangeLocation"
          />

          <v-responsive :aspect-ratio="16/9" class="mb-6 pa-0 rounded">
            <custom-map
              :latitude="mapData.lat ?? -6.9344694"
              :longitude="mapData.lng ?? 107.6049539"
              :zoom="11"
            >
              <template #marker>
                <l-marker
                  v-if="form.locationId"
                  :lat-lng="[ mapData.lat, mapData.lng ]"
                />
              </template>
            </custom-map>
          </v-responsive>

          <div>
            <p class="body-1 mb-2">
              Note change Destination
            </p>
            <v-textarea
              v-model="form.note"
              outlined
              no-resize
              clearable
              row-height="4"
              placeholder="Note"
            />
          </div>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-10 pt-4">
        <v-row class="ma-0 mx-n3">
          <v-col class="pa-0 px-3">
            <v-btn
              block
              x-large
              depressed
              color="primary"
              :disabled="!isValid"
              :loading="isLoading"
              class="subtitle-1 text-capitalize"
              @click="onClickChangeOrder"
            >
              Change Order
            </v-btn>
          </v-col>
          <v-col class="pa-0 px-3">
            <v-btn
              block
              x-large
              outlined
              color="primary"
              class="subtitle-1 text-capitalize"
              @click="$emit('on-click-close')"
            >
              Cancel
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { Order, PickupDropOffLocationPoint, Product, SubOrder } from '~/types/product'
import CustomMap from '~/components/shipping-company/CustomMap.vue'
import { Route } from '~/types/shipment'
import { rules } from '~/utils/functions'

interface FormValues {
  orderNumber: string | null,
  locationId: string | null,
  note: string | null
}

export default Vue.extend({
  name: 'ChangeDropOffDestinationDialog',

  components: {
    CustomMap
  },

  props: {
    dialog: {
      type: Boolean,
      default: false
    },

    isLoading: {
      type: Boolean,
      default: false
    },

    route: {
      type: Object as () => Route | null,
      default: null
    },

    orders: {
      type: Array as () => Order[],
      default: () => []
    },

    dropOffLocations: {
      type: Array as () => PickupDropOffLocationPoint[],
      default: () => []
    }
  },

  data: () => ({
    mapData: {
      lat: null,
      lng: null,
      zoom: null
    } as { lat: number | null, lng: number | null, zoom: number | null },
    isValid: false as boolean,
    form: {
      orderNumber: null,
      locationId: null,
      note: null
    } as FormValues
  }),

  computed: {
    order (): {
      identity: string,
      shippingCompanyId: string,
      shippingCompanyName: string,
      subOrders: SubOrder[],
      productIds: string[]
    } | null {
      if (!this.route) { return null }

      const productIds: string[] = this.route?.products?.map(route => (route.id))

      return this.orders?.map(order => ({
        identity: order.identity,
        shippingCompanyId: order.shipment_company?.id || '',
        shippingCompanyName: order.shipment_company?.name || '',
        subOrders: order.suborders || [],
        productIds: order.pickup_suborders?.flatMap(suborder =>
          suborder?.products?.map(product => product.id)
        ) || []
      })).find(order => order?.productIds?.some(id => (
        productIds?.includes(id)
      ))) || null
    },

    subOrderId (): string | null | any {
      if (!this.route || !this.order) { return null }

      const subOrderIds = this.order?.subOrders
        .filter(suborder => suborder.type === 'DROPOFF')
        .flatMap(suborder => suborder.products)
        .filter(product => this.route?.products.some(p => product.id === p.id))
        .map(product => product.pivot.suborder_id)

      return subOrderIds.pop()
    }
  },

  watch: {
    dialog (isOpen: boolean) {
      if (isOpen) {
        this.form.note = null
        this.form.locationId = null
        this.form.orderNumber = null
      }
    }
  },

  methods: {
    onChangeLocation (locationId: string | null) {
      if (!locationId) { return }

      const selectedLocation: PickupDropOffLocationPoint = this.dropOffLocations.find(location => (
        location.id === locationId
      )) as PickupDropOffLocationPoint

      this.mapData.lat = Number(selectedLocation.latitude)
      this.mapData.lng = Number(selectedLocation.longitude)
    },

    onClickChangeOrder () {
      this.$emit('on-click-change', { subOrderId: this.subOrderId, ...this.form })
    },

    ruleRequired (value: string) {
      return rules.required(value)
    }
  }
})

</script>

<style scoped lang="scss"> </style>

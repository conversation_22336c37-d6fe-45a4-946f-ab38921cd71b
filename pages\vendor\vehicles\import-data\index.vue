<template>
  <v-container fluid class="pa-0 px-5 px-sm-10 mb-10">
    <v-btn
      plain
      class="text-capitalize subtitle-1 mb-6"
      @click="$router.back()"
    >
      <v-icon class="mr-2">
        mdi-chevron-left
      </v-icon>
      Back to List
    </v-btn>

    <import-data-component
      type="VEHICLE"
      :tab="tab"
      :import-keys="vehicleKeys"
      :vehicles="listVehicles"
      :vehicle-details="vehicleDetails"
      @on-select-header-plate-number="isSelectHeaderPlateNumber = $event"
      @on-change-form-values="formValues = $event"
    >
      <template #footer-bulk-import>
        <v-row class="ma-0 mx-n3 col-md-auto pa-md-0">
          <v-col class="col-6 col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              depressed
              color="primary"
              :loading="isLoadingForm"
              :disabled="formValues.length === 0"
              class="subtitle-1 text-capitalize"
              @click="onClickSelect"
            >
              Import Data
            </v-btn>
          </v-col>

          <v-col class="col-6 col-md-auto pa-0 px-3">
            <v-btn
              block
              x-large
              outlined
              color="primary"
              class="subtitle-1 text-capitalize"
              @click="$router.back()"
            >
              Cancel
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </import-data-component>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import ImportDataComponent from '~/components/ImportDataComponent.vue'
import { Vehicle, VehicleDetail, VehicleKey } from '~/types/vehicle'

export default Vue.extend({
  name: 'ImportDataPage',

  components: {
    ImportDataComponent
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    tab: 'tabBulkImport',
    isSelectHeaderPlateNumber: false as boolean,
    formValues: [] as object[],
    formValuesPlateNumber: [] as object[]
  }),

  computed: {
    listVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/listVehicles']
    },

    vehicleDetails (): VehicleDetail[] {
      return this.$store.getters['vehicle/details/data'].items
    },

    vehicleKeys (): VehicleKey[] {
      return this.$store.getters['vehicle/details/vehicleKeys']
    },

    isLoadingForm (): boolean {
      return this.$store.getters['vehicle/details/isLoadingForm']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', 'Import Data')
  },

  mounted () {
    this.getVehicles()
    this.getVehicleKeys()
  },

  methods: {
    getVehicles () {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/getItems', {
        searchKey: '',
        page: '',
        entries: -1,
        filterColumns: 'vendor_id',
        filterKeys: user.vendor.id
      })
    },

    getVehicleKeys () {
      this.$store.dispatch('vehicle/details/getImportVehicleKeys')
    },

    async onClickSelect () {
      const response = await this.$store.dispatch('vehicle/details/bulkImportVehicles', {
        formValues: this.formValues
      })

      if (response) {
        this.isSelectHeaderPlateNumber ? this.tab = 'tabSelectPlate' : this.$router.back()
        this.getVehicles()
      }
    }
  }
})
</script>

<style scoped> </style>

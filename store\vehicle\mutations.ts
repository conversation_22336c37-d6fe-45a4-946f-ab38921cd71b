import { MutationTree } from 'vuex'
import { VehicleState } from './state'
import { Vehicle } from '~/types/vehicle'

export const mutations: MutationTree<VehicleState> = {
  SET_LIST_VEHICLES (state, vehicles: Vehicle[]) {
    state.listVehicles = vehicles
  },

  SET_RESULT (state, response: any) {
    const selectedVehicles = state.items.filter(item => item.isSelected)
    state.items = response.data

    const commonVehicles = selectedVehicles.filter((vehicle) => {
      return state.items.some(item => item.id === vehicle.id)
    })

    if (commonVehicles.length > 0) {
      commonVehicles.forEach((vehicle) => {
        const commonVehicle: Vehicle = state.items.find(item => item.id === vehicle.id) as Vehicle

        const idx = state.items.findIndex(item => item.id === commonVehicle.id)
        state.items[idx] = vehicle
      })
    } else {
      state.items.push(...selectedVehicles)
    }

    state.totalPage = response.meta.last_page
    state.page = response.meta.current_page
  },

  SET_SELECTED_VEHICLE (state, Vehicle: any) {
    state.selectedVehicle = Vehicle
  },

  SET_IS_LOADING_LIST (state, isLoading: boolean) {
    state.isLoadingList = isLoading
  },

  SET_IS_LOADING (state, isLoading) {
    state.isLoading = isLoading
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  },

  SET_IS_LOADING_DELETE (state, isLoadingDelete) {
    state.isLoadingDelete = isLoadingDelete
  },

  SET_VEHICLE_QUANTITY (state, {
    id,
    quantity
  }) {
    const index = state.listVehicles.findIndex(item => item.id === id)
    state.listVehicles[index].quantity = quantity
    state.listVehicles[index].isSelected = Boolean(quantity || quantity > 0)
    state.listVehicles = [...state.listVehicles]
  },

  SET_WEIGHT_VEHICLE (state, {
    id,
    weight
  }) :void {
    for (let i :number = 0; i < state.listVehicles.length; i++) {
      if (state.listVehicles[i].vendor.id === id) {
        const vehicleWeight = weight[id] || null
        state.listVehicles[i].vendor.weight = vehicleWeight
        state.listVehicles[i].isSelected = Boolean(vehicleWeight)
        state.listVehicles = [...state.listVehicles]
      }
    }
  },

  SET_WEIGHT_VENDOR (state, vendorWeight) {
    state.vendorWeight = vendorWeight
  },

  SET_VEHICLE_LIST_ITEM (state, items) {
    state.items = items
    state.listVehicles = items
  },

  SET_PAGE (state, page: number) {
    state.page = page
  },

  SET_TOTAL_PAGE (state, totalPage: number) {
    state.totalPage = totalPage
  },

  SET_SEARCH_KEY (state, searchKey: string) {
    state.searchKey = searchKey
  },

  SET_EXCEED_STATUS (state, { id, isExceeded }) {
    if (!state.exceedMessages) {
      state.exceedMessages = {}
    }
    state.exceedMessages[id] = isExceeded
  }
}

export default mutations

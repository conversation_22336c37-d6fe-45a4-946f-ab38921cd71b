import { GetterTree } from 'vuex'
import { LicenseCategoryState } from '~/store/vehicle/drivers/license-category/state'
import { LicenseCategory } from '~/types/driver'

export const getters: GetterTree<LicenseCategoryState, LicenseCategoryState> = {
  isLoading (state): boolean {
    return state.isLoading
  },

  items (state): LicenseCategory[] {
    return state.items
  }
}

export default getters

import { ActionTree } from 'vuex'
import { SyncState } from './state'
import { RootState } from '~/store'
import { exception<PERSON><PERSON><PERSON> } from '~/utils/functions'

export const actions: ActionTree<SyncState, RootState> = {
  async submitSync ({ commit }, payload) {
    commit('SET_IS_LOADING_SUBMIT_SYNC', true)

    await this.$axios.post('/v1/fms/sync', {
      email: payload.email,
      password: payload.password
    }).then((response: any) => {
      commit('SET_VEHICLES_FMS_RESULT', response.data.data)
      commit('SET_IS_LOADING_SUBMIT_SUCCESS', true)
    }).catch((error: any) => {
      exceptionHandler(error, this)
    }).finally(() => {
      commit('SET_IS_LOADING_SUBMIT_SYNC', false)
    })
  },

  async unSync({ commit }, payload) {
    console.log(payload, 'payload');
    
    commit('SET_IS_LOADING_SUBMIT_SYNC', true);
    try {
        const response = await this.$axios.put('/v1/fms/unsync', payload);
        return true;
    } catch (error) {
        exceptionHandler(error, this);
        return false;
    } finally {
        commit('SET_IS_LOADING_SUBMIT_SYNC', false);
    }
}
}

export default actions

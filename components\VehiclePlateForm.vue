<template>
  <v-container fluid class="pa-0">
    <v-form ref="form" v-model="isValid">
      <v-container
        v-if="formType === 'MOVE' || formType === 'UPDATE'"
        fluid
        class="pa-0"
      >
        <p class="mb-2 subtitle-1">
          {{ $t('vehiclePlateForm.select_plate') }}
        </p>

        <v-autocomplete
          v-model="formUpdate.plateNumberId"
          outlined
          clearable
          hide-details
          :label="$t('vehiclePlateForm.label_select_plate')"
          :items="plateNumbers.map(pn => {
            return { text: pn.plate_number, value: pn.id }
          })"
          class="mb-6"
        />
      </v-container>

      <v-container
        v-if="formType === 'CREATE' || formType === 'MOVE'"
        fluid
        class="pa-0"
      >
        <p class="mb-2 subtitle-1">
          {{ $t('vehiclePlateForm.select_vehicle') }}
        </p>

        <v-autocomplete
          outlined
          clearable
          hide-details
          :loading="isLoadingList"
          :disabled="isLoadingList"
          :rules="[ruleRequired]"
          :label="$t('vehiclePlateForm.label_vehicle')"
          :items="listVehicles.map(v => {
            return { text: v.name, value: v.id }
          })"
          :value="formCreate.vehicleId"
          class="mb-6"
          @change="
            formType === 'CREATE'
              ? formCreate.vehicleId = $event
              : formUpdate.vehicleId = $event
          "
        />
      </v-container>

      <v-container
        v-if="formType === 'CREATE' || formType === 'UPDATE'"
        fluid
        class="pa-0"
      >
        <p class="mb-2 subtitle-1">
          {{ $t('vehiclePlateForm.new_plate_number') }}
        </p>

        <v-row
          v-for="(detail, i) in formCreate.vehicleDetails"
          :key="i"
          class="ma-n3"
        >
          <v-col class="pa-3">
            <v-text-field
              :value="
                formType === 'CREATE'
                  ? formCreate.vehicleDetails[i].plate_numbers
                  : formUpdate.plateNumber
              "
              outlined
              persistent-hint
              :rules="[ruleRequired]"
              :label="$t('vehiclePlateForm.label_plate')"
              :hint="$t('vehiclePlateForm.hint_plate')"
              @input="
                formType === 'CREATE'
                  ? formCreate.vehicleDetails[i].plate_numbers = $event
                  : formUpdate.plateNumber = $event
              "
            />
          </v-col>

          <v-col class="pa-3 pb-10">
            <v-autocomplete
              :value="
                formType === 'CREATE'
                  ? formCreate.vehicleDetails[i].driver_ids
                  : formUpdate.driverId
              "
              outlined
              clearable
              hide-details
              :label="$t('vehiclePlateForm.label_driver')"
              :items="dataDrivers.map((d) => {
                return { text: d.name, value: d.id}
              })"
              @change="
                formType === 'CREATE'
                  ? formCreate.vehicleDetails[i].driver_ids = $event
                  : formUpdate.driverId = $event
              "
            />
          </v-col>

          <v-col
            v-if="formType === 'CREATE'"
            class="pa-3 pb-10 col-auto"
          >
            <v-btn
              v-if="i === 0"
              fab
              text
              outlined
              class="rounded"
              @click="addItemDetail"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>

            <v-btn
              v-else
              fab
              text
              outlined
              class="rounded"
              @click="removeItemDetail(i)"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </v-container>

      <v-row class="ma-n3">
        <v-col class="pa-3">
          <v-btn
            v-if="formType === 'CREATE'"
            block
            x-large
            depressed
            color="primary"
            :disabled="!isValid"
            :loading="isLoadingForm"
            class="subtitle-1 text-capitalize"
            @click="$emit('on-click-add', formCreate)"
          >
            {{ $t('vehiclePlateForm.button_add') }}
          </v-btn>

          <v-btn
            v-else-if="formType === 'MOVE'"
            block
            x-large
            depressed
            color="primary"
            :loading="isLoadingForm"
            class="subtitle-1 text-capitalize"
            @click="$emit('on-click-move', formUpdate)"
          >
            {{ $t('vehiclePlateForm.button_move') }}
          </v-btn>

          <v-btn
            v-else-if="formType === 'UPDATE'"
            block
            x-large
            depressed
            color="primary"
            :loading="isLoadingForm"
            class="subtitle-1 text-capitalize"
            @click="$emit('on-click-update', formUpdate)"
          >
            {{ $t('vehiclePlateForm.button_update') }}
          </v-btn>
        </v-col>

        <v-col class="pa-3">
          <v-btn
            block
            x-large
            outlined
            color="primary"
            class="subtitle-1 text-capitalize"
            @click="$emit('on-click-cancel')"
          >
            {{ $t('vehiclePlateForm.button_cancel') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { Vehicle, VehicleDetail } from '~/types/vehicle'
import { Driver } from '~/types/driver'
import { rules } from '~/utils/functions'

export default Vue.extend({
  name: 'VehiclePlateForm',

  props: {
    formType: {
      type: String,
      default: ''
    },
    plateNumbers: {
      type: Array as () => VehicleDetail[],
      default: () => ([])
    },
    listVehicle: {
      type: Array as () => Vehicle[],
      default: () => ([])
    },
    selectedVehicle: {
      type: Object as () => Vehicle | null,
      default: null
    },
    selectedVehicleId: {
      type: String,
      default: ''
    },
    tab: {
      type: Number,
      default: 0
    },
    dialog: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    isValid: false as boolean,
    formCreate: {
      vehicleId: '' as string,
      vehicleDetails: [{
        plate_numbers: '' as string,
        fms_identity: '' as string,
        driver_ids: '' as string
      }] as any[]
    },

    formUpdate: {
      plateNumberId: '' as string,
      plateNumber: '' as string,
      driverId: '' as string,
      vehicleId: '' as string
    }
  }),

  computed: {
    listVehicles (): Vehicle[] {
      return this.$store.getters['vehicle/listVehicles']
    },

    dataDrivers (): Driver[] {
      return this.$store.getters['vehicle/drivers/data'].items
    },

    isLoadingList (): boolean {
      return this.$store.getters['vehicle/isLoadingList']
    },

    isLoadingForm (): Boolean {
      return this.$store.getters['vehicle/details/isLoadingForm']
    }
  },

  watch: {
    dialog (currentValue: boolean) {
      if (currentValue) {
        this.formCreate.vehicleDetails = [{
          plate_numbers: '' as string,
          fms_identity: '' as string,
          driver_ids: '' as string
        }] as any[]
      }
    },

    tab: {
      handler (currentValue) {
        if (currentValue === 1) {
          this.formUpdate.vehicleId = this.selectedVehicleId
        } else {
          this.formUpdate.vehicleId = ''
        }
      },
      immediate: true
    },

    'formUpdate.plateNumberId' (currentValue) {
      if (this.tab === 0) {
        const plateNumber = this.plateNumbers?.find((plateNumber) => {
          return plateNumber.id === currentValue
        })

        if (plateNumber === undefined) {
          this.formUpdate.driverId = ''
          this.formUpdate.plateNumber = ''
        } else {
          this.formUpdate.driverId = plateNumber.driver_id
          this.formUpdate.plateNumber = plateNumber.plate_number
        }
      }
    }
  },

  mounted () {
    if (this.selectedVehicle) {
      this.formCreate.vehicleId = this.selectedVehicle.id
    }
  },

  methods: {
    addItemDetail () {
      this.formCreate.vehicleDetails.push({
        plate_numbers: '' as string,
        fms_identity: '' as string,
        driver_ids: '' as string
      })
    },

    removeItemDetail (index: number) {
      this.formCreate.vehicleDetails.splice(index, 1)
    },

    ruleRequired (value: string) {
      return rules.required(value)
    }
  }
})
</script>

<style scoped lang="scss"></style>

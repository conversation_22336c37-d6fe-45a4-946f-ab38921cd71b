import { MutationTree } from 'vuex'
import { LogisticServiceProviderState } from './state'

export const mutations: MutationTree<LogisticServiceProviderState> = {
  SET_IS_LOADING_REGISTER_LINK (state, isLoading) {
    state.isLoadingRegisterLink = isLoading
  },

  SET_REGISTER_LINK (state, registerLink) {
    state.registerLink = registerLink
  },

  SET_SELECTED_SHIPMENT_COMPANY (state, shippingCompany) {
    state.selectedShipmentCompany = shippingCompany
  },

  SET_IS_LOADING_FORM (state, isLoadingForm) {
    state.isLoadingForm = isLoadingForm
  }
}

export default mutations

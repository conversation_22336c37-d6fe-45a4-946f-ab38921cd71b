<template>
  <v-app>
    <network-error-overlay v-if="isNetworkError" />

    <side-bar
      v-if="$vuetify.breakpoint.smAndUp"
      title="Logistic Service Integrator"
      :logo="require(`~/assets/images/logo-lsi.png`)"
    >
      <template #navigation-list>
        <navigation-list />
      </template>
    </side-bar>
    <head-bar
      v-if="$vuetify.breakpoint.smAndUp"
      account-setting-url="/profile/account-setting"
    />
    <mobile-head-bar v-else account-setting-url="/profile/account-setting" :logo="require(`~/assets/images/logo-lsi.png`)">
      <template #navigation-list>
        <navigation-list />
      </template>
    </mobile-head-bar>
    <v-main class="bg-color">
      <nuxt />
      <v-snackbar v-model="snackbar" vertical right>
        <div class="text-subtitle-1 pb-2 font-weight-bold">
          Requesting browser notification
        </div>

        <p>We need to access notification for better experience.</p>

        <template #action>
          <v-btn class="mr-3" color="primary" text @click="snackbar=false">
            Close
          </v-btn>
          <v-btn color="primary" @click="requestNotification">
            Request
          </v-btn>
        </template>
      </v-snackbar>
    </v-main>
  </v-app>
</template>

<script lang="ts">
import Vue from 'vue'
import SideBar from '~/components/navigation-drawer/SideBar.vue'
import HeadBar from '~/components/head-bar/HeadBar.vue'
import MobileHeadBar from '~/components/head-bar/MobileHeadBar.vue'
import NavigationList from '~/components/admin/NavigationList.vue'
import NetworkErrorOverlay from '~/components/NetworkErrorOverlay.vue'
import { toastNetworkSuccess } from '~/utils/functions'

export default Vue.extend({
  name: 'LayoutScBody',

  components: { SideBar, HeadBar, MobileHeadBar, NavigationList, NetworkErrorOverlay },

  data: () => ({
    snackbar: false
  }),

  computed: {
    isNetworkError (): Boolean {
      return this.$store.getters['network-error/isNetworkError']
    }
  },

  watch: {
    isNetworkError () {
      if (!this.isNetworkError) {
        toastNetworkSuccess(this)
      }
    }
  },

  mounted () {
    if (
      Notification.permission === 'default' ||
      Notification.permission === 'denied'
    ) {
      this.snackbar = true
    }

    const user = this.$auth.user as any
    const userData = user.data as any
    const firebasePath = `/users/${userData.id}`

    this.$store.dispatch('firebase/getDataRealtime', {
      path: firebasePath,
      appEnv: this.$config.appEnv
    })
  },

  methods: {
    requestNotification () {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          this.snackbar = false
        }
      })
    }
  }
})
</script>

<style lang="scss"></style>

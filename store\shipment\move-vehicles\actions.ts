import { ActionTree } from 'vuex'
import { RootState } from '../../index'
import { MoveVehiclesState } from './state'
import { exceptionHandler } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<MoveVehiclesState, RootState> = {
  async submitMoveDO ({ commit }, payload) {
    commit('SET_IS_LOADING_FORM', true)

    const formData = new FormData()

    formData.append('type', payload.type)
    formData.append('file', payload.file)
    payload.track_ids.forEach((id: any) => {
      formData.append('track_ids[]', id)
    })
    formData.append('order_id', payload.order_id)
    formData.append('new_order_identity', payload.new_order_identity)

    payload.suborders.forEach((suborder: any, index: number) => {
      formData.append(`suborders[${index}][type]`, suborder.type)
      formData.append(`suborders[${index}][estimation_date]`, suborder.estimation_date)
      formData.append(`suborders[${index}][pickup_dropoff_location_point_id]`, suborder.pickup_dropoff_location_point_id)
      suborder.products?.forEach((product: any, productIndex: number) => {
        formData.append(`suborders[${index}][products][${productIndex}][product_id]`, product.product_id)
        formData.append(`suborders[${index}][products][${productIndex}][quantity]`, product.quantity)
      })
    })

    payload.additionals.forEach((additional: any, index: number) => {
      formData.append(`additionals[${index}][vendor_id]`, additional.vendor_id)
      formData.append(`additionals[${index}][weight]`, additional.weight)
      additional.vehicles.forEach((vehicle: any, vehicleIndex: number) => {
        formData.append(`additionals[${index}][vehicles][${vehicleIndex}][vehicle_id]`, vehicle.vehicle_id)
        formData.append(`additionals[${index}][vehicles][${vehicleIndex}][quantity]`, vehicle.quantity)
      })
    })

    return await this.$axios.post('/v1/shipment/move-vehicle', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then((response: any) => {
        toastSuccess(response.data.message, this)
        commit('SET_IS_LOADING_FORM', true)

        this.$router.push(
          '/logistic-service-provider/order-shipment/history-order/detail-history-order/' +
          response.data.data.id
        )

        commit('RESET_STATE')

        return true
      }).catch((error: any) => {
        exceptionHandler(error, this)
        return false
      }).finally(() => {
        commit('SET_IS_LOADING_FORM', false)
      })
  }

}

export default actions

<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container class="pa-0" fluid>
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-filter-change="getData({filter: $event, page: $route.query?.page})"
        @on-search-icon-click="getData({searchKey: $event})"
      >
        <template #button>
          <user-role-form-item
            label="Logistic Provider"
            :is-loading-form="isLoadingForm"
            :dialog="dialogCreateLsp"
            :clear-form="clearForm"
            @on-click-save="createItem"
            @on-close-dialog="
              dialogCreateLsp = false
              clearForm = true
            "
          >
            <template v-if="userEmail === '<EMAIL>'" #activator="{ on, attrs }">
              <v-btn
                v-if="$vuetify.breakpoint.xs"
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                block
                class="mb-4 mt-5"
                v-on="on"
                @click="
                  dialogCreateLsp = true
                  clearForm = false
                "
              >
                {{ $t('adminLsp.add_lsp') }}
              </v-btn>
              <v-btn
                v-else
                depressed
                color="primary"
                height="52"
                v-bind="attrs"
                v-on="on"
                @click="
                  dialogCreateLsp = true
                  clearForm = false
                "
              >
                {{ $t('adminLsp.add_lsp') }}
              </v-btn>
            </template>
          </user-role-form-item>
        </template>
      </header-datatable>
    </v-container>
    <company-loading v-if="isLoading" />
    <v-row v-else class="ma-n5 pa-3">
      <v-row v-if="(data.items.length !== 0)">
        <v-col
          v-for="(item, i) in data.items"
          :key="item.id"
          md="4"
          sm="6"
          class="pa-5"
        >
          <user-role-card-item
            :item="item"
            :is-location-visible="true"
            :is-loading-form="isLoadingForm"
            :is-has-domain-company="true"
            :index="i"
            :dialog-update="dialogUpdateLsp[i]"
            :dialog-delete="dialogDeleteLsp[i]"
            detail-route="/admin/logistic-service-provider-management"
            @on-click-save-edit="editItem"
            @on-click-save-delete="deleteItem"
            @on-open-update-dialog="$set(dialogUpdateLsp, i, true)"
            @on-close-update-dialog="$set(dialogUpdateLsp, i, false)"
            @on-open-delete-dialog="$set(dialogDeleteLsp, i, true)"
            @on-close-delete-dialog="$set(dialogDeleteLsp, i, false)"
          />
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col class="justify-center align-center fill-height">
          <empty-placeholder
            hero="empty-shipment.svg"
            :message-title="$t('adminLsp.empty_message_title')"
            :message-description="$t('adminLsp.empty_message_description')"
          />
        </v-col>
      </v-row>
    </v-row>
    <v-row class="ma-0 mt-10 justify-end">
      <pagination-component
        :page="data.page"
        :total-page="data.totalPage"
        page-id="page"
        @on-change-page="getData({
          page: $event,
          filter: {
            sortColumn: $route.query?.sort_column,
            sortType: $route.query?.sort_type
          }
        })"
      />
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import UserRoleCardItem from '~/components/UserRoleCardItem.vue'
import CompanyLoading from '~/components/loading/CompanyLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'LspManagementPage',

  components: {
    UserRoleCardItem,
    CompanyLoading,
    EmptyPlaceholder,
    PaginationComponent
  },

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  data: () => ({
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    dialogCreateLsp: false,
    dialogUpdateLsp: [] as Array<Boolean>,
    dialogDeleteLsp: [] as Array<Boolean>,
    clearForm: false
  }),

  computed: {
    data () {
      return this.$store.getters['admin/logistic-service-provider/data']
    },
    isLoading () {
      return this.$store.getters['admin/logistic-service-provider/isLoading']
    },
    isLoadingForm () {
      return this.$store.getters[
        'admin/logistic-service-provider/isLoadingForm'
      ]
    },
    userEmail (): string {
      return this.$auth.$state.user.data.email
    }
  },

  mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'Logistic Provider'
    )
    this.getData({
      page: this.$route.query?.page as string
    })
  },

  methods: {
    getData ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      this.$store.dispatch('admin/logistic-service-provider/getItems', {
        name: searchKey,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page
      })
    },
    async createItem (value: any) {
      const response = await this.$store.dispatch(
        'admin/logistic-service-provider/createItem',
        value
      )
      if (response) {
        this.dialogCreateLsp = false
        this.clearForm = true
      }
    },
    async editItem (value: any, i: any) {
      const response = await this.$store.dispatch(
        'admin/logistic-service-provider/editItem',
        value
      )
      if (response) {
        this.$set(this.dialogUpdateLsp, i, false)
      }
    },
    async deleteItem (id: any, i: any) {
      await this.$store.dispatch(
        'admin/logistic-service-provider/deleteItem',
        {
          id
        }
      )
      this.$set(this.dialogDeleteLsp, i, false)
    }
  }
})
</script>

<style scoped lang="scss"> </style>

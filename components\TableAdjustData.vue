<template>
  <v-container fluid class="pa-0 overflow-x-auto">
    <v-data-table
      v-if="dataTableHeaders.length > 0 && dataTableItems.length > 0"
      hide-default-header
      hide-default-footer
      :page.sync="page"
      :headers="dataTableHeaders"
      :items="dataTableItems"
      :items-per-page="10"
      @page-count="$emit('on-page-count', $event)"
    >
      <template #header="{ props: { headers }}">
        <thead class="ma-0" style="height: 56px !important;">
          <tr class="d-flex mx-n2" style="height: 56px !important;">
            <th
              v-for="(header, i) in headers"
              :id="`${i}_header`"
              :key="header.value"
              class="subtitle-1 col-6 col-md-4 col-lg-2 pa-0 px-1"
              style="height: 56px !important;"
            >
              <v-autocomplete
                v-model="selectedHeaders[i]"
                :items="itemHeaders"
                outlined
                hide-details
                return-object
                :background-color="selectedHeaders[i].value === 'plate_number' ? 'success' : 'white'"
                :class="selectedHeaders[i].value === 'plate_number' ? 'flag-autocomplete' : ''"
                @change="oldHeader = header; newHeader = $event; onChangeHeaderKey($event, i)"
              >
                <template #item="{ item }">
                  <p class="body-1 ma-0">
                    {{ item.text }} <span v-if="item.is_required" class="text-primary">*</span>
                  </p>
                </template>

                <template #selection="{ item }">
                  <v-badge dot :value="isHeaderUnchanged[i]" style="max-width: 100% !important">
                    <p class="ma-0 subtitle-1 text-truncate" :style="item.value === 'plate_number' ? 'color: white' : ''">
                      {{ item.text }}
                    </p>
                  </v-badge>
                </template>
              </v-autocomplete>
            </th>
          </tr>
        </thead>
      </template>

      <template #body="{ items }">
        <tbody>
          <tr v-for="(item, i) in items" :key="i" class="d-flex mx-n2">
            <td
              v-for="(data, j) in item"
              :key="j + '_' + i"
              class="d-flex col-6 col-md-4 col-lg-2 px-1 overflow-y-auto"
              style="height: auto !important; max-height: 120px !important;"
              :style="j === 'plate_number' ? 'border-color: white' : ''"
            >
              <div
                class="py-2 d-flex align-center my-auto fill-height"
                style="width: 100%"
                :style="j === 'plate_number' ? 'background-color: #EAF6EC;' : ''"
              >
                <p class="body-1 ma-0 px-2">
                  {{ data }}
                </p>
              </div>
            </td>
          </tr>
        </tbody>
      </template>
    </v-data-table>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { ImportKey } from '~/types/import-key'

interface Header {
  text: string
  value: string
  disabled: boolean
  is_required: boolean
}

export default Vue.extend({
  name: 'TableAdjustData',

  props: {
    page: {
      type: Number,
      default: 1
    },
    type: {
      type: String,
      default: ''
    },
    dataJson: {
      type: Array as () => any[],
      default: () => []
    },
    dataTableHeaders: {
      type: Array as () => Header[],
      default: () => []
    },
    dataTableItems: {
      type: Array as () => any[],
      default: () => []
    },
    importKeys: {
      type: Array as () => ImportKey[],
      default: () => []
    }
  },

  data: () => ({
    formValues: [] as { [key: string]: any },
    formValuesPlateNumber: [] as object[],
    headerOptions: [] as Header[],
    selectedHeaders: [] as Header[],
    oldHeader: null as Header | null,
    newHeader: null as Header | null,
    itemHeaders: [] as Header[],
    isHeaderUnchanged: [] as boolean[]
  }),

  watch: {
    dataJson: {
      handler () {
        this.oldHeader = null
        this.newHeader = null
      },
      immediate: true
    },

    dataTableHeaders: {
      handler () {
        if (!this.newHeader && !this.oldHeader) {
          this.generateHeaderOptions()
        }
      },
      immediate: true
    },

    dataTableItems: {
      handler () {
        if (this.dataTableHeaders.length > 0 || this.dataTableItems.length > 0) {
          this.isHeaderUnchanged = []

          this.dataTableHeaders.forEach((_) => {
            this.isHeaderUnchanged.push(true)
          })

          this.generatePreSelectedHeaders()
        } else {
          this.isHeaderUnchanged = []
          this.formValues = []
          this.emitFormValues()
        }
      },
      immediate: true
    },

    selectedHeaders: {
      handler () {
        this.generateItemHeaders()
      },
      immediate: true
    }
  },

  methods: {
    generatePreSelectedHeaders () {
      this.formValues = []
      this.selectedHeaders = [] as Header[]

      this.dataTableHeaders?.forEach((header: Header) => {
        this.selectedHeaders.push(header)
      })

      this.dataTableItems?.forEach((item: object) => {
        this.formValues.push(item)
      })

      this.emitFormValues()
    },

    onChangeHeaderKey (header: Header, i: number) {
      const newKey = header.value
      const formValues = this.formValues as object[]
      const keys = Object.keys(formValues[0]) as string[]
      const currentKey = keys[i]
      let tempVal = '' as string | string[]

      formValues.forEach((value: any, index: number) => {
        Object.keys(value).forEach((key) => {
          tempVal = value[key]

          if (currentKey === key) {
            this.formValues[index][newKey] = tempVal
            delete this.formValues[index][key]
          } else {
            delete this.formValues[index][key]
            this.formValues[index][key] = tempVal
          }
        })
      })

      this.$set(this.isHeaderUnchanged, i, false)

      this.generateItemHeaders()
      this.generateTableHeaders(header, i)
      this.emitFormValues()
    },

    generateHeaderOptions () {
      this.headerOptions = this.importKeys?.map((header: any) => {
        return {
          text: header.name,
          value: header.key,
          is_required: header.is_required
        }
      }) as Header[]

      this.dataTableHeaders?.forEach((header: Header) => {
        this.headerOptions.push(header)
      })

      if (this.type === 'VEHICLE') {
        const isOptionIncludePlateNumber = this.headerOptions.some(o => o.value === 'plate_number')

        if (!isOptionIncludePlateNumber) {
          this.headerOptions.push({
            text: 'Plate Number',
            value: 'plate_number',
            disabled: false,
            is_required: false
          })
        }
      }
    },

    regenerateHeaderOption () {
      if (!this.oldHeader || !this.newHeader) { return }

      const indexNewHeader = this.headerOptions.findIndex(option => option.value === this.newHeader?.value)
      const indexOldHeader = this.headerOptions.findIndex(option => option.value === this.oldHeader?.value)

      this.$set(this.headerOptions, indexNewHeader, this.oldHeader)
      this.$set(this.headerOptions, indexOldHeader, this.newHeader)
    },

    generateItemHeaders () {
      this.regenerateHeaderOption()

      this.itemHeaders = this.headerOptions.map((option: Header) => {
        return {
          text: option.text,
          value: option.value,
          disabled: this.selectedHeaders.some(header => header.value === option.value),
          is_required: option.is_required
        }
      })
    },

    generateTableHeaders (header: Header, i: number) {
      this.$emit('on-change-header', { header, i })
    },

    emitFormValues () {
      const formValues = this.formValues as any[]
      let formValuesPlateNumber = [] as object[]

      if (this.type === 'VEHICLE') {
        const isIncludePlateNumber = formValues.some(fv => Object.keys(fv).includes('plate_number'))

        if (isIncludePlateNumber) {
          formValuesPlateNumber = formValues.map(fv => fv.plate_number)
        } else {
          formValuesPlateNumber = []
        }

        this.$emit('on-change-form-values', {
          formValuesVehicle: formValues,
          formValuesPlateNumber
        })
      } else {
        this.$emit('on-change-form-values', formValues)
      }
    }
  }
})
</script>

<style scoped>
tr:hover {
  background-color: rgba(0, 0, 0, 0) !important;
}

.flag-autocomplete >>> .v-input__slot{
  transition: .33s;
}

.flag-autocomplete >>> fieldset {
  transition: .33s;
  border-color: #EAF6EC;
}

.flag-autocomplete >>> i {
  transition: .33s;
  color: white;
}

.flag-autocomplete >>> .primary--text {
  color: white !important;
}
</style>

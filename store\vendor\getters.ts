import { GetterTree } from 'vuex'
import { RootState } from '~/store'
import { VendorState } from '~/store/vendor/state'
import { colors } from '~/utils/colors'

export const getters: GetterTree<VendorState, RootState> = {
  data (state) {
    return {
      items: state.items,
      page: state.page,
      totalPage: state.totalPage
    }
  },

  dataRepotVendor (state) {
    return {
      items: state.itemsVendorReport,
      page: state.pageVendorReport,
      totalPage: state.totalPageVendorReport
    }
  },

  dataVendorOrderShipment (state) {
    return state.itemsVendorOrderShipment
  },

  totalWeightOrder (state) {
    return state.itemsVendorOrderShipment.length > 0 ? state.itemsVendorOrderShipment.reduce((sum, record) => sum + record.mill_weight, 0) : 0
  },

  pieChartData (state) {
    let totalRefineryWeight = 0

    const initPieChart = {
      labels: [] as any,
      datasets: [
        {
          label: 'Pie Dataset',
          backgroundColor: [] as any,
          data: [] as any
        }
      ]
    }

    const vendorData = [
        {
            "vendor": "JAYA",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 50,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "SUPER",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 50,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "GUDANG GARAM",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 50,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
          "vendor": "SAMPOERNA",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 50,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
        {
          "vendor": "MILD",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 50,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        }
    ];

    vendorData.forEach((item, index) => {
        initPieChart.labels.push(item.vendor)
        initPieChart.datasets[0].backgroundColor.push(colors[index])
        initPieChart.datasets[0].data.push(item.total_netto_pickup)
    });

    return initPieChart
  },

  barChartData (state) {
    const initBarChart = {
      labels: [] as any,
      datasets: [
        {
          type: 'bar',
          label: 'Total Pickup Weight',
          backgroundColor: '#3E4784',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Total Dropoff Weight',
          backgroundColor: '#0094BC',
          data: [] as any,
          order: 2
        },
        {
          type: 'line',
          label: 'Netto Loss Percentage',
          borderColor: '#FF4560',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        }
      ]
    }

    const vendorData = [
        {
            "vendor": "JAYA",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 10,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "SUPER",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 20,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "GUDANG GARAM",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 30,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
          "vendor": "SAMPOERNA",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 40,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
        {
          "vendor": "MILD",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 70,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
    ];

    if (vendorData.length > 0) {
      vendorData.forEach((item) => {
        initBarChart.labels.push(item.vendor)
        initBarChart.datasets[0].data.push(item.total_netto_pickup)
        initBarChart.datasets[1].data.push(item.total_netto_dropoff)
        initBarChart.datasets[2].data.push(item.netto_loss_percentage)
      })
    }

    return initBarChart
  },

  barChartDataTrips (state) {
    const initBarChartTrips = {
      labels: [] as any,
      datasets: [
        {
          label: 'Delivery Order',
          backgroundColor: '#486945',
          data: [] as any
        },
        {
          label: 'Shipment',
          backgroundColor: '#85D149',
          data: [] as any
        }
      ]
    }

    const vendorData = [
      {
          "vendor": "JAYA",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 10,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
      },
      {
          "vendor": "SUPER",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 20,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
      },
      {
          "vendor": "GUDANG GARAM",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 30,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
      },
      {
        "vendor": "SAMPOERNA",
        "total_order_requested": 19,
        "total_order_accepted": 18,
        "order_acceptance_rate": 94.74,
        "total_netto_pickup": 2000,
        "total_netto_dropoff": 1000,
        "total_netto_loss": 1000,
        "netto_loss_percentage": 40,
        "total_shipment_track_trip": 2,
        "total_order_trip": 1
      },
      {
        "vendor": "MILD",
        "total_order_requested": 19,
        "total_order_accepted": 18,
        "order_acceptance_rate": 94.74,
        "total_netto_pickup": 2000,
        "total_netto_dropoff": 1000,
        "total_netto_loss": 1000,
        "netto_loss_percentage": 70,
        "total_shipment_track_trip": 2,
        "total_order_trip": 1
      },
  ];

    if (vendorData.length > 0) {
      vendorData.forEach((item) => {
        initBarChartTrips.labels.push(item.vendor)
        initBarChartTrips.datasets[0].data.push(item.total_order_trip)
        initBarChartTrips.datasets[1].data.push(item.total_shipment_track_trip)
      })
    }

    return initBarChartTrips
  },

  barChartDataAssignmentAccuracy (state) {
    const initBarChart = {
      labels: [] as any,
      datasets: [
        {
          type: 'bar',
          label: 'Request Unit',
          backgroundColor: '#486945',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Acc by Actual Coming',
          backgroundColor: '#85D149',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Acc by Transporter',
          backgroundColor: '#854A0E',
          data: [] as any,
          order: 2
        },
        {
          type: 'bar',
          label: 'Auto WB',
          backgroundColor: '#FB6514',
          data: [] as any,
          order: 2
        },
        {
          type: 'line',
          label: 'Netto Loss Percentage',
          borderColor: '#FF4560',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        },
        {
          type: 'line',
          label: 'Netto Loss Percentage',
          borderColor: '#EE46BC',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        },
        {
          type: 'line',
          label: 'Netto Loss Percentage',
          borderColor: '#EF3434',
          borderWidth: 2,
          data: [] as any,
          yAxisID: 'percentage',
          order: 1
        }
      ]
    }

    const vendorData = [
        {
            "vendor": "JAYA",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 10,
            "percentage": 20,
            "percentage_two": 80,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "SUPER",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 20,
            "percentage": 30,
            "percentage_two": 70,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
            "vendor": "GUDANG GARAM",
            "total_order_requested": 19,
            "total_order_accepted": 18,
            "order_acceptance_rate": 94.74,
            "total_netto_pickup": 2000,
            "total_netto_dropoff": 1000,
            "total_netto_loss": 1000,
            "netto_loss_percentage": 30,
            "percentage": 40,
            "percentage_two": 60,
            "total_shipment_track_trip": 2,
            "total_order_trip": 1
        },
        {
          "vendor": "SAMPOERNA",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 40,
          "percentage": 70,
          "percentage_two": 30,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
        {
          "vendor": "MILD",
          "total_order_requested": 19,
          "total_order_accepted": 18,
          "order_acceptance_rate": 94.74,
          "total_netto_pickup": 2000,
          "total_netto_dropoff": 1000,
          "total_netto_loss": 1000,
          "netto_loss_percentage": 70,
          "percentage": 80,
          "percentage_two": 20,
          "total_shipment_track_trip": 2,
          "total_order_trip": 1
        },
    ];

    if (vendorData.length > 0) {
      vendorData.forEach((item) => {
        initBarChart.labels.push(item.vendor)
        initBarChart.datasets[0].data.push(item.total_netto_pickup)
        initBarChart.datasets[1].data.push(item.total_netto_dropoff)
        initBarChart.datasets[2].data.push(item.total_netto_pickup)
        initBarChart.datasets[3].data.push(item.total_netto_dropoff)
        initBarChart.datasets[4].data.push(item.netto_loss_percentage)
        initBarChart.datasets[5].data.push(item.percentage)
        initBarChart.datasets[6].data.push(item.percentage_two)
      })
    }

    return initBarChart
  },

  selectedVendor (state) {
    return state.item
  },

  isLoading (state) {
    return state.isLoading
  },

  isLoadingForm (state) {
    return state.isLoadingForm
  },

  isLoadingVendorReport (state) {
    return state.isLoadingVendorReport
  },

  isLoadingFormStatus (state) {
    return {
      accept: state.isLoadingFormStatus.accept,
      reject: state.isLoadingFormStatus.reject
    }
  },

  isLoadingExportExcel (state) {
    return state.isLoadingExportExcel
  },

  isLoadingVendorOrderShipment (state) {
    return state.isLoadingVendorOrderShipment
  }
}

export default getters

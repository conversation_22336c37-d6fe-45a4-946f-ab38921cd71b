<template>
  <v-row justify="space-between">
    <v-col class="mt-16 ma-10">
      <company-logo v-if="!isLoadingPersonalize" :image="personalize.logo_url" :text="personalize.name" />
      <div v-else class="row align-center justify-center">
        {{/* skeleton loading */}}
        <v-skeleton-loader
          type="image"
          width="220"
          height="80"
        />
      </div>
      <div class="text-heading-5 mt-10">
        Login
      </div>
      <div v-if="!isLoadingPersonalize" class="text-body">
        {{ $t('login.welcome_to') }} {{ personalize.name }}, {{ $t('login.please_input') }}
      </div>
      <div v-else class="text-body">
        {{/* skeleton loading */}}
        <v-skeleton-loader
          type="text"
          width="300"
          height="20"
        />
      </div>
      <v-form class="mt-5">
        <custom-text-field
          v-model="email"
          label="Email"
          type="email"
          :hint="$t('login.enter_email')"
          prepend-inner-icon="mdi-email"
          :rules="[rulesRequired, rulesEmail]"
          :hidden-hint-outlined="true"
          is-required
          data-testid="input-email"
          @on-enter="onClickLogin"
        />

        <custom-text-field
          v-model="password"
          label="Password"
          type="password"
          :hint="$t('login.enter_password')"
          :type="isShowPassword ? 'text' : 'password'"
          prepend-inner-icon="mdi-lock"
          :rules="[rulesRequired]"
          :append-icon="isShowPassword ? 'mdi-eye' : 'mdi-eye-off'"
          :hidden-hint-outlined="true"
          is-required
          data-testid="input-password"
          @click:append="isShowPassword = !isShowPassword"
          @on-enter="onClickLogin"
        />
        <div class="spacer-y-lg" />
        <v-btn
          height="52"
          color="primary"
          block
          depressed
          :loading="isLoading"
          @click="onClickLogin"
        >
          {{ $t('login.login') }}
        </v-btn>
        <div class="spacer-y-lg" />
        <v-btn
          height="52"
          color="primary"
          outlined
          block
          depressed
          @click="$router.push('/register-vendor')"
        >
          {{ $t('login.register') }}
        </v-btn>
        <div class="d-flex justify-space-between mt-4 mb-2">
          <a class="text-primary" @click="$router.push('/login-admin')">
            {{ $t('login.login_as_admin') }}
          </a>
          <a class="blue--text" @click="$router.push('/login/forgot-password')">Forgot Password?</a>
        </div>
        <div class="d-flex justify-end">
          <a class="black--text"> v{{ version }}</a>
        </div>
        <div class="mt-8 d-flex justify-center">
          <v-img :src="require(`~/assets/icons/powered.svg`)" max-width="150" />
        </div>
      </v-form>
    </v-col>
    <div
      v-if="$vuetify.breakpoint.mdAndUp"
      class="ma-0 pa-0"
      style="width: 50%"
      data-testid="image-side"
    >
      <v-img
        v-if="personalize?.register_banner_url"
        :src="personalize?.register_banner_url"
        height="100vh"
        contain
      />
      <v-img
        v-else
        :src="require(`~/assets/images/login-side-image-lsp.png`)"
        height="100vh"
      />
    </div>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import CustomTextField from '@/components/CustomTextField.vue'
import { rules } from '~/utils/functions'
import { Personalize } from '~/types/user'

export default Vue.extend({
  name: 'LoginCustomerPage',
  components: {
    CustomTextField
  },

  middleware: 'is-logged-in',

  data: () => ({
    email: process.env.NODE_ENV === 'production' ? '' : '<EMAIL>',
    password: process.env.NODE_ENV === 'production' ? '' : 'password',
    isShowPassword: false,
    recaptchaToken: ''
  }),

  computed: {
    isLoading () {
      return this.$store.getters['login/isLoading']
    },

    isLoadingPersonalize () {
      return this.$store.getters['logistic-service-provider/personalize/isLoading']
    },

    personalize () {
      return this.$store.getters['logistic-service-provider/personalize/data'] as Personalize
    },

    messagingToken (): string {
      return this.$store.getters['firebase/messagingToken']
    },

    version () {
      return require('../../package.json').version
    }
  },

  mounted () {
    this.getPersonalize()
    const domain = this.$store.getters.domain

    this.$store.dispatch(
      'logistic-service-provider/personalize/getPersonalize',
      {
        domain
      }
    )
  },

  methods: {
    getPersonalize () {
      const ctx = this
      setTimeout(function () {
        const personalize = ctx.$store.getters['logistic-service-provider/personalize/data'] as Personalize
        ctx.$vuetify.theme.themes.light.primary = personalize.primary_color ?? ctx.$vuetify.theme.themes.light.primary
      }, 100)
    },

    onClickLogin () {
      this.executeRecaptcha()
        .then(() => this.login())
        .finally(() => { this.getMessagingToken() })
    },

    async executeRecaptcha () {
      try {
        // Check if recaptcha is available
        if (this.$recaptcha) {
          // Execute reCAPTCHA with action 'login'
          this.recaptchaToken = await this.$recaptcha.execute('login')
          console.log('reCAPTCHA executed successfully')
        } else {
          console.warn('reCAPTCHA instance not available')
          this.recaptchaToken = ''
        }
      } catch (error) {
        console.error('reCAPTCHA error:', error)
        // Continue with login even if reCAPTCHA fails
        this.recaptchaToken = ''
      }
    },

    async getMessagingToken () {
      await this.$store.dispatch('firebase/getMessagingToken', {
        vapidKey: this.$config.vapidKey
      })
    },

    async login () {
      const domain = this.$store.getters.domain

      await this.$store.dispatch('login/submitLogin', {
        email: this.email,
        password: this.password,
        domain,
        fcmToken: this.messagingToken,
        recaptchaToken: this.recaptchaToken
      })
    },

    rulesRequired (value: string) {
      return rules.required(value)
    },
    rulesEmail (value: string) {
      return rules.email(value)
    }
  }
})
</script>

<style scoped>
.side-image {
  background-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.75) 0%,
    transparent 35%
  );
}
</style>

<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-container fluid class="pa-0">
      <v-alert
        v-if="syncedVehicles.length !== 0"
        type="info"
        elevation="0"
        prominent
        colored-border
        text
        color="info"
        style="background-color: #ffffff"
      >
        <v-row align="center">
          <v-col class="grow">
            {{ $t('vendorVehicles.alert_uncategorize_fms') }}
          </v-col>
          <v-col class="shrink">
            <categorize-vehicle-dialog
              type="SYNC"
              :dialog="dialogCategorize"
              @on-close-dialog="dialogCategorize = false"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  outlined
                  color="primary"
                  style="height: 55px"
                  v-on="on"
                  @click="dialogCategorize = true; categorizeFms = true"
                >
                  {{ $t('vendorVehicles.categorize_vehicle_now') }}
                </v-btn>
              </template>
            </categorize-vehicle-dialog>
          </v-col>
        </v-row>
      </v-alert>
      <header-datatable
        default-sort-column="name"
        default-sort-type="asc"
        :sort-column-items="sortColumnItems"
        :sort-type-items="sortTypeItems"
        sort-column-id="sort_column"
        sort-type-id="sort_type"
        @on-filter-change="
          getDataVehicles({
            page: $route.query?.page,
            filter: $event,
            entries: entriesMode
          })"
        @on-search-icon-click="
          getDataVehicles({
            searchKey: $event,
            entries: entriesMode
          })"
      >
        <template #data-button>
          <menu-export-import
            :route-import="`${$route.path}/import-data`"
          />
        </template>

        <template #toggle-button>
          <toggle-button @grid="getDataVehicles({ entries: 3 })" @table="getDataVehicles({ entries: 9 })" />
        </template>

        <template #button>
          <div class="d-flex mt-5 mt-md-0">
            <sync-fms-dialog
              :dialog="dialogSync"
              @on-close-dialog="dialogSync = false; handleCloseSyncDialog()"
              @on-close-dialog-by-success="dialogSync = false; dialogCategorize = true"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-if="$vuetify.breakpoint.xs"
                  depressed
                  color="primary"
                  outlined
                  height="52"
                  class="mr-5"
                  :on="on"
                  :attrs="attrs"
                  @click="dialogSync = true; $store.commit('vendor/sync-vehicles/RESET_STATE'); categorizeFms = true"
                >
                  {{ $t('vendorVehicles.sync_vehicle') }}
                </v-btn>
                <v-btn
                  v-else
                  depressed
                  color="primary"
                  outlined
                  height="52"
                  class="mr-5"
                  :on="on"
                  :attrs="attrs"
                  @click="dialogSync = true; $store.commit('vendor/sync-vehicles/RESET_STATE'); categorizeFms = true"
                >
                  {{ $t('vendorVehicles.sync_vehicle') }}
                </v-btn>
              </template>
            </sync-fms-dialog>

            <vehicle-form-dialog
              :dialog="dialogCreateVehicle"
              @on-click-close="dialogCreateVehicle = false"
              @on-success-create="getDataVehicles({
                page: $route.query?.page,
                entries: entriesMode
              })"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  x-large
                  elevation="0"
                  color="primary"
                  class="text-capitalize"
                  v-on="on"
                  @click="dialogCreateVehicle = true"
                >
                  <p class="subtitle-1 ma-0">
                    {{ $t('vendorVehicles.add_vehicle') }}
                  </p>
                </v-btn>
              </template>
            </vehicle-form-dialog>
          </div>
        </template>
      </header-datatable>
    </v-container>

    <div v-if="isHasNullVehicle" style="background-color: #E6F4F8" class="align-center mb-5">
      <v-col class="grow d-flex justify-center align-center">
        <p style="color: #0094BC" class="mb-0 d-inline-flex">
          {{ $t('vendorVehicles.alert_uncategorize_vehicle_detail') }}
        </p>
        <categorize-vehicle-dialog
          type="CATEGORIZE"
          :dialog="dialogCategorize"
          @on-close-dialog="dialogCategorize = false"
          @on-success-add-vehicle-detail="getDataVehicleDetails()"
          @on-success-delete-vehicle-detail="getDataVehicleDetails()"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              v-bind="attrs"
              color="primary"
              style="height: 55px"
              class="d-inline-flex ml-8"
              v-on="on"
              @click="dialogCategorize = true; categorizeFms = false; "
            >
              {{ $t('vendorVehicles.categorize_now') }}
            </v-btn>
          </template>
        </categorize-vehicle-dialog>
      </v-col>
    </div>

    <!-- <vehicles-loading v-if="isLoadingVehicle" /> -->
    <!-- <v-col v-else class="pa-0">
      <v-col v-if="vehicles.items.length !== 0" class="pa-0">
        <v-col
          v-for="(item, i) in vehicles.items"
          :key="item.id"
          class="col-12 pa-0 mb-10"
        >
          <vehicle-card-item
            :vehicle="item"
            :is-loading-form="isLoadingFormVehicle"
            :index="i"
            :dialog-delete="dialogDeleteVehicle[i]"
            @on-click-save-delete="deleteVehicle"
            @on-open-delete-dialog="$set(dialogDeleteVehicle, i, true)"
            @on-close-delete-dialog="$set(dialogDeleteVehicle, i, false)"
            @on-success-create-plate="
              getDataVehicles({
                page: $route.query?.page,
                entries: entriesMode
              })
            "
            @on-success-delete-plate="
              getDataVehicles({
                page: $route.query?.page,
                entries: entriesMode
              })
            "
          >
            <template #form-update-vehicle>
              <vehicle-form-dialog
                :dialog="dialogUpdateVehicle[i]"
                :vehicle="item"
                @on-click-close="$set(dialogUpdateVehicle, i, false)"
                @on-success-remove-image="
                  getDataVehicles({
                    page: $route.query?.page,
                    entries: entriesMode
                  })
                "
                @on-success-update="
                  getDataVehicles({
                    page: $route.query?.page,
                    entries: entriesMode
                  })
                "
              >
                <template #activator="{ on, attrs }">
                  <v-list-item
                    class="ma-n4"
                    v-bind="attrs"
                    v-on="on"
                    @click="$set(dialogUpdateVehicle, i, true)"
                  >
                    <v-list-item-title>
                      <v-icon style="color: black">
                        mdi-pencil
                      </v-icon>
                      {{ $t('vendorVehicles.edit') }}
                    </v-list-item-title>
                  </v-list-item>
                </template>
              </vehicle-form-dialog>
            </template>

            <template #form-update-plate>
              <vehicle-plate-form-update-dialog
                :dialog="dialogUpdateVehiclePlate[i]"
                :plate-numbers="item.vehicle_details"
                :selected-vehicle-id="item.id"
                :vendor-id="vendorId"
                @on-close-dialog="$set(dialogUpdateVehiclePlate, i, false)"
                @on-success-update="
                  getDataVehicles({
                    page: $route.query?.page,
                    entries: entriesMode
                  })
                "
              >
                <template #activator="{ on, attrs }">
                  <v-list-item
                    class="ma-n4"
                    v-bind="attrs"
                    v-on="on"
                    @click="$set(dialogUpdateVehiclePlate, i, true)"
                  >
                    <v-list-item-title>
                      <v-icon style="color: black">
                        mdi-pencil
                      </v-icon>
                      {{ $t('vendorVehicles.edit') }}
                    </v-list-item-title>
                  </v-list-item>
                </template>
              </vehicle-plate-form-update-dialog>
            </template>
          </vehicle-card-item>
        </v-col>
      </v-col>
      <v-row v-else>
        <v-col class="justify-center align-center fill-height">
          <empty-placeholder
            hero="empty-placeholder.svg"
            :message-title="$t('vendorVehicles.empty_message_title')"
            :message-description="
              $t('vendorVehicles.empty_message_description')
            "
          />
        </v-col>
      </v-row>
    </v-col> -->

    <display-mode>
      <template #card-mode>
        <vehicles-loading v-if="isLoadingVehicle" />
        <v-col v-else class="pa-0">
          <v-col v-if="vehicles.items.length !== 0" class="pa-0">
            <v-col
              v-for="(item, i) in vehicles.items"
              :key="item.id"
              class="col-12 pa-0 mb-10"
            >
              <vehicle-card-item
                :vehicle="item"
                :is-loading-form="isLoadingFormVehicle"
                :index="i"
                :is-has-plate="true"
                :dialog-delete="dialogDeleteVehicle[i]"
                @on-click-save-delete="deleteVehicle"
                @on-open-delete-dialog="$set(dialogDeleteVehicle, i, true)"
                @on-close-delete-dialog="$set(dialogDeleteVehicle, i, false)"
                @on-success-create-plate="
                  getDataVehicles({
                    page: $route.query?.page,
                    entries: entriesMode
                  })
                "
                @on-success-delete-plate="
                  getDataVehicles({
                    page: $route.query?.page,
                    entries: entriesMode
                  })
                "
              >
                <template #form-update-vehicle>
                  <vehicle-form-dialog
                    :dialog="dialogUpdateVehicle[i]"
                    :vehicle="item"
                    @on-click-close="$set(dialogUpdateVehicle, i, false)"
                    @on-success-remove-image="
                      getDataVehicles({
                        page: $route.query?.page,
                        entries: entriesMode
                      })
                    "
                    @on-success-update="
                      getDataVehicles({
                        page: $route.query?.page,
                        entries: entriesMode
                      })
                    "
                  >
                    <template #activator="{ on, attrs }">
                      <v-list-item
                        class="ma-n4"
                        v-bind="attrs"
                        v-on="on"
                        @click="$set(dialogUpdateVehicle, i, true)"
                      >
                        <v-list-item-title>
                          <v-icon style="color: black">
                            mdi-pencil
                          </v-icon>
                          {{ $t('vendorVehicles.edit') }}
                        </v-list-item-title>
                      </v-list-item>
                    </template>
                  </vehicle-form-dialog>
                </template>

                <template #form-update-plate>
                  <vehicle-plate-form-update-dialog
                    :dialog="dialogUpdateVehiclePlate[i]"
                    :plate-numbers="item.vehicle_details"
                    :list-vehicle="vehicles.items"
                    :selected-vehicle-id="item.id"
                    :vendor-id="vendorId"
                    @on-close-dialog="$set(dialogUpdateVehiclePlate, i, false)"
                    @on-success-update="
                      getDataVehicles({
                        page: $route.query?.page,
                        entries: entriesMode
                      })
                    "
                  >
                    <template #activator="{ on, attrs }">
                      <v-list-item
                        class="ma-n4"
                        v-bind="attrs"
                        v-on="on"
                        @click="$set(dialogUpdateVehiclePlate, i, true)"
                      >
                        <v-list-item-title>
                          <v-icon style="color: black">
                            mdi-pencil
                          </v-icon>
                          {{ $t('vendorVehicles.edit') }}
                        </v-list-item-title>
                      </v-list-item>
                    </template>
                  </vehicle-plate-form-update-dialog>
                </template>
              </vehicle-card-item>
            </v-col>
          </v-col>
          <v-row v-else>
            <v-col class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-placeholder.svg"
                :message-title="$t('vendorVehicles.empty_message_title')"
                :message-description="
                  $t('vendorVehicles.empty_message_description')
                "
              />
            </v-col>
          </v-row>
        </v-col>
      </template>
      <template #data-table-mode>
        <v-container
          fluid
          class="pa-0 mb-10"
          style="background-color: #f0f0f0"
        >
          <div v-if="vehicles.items">
            <v-data-table
              :loading="isLoadingVehicle"
              loading-text="Loading... Please wait"
              :headers="tableHeaders"
              :items="vehicles.items"
              :page.sync="page"
              :single-expand="singleExpand"
              :expanded.sync="expanded"
              :items-per-page="-1"
              hide-default-footer
              class="pa-md-10 pa-5"
              style=""
              @page-count="pageCount = $event"
            >
              <template #item.vehicle="{ item, index }">
                <div class="d-flex align-center">
                  <v-img
                    :src="item?.photo_url"
                    aspect-ratio="1"
                    min-width="50"
                    max-width="50"
                    contain
                  >
                    <template #placeholder>
                      <v-img
                        min-width="80"
                        max-width="80"
                        aspect-ratio="1"
                        contain
                        :src="require(`~/assets/images/icon-select-image.png`)"
                      />
                    </template>
                  </v-img>
                  <h5 class="ml-4" style="max-width: 170px">
                    {{ item.name }}
                  </h5>

                  <v-tooltip right color="white">
                    <template #activator="{ on, attrs }">
                      <v-btn
                        icon
                        class="ml-4"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-icon
                          small
                        >
                          mdi-information
                        </v-icon>
                      </v-btn>
                    </template>
                    <v-card elevation="0" class="pa-5">
                      <v-card-title class="ma-0">
                        <p class="subtitle-1 ma-0">
                          {{ $t('vehicleCard.specification') }}
                        </p>
                      </v-card-title>
                      <v-row class="ma-0 mb-5">
                        <v-col class="pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.length') }}
                          </p>
                          <p class="mb-0">
                            {{ item.length }}
                          </p>
                        </v-col>
                        <v-col class="mx-5 pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.width') }}
                          </p>
                          <p class="mb-0">
                            {{ item.width }}
                          </p>
                        </v-col>
                        <v-col class="pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.height') }}
                          </p>
                          <p class="mb-0">
                            {{ item.height }}
                          </p>
                        </v-col>
                      </v-row>
                      <v-row class="ma-0 mb-5">
                        <v-col class="mr-5 pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            Volume
                          </p>
                          <p class="mb-0">
                            {{ item.max_volume }}
                          </p>
                        </v-col>
                        <v-col class="pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.weight') }}
                          </p>
                          <p class="mb-0">
                            {{ item.max_weight }}
                          </p>
                        </v-col>
                      </v-row>
                      <v-row class="ma-0 mb-5 justify-content-between">
                        <v-col class="mr-5 pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.features') }}
                          </p>

                          <div
                            v-for="feature in item.vehicle_features"
                            :key="feature.id"
                            class="pa-2 ma-0 my-1 d-flex caption"
                            style="
                                  border-color: #cfcccc;
                                  border-style: solid;
                                  border-radius: 4px;
                                "
                          >
                            {{ feature.name?.toUpperCase() }}
                          </div>
                        </v-col>
                        <v-col class="pa-0">
                          <p class="mb-2 text&#45;&#45;secondary">
                            {{ $t('vehicleCard.type') }}
                          </p>

                          <p class="mb-0">
                            {{ item.vehicle_type?.name }}
                          </p>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-tooltip>
                </div>
              </template>
              <template #item.plate="{ item, index }">
                <v-row class="ma-0 my-2 d-flex">
                  <div
                    v-for="detail in item.vehicle_details"
                    :key="detail.id"
                    :class="[`pa-2 ma-1 my-1 caption`, detail.is_active ? 'text-primary' : '']"
                    style="
                          border-color: #cfcccc;
                          border-style: solid;
                          border-radius: 4px;
                        "
                  >
                    <div>
                      <v-chip v-if="detail.fms_identity" label x-small class="chip-success mb-2 font-weight-medium">
                        <p class="ma-0 subtitle-2 text-info">
                          FMS Synced
                        </p>
                      </v-chip>
                    </div>
                    <div class="d-flex justify-center">
                      {{ detail.plate_number?.toUpperCase() }}
                    </div>
                  </div>
                </v-row>
              </template>
              <template #item.detail="{ item, index}">
                <v-menu
                  bottom
                  transition="slide-y-transition"
                >
                  <template #activator="{on, attrs}">
                    <v-btn
                      icon
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-icon color="black">
                        mdi-dots-vertical
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item>
                      <vehicle-form-dialog
                        :dialog="dialogUpdateVehicle[index]"
                        :vehicle="item"
                        @on-click-close="$set(dialogUpdateVehicle, index, false)"
                      >
                        <template #activator="{ on, attrs }">
                          <v-list-item
                            v-bind="attrs"
                            class="ma-n4"
                            v-on="on"
                            @click="$set(dialogUpdateVehicle, index, true)"
                          >
                            <v-list-item-title>
                              <v-icon style="color: black">
                                mdi-pencil
                              </v-icon> {{ $t('vendorVehicles.edit') }}
                            </v-list-item-title>
                          </v-list-item>
                        </template>
                      </vehicle-form-dialog>
                    </v-list-item>
                    <v-dialog v-model="dialogDeleteVehicle[index]" max-width="600px">
                      <template #activator="{ on, attrs }">
                        <v-list-item
                          key="delete"
                          v-bind="attrs"
                          :disabled="item.vehicle_details.map((e) => e.is_active).length > 0"
                          v-on="on"
                          @click="$set(dialogDeleteVehicle, index, true)"
                        >
                          <v-list-item-title>
                            <v-icon style="color: black">
                              mdi-delete
                            </v-icon>
                            {{ $t('vendorVehicles.delete') }}
                          </v-list-item-title>
                        </v-list-item>
                      </template>
                      <v-card>
                        <v-card-title class="text-h6 lighten-2">
                          {{ $t('vehicleCard.confirm_delete_title') }}
                        </v-card-title>

                        <v-card-text>
                          {{ $t('vehicleCard.confirm_delete_text') }}
                        </v-card-text>

                        <v-divider />

                        <v-card-actions>
                          <v-spacer />
                          <v-btn
                            color="primary"
                            text
                            :loading="isLoadingFormVehicle"
                            @click="deleteVehicle(item.id, index)"
                          >
                            {{ $t('vehicleCard.yes') }}
                          </v-btn>
                          <v-btn color="primary" @click="$set(dialogDeleteVehicle, index, false)">
                            {{ $t('vehicleCard.cancel') }}
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog>
                  </v-list>
                </v-menu>
              </template>
            </v-data-table>
          </div>
          <div v-else>
            <div class="justify-center align-center fill-height">
              <empty-placeholder
                hero="empty-placeholder.svg"
                :message-title="$t('vendorUsers.empty_message_title')"
                :message-description="$t('vendorUsers.empty_message_description') "
              />
            </div>
          </div>
        </v-container>
      </template>
    </display-mode>

    <pagination-component
      :page="vehicles.page"
      :total-page="vehicles.totalPage"
      page-id="page"
      class="float-end mb-10"
      @on-change-page="getDataVehicles({
        page: $event,
        entries: entriesMode,
        filter: {
          sortType: $route.query?.name
        }})"
    />
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import MenuExportImport from '~/components/MenuExportImport.vue'
import VehicleCardItem from '~/components/VehicleCardItem.vue'
import VehiclesLoading from '~/components/loading/VehiclesLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import SyncFmsDialog from '~/components/vendor/SyncFmsDialog.vue'
import CategorizeVehicleDialog from '~/components/vendor/CategorizeVehicleDialog.vue'
import { VehicleFromFms } from '~/types/vehicle'
import ToggleButton from '~/components/ToggleButton.vue'
import DisplayMode from '~/components/DisplayMode.vue'
import VehiclePlateFormUpdateDialog from '~/components/VehiclePlateFormUpdateDialog.vue'
import VehicleFormDialog from '~/components/VehicleFormDialog.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'

export default Vue.extend({
  name: 'VehicleManagementPage',

  components: {
    MenuExportImport,
    CategorizeVehicleDialog,
    VehicleCardItem,
    VehiclesLoading,
    EmptyPlaceholder,
    SyncFmsDialog,
    ToggleButton,
    DisplayMode,
    VehiclePlateFormUpdateDialog,
    VehicleFormDialog,
    PaginationComponent
  },

  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    sortColumnItems: {
      name: {
        label: 'Name',
        value: 'name'
      }
    },
    sortTypeItems: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    searchKeyDrivers: '',
    dialogCreateVehicle: false as boolean,
    dialogUpdateVehicle: [] as Array<Boolean>,
    dialogDeleteVehicle: [] as Array<Boolean>,
    dialogCreateVehicleType: false,
    dialogCreateVehiclePlate: [] as boolean[],
    dialogUpdateVehiclePlate: [] as boolean[],
    clearFormVehicle: false,
    clearFormVehicleType: false,
    clearFormVehiclePlate: false,
    dialogSync: false,
    dialogCategorize: false,
    categorizeFms: false,
    expanded: [],
    singleExpand: true,
    button: true,
    pageCount: 0,
    page: 1,
    show: [] as Array<false>,
    tableHeaders: [
      {
        text: 'Vehicle',
        value: 'vehicle'
      },
      {
        text: 'Plate Number',
        value: 'plate'
      },
      {
        text: '',
        value: 'detail'
      }
    ]
  }),

  computed: {
    vendorId (): String {
      return this.$auth.$state.user.data.vendor_id
    },

    vehicles () {
      return this.$store.getters['vehicle/data']
    },
    isHasNullVehicle () {
      return this.$store.getters['vehicle/details/isHasNullVehicleId']
    },

    isLoadingFormVehicle () {
      return this.$store.getters['vehicle/isLoadingForm']
    },

    isLoadingVehicle () {
      return this.$store.getters['vehicle/isLoading']
    },

    drivers () {
      return this.$store.getters['vehicle/drivers/data'].items
    },

    syncedVehicles (): VehicleFromFms[] {
      return this.$store.getters['vendor/sync-vehicles/syncedVehicles']
    },
    entriesMode () {
      return this.$store.getters['layout/entriesMode']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', this.$t('vendorVehicles.vehicles'))
  },

  mounted () {
    this.getDataVehicles({
      entries: this.entriesMode,
      page: this.$route?.query?.page as string
    })
    this.getDataDrivers(1)
    this.getDataVehicleTypes()
    this.getDataVehicleFeatures()
    this.getDataVehicleDetails()
  },

  methods: {
    getDataVehicleDetails () {
      this.$store.dispatch('vehicle/details/getItems')
    },

    getDataVehicleTypes () {
      this.$store.dispatch('vehicle/types/getItems')
    },

    getDataVehicleFeatures () {
      this.$store.dispatch('vehicle/features/getItems')
    },

    getDataVehicles ({
      page = '',
      searchKey = '',
      entries = 0,
      filter = {
        sortColumn: 'name',
        sortType: 'asc'
      }
    }) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/getItems', {
        filterColumns: 'vendor_id',
        filterKeys: user?.vendor?.id,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        searchKey,
        page,
        entries
      })
    },

    handleCloseSyncDialog() {
      this.getDataVehicles({});
    },


    getDataDrivers (page: any) {
      const user = this.$auth.user?.data as any
      this.$store.dispatch('vehicle/drivers/getItems', {
        mode: "simplified",
        vendorId: user?.vendor?.id,
        searchKey: this.searchKeyDrivers,
        page,
        entries: -1
      })
    },

    async deleteVehicle (id: any, i: any) {
      const user = this.$auth.user?.data as any
      const response = await this.$store.dispatch('vehicle/deleteItem', {
        id,
        vendorId: user.vendor.id,
        entries: this.entriesMode
      })

      if (response) {
        this.getDataVehicles({
          page: this.$route.query?.page as string,
          entries: this.entriesMode
        })
        await this.$store.dispatch('vehicle/details/getItems')

        this.$set(this.dialogDeleteVehicle, i, false)
      }
    }
  }
})
</script>

<style scoped lang="scss">
.custom-btn {
  transition: 0.28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #ef3434 !important;
  color: white !important;
}

.chip-success {
  background-color: #EAF6EC !important;
}

</style>

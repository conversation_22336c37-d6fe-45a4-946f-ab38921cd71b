<template>
  <dashboard-admin-loading v-if="isLoadingAdmin" />
  <v-container v-else fluid class="pa-0 px-md-10 px-5 mb-10">
    <v-row>
      <v-col class="col-12 col-md-7">
        <v-card class="pa-7" elevation="0">
          <h4>
            {{ $t('adminDashboard.report') }}
          </h4>
          <v-row class="mt-6">
            <v-col class="col-12 col-md-6 pa-3">
              <h5>
                Product Owner
              </h5>
              <div class="d-flex mr-4 mt-4 align-center">
                <v-img
                  class="mr-5"
                  :src="require(`~/assets/images/shipping-company-icon.svg`)"
                  max-width="80"
                  max-height="80"
                  position="center center"
                />
                <div class="d-block">
                  <h3 class="text-capitalize ma-0">
                    {{ dataDashboard?.shipment_company }}
                  </h3>
                  <p class="subtitle-2 black--text fs-12 ma-0">
                    {{ $t('adminDashboard.company_registered') }}
                  </p>
                </div>
              </div>
            </v-col>
            <v-col class="col-12 col-md-6 pa-3">
              <h5>
                Logistic Provider
              </h5>
              <div class="d-flex mr-4 mt-4 align-center">
                <v-img
                  class="mr-5"
                  :src="require(`~/assets/images/LSP-icon.svg`)"
                  max-width="80"
                  max-height="80"
                  position="center center"
                />
                <div class="d-block">
                  <h3 class="text-capitalize ma-0">
                    {{ dataDashboard?.logistic_service_integrator }}
                  </h3>
                  <p class="subtitle-2 black--text fs-12 ma-0">
                    {{ $t('adminDashboard.company_registered') }}
                  </p>
                </div>
              </div>
            </v-col>
            <v-col class="col-12 col-md-6 pa-3">
              <h5>
                Transporter
              </h5>
              <div class="d-flex mr-4 mt-4 align-center">
                <v-img
                  class="mr-5"
                  :src="require(`~/assets/images/vendor-icon.svg`)"
                  max-width="80"
                  max-height="80"
                  position="center center"
                />
                <div class="d-block">
                  <h3 class="text-capitalize ma-0">
                    {{ dataDashboard?.vendor }}
                  </h3>
                  <p class="subtitle-2 black--text fs-12 ma-0">
                    {{ $t('adminDashboard.company_registered') }}
                  </p>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <v-col class="col-12 col-md-5 rounded">
        <v-card class="pa-7" elevation="0">
          <client-only v-if="chartData">
            <h4 class="mb-5">
              {{ $t('adminDashboard.new_user') }}
            </h4>
            <LineChart
              :chart-options="chartOptions"
              :chart-data="chartData"
              :chart-id="chartId"
              :dataset-id-key="datasetIdKey"
              :css-classes="cssClasses"
              :height="height"
            />
          </client-only>
        </v-card>
      </v-col>
      <v-col class="col-12 rounded">
        <v-card class="pa-7" elevation="0">
          <h4 class="mb-4">
            {{ $t('adminDashboard.last_added') }}
          </h4>
          <v-tabs v-model="tab" class="mb-5" background-color="transparent">
            <v-tab>Logistic Provider</v-tab>
            <v-tab>Product Owner</v-tab>
            <v-tab>Transporter</v-tab>
          </v-tabs>

          <v-tabs-items v-model="tab" style="background-color: transparent">
            <v-tab-item>
              <v-container fluid class="pa-0">
                <v-row>
                  <v-col
                    v-for="(item, i) in dataDashboard?.last_added_lsp.data"
                    :key="i"
                    class="col-12 col-md-3 d-flex mt-4 align-center"
                  >
                    <image-component
                      class="mr-5"
                      :image="item.logo_url"
                      max-width="35"
                      max-height="35"
                      position="center center"
                    />
                    <div class="d-block">
                      <p v-if="i < 4" class="text-primary text-capitalize fs-12 ma-0">
                        {{ $t('adminDashboard.new_added') }}
                      </p>
                      <p class="subtitle-2 black--text ma-0">
                        {{ item.name }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>

            <v-tab-item>
              <v-container fluid class="pa-0">
                <v-row>
                  <v-col
                    v-for="(item, i) in dataDashboard?.last_added_sc.data"
                    :key="i"
                    class="col-12 col-md-3 d-flex mt-4 align-center"
                  >
                    <image-component
                      class="mr-5"
                      :image="item.logo_url"
                      max-width="35"
                      max-height="35"
                      position="center center"
                    />
                    <div class="d-block">
                      <p v-if="i < 4" class="text-primary text-capitalize fs-12 ma-0">
                        {{ $t('adminDashboard.new_added') }}
                      </p>
                      <p class="subtitle-2 black--text ma-0">
                        {{ item.name }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>

            <v-tab-item>
              <v-container fluid class="pa-0">
                <v-row>
                  <v-col
                    v-for="(item, i) in dataDashboard?.last_added_vendor.data"
                    :key="i"
                    class="col-12 col-md-3 d-flex mt-4 align-center"
                  >
                    <image-component
                      class="mr-5"
                      :image="item.logo_url"
                      max-width="35"
                      max-height="35"
                      position="center center"
                    />
                    <div class="d-block">
                      <p v-if="i < 4" class="text-primary text-capitalize fs-12 ma-0">
                        {{ $t('adminDashboard.new_added') }}
                      </p>
                      <p class="subtitle-2 black--text ma-0">
                        {{ item.name }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
              </v-container>
            </v-tab-item>
          </v-tabs-items>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import { DashboardAdmin } from '~/types/dashboard'
import ImageComponent from '~/components/ImageComponent.vue'
import DashboardAdminLoading from '~/components/loading/DashboardAdminLoading.vue'
export default Vue.extend({
  name: 'AdminDashboard',

  components: {
    ImageComponent,
    DashboardAdminLoading
  },

  layout: 'admin/body',

  middleware: ['auth', 'is-admin'],

  data: () => ({
    tab: null,
    chartDataSet: [],
    chartId: 'line-chart',
    datasetIdKey: 'label',
    height: 335.5,
    cssClasses: '',
    chartData: {
      labels: [] as any,
      datasets: [
        {
          label: 'Product Owner',
          backgroundColor: '#EF3434',
          lineTension: 0.27,
          data: [] as any,
          borderColor: '#EF3434'
        },
        {
          label: 'Transporter',
          backgroundColor: '#2FA841',
          lineTension: 0.27,
          data: [] as any,
          borderColor: '#2FA841'
        },
        {
          label: 'Logistic Provider',
          backgroundColor: '#0094BC',
          lineTension: 0.27,
          data: [] as any,
          borderColor: '#0094BC'
        }
      ]
    },
    chartOptions: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
          align: 'start',
          labels: {
            textAlign: 'left',
            boxWidth: 100,
            usePointStyle: true
          }

        }
      }
    }
  }),

  computed: {
    dataDashboard () {
      return this.$store.getters['dashboard/dataAdmin'] as DashboardAdmin | null
    },
    isLoadingLsp () {
      return this.$store.getters['dashboard/isLoadingLogisticServiceProvider']
    },
    isLoadingAdmin () {
      return this.$store.getters['dashboard/isLoadingAdmin']
    },
    isLoadingSc () {
      return this.$store.getters['dashboard/isLoadingShipmentCompany']
    }

  },

  async mounted () {
    this.$store.commit(
      'layout/SET_TITLE',
      'Dashboard'
    )
    await this.getData()
    this.getDataChartSC()
    this.getDataChartLSP()
    this.getDataChartVendor()
    this.getLabelChart()
  },

  methods: {
    getData () {
      return this.$store.dispatch('dashboard/getItemAdmin')
    },

    getDataChartSC () {
      const dataSetSC = []
      const dataSC = this.chartData.datasets[0].data

      const dataDashboard = this.dataDashboard!

      for (let i = 0; i < dataDashboard?.user_growth_sc.length; i++) {
        const getCount = dataDashboard?.user_growth_sc[i].count
        dataSetSC.push({
          count: getCount
        })

        dataSC.unshift(dataSetSC[i].count)
      }

      return dataSC
    },

    getDataChartLSP () {
      const dataSetLSP = []
      const dataLSP = this.chartData.datasets[2].data
      for (let i = 0; i < (this as any).dataDashboard?.user_growth_lsp.length; i++) {
        const getCount = (this as any).dataDashboard?.user_growth_lsp[i].count
        dataSetLSP.push({
          count: getCount
        })
        dataLSP.unshift(dataSetLSP[i].count)
      }

      return dataLSP
    },

    getDataChartVendor () {
      const dataSetVendor = []
      const dataVendor = this.chartData.datasets[1].data
      for (let i = 0; i < (this as any).dataDashboard?.user_growth_vendor.length; i++) {
        const getCount = (this as any).dataDashboard?.user_growth_vendor[i].count
        dataSetVendor.push({
          count: getCount
        })

        dataVendor.unshift(dataSetVendor[i].count)
      }

      return dataVendor
    },

    getLabelChart () {
      const dataSetSC = []
      const label = this.chartData.labels

      for (let i = 0; i < (this as any).dataDashboard?.user_growth_sc.length; i++) {
        const getMonth = (this as any).dataDashboard?.user_growth_sc[i].month
        const pickDate = moment(getMonth.toString(), 'YYYY-MM-DD')
        const dateMonth = pickDate.format('MMMM')
        dataSetSC.push({
          month: dateMonth
        })

        label.unshift(dataSetSC[i].month)
      }

      return label
    }
  }
})
</script>

<style scoped lang="scss">
.fs-12{
  font-size: 12px !important;
}
</style>

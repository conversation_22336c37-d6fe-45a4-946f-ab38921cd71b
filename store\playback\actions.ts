import { ActionTree } from 'vuex'
import { RootState } from '../index'
import { PlaybackState } from './state'
import { exception<PERSON>and<PERSON> } from '~/utils/functions'
import { toastSuccess } from '~/utils/toasts'

export const actions: ActionTree<PlaybackState, RootState> = {
  getPlayback ({ commit }, payload) {
    commit('SET_IS_LOADING', true)

    this.$axios
      .get(`/v1/tracks/${payload.id}/playbacks`, {
        params: {
          from_date: payload.fromDate == null ? '' : payload.fromDate,
          from_time: payload.fromTime == null ? '' : payload.fromTime,
          to_date: payload.toDate == null ? '' : payload.toDate,
          to_time: payload.toTime == null ? '' : payload.toTime
        }
      })
      .then((response: any) => {
        if (response.data.meta) {
          commit('SET_RESULT', response.data)
        } else {
          commit('SET_ITEMS', response.data.data)
          if (response.data.data.length === 0) {
            toastSuccess('Data playback tidak ditemukan', this)
          }
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING', false)
      })
  },
  getPointOfInterest ({ commit }) {
    commit('SET_IS_LOADING_POI', true)

    this.$axios
      .get('/v1/point_of_interest')
      .then((response: any) => {
        if (response.data.meta) {
          commit('SET_POI_RESULT', response.data)
        } else {
          commit('SET_POI_ITEMS', response.data.data)
          if (response.data.data.length === 0) {
            toastSuccess('Data POI tidak ditemukan', this)
          }
        }
      })
      .catch((error: any) => {
        exceptionHandler(error, this)
      })
      .finally(() => {
        commit('SET_IS_LOADING_POI', false)
      })
  }
}

export default actions

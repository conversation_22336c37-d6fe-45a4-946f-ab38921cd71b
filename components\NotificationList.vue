<template>
  <div v-if="isLoading" class="mt-10">
    <v-skeleton-loader v-for="i in 3" :key="i" type="image" height="100" class="mx-5 mt-5" />
  </div>
  <div v-else>
    <v-tabs v-model="activeTab" class="mx-4">
      <v-tab>All</v-tab>
      <v-tab>WB</v-tab>
    </v-tabs>
    <v-tabs-items v-model="activeTab">
      <v-tab-item>
        <v-list v-if="listNotifGroupedByDate" class="mx-5 pa-0">
          <div v-for="(items, key) in listNotifGroupedByDate" :key="key">
            <p class="subtitle-1 text--secondary mt-5">
              {{ key }}
            </p>
            <v-list-item-group color="info">
              <v-list-item
                v-for="(item, index) in items"
                :key="index"
                class="pa-5 mt-5 rounded overflow-hidden v-sheet--outlined"
              >
                <v-list-item-content class="pa-0">
                  <v-list-item-title class="mb-5">
                    <v-container class="pa-0 d-flex align-center">
                      <v-icon class="mr-5" color="info">
                        mdi-bullhorn
                      </v-icon>
                      <div>
                        <p class="ma-0 black--text text-wrap">
                          {{ item.title }}
                        </p>
                      </div>
                    </v-container>
                  </v-list-item-title>
                  <div v-html="item.body" />
                  <v-list-item-subtitle>
                    <v-container class="pa-0 d-flex align-center justify-end">
                      <p class="ma-0 mt-2 ext-secondary">
                        {{ $moment(item.created_at).format('HH:mm') }} WIB
                      </p>
                    </v-container>
                  </v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </v-list-item-group>
          </div>
        </v-list>
      </v-tab-item>
      <v-tab-item>
        <v-list v-if="filteredWBNotifications" class="mx-5 pa-0">
          <div v-for="(items, key) in filteredWBNotifications" :key="key">
            <p class="subtitle-1 text--secondary mt-5">
              {{ key }}
            </p>
            <v-list-item-group color="info">
              <v-list-item
                v-for="(item, index) in items"
                :key="index"
                class="pa-5 mt-5 rounded overflow-hidden v-sheet--outlined"
                @click="$router.push(localePath('/logistic-service-provider/order-shipment/history-order/detail-history-order/' + item?.shipment_id))"
                :ripple="true"
              >
                <v-list-item-content class="pa-0">
                  <v-list-item-title class="mb-5">
                    <v-container class="pa-0 d-flex align-center">
                      <v-icon class="mr-5" color="info">
                        mdi-bullhorn
                      </v-icon>
                      <div>
                        <p class="ma-0 black--text text-wrap">
                          {{ item.title }}
                        </p>
                      </div>
                    </v-container>
                  </v-list-item-title>
                  <div v-html="item.body" />
                  <v-list-item-subtitle>
                    <v-container class="pa-0 d-flex align-center justify-end">
                      <p class="ma-0 mt-2 ext-secondary">
                        {{ $moment(item.created_at).format('HH:mm') }} WIB
                      </p>
                    </v-container>
                  </v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </v-list-item-group>
          </div>
        </v-list>
      </v-tab-item>
    </v-tabs-items>
    <div
      v-if="!listNotifGroupedByDate && !filteredWBNotifications"
      class="d-flex justify-center"
    >
      <p class="body-1 ma-0">
        {{ $t('notificationList.info_notification') }}
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import moment from 'moment'
import Vue from 'vue'
import { Notification } from '~/types/notification'

export default Vue.extend({
  name: 'NotificationList',

  data() {
    return {
      activeTab: 0,
    }
  },

  computed: {
    isLoading (): boolean {
      return this.$store.getters['notification/isLoading']
    },
    data (): {items: Notification[], totalPage: number, page: number} {
      return this.$store.getters['notification/data']
    },
    listNotifGroupedByDate (): any {
      const currentValue = {} as {[key:string]:Notification[]}
      this.data.items.forEach((element) => {
        const key = moment(element.created_at).format('DD/MM/YY')
        if (!currentValue[key]) {
          currentValue[key] = []
        }
        currentValue[key].push(element)
      })
      return currentValue
    },
    filteredWBNotifications (): any {
      const currentValue = {} as {[key:string]:Notification[]}
      this.data.items.forEach((element) => {
        if (element.tag === 'WEIGHTBRIDGE') {
          const key = moment(element.created_at).format('DD/MM/YY')
          if (!currentValue[key]) {
            currentValue[key] = []
          }
          currentValue[key].push(element)
        }
      })
      return currentValue
    }
  }
})
</script>

<style lang="scss" scoped> </style>

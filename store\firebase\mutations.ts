import { MutationTree } from 'vuex'
import { FirebaseState } from './state'

export const mutations: MutationTree<FirebaseState> = {
  SET_FIREBASE_PATH (state, path: any) {
    state.firebasePath = path
  },

  SET_DATA_NOTIFICATION (state, data: any) {
    state.dataNotification = data
  },

  SET_DATA_LSP (state, data: any) {
    state.dataLsp = data
  },

  SET_DATA_SC (state, data: any) {
    state.dataSc = data
  },

  SET_DATA_VENDOR (state, data: any) {
    state.dataVendor = data
  },

  SET_MESSAGING_TOKEN (state, token: string) {
    state.messagingToken = token
  }
}

export default mutations

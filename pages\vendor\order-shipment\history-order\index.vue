<template>
  <v-container fluid class="pa-0 px-md-10 px-5 mb-10">
    <header-datatable
      default-sort-column="created_at"
      default-sort-type="desc"
      sort-column-id="sort_column"
      sort-type-id="sort_type"
      :is-sort-by="false"
      @on-search-icon-click="
          applyFilter({searchKey: $event})
      "
    >
    <template #filter-user>
        <v-card
          elevation="0"
          class="pa-5 d-flex justify-start flex-column"
          max-width="400"
        >
          <v-container class="pa-0">
            <v-row class="ma-1 d-flex mt-3">
              <v-menu
                ref="menu"
                v-model="menuDateRange"
                max-width="280"
                :close-on-content-click="false"
                transition="slide-y-transition"
                offset-y
              >
                <template #activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateRange"
                    outlined
                    clearable
                    readonly
                    label="Date"
                    v-bind="attrs"
                    append-icon="mdi-calendar-range"
                    v-on="on"
                    @click:clear="getDataShipment({})"
                  >
                    Select Date Range
                  </v-text-field>
                </template>

                <v-date-picker
                  v-model="dateRange"
                  range
                  no-title
                  color="primary"
                >
                  <v-btn text color="primary" @click="menuDateRange = false">
                    Save
                  </v-btn>
                  <v-btn text color="primary" @click="menuDateRange = false">
                    Cancel
                  </v-btn>
                </v-date-picker>
              </v-menu>
            </v-row>
            <div class="pa-1 mt-2">
              <h5>Status DO</h5>
              <div class="ma-1">
                <v-radio-group v-model="selectedStatus"  class="pa-0" row>
                  <v-radio
                    value=""
                    label="All"
                    hide-details
                  />
                  <v-radio
                    value="<null>"
                    label="Unfinished"
                    hide-details
                  />
                  <v-radio
                    value="FINISHED"
                    label="Finished"
                    hide-details
                  />
                </v-radio-group>
              </div>
            </div>
            <v-row class="ma-0 d-flex mt-3">
              <v-btn
                block
                elevation="0"
                color="primary"
                class="text-capitalize"
                x-large
                :disabled="!dateRange"
                @click="applyFilter"
              >
                Apply Filter
              </v-btn>
            </v-row>
            <div class="pa-1">
              <v-btn
                color="transparent"
                elevation="0"
                class="mt-2 pa-0 text-capitalize text-secondary"
                @click="resetFilter"
              >
                <p class="text-secondary">
                  Reset Filter
                </p>
              </v-btn>
            </div>
          </v-container>
        </v-card>
      </template>
    </header-datatable>

    <v-container fluid class="pa-0">
      <tab-component>
        <template #tab>
          <v-tab class="subtitle-1 text-capitalize">
            Order Shipment
          </v-tab>
          <!-- <v-tab class="subtitle-1 text-capitalize">
            Invoice to Logistic Provider
          </v-tab> -->
        </template>

        <template #tab-item>
          <v-tab-item>
            <table-order-shipment-loading v-if="isLoadingShipment" />

            <v-container v-else fluid class="pa-0">
              <v-container
                v-if="dataShipment?.items?.length !== 0"
                fluid
                class="pa-0"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="dataShipment.items"
                  :page.sync="page"
                  :single-expand="singleExpand"
                  :expanded.sync="expanded"
                  :items-per-page="-1"
                  show-expand
                  hide-default-footer
                  item-key="detail"
                  class="pa-md-10 pa-5 mb-10"
                  @page-count="pageCount = $event"
                  @item-expanded="getDataShipmentRitase"
                >
                  <template #item.date="{ item }">
                    {{ $moment(item.date).format('DD-MM-yyyy | HH:mm [WIB]') }}
                  </template>
                  <template #item.status="{ item }">
                    <div class="d-flex">
                    <v-chip
                      v-if="item.status === 'FINISHED'"
                      class="chip-success font-weight-medium"
                      label
                    >
                      <p class="ma-0 subtitle-1 text-success">
                        {{ item.status }}
                      </p>
                    </v-chip>
                    <v-chip
                      v-else
                      class="font-weight-medium"
                      label
                    >
                      <p class="ma-0 subtitle-1 text-secondary">
                        {{ item.status ?? 'UNFINISHED' }}
                      </p>
                    </v-chip>
                  </div>
                  </template>
                  <template #item.detail="{ item }">
                    <v-btn
                      class="font-weight-medium pa-0 text-capitalize"
                      text
                      plain
                      @click="
                        $router.push(
                          localePath(
                            '/vendor/order-shipment/history-order/detail-history-order/' +
                              `${item.detail}`
                          )
                        )
                      "
                    >
                      Detail
                      <v-icon> mdi-chevron-right </v-icon>
                    </v-btn>
                  </template>
                  <template #expanded-item="{ headers, item }">
                  <td :colspan="headers.length">
                    <v-card class="pa-1 ma-3">
                      <tab-component>
                        <template #tab>
                          <v-tab class="subtitle-1 text-capitalize">
                            Progress-Finished
                          </v-tab>
                          <v-tab class="subtitle-1 text-capitalize">
                            Dispatch
                          </v-tab>
                        </template>
                        <template #tab-item>
                          <v-tab-item>
                            <v-sheet v-if="dataShipmentRitase.length < 0" class="pa-5 overflow-hidden" height="100">
                              <div class="d-flex">
                                <div class="d-flex flex-row align-center" style="width: 100%">
                                  <v-skeleton-loader type="image" height="50" width="50" />
                                  <div class="d-flex flex-column" style="width: 100%">
                                    <v-skeleton-loader type="list-item-two-line" width="100%" />
                                  </div>
                                </div>
                              </div>
                            </v-sheet>
                            <v-card v-else-if="dataShipmentRitase.length > 0" class="pa-1 ma-3">
                              <div>
                                <v-data-table
                                  :headers="tableRitase"
                                  :items="dataShipmentRitase"
                                  :items-per-page="100000"
                                  hide-default-footer
                                  disable-pagination
                                >
                                  <template #item.no="{ index }">
                                    <div>
                                      {{ index + 1 }}
                                    </div>
                                  </template>
                                  <template #item.vendor_name="{ item }">
                                    <div>
                                      {{ item?.vendorName }}
                                    </div>
                                  </template>
                                  <template #item.plateNumber="{ item }">
                                    <div>
                                      <span v-if="item.plateNumber !== null">{{ item.plateNumber }}</span>
                                      <span v-else>-</span>
                                    </div>
                                  </template>
                                  <template #item.ritase="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item.ritase }}
                                    </div>
                                  </template>
                                  <template #item.quantityPickup="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.wbQuantityPu }}
                                    </div>
                                  </template>
                                  <template #item.quantityDropoff="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.wbQuantityDo }}
                                    </div>
                                  </template>
                                  <template #item.mill="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.mill }}
                                    </div>
                                  </template>
                                  <template #item.createdAt="{ item }">
                                    <div>
                                      {{ item.createdAt }}
                                    </div>
                                  </template>
                                  <template #item.status="{ item }">
                                    <div class="d-flex">
                                      <v-chip
                                        v-if="item.status === 'PROPOSED'"
                                        label
                                        class="chip-secondary font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-secondary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'REJECT'"
                                        label
                                        class="chip-danger font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-primary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'DISPATCH'"
                                        label
                                        style="color: #FFEBD4;"
                                        class="chip-orange font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-orange">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'FINISHED'"
                                        label
                                        class="chip-success font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-success">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'EXPIRED'"
                                        label
                                        class="chip-danger font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-primary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'REMOVED'"
                                        label
                                        class="font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'ON_PROGRESS'"
                                        label
                                        class="chip-success font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-info">
                                          ON PROGRESS
                                        </p>
                                      </v-chip>
                                      <v-chip v-else label class="chip-success font-weight-medium">
                                        <p class="ma-0 subtitle-1 text-info">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <div v-if="item.isFlag === false" class="ml-2">
                                        <v-icon style="color: #0094BC">
                                          mdi-information-outline
                                        </v-icon>
                                      </div>
                                    </div>
                                  </template>
                                </v-data-table>
                              </div>
                            </v-card>
                            <v-card v-else class="elevation-0 d-flex justify-center">
                              <p class="pa-1 ma-2">
                                Ritase is Empty
                              </p>
                            </v-card>
                          </v-tab-item>
                          <v-tab-item>
                            <v-sheet v-if="dataShipmentRitaseDispatch.length < 0" class="pa-5 overflow-hidden" height="100">
                              <div class="d-flex">
                                <div class="d-flex flex-row align-center" style="width: 100%">
                                  <v-skeleton-loader type="image" height="50" width="50" />
                                  <div class="d-flex flex-column" style="width: 100%">
                                    <v-skeleton-loader type="list-item-two-line" width="100%" />
                                  </div>
                                </div>
                              </div>
                            </v-sheet>
                            <v-card v-else-if="dataShipmentRitaseDispatch.length > 0" class="pa-1 ma-3">
                              <div>
                                <v-data-table
                                  :headers="tableRitase"
                                  :items="dataShipmentRitaseDispatch"
                                  :items-per-page="100000"
                                  hide-default-footer
                                  disable-pagination
                                >
                                  <template #item.no="{ index }">
                                    <p>
                                      {{ index + 1 }}
                                    </p>
                                  </template>
                                  <template #item.vendor_name="{ item }">
                                    <div>
                                      {{ item?.vendorName }}
                                    </div>
                                  </template>
                                  <template #item.plateNumber="{ item }">
                                    <div>
                                      <span v-if="item.plateNumber !== null">{{ item.plateNumber }}</span>
                                      <span v-else>-</span>
                                    </div>
                                  </template>
                                  <template #item.ritase="{ item }">
                                    <div class="pa-2">
                                      <span v-if="item.plateNumber !== null">{{ item.ritase }}</span>
                                      <span v-else>-</span>
                                    </div>
                                  </template>
                                  <template #item.quantityPickup="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.wbQuantityPu }}
                                    </div>
                                  </template>
                                  <template #item.quantityDropoff="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.wbQuantityDo }}
                                    </div>
                                  </template>
                                  <template #item.mill="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      {{ item?.mill }}
                                    </div>
                                  </template>
                                  <template #item.createdAt="{ item }">
                                    <div v-if="item.plateNumber !== null">
                                      <p v-if="item.ritase" class="ma-1">
                                        {{ item.tag === 'AUTO_WB' ? '-' : item?.createdAt }}
                                      </p>
                                    </div>
                                  </template>
                                  <template #item.status="{ item }">
                                    <div class="d-flex">
                                      <v-chip
                                        v-if="item.status === 'PROPOSED'"
                                        label
                                        class="chip-secondary font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-secondary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'REJECT'"
                                        label
                                        class="chip-danger font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-primary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'DISPATCH'"
                                        label
                                        class="chip-orange font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-orange">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'FINISHED'"
                                        label
                                        class="chip-success font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-success">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'EXPIRED'"
                                        label
                                        class="chip-danger font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-primary">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'REMOVED'"
                                        label
                                        class="font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <v-chip
                                        v-else-if="item.status === 'ON_PROGRESS'"
                                        label
                                        class="chip-success font-weight-medium"
                                      >
                                        <p class="ma-0 subtitle-1 text-info">
                                          ON PROGRESS
                                        </p>
                                      </v-chip>
                                      <v-chip v-else label class="chip-success font-weight-medium">
                                        <p class="ma-0 subtitle-1 text-info">
                                          {{ item.status }}
                                        </p>
                                      </v-chip>
                                      <div v-if="item.isFlag === false" class="ml-2">
                                        <v-icon style="color: #0094BC">
                                          mdi-information-outline
                                        </v-icon>
                                      </div>
                                    </div>
                                  </template>
                                </v-data-table>
                              </div>
                            </v-card>
                            <v-card v-else class="elevation-0 d-flex justify-center">
                              <p class="pa-1 ma-2">
                                Ritase is Empty
                              </p>
                            </v-card>
                          </v-tab-item>
                        </template>
                      </tab-component>
                    </v-card>
                  </td>
                </template>
                </v-data-table>

                <pagination-component
                  :page="dataShipment.page"
                  :total-page="dataShipment.totalPage"
                  page-id="page_history"
                  class="float-end"
                  @on-change-page="applyFilter({
                    page: $event
                  })"
                />
              </v-container>

              <div v-else>
                <div class="justify-center align-center fill-height">
                  <empty-placeholder
                    hero="empty-placeholder.svg"
                    :message-title="$t('vendorHistoryOrder.empty_history_order_message_title')"
                    :message-description="$t('vendorHistoryOrder.empty_history_order_message_description')"
                  />
                </div>
              </div>
            </v-container>
          </v-tab-item>
        </template>
      </tab-component>
    </v-container>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import HeaderDatatable from '~/components/HeaderDatatable.vue'
import HistoryInvoiceCardItem from '~/components/vendor/HistoryInvoiceCardItem.vue'
import TableOrderShipmentLoading from '~/components/loading/TableOrderShipmentLoading.vue'
import EmptyPlaceholder from '~/components/EmptyPlaceholder.vue'
import TabComponent from '~/components/TabComponent.vue'
import PaginationComponent from '~/components/PaginationComponent.vue'
import { saveDateRange } from '~/utils/functions'

export default Vue.extend({
  name: 'HistoryOrderPage',

  components: {
    HeaderDatatable,
    HistoryInvoiceCardItem,
    TableOrderShipmentLoading,
    EmptyPlaceholder,
    TabComponent,
    PaginationComponent
  },
  layout: 'vendor/body',

  middleware: ['auth', 'is-vendor'],

  data: () => ({
    windowWidth: 0,
    menuDateRange: false as boolean,
    dateRange: [] as Date[],
    selectedStatus: '' as any,
    expanded: [],
    dialog: false,
    singleExpand: true,
    button: true,
    page: 1,
    pageCount: 0,
    tableHeaders: [
      { text: 'Order Number', value: 'identityOrder', sortable: false },
      { text: 'Total Vehicle', value: 'totalVehicle', sortable: false },
      { text: 'Date & Time', value: 'date', sortable: false },
      { text: 'Status', value: 'status', sortable: false },
      { text: '', value: 'detail', sortable: false }
    ],
    tableRitase: [
      { text: 'No', value: 'no'},
      { text: 'Transporter', value: 'vendorName', sortable: false },
      { text: 'Plate Number', value: 'plateNumber', sortable: false },
      { text: 'Quantity Pickup', value: 'wbQuantityPu', sortable: false },
      { text: 'Quantity Dropoff', value: 'wbQuantityDo', sortable: false },
      { text: 'Order', value: 'ritase', sortable: false },
      { text: 'Time Stamp Reorder', value: 'createdAt', sortable: false },
      { text: 'Status', value: 'status', sortable: false }
    ],
    pageSize: 9,
    itemCount: 0,
    searchKey: '',
    sortColumnShipment: {
      date: {
        label: 'Date',
        value: 'created_at'
      },
      status: {
        label: 'Status',
        value: 'status'
      },
      identity: {
        label: 'Identity',
        value: 'identity'
      }
    },
    sortColumnInvoice: {
      date: {
        label: 'Date',
        value: 'created_at'
      }
    },
    sortType: {
      asc: {
        label: 'A-Z',
        value: 'asc'
      },
      desc: {
        label: 'Z-A',
        value: 'desc'
      }
    },
    filterColumnShipment: {
      status: {
        label: 'Status',
        value: 'status'
      }
    },
    filterTypeShipment: {
      all: {
        label: 'All',
        value: 'PROPOSED|DISPATCH|FINISHED|REJECT'
      },
      proposed: {
        label: 'Proposed',
        value: 'PROPOSED'
      },
      dispatch: {
        label: 'Dispatch',
        value: 'DISPATCH'
      },
      finished: {
        label: 'Finished',
        value: 'FINISHED'
      },
      reject: {
        label: 'Reject',
        value: 'REJECT'
      }
    },
    index: null as number | null
  }),

  computed: {
    dataShipmentRitase () {
      const response = this.$store.getters['shipment/dataShipmentRitase']


      const map : any = {}

      response?.forEach((item: any) => {
        const key = item.plate_number

        if (!map[key]) {
          map[key] = []
        }
        map[key].push(item)
      })


      const filteredData = Object.values(map).flat()

      const items = filteredData.filter((f: any) => f.status === 'FINISHED' || f.status === 'PROCESS').map((data: any) => {
        const ritaseData = data.ritase === null ? 0 : data.ritase
        let statusData = ''
        const shipmentVendorStatus = data.shipment_vendor_status
        if (shipmentVendorStatus === 'PROPOSED' || shipmentVendorStatus === 'EXPIRED' || shipmentVendorStatus === 'REJECT') {
          statusData = shipmentVendorStatus
        } else {
          statusData = data.status
        }
        return {
          vendorName: data.vendor,
          plateNumber: data.plate_number,
          wbQuantityPu: data.wb_quantity_pickup,
          wbQuantityDo: data.wb_quantity_dropoff,
          ritase: data.tag === 'AUTO_WB' ? 'Auto WB' : `Order ${ritaseData + 1}`,
          tag: data.tag,
          mill: data.mill ? data.mill : '-',
          createdAt: data.tag === 'AUTO_WB' ? '-' : this.$moment(data.created_at).format('DD-MM-yyyy | HH:mm [WIB]'),
          status: statusData
        }
      })
      return items
    },
    dataShipmentRitaseDispatch () {
      const response = this.$store.getters['shipment/dataShipmentRitase']

      const map : any = {}

      response?.forEach((item: any) => {
        const key = item.plate_number

        if (!map[key]) {
          map[key] = []
        }
        map[key].push(item)
      })
      const filteredData = Object.values(map).flat()

      const items = filteredData.filter((f: any) => f.status !== 'FINISHED' && f.status !== 'PROCESS').map((data: any) => {
        const ritaseData = data.ritase === null ? 0 : data.ritase
        let statusData = ''
        const shipmentVendorStatus = data.shipment_vendor_status
        if (shipmentVendorStatus === 'PROPOSED' || shipmentVendorStatus === 'EXPIRED' || shipmentVendorStatus === 'REJECT') {
          statusData = shipmentVendorStatus
        } else {
          statusData = data.status
        }
        return {
          vendorName: data.vendor,
          plateNumber: data.plate_number,
          wbQuantityPu: data.wb_quantity_pickup,
          wbQuantityDo: data.wb_quantity_dropoff,
          ritase: data.tag === 'AUTO_WB' ? 'Auto WB' : `Order ${ritaseData + 1}`,
          tag: data.tag,
          mill: data.mill ? data.mill : '-',
          createdAt: data.tag === 'AUTO_WB' ? '-' : this.$moment(data.created_at).format('DD-MM-yyyy | HH:mm [WIB]'),
          status: statusData
        }
      })
      return items
    },
    dataShipment () {
      const response = this.$store.getters['shipment/dataShipment']

      const items = response.items?.map((data: any) => {
        return {
          identityOrder: data.orders[0].identity,
          totalVehicle: data.tracks_count + ' vehicle',
          date: data.created_at,
          status: data.orders[0]?.completion_status,
          isExpired: data.is_expired,
          detail: data.id
        }
      })

      return {
        items,
        totalPage: response.totalPage,
        page: response.page
      }
    },
    dataInvoice () {
      return this.$store.getters['invoice/data']
    },
    isLoadingShipment () {
      return this.$store.getters['shipment/isLoading']
    },
    isLoadingDownload (): boolean[] {
      const loading = [] as boolean[]

      loading[this.index!] = this.$store.getters['shipment/isLoadingDownload']

      return loading
    },
    tabIndex (): number {
      return this.$store.getters['tab/index']
    }
  },

  created () {
    this.$store.commit('layout/SET_TITLE', this.$t('vendorHistoryOrder.history_order_shipment'))
  },

  mounted () {
    this.applyFilter({
      page: this.$route?.query?.page_history as string
    })
  },

  methods: {
    getDataShipment ({
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc',
        filterColumn: 'status',
        filterType: 'PROPOSED|DISPATCH|FINISHED|REJECT|ON_PROGRESS|EXPIRED'
      }
    }) {

      const user = this.$auth.user?.data as any
      this.$store.dispatch('shipment/getItemsV2', {
        vendorId: user.id,
        searchColumns: 'orders.identity',
        searchKey,
        filterColumns: filter.filterColumn,
        filterKeys: filter.filterType,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page
      })
    },

    applyFilter ({
      filterDate = { column: 'created_at', start: null, end: null },
      page = '',
      searchKey = '',
      filter = {
        sortColumn: 'created_at',
        sortType: 'desc',
        filterColumn: 'orders.completion_status',
      }
    }) {

      let filterDateStart = '' as any;
      let filterDateEnd = '' as any;

        if (filterDate.start !== null && filterDate.end !== null) {
          filterDateStart = filterDate.start || new Date();
          filterDateEnd = filterDate.end || new Date();
        }

      if (this.dateRange) {
          const { start, end } = saveDateRange(this.dateRange);

          filterDateStart = start 
              ? new Date(start.toISOString().substring(0, 10) + 'T00:00:00.000Z') 
              : filterDateStart;

          filterDateEnd = end 
              ? new Date(end.toISOString().substring(0, 10) + 'T23:59:00.000Z') 
              : filterDateEnd;
        }

      const user = this.$auth.user?.data as any
      this.$store.dispatch('shipment/getItemsV2', {
        vendorId: user.id,
        searchColumns: 'orders.identity',
        searchKey,
        filterDateColumn: 'created_at',
        filterDateStart,
        filterDateEnd,
        filterColumns: filter.filterColumn,
        filterKeys: this.selectedStatus,
        sortColumn: filter.sortColumn,
        sortType: filter.sortType,
        page
      })
    },

    resetFilter () {
      this.dateRange = []
      this.selectedStatus = ''
      this.getDataShipment({})
    },

    getDataShipmentRitase (item: any, value: any) {
      console.log(item, 'item');
      
      if (item.value === true) {
        this.$store.dispatch('shipment/getItemDetailRitase', { id: item?.item?.detail })
      }
    },

    downloadDispatchNote (shipmentId: string, shipmentIdentity: string, index: number) {
      this.index = index

      this.$store.dispatch('shipment/downloadDispatchNote', {
        shipmentId,
        shipmentIdentity
      })
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-btn {
  transition: 0.28s !important;
}

.custom-icon {
  transition: 0s !important;
}

.custom-btn:hover {
  background-color: #ef3434 !important;
  color: white !important;
}

.chip-success {
  background: #eaf6ec !important;
}

.chip-danger {
  background: #fde0e0 !important;
}

.chip-orange {
  background: #FFEBD4 !important;
}

.chip-info {
  background: #e6f4f8 !important;
}
</style>

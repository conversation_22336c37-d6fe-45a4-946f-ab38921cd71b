<template>
  <v-card outlined class="pa-5 d-flex flex-column justify-space-between">
    <v-container class="pa-0">
      <v-card-title class="mb-5 pa-0 d-flex justify-space-between">
        <div class="d-flex">
          <div class="mx-2">
            <image-component v-if="product.photo_url" :image="product.photo_url" max-width="40" max-height="40" class="mr-5" />

            <v-avatar
              v-else
              color="primary"
              rounded
              size="40"
              class="mr-5"
            >
              <v-img :src="require(`~/assets/icons/product-empty.svg`)" />
            </v-avatar>
          </div>
          <h4 class="mt-2">
            {{ product.name }}<span v-if="isHasNew" class="pl-2 red--text">{{ checkNew(product.created_at) }}</span>
          </h4>
        </div>

        <v-menu
          bottom
          transition="slide-y-transition"
        >
          <template #activator="{on, attrs}">
            <v-btn
              icon
              v-bind="attrs"
              v-on="on"
            >
              <v-icon color="black">
                mdi-dots-vertical
              </v-icon>
            </v-btn>
          </template>

          <v-list>
            <v-list-item link>
              <form-product-dialog
                :product="product"
                :is-loading-form="isLoadingForm"
                :submit-type="`update`"
                :dialog="dialogProduct"
                @on-click-close="onClickClose"
                @on-click-save="onClickSave"
              >
                <template #activator="{ on, attrs }">
                  <v-list-item-title
                    v-bind="attrs"
                    v-on="on"
                    @click="onClickEdit"
                  >
                    {{ $t('lspCustomer.edit') }}
                  </v-list-item-title>
                </template>
              </form-product-dialog>
            </v-list-item>
            <!-- <v-dialog
              v-model="dialogDeleteProduct"
              max-width="600px"
            >
              <template #activator="{ on, attrs }">
                <v-list-item
                  key="delete"
                  v-bind="attrs"
                  v-on="on"
                  @click="onOpenDeleteDialog"
                >
                  <v-list-item-title>
                    {{ $t('lspCustomer.delete') }}
                  </v-list-item-title>
                </v-list-item>
              </template>
              <v-card>
                <v-card-title class="text-h6 lighten-2">
                  {{ $t('lspCustomer.confirmation_delete') }} {{ product.name }}
                </v-card-title>

                <v-card-text>
                  {{ $t('lspCustomer.confirm_delete_text') }}
                </v-card-text>

                <v-divider />

                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    color="primary"
                    text
                    :loading="isLoadingForm"
                    @click="onClickDelete"
                  >
                    {{ $t('lspCustomer.yes') }}
                  </v-btn>
                  <v-btn
                    color="primary"
                    @click="onCloseDeleteDialog"
                  >
                    {{ $t('lspCustomer.cancel') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog> -->
          </v-list>
        </v-menu>
      </v-card-title>

      <v-card-text class="pa-0">
        <v-row class="ma-0">
          <v-col class="pa-0 mr-2 text-secondary">
            <p class="body-1 ma-0 mb-2">
              {{ $t('lspCustomer.identity') }}: <span class="black--text">{{ product.identity }}</span>
            </p>
            <p class="body-1 ma-0 mb-2">
              {{ $t('lspCustomer.desc_unit') }}: <span class="black--text">{{ product.unit }}</span>
            </p>
            <p class="body-1 ma-0">
              {{ $t('lspCustomer.weight') }}: <span class="black--text">{{ product.weight }} KG</span>
            </p>
          </v-col>
          <v-col class="pa-0 mr-5">
            <p class="body-1 ma-0 mb-2">
              {{ $t('lspCustomer.type') }}: <span class="black--text">{{ product.product_type }}</span>
            </p>
            <p class="body-1 ma-0 mb-2">
              {{ $t('lspCustomer.unit_type') }}: <span class="black--text">{{ reformattedUnitType }}</span>
            </p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-container>

    <!-- <v-container class="pa-0">
      <v-divider class="my-5" />

      <v-card-actions class="pa-0">
        <v-expansion-panels accordion flat class="pa-0 custom-panel">
          <v-expansion-panel class="pa-0">
            <v-expansion-panel-header class="pa-0">
              <p class="subtitle-1 ma-0">
                {{ $t('lspCustomer.product_size') }}
              </p>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="body-1 pa-0">
              <v-row class="ma-0 mb-5">
                <v-col class="pa-0">
                  <p class="mb-2 text--secondary">
                    {{ $t('lspCustomer.length') }}
                  </p>
                  <p class="mb-0">
                    {{ product.dimension_length }} CM
                  </p>
                </v-col>
                <v-col class="mx-5 pa-0">
                  <p class="mb-2 text--secondary">
                    {{ $t('lspCustomer.width') }}
                  </p>
                  <p class="mb-0">
                    {{ product.dimension_width }} CM
                  </p>
                </v-col>
                <v-col class="pa-0">
                  <p class="mb-2 text--secondary">
                    {{ $t('lspCustomer.height') }}
                  </p>
                  <p class="mb-0">
                    {{ product.dimension_height }} CM
                  </p>
                </v-col>
              </v-row>
              <v-row class="ma-0">
                <v-col class="mr-5 pa-0">
                  <p class="mb-2 text--secondary">
                    Volume
                  </p>
                  <p class="mb-0">
                    {{ product.volume }} CBM
                  </p>
                </v-col>
              </v-row>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-actions>
    </v-container> -->
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import FormProductDialog from '~/components/shipping-company/FormProductDialog.vue'
import { Product } from '~/types/product'
import { checkIsNew } from '~/utils/functions'

export default Vue.extend({
  name: 'ProductCardItem',

  components: { FormProductDialog },

  props: {
    product: {
      type: Object as () => Product,
      default: null
    },
    isLoadingForm: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    },
    dialogProduct: {
      type: Boolean,
      default: false
    },
    dialogDeleteProduct: {
      type: Boolean,
      default: false
    }
  },

  data: () => ({ }),

  computed: {
    reformattedUnitType (): any {
      const unitType = this.product?.unit_type

      if (unitType === 'PACKAGE') {
        return 'Package'
      } else {
        return 'Non-Package'
      }
    },
    isHasNew () {
      const user = this.$auth.user?.data as any

      if (user.role === 'ADMIN') {
        return false
      } else {
        return true
      }
    }

  },

  methods: {
    onClickEdit () {
      this.$emit('on-open-edit-dialog')
    },
    onClickClose () {
      this.$emit('on-close-edit-dialog')
    },
    onClickSave (formValue: any) {
      this.$emit('on-click-save', formValue, this.index)
    },
    onOpenDeleteDialog () {
      this.$emit('on-open-delete-dialog', this.index)
    },
    onCloseDeleteDialog () {
      this.$emit('on-close-delete-dialog', this.index)
    },
    onClickDelete () {
      this.$emit('on-click-delete', this.product?.id, this.index)
    },
    checkNew (date: string) {
      return checkIsNew(date)
    }
  }
})
</script>

<style lang="scss" scoped>

.v-expansion-panels.custom-panel {
  overflow: hidden;
}

.v-expansion-panels.custom-panel .v-expansion-panel-header {
  border-radius: 4px;
  padding: 0 20px;
  min-height: 52px;
}

.v-expansion-panels.custom-panel .v-expansion-panel-content::v-deep .v-expansion-panel-content__wrap {
  padding: 0 !important;
  height: auto !important;
}
</style>

import { MutationTree } from 'vuex'
import { ShipmentCompanyCreateOrderState } from './state'

export const mutations: MutationTree<ShipmentCompanyCreateOrderState> = {
  SET_DRAFT_ITEM (state, item: any) {
    state.itemDraft = item
  },
  SET_NEW_ORDER_ID (state, item: any) {
    state.newOrderId = item
  },
  SET_PUBLISHED_ITEM (state, item: any) {
    state.itemPublished = item
  },
  SET_IS_LOADING (state, isLoading: any) {
    state.isLoading = isLoading
  },
  SET_IS_LOADING_FORM (state, isLoadingForm: any) {
    state.isLoadingForm = isLoadingForm
  },
  SET_IS_LOADING_DETAIL (state, isLoadingDetail: any) {
    state.isLoadingDetail = isLoadingDetail
  },
  SET_DRAFT_IS_EXIST (state, draftIsExist: any) {
    state.draftIsExist = draftIsExist
  },
  SET_ERROR_MESSAGE (state, errorMessage: any) {
    state.errorMessage = errorMessage
  }
}

export default mutations
